import {
    FileDeletedEvent,
    FileDidMoveEvent,
} from "@augment-internal/sidecar-libs/src/agent/agent-edit-types";
import { FileType } from "@augment-internal/sidecar-libs/src/client-interfaces/client-workspaces";
import * as fs from "fs";
import * as os from "os";
import * as path from "path";

import {
    BeachheadClientWorkspaces,
    getQualifiedPath,
} from "../../client-interfaces/client-workspaces";
import * as vscode from "../../vscode";
import { BeachheadWorkspaceManager } from "../../workspace-manager";

describe("ClientWorkspaces utils", () => {
    test("getQualifiedPath simple", async () => {
        const result = getQualifiedPath("file.txt", "/workspace/root");
        expect(result).toEqual({
            rootPath: "/workspace/root",
            relPath: "file.txt",
        });
    });

    test("getQualifiedPath nested", async () => {
        const result = getQualifiedPath("example/dir/file.txt", "/workspace/root");
        expect(result).toEqual({
            rootPath: "/workspace/root",
            relPath: "example/dir/file.txt",
        });
    });

    test("getQualifiedPath absolute", async () => {
        const result = getQualifiedPath("/workspace/root/example/dir/file.txt", "/workspace/root");
        expect(result).toEqual({
            rootPath: "/workspace/root",
            relPath: "example/dir/file.txt",
        });
    });

    test("getQualifiedPath path not in workspace", async () => {
        const result = getQualifiedPath("/other/root/example/dir/file.txt", "/workspace/root");
        expect(result).toBeUndefined();
    });
});

describe("BeachheadClientWorkspaces.getPathInfo", () => {
    let workspaceManager: BeachheadWorkspaceManager;
    let clientWorkspaces: BeachheadClientWorkspaces;
    let tempDir: string;

    beforeEach(() => {
        // Create a temporary directory for testing
        tempDir = fs.mkdtempSync(path.join(os.tmpdir(), "beachhead-test-"));

        // Create a mock workspace manager
        workspaceManager = {
            workspaceRoot: tempDir,
            getCheckpoint: jest.fn().mockReturnValue(undefined),
            createSnapshot: jest.fn().mockReturnValue(Date.now()),
            getChangesSince: jest.fn().mockResolvedValue([]),
            awaitBlobsUploaded: jest.fn().mockResolvedValue(undefined),
            onFileDeleted: new vscode.EventEmitter<FileDeletedEvent>().event,
            onFileDidMove: new vscode.EventEmitter<FileDidMoveEvent>().event,
        };

        clientWorkspaces = new BeachheadClientWorkspaces(workspaceManager);
    });

    afterEach(() => {
        // Clean up the temporary directory
        fs.rmSync(tempDir, { recursive: true, force: true });
    });

    test("getPathInfo for a file", async () => {
        // Create a test file
        const filePath = path.join(tempDir, "test-file.txt");
        fs.writeFileSync(filePath, "test content");

        // Test getPathInfo for the file
        const pathInfo = await clientWorkspaces.getPathInfo(filePath);

        expect(pathInfo.exists).toBe(true);
        expect(pathInfo.type).toBe(FileType.File);
        expect(pathInfo.filepath).toBeDefined();
        expect(pathInfo.filepath?.rootPath).toBe(tempDir);
        expect(pathInfo.filepath?.relPath).toBe("test-file.txt");
    });

    test("getPathInfo for a directory", async () => {
        // Create a test directory
        const dirPath = path.join(tempDir, "test-dir");
        fs.mkdirSync(dirPath);

        // Test getPathInfo for the directory
        const pathInfo = await clientWorkspaces.getPathInfo(dirPath);

        expect(pathInfo.exists).toBe(true);
        expect(pathInfo.type).toBe(FileType.Directory);
        expect(pathInfo.filepath).toBeDefined();
        expect(pathInfo.filepath?.rootPath).toBe(tempDir);
        expect(pathInfo.filepath?.relPath).toBe("test-dir");
    });

    test("getPathInfo for a non-existent path", async () => {
        // Test getPathInfo for a non-existent path
        const nonExistentPath = path.join(tempDir, "non-existent");
        const pathInfo = await clientWorkspaces.getPathInfo(nonExistentPath);

        expect(pathInfo.exists).toBe(false);
        expect(pathInfo.filepath).toBeDefined();
        expect(pathInfo.filepath?.rootPath).toBe(tempDir);
        expect(pathInfo.filepath?.relPath).toBe("non-existent");
    });

    test("getPathInfo for a path outside the workspace", async () => {
        // Test getPathInfo for a path outside the workspace
        const outsidePath = path.join(os.tmpdir(), "outside-workspace");
        const pathInfo = await clientWorkspaces.getPathInfo(outsidePath);

        expect(pathInfo.exists).toBe(false);
        expect(pathInfo.filepath).toBeUndefined();
    });
});
