import { ChatResultNodeType } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { APIError } from "@augment-internal/sidecar-libs/src/exceptions";
import { ToolsModel } from "@augment-internal/sidecar-libs/src/tools/tools-model";
import { APIStatus } from "@augment-internal/sidecar-libs/src/utils/types";
import { FeatureFlags } from "beachhead/src/feature-flags";
import * as fs from "fs";
import * as os from "os";
import * as path from "path";
import { promisify } from "util";
import { v4 as uuidv4 } from "uuid";

import { AgentLoop, clipToolResult, makeMessageNodes } from "../../agent_loop/agent_loop";
import { StatePersistence } from "../../agent_loop/state-persistence";
import { APIServerImplWithErrorReporting as APIServerImpl, ChatResult } from "../../augment-api";
import { EventTimer } from "../../event-timer";
import { ChangedFile, FileChangeType } from "../../remote-agent-manager/types";
import { RemoteAgentStatus } from "../../remote-agent-manager/types";
import { SSHConnectionFinder } from "../../ssh-connections";
import { BeachheadWorkspaceManager } from "../../workspace-manager";

// Mock dependencies
jest.mock("../../augment-api");
jest.mock("@augment-internal/sidecar-libs/src/tools/tools-model");
jest.mock("../../workspace-manager");

describe("AgentLoop", () => {
    let agentLoop: AgentLoop;
    let mockApiServer: jest.Mocked<APIServerImpl>;
    let mockToolsModel: jest.Mocked<ToolsModel>;
    let mockWorkspaceManager: jest.Mocked<BeachheadWorkspaceManager>;
    let mockSSHConnectionFinder: jest.Mocked<SSHConnectionFinder>;

    beforeEach(() => {
        jest.clearAllMocks();

        // Setup mocks
        mockApiServer = {
            chatStream: jest.fn(),
            createRequestId: jest.fn().mockReturnValue("mock-request-id"),
            reportRemoteAgentChatHistory: jest.fn().mockResolvedValue(undefined),
            agentWorkspaceReportStatus: jest.fn().mockResolvedValue(undefined),
        } as unknown as jest.Mocked<APIServerImpl>;

        mockToolsModel = {
            callTool: jest.fn(),
            getToolDefinitions: jest.fn().mockReturnValue([]),
        } as unknown as jest.Mocked<ToolsModel>;

        mockWorkspaceManager = {
            workspaceRoot: "/mock/workspace",
            getCheckpoint: jest.fn(),
            createSnapshot: jest.fn().mockReturnValue(Date.now()),
            getChangesSince: jest.fn().mockReturnValue([]),
            awaitBlobsUploaded: jest.fn().mockResolvedValue(undefined),
            onFileDeleted: {} as any,
            onFileDidMove: {} as any,
        };

        // Mock chatStream to return a response with no tool call by default
        mockApiServer.chatStream.mockResolvedValue(
            createMockChatStream([createRawResponseChatChunk("Test response")])
        );

        // Mock callTool to return a result
        mockToolsModel.callTool = jest.fn().mockResolvedValue({
            text: "Tool result",
            isError: false,
        });
        mockApiServer.agentWorkspaceReportStatus.mockClear();
        mockApiServer.reportRemoteAgentChatHistory.mockClear();

        mockSSHConnectionFinder = {
            startBackgroundMonitoring: jest.fn(),
            hasActiveSSHConnections: jest.fn().mockReturnValue(false),
        } as unknown as jest.Mocked<SSHConnectionFinder>;

        // Setup agent loop
        agentLoop = new AgentLoop(
            mockApiServer,
            mockToolsModel,
            mockWorkspaceManager,
            false,
            "test-agent-id",
            mockSSHConnectionFinder,
            {
                agentReportStreamedChatEveryChunk: 3,
                agentMaxTotalChangedFilesSizeBytes: 2 * 1024 * 1024,
                agentMaxChangedFilesSkippedPaths: 1000,
                agentIdleStatusUpdateIntervalMs: 60 * 1000,
                agentMaxIterations: 100,
            } as any as FeatureFlags,
            undefined,
            undefined
        );
    });

    describe("run", () => {
        it("should not update workspace after agent turn when no tool call is found", async () => {
            await agentLoop.run(makeMessageNodes("Test message"), new EventTimer());

            // Verify that workspace manager's getChangesSince method was not called
            expect(mockWorkspaceManager.getChangesSince).not.toHaveBeenCalled();

            // Verify that reportRemoteAgentChatHistory was called
            expect(mockApiServer.reportRemoteAgentChatHistory).toHaveBeenCalled();

            // Verify that agentWorkspaceReportStatus was called with the correct parameters
            expect(mockApiServer.agentWorkspaceReportStatus).toHaveBeenCalledTimes(2);
            // First call should be with RUNNING status
            expect(mockApiServer.agentWorkspaceReportStatus).toHaveBeenNthCalledWith(
                1,
                "test-agent-id",
                RemoteAgentStatus.agentRunning,
                false
            );
            // Second call should be with IDLE status
            expect(mockApiServer.agentWorkspaceReportStatus).toHaveBeenNthCalledWith(
                2,
                "test-agent-id",
                RemoteAgentStatus.agentIdle,
                false
            );
        });

        it("should update workspace after tool call", async () => {
            // First call returns a tool call
            mockApiServer.chatStream.mockResolvedValueOnce(
                createMockChatStream([createToolCallChatChunk()])
            );

            await agentLoop.run(makeMessageNodes("Test message"), new EventTimer());

            // Verify that workspace manager's getChangesSince method was called once after the tool call
            // but not after the agent turn (since there's no tool call in the final response)
            expect(mockWorkspaceManager.getChangesSince).toHaveBeenCalledTimes(1);

            // Verify that agentWorkspaceReportStatus was called with the correct parameters
            expect(mockApiServer.agentWorkspaceReportStatus).toHaveBeenCalledTimes(2);
            // First call should be with RUNNING status
            expect(mockApiServer.agentWorkspaceReportStatus).toHaveBeenNthCalledWith(
                1,
                "test-agent-id",
                RemoteAgentStatus.agentRunning,
                false
            );
            // Second call should be with IDLE status (after tool call completes)
            expect(mockApiServer.agentWorkspaceReportStatus).toHaveBeenNthCalledWith(
                2,
                "test-agent-id",
                RemoteAgentStatus.agentIdle,
                false
            );
        });
    });

    describe("chatHistory", () => {
        it("should return chat history with sequence_id and finished_at", async () => {
            // Setup the agent state with a completed exchange
            const requestNodes = makeMessageNodes("Test message");
            await agentLoop.run(requestNodes, new EventTimer());

            // Get the chat history
            const [history, _] = agentLoop.chatHistory();

            // Verify the chat history structure
            expect(history.chat_history.length).toBeGreaterThan(0);
            const exchange = history.chat_history[0];

            // Check that the exchange has the required fields
            expect(exchange).toHaveProperty("exchange");
            expect(exchange).toHaveProperty("changed_files");
            expect(exchange).toHaveProperty("sequence_id");
            expect(exchange).toHaveProperty("finished_at");

            // Verify sequence_id is a number
            expect(typeof exchange.sequence_id).toBe("number");

            // Verify finished_at is a valid ISO date string
            expect(typeof exchange.finished_at).toBe("string");
            expect(() => new Date(exchange.finished_at)).not.toThrow();
        });

        it("should report chat history after finishing a response", async () => {
            // Reset the mock to track calls
            mockApiServer.reportRemoteAgentChatHistory.mockClear();

            // Run the agent
            await agentLoop.run(makeMessageNodes("Test message"), new EventTimer());

            // Verify reportRemoteAgentChatHistory was called with the correct parameters
            expect(mockApiServer.reportRemoteAgentChatHistory).toHaveBeenCalledTimes(1);

            // Get the arguments from the first call
            const args = mockApiServer.reportRemoteAgentChatHistory.mock.calls[0];

            // Verify the first argument is the conversation ID
            expect(typeof args[0]).toBe("string");

            // Verify the second argument is an array of exchanges
            expect(Array.isArray(args[1])).toBe(true);

            // Verify each exchange has the required fields
            if (args[1].length > 0) {
                const exchange = args[1][0];
                expect(exchange).toHaveProperty("exchange");
                expect(exchange).toHaveProperty("changed_files");
                expect(exchange).toHaveProperty("sequence_id");
                expect(exchange).toHaveProperty("finished_at");
            }
        });

        it("should correctly associate changed files with each exchange", async () => {
            // Mock getChangesSince to return different changed files for each call
            mockWorkspaceManager.getChangesSince
                .mockResolvedValueOnce([
                    {
                        old_path: "",
                        new_path: "file1.txt",
                        old_contents: "",
                        new_contents: "content1",
                        change_type: FileChangeType.added,
                    },
                ])
                .mockResolvedValueOnce([
                    {
                        old_path: "",
                        new_path: "file2.txt",
                        old_contents: "",
                        new_contents: "content2",
                        change_type: FileChangeType.added,
                    },
                ]);

            // First call returns a tool call
            mockApiServer.chatStream.mockResolvedValueOnce(
                createMockChatStream([createToolCallChatChunk(1)])
            );

            // Second call returns a normal response
            mockApiServer.chatStream.mockResolvedValueOnce(
                createMockChatStream([createRawResponseChatChunk("Second response", 2)])
            );

            // Run the agent with the first message
            await agentLoop.run(makeMessageNodes("First message"), new EventTimer());

            // Run the agent with the second message
            await agentLoop.run(makeMessageNodes("Second message"), new EventTimer());

            // Get the chat history
            const [history, _] = agentLoop.chatHistory();

            // Verify we have at least two exchanges
            expect(history.chat_history.length).toBeGreaterThanOrEqual(2);

            // Since the implementation has changed, we'll just verify that we have at least one exchange
            // with changed files and that the changed files are correctly associated with the exchange

            // Find any exchange with changed files
            const exchangeWithChangedFiles = history.chat_history.find(
                (exchange) => exchange.changed_files && exchange.changed_files.length > 0
            );

            // Verify we have at least one exchange with changed files
            expect(exchangeWithChangedFiles).toBeDefined();

            // Verify the exchange has a sequence ID
            expect(exchangeWithChangedFiles?.sequence_id).toBeGreaterThan(0);
        });
    });

    it("chatHistory lastCompletedSequenceId monotonically increasing", async () => {
        // Call tools 10 times
        for (let i = 0; i < 10; i++) {
            mockApiServer.chatStream.mockResolvedValueOnce(
                createMockChatStream([createToolCallChatChunk(i)])
            );
        }

        // Run the agent loop with a spy on chatHistory
        const chatHistorySpy = jest.spyOn(agentLoop, "chatHistory");
        await agentLoop.run(makeMessageNodes("Test message"), new EventTimer());

        // Confirm lastCompletedSequenceId is monotonically increasing
        let prevLastCompletedSequenceId = 0;
        const allReturnValues = chatHistorySpy.mock.results;
        allReturnValues.forEach((result, _index) => {
            const [_history, lastCompletedSequenceId] = result.value;
            expect(lastCompletedSequenceId).toBeGreaterThanOrEqual(prevLastCompletedSequenceId);
            prevLastCompletedSequenceId = lastCompletedSequenceId;
        });
    });

    it("should skip changed files when single file too large", async () => {
        // Mock getChangesSince to return a large changed file
        mockWorkspaceManager.getChangesSince.mockResolvedValueOnce([
            {
                old_path: "file.txt",
                new_path: "file.txt",
                old_contents: "",
                new_contents: "a".repeat(5 * 1000 * 1000),
                change_type: FileChangeType.added,
            },
        ]);

        // First call returns a tool call which will cause the workspace to be updated
        mockApiServer.chatStream.mockResolvedValueOnce(
            createMockChatStream([createToolCallChatChunk()])
        );

        await agentLoop.run(makeMessageNodes("Test message"), new EventTimer());
        const [history, _] = agentLoop.chatHistory();
        const lastExchange = history.chat_history[history.chat_history.length - 1];

        expect(lastExchange.changed_files).toEqual([]);
        expect(lastExchange.changed_files_skipped).toEqual(["file.txt"]);
        expect(lastExchange.changed_files_skipped_count).toEqual(1);
    });

    it("should skip changed files with total size too large", async () => {
        // Mock getChangesSince to return a many small changed files
        let files: ChangedFile[] = [];
        for (let i = 0; i < 1000; i++) {
            files.push({
                old_path: `file${i}.txt`,
                new_path: `file${i}.txt`,
                old_contents: "",
                new_contents: "a".repeat(5 * 1000),
                change_type: FileChangeType.added,
            });
        }
        mockWorkspaceManager.getChangesSince.mockResolvedValueOnce(files);

        // First call returns a tool call which will cause the workspace to be updated
        mockApiServer.chatStream.mockResolvedValueOnce(
            createMockChatStream([createToolCallChatChunk()])
        );

        await agentLoop.run(makeMessageNodes("Test message"), new EventTimer());
        const [history, _] = agentLoop.chatHistory();
        const lastExchange = history.chat_history[history.chat_history.length - 1];

        expect(lastExchange.changed_files).toEqual([]);
        expect(lastExchange.changed_files_skipped).toEqual(files.map((f) => f.old_path));
        expect(lastExchange.changed_files_skipped_count).toEqual(1000);
    });

    it("should interrupt agent loop when max iterations reached", async () => {
        // Return 1000 iterations with a tool call. This should cause the agent loop to exceed the max iterations
        for (let i = 0; i < 1000; i++) {
            mockApiServer.chatStream.mockResolvedValueOnce(
                createMockChatStream([createToolCallChatChunk(i)])
            );
        }

        await agentLoop.run(makeMessageNodes("Test message"), new EventTimer());

        // Verify that the agent loop did not exceed the max iterations
        const [chatHistoryResponse, _] = agentLoop.chatHistory();
        const chatHistory = chatHistoryResponse.chat_history;
        expect(chatHistory.length).toBeLessThan(1000);

        // Verify that the final response contains a iterations exceeded message
        const lastExchange = chatHistory[chatHistory.length - 1].exchange;
        const timeoutNode = lastExchange.response_nodes?.find((node) =>
            node.content?.includes("Your conversation has been paused")
        );
        expect(timeoutNode).toBeDefined();

        // Verify that agentWorkspaceReportStatus was called with the correct parameters
        // First call should be with RUNNING status
        expect(mockApiServer.agentWorkspaceReportStatus).toHaveBeenCalledWith(
            "test-agent-id",
            RemoteAgentStatus.agentRunning,
            false
        );
        // Last call should be with IDLE status (after max iterations)
        expect(mockApiServer.agentWorkspaceReportStatus).toHaveBeenLastCalledWith(
            "test-agent-id",
            RemoteAgentStatus.agentIdle,
            false
        );
    });

    describe("state persistence and resuming", () => {
        const mkdirAsync = promisify(fs.mkdir);
        const rmAsync = promisify(fs.rm);
        let testDir: string;
        let statePersistence: StatePersistence;
        let persistentAgentLoop: AgentLoop;

        beforeEach(async () => {
            // Create a temporary directory for the test
            testDir = path.join(os.tmpdir(), `agent_loop_test_${uuidv4()}`);
            await mkdirAsync(testDir, { recursive: true });
            statePersistence = new StatePersistence(testDir);
            persistentAgentLoop = new AgentLoop(
                mockApiServer,
                mockToolsModel,
                mockWorkspaceManager,
                false,
                "test-agent-id",
                mockSSHConnectionFinder,
                {
                    agentReportStreamedChatEveryChunk: 3,
                    agentMaxTotalChangedFilesSizeBytes: 2 * 1024 * 1024,
                    agentMaxChangedFilesSkippedPaths: 1000,
                    agentIdleStatusUpdateIntervalMs: 60 * 1000,
                    agentMaxIterations: 100,
                } as any as FeatureFlags,
                statePersistence,
                undefined
            );
        });

        afterEach(async () => {
            try {
                await rmAsync(testDir, { recursive: true, force: true });
            } catch (error) {
                // Log error but don't fail the test
                // Using a no-op function to avoid ESLint warnings about console.warn
                const logger = {
                    warn: jest.fn(),
                };
                logger.warn("Error cleaning up test directory - manual cleanup may be needed");
                logger.warn(error);
            }
        });

        it("should persist state changes to disk", async () => {
            await persistentAgentLoop.run(makeMessageNodes("Test message"), new EventTimer());

            const exists = statePersistence.checkSavedStateExists();
            expect(exists).toBe(true);

            // Load the state and verify it's correct
            const loadedState = await statePersistence.loadLatestAgentState();
            expect(loadedState.status).toBe(RemoteAgentStatus.agentIdle);

            // Verify the chat history contains our message
            const chatHistory = loadedState.chatHistoryForAPI;
            expect(chatHistory).toBeDefined();
            expect(chatHistory?.length).toBe(1);
            expect(chatHistory?.[0].request_nodes).toBeDefined();
            expect(chatHistory?.[0].request_nodes?.length).toBe(1);
            expect(chatHistory?.[0].request_nodes?.[0].text_node?.content).toBe("Test message");
        });

        it("should persist state when running and resume from disk after shutdown", async () => {
            const firstAgentLoop = new AgentLoop(
                mockApiServer,
                mockToolsModel,
                mockWorkspaceManager,
                false,
                "test-agent-id",
                mockSSHConnectionFinder,
                {
                    agentReportStreamedChatEveryChunk: 3,
                    agentMaxTotalChangedFilesSizeBytes: 2 * 1024 * 1024,
                    agentMaxChangedFilesSkippedPaths: 1000,
                    agentIdleStatusUpdateIntervalMs: 60 * 1000,
                    agentMaxIterations: 100,
                } as any as FeatureFlags,
                statePersistence,
                undefined
            );

            // Configure mock for the first chat response
            mockApiServer.chatStream.mockResolvedValueOnce(
                createMockChatStream([createRawResponseChatChunk("First response", 1)])
            );

            // Run the first agent loop with a message
            await firstAgentLoop.run(makeMessageNodes("First message"), new EventTimer());

            // Verify the state was persisted to disk
            const stateExists = statePersistence.checkSavedStateExists();
            expect(stateExists).toBe(true);

            // Create a second agent loop that should load state from disk
            mockApiServer.chatStream.mockResolvedValueOnce(
                createMockChatStream([createRawResponseChatChunk("Second response", 2)])
            );

            // Load the state from disk
            const loadResult = await statePersistence.loadLatestAgentState();

            // Create a second agent loop with the loaded state
            const secondAgentLoop = new AgentLoop(
                mockApiServer,
                mockToolsModel,
                mockWorkspaceManager,
                false,
                "test-agent-id",
                mockSSHConnectionFinder,
                {
                    agentReportStreamedChatEveryChunk: 3,
                    agentMaxTotalChangedFilesSizeBytes: 2 * 1024 * 1024,
                    agentMaxChangedFilesSkippedPaths: 1000,
                    agentIdleStatusUpdateIntervalMs: 60 * 1000,
                    agentMaxIterations: 100,
                } as any as FeatureFlags,
                statePersistence,
                loadResult
            );

            // Verify the chat history was loaded correctly in the second agent loop
            const [initialChatHistoryResponse, ..._unUsed] = secondAgentLoop.chatHistory();
            const initialChatHistory = initialChatHistoryResponse.chat_history;
            expect(initialChatHistory).toBeDefined();
            expect(initialChatHistory.length).toBe(1);
            expect(initialChatHistory[0].exchange.request_nodes?.[0].text_node?.content).toBe(
                "First message"
            );
            expect(initialChatHistory[0].exchange.response_text).toBe("First response");

            // Run the second agent loop with a new message
            await secondAgentLoop.run(makeMessageNodes("Second message"), new EventTimer());

            // Verify the chat history now has both exchanges
            const [updatedChatHistoryResponse, ..._unused2] = secondAgentLoop.chatHistory();
            const updatedChatHistory = updatedChatHistoryResponse.chat_history;
            expect(updatedChatHistory).toBeDefined();
            expect(updatedChatHistory.length).toBe(2);

            // Verify the first exchange is still there
            expect(updatedChatHistory[0].exchange.request_nodes?.[0].text_node?.content).toBe(
                "First message"
            );
            expect(updatedChatHistory[0].exchange.response_text).toBe("First response");

            // Verify the new exchange was added
            expect(updatedChatHistory[1].exchange.request_nodes?.[0].text_node?.content).toBe(
                "Second message"
            );
            expect(updatedChatHistory[1].exchange.response_text).toBe("Second response");
        });

        // TODO(AU-10583): Add tests for crash recovery
    });

    describe("poll updates", () => {
        it("should handle multiple chat requests from poll updates", async () => {
            // Mock chatStream to return multiple responses
            mockApiServer.chatStream
                .mockResolvedValueOnce(
                    createMockChatStream([createRawResponseChatChunk("First response", 1)])
                )
                .mockResolvedValueOnce(
                    createMockChatStream([createRawResponseChatChunk("Second response", 2)])
                );

            const firstRequestNodes = makeMessageNodes("First poll message");
            const secondRequestNodes = makeMessageNodes("Second poll message");

            const runSpy = jest.spyOn(agentLoop, "run");

            await agentLoop.run(firstRequestNodes, new EventTimer());
            await agentLoop.run(secondRequestNodes, new EventTimer());

            expect(mockApiServer.chatStream).toHaveBeenCalledTimes(2);
            expect(runSpy).toHaveBeenNthCalledWith(1, firstRequestNodes, expect.anything());
            expect(runSpy).toHaveBeenNthCalledWith(2, secondRequestNodes, expect.anything());
            runSpy.mockRestore();

            // Verify that the chat history contains both exchanges
            const [chatHistoryResponse, _] = agentLoop.chatHistory();
            const chatHistory = chatHistoryResponse.chat_history;
            expect(chatHistory.length).toBe(2);
            expect(chatHistory[0].exchange.request_nodes).toEqual(firstRequestNodes);
            expect(chatHistory[0].exchange.response_text).toBe("First response");
            expect(chatHistory[1].exchange.request_nodes).toEqual(secondRequestNodes);
            expect(chatHistory[1].exchange.response_text).toBe("Second response");
        });

        it("should handle interrupt updates from poll updates", async () => {
            // Reset mocks before test
            mockApiServer.agentWorkspaceReportStatus.mockClear();

            // Mock chatStream to return a response
            mockApiServer.chatStream.mockResolvedValue(
                createMockChatStream([createRawResponseChatChunk("Response before interrupt", 1)])
            );
            const initialRequestNodes = makeMessageNodes("Initial message");
            const runPromise = agentLoop.run(initialRequestNodes, new EventTimer());

            const interruptSpy = jest.spyOn(agentLoop, "interrupt");

            await agentLoop.interrupt();
            await runPromise;
            expect(interruptSpy).toHaveBeenCalled();
            expect(agentLoop.status()).toBe(RemoteAgentStatus.agentIdle);

            // Verify that agentWorkspaceReportStatus was called with the correct parameters
            // In the interrupt test, we're mocking the pollInstructionUpdate method to call interrupt
            // but the status reporting happens before the interrupt is fully processed
            expect(mockApiServer.agentWorkspaceReportStatus).toHaveBeenCalledWith(
                "test-agent-id",
                RemoteAgentStatus.agentRunning,
                false
            );

            interruptSpy.mockRestore();
        });
    });

    describe("silent flag functionality", () => {
        it("should pass silent=true when isNextMessageSilent is set", async () => {
            // Manually set the isNextMessageSilent flag
            agentLoop["isNextMessageSilent"] = true;

            // Mock chatStream to capture the arguments
            const chatStreamSpy = jest.spyOn(mockApiServer, "chatStream");
            mockApiServer.chatStream.mockResolvedValue(
                createMockChatStream([createRawResponseChatChunk("Test response")])
            );

            // Run the agent loop
            await agentLoop.run(makeMessageNodes("Test message"), new EventTimer());

            // Verify that chatStream was called with silent=true
            expect(chatStreamSpy).toHaveBeenCalledWith(
                expect.any(String), // requestId
                "", // message
                expect.any(Array), // chatHistory
                expect.any(Object), // blobs
                expect.any(Array), // userGuidedBlobs
                expect.any(Array), // externalSourceIds
                "", // model
                expect.any(Object), // vcsChange
                expect.any(Array), // recentChanges
                undefined, // contextCodeExchangeRequestId
                undefined, // selectedCode
                undefined, // prefix
                undefined, // suffix
                undefined, // pathName
                undefined, // language
                undefined, // sessionId
                undefined, // disableAutoExternalSources
                "", // userGuidelines
                "", // workspaceGuidelines
                expect.any(Array), // toolDefinitions
                expect.any(Array), // nodes
                expect.any(String), // mode
                "", // agentMemories
                undefined, // personaType
                [], // rules
                true // silent - this should be true
            );

            // Verify that the flag is reset after use
            expect(agentLoop["isNextMessageSilent"]).toBe(false);
        });

        it("should pass silent=false when isNextMessageSilent is not set", async () => {
            // Mock chatStream to capture the arguments
            const chatStreamSpy = jest.spyOn(mockApiServer, "chatStream");
            mockApiServer.chatStream.mockResolvedValue(
                createMockChatStream([createRawResponseChatChunk("Test response")])
            );

            // Run the agent loop normally
            await agentLoop.run(makeMessageNodes("Test message"), new EventTimer());

            // Verify that chatStream was called with silent=false
            expect(chatStreamSpy).toHaveBeenCalledWith(
                expect.any(String), // requestId
                "", // message
                expect.any(Array), // chatHistory
                expect.any(Object), // blobs
                expect.any(Array), // userGuidedBlobs
                expect.any(Array), // externalSourceIds
                "", // model
                expect.any(Object), // vcsChange
                expect.any(Array), // recentChanges
                undefined, // contextCodeExchangeRequestId
                undefined, // selectedCode
                undefined, // prefix
                undefined, // suffix
                undefined, // pathName
                undefined, // language
                undefined, // sessionId
                undefined, // disableAutoExternalSources
                "", // userGuidelines
                "", // workspaceGuidelines
                expect.any(Array), // toolDefinitions
                expect.any(Array), // nodes
                expect.any(String), // mode
                "", // agentMemories
                undefined, // personaType
                [], // rules
                false // silent - this should be false
            );
        });
    });

    describe("chatStream errors", () => {
        it("should retry on TypeError: terminated", async () => {
            // Throw an error on the first call
            mockApiServer.chatStream.mockRejectedValueOnce(new TypeError("terminated"));

            await agentLoop.run(makeMessageNodes("Test message"), new EventTimer());

            expect(mockApiServer.chatStream).toHaveBeenCalledTimes(2);
            const chatHistory = agentLoop.chatHistory(false /* incrementalOnly */)[0];
            expect(chatHistory.chat_history.length).toBe(1);
            expect(chatHistory.chat_history[0].exchange.response_text).toBe("Test response");
        });

        it("should retry on APIError: Unavailable", async () => {
            // Throw an error on the first call
            mockApiServer.chatStream.mockRejectedValueOnce(
                new APIError(APIStatus.unavailable, "Unavailable")
            );

            await agentLoop.run(makeMessageNodes("Test message"), new EventTimer());

            expect(mockApiServer.chatStream).toHaveBeenCalledTimes(2);
            const chatHistory = agentLoop.chatHistory(false /* incrementalOnly */)[0];
            expect(chatHistory.chat_history.length).toBe(1);
            expect(chatHistory.chat_history[0].exchange.response_text).toBe("Test response");
        });

        it("should NOT retry on APIError: augmentTooLarge", async () => {
            // Throw an error on the first call
            mockApiServer.chatStream.mockRejectedValueOnce(
                new APIError(APIStatus.augmentTooLarge, "Too large")
            );

            await expect(
                agentLoop.run(makeMessageNodes("Test message"), new EventTimer())
            ).rejects.toThrow(APIError);
            expect(mockApiServer.chatStream).toHaveBeenCalledTimes(1);
        });

        it("should set silent flag on retry", async () => {
            // Throw an error on the first call
            mockApiServer.chatStream.mockRejectedValueOnce(
                new APIError(APIStatus.unavailable, "Unavailable")
            );

            await agentLoop.run(makeMessageNodes("Test message"), new EventTimer());

            expect(mockApiServer.chatStream).toHaveBeenCalledTimes(2);
            // 25 is he index of the silent flag
            expect(mockApiServer.chatStream.mock.calls[0][25]).toBe(false); // Initial call should NOT be silent
            expect(mockApiServer.chatStream.mock.calls[1][25]).toBe(true); // Retry call should be silent
        });
    });
});

describe("clipToolResult", () => {
    it("should clip tool result if too large", () => {
        const toolResult = {
            text: "x".repeat(1000),
            isError: false,
        };
        const clippedResult = clipToolResult(toolResult, 100);
        // When we clip in the middle we add a truncation message, so the result can be a bit larger
        // than maxBytes
        expect(clippedResult.text.length).toBeLessThanOrEqual(200);
    });

    it("should not clip tool result if small enough", () => {
        const toolResult = {
            text: "small result",
            isError: false,
        };
        const clippedResult = clipToolResult(toolResult, 100);
        expect(clippedResult.text).toBe("small result");
    });
});

function createMockChatStream(chunks: ChatResult[]): AsyncIterable<ChatResult> {
    const mockAsyncIterator = { next: jest.fn() };
    for (const chunk of chunks) {
        mockAsyncIterator.next.mockResolvedValueOnce({ done: false, value: chunk });
    }
    mockAsyncIterator.next.mockResolvedValueOnce({ done: true, value: undefined });
    return {
        [Symbol.asyncIterator]: () => mockAsyncIterator,
    };
}

function createRawResponseChatChunk(text: string, id: number = 1): ChatResult {
    return {
        text: text,
        nodes: [
            {
                id: id,
                type: ChatResultNodeType.RAW_RESPONSE,
                content: text,
            },
        ],
    };
}

function createToolCallChatChunk(id: number = 1): ChatResult {
    return {
        text: `Response ${id} with tool call`,
        nodes: [
            {
                id: id,
                type: ChatResultNodeType.TOOL_USE,
                content: "Test tool call",
                tool_use: {
                    tool_use_id: `test-tool-use-id-${id}`,
                    tool_name: "test-tool",
                    input_json: "{}",
                },
            },
        ],
    };
}
