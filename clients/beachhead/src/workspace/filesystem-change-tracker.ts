import { exec, execSync } from "child_process";
import { FSWatcher, watch, WatchEventType } from "fs";
import * as fs from "fs";
import * as path from "path";
import { promisify } from "util";

import { getErrmsg } from "../exceptions";
import { type AugmentLogger, getLogger } from "../logging";
import { ChangedFile, FileChangeType } from "../remote-agent-manager/types";
import { DisposableService } from "../utils/disposable-service";
import { PathAcceptance } from "../utils/path-acceptance";
import { FullPathFilter } from "../utils/path-iterator";
import { FileType } from "../utils/types";
import * as vscode from "../vscode";

const execAsync = promisify(exec);

/**
 * File metadata for incremental tracking with content storage
 */
export interface FileMetadata {
    /** The normalized path of the file relative to the workspace root */
    path: string;
    /** The modification time of the file */
    mtime: number;
    /** The content of the file at the time of tracking (undefined for files exceeding size limit) */
    content?: string;
    /** Whether this file is marked as deleted (used to prevent git fallback for deleted files) */
    deleted?: boolean;
}

/**
 * Tracks filesystem changes using filesystem notifications with monotonic timestamps
 * for optimized change detection. Stores file contents to provide before/after content
 * in change events.
 */
export class FilesystemChangeTracker extends DisposableService {
    private _logger: AugmentLogger;
    private _workspaceRoot: string;
    private _pathFilter: FullPathFilter;
    private _currentState: Map<string, FileMetadata>;
    private _watchers = new Map<string, FSWatcher>();
    private _watcherInitialized = false;
    private _pendingChanges = new Map<string, ChangedFile>();
    private _debounceTimer: NodeJS.Timeout | undefined;
    private _debounceMs = 100;
    private _recentChanges = new Map<string, number>();
    private _changeHistoryMs = 1000;
    private _maxPendingChanges = 10000;
    private _rapidChangeDebounceMs = 50;

    // Snapshot tracking for efficient getChangesSince
    private readonly _snapshots = new Map<number, Map<string, FileMetadata>>(); // timestamp -> snapshot
    private _maxFileSizeBytes: number;
    private _baselineCommit: string;
    private _isGitRepository: boolean;

    // Event emitter for file change events
    private readonly _onFileChangeEmitter = this.addDisposable(
        new vscode.EventEmitter<ChangedFile>()
    );

    constructor(
        workspaceRoot: string,
        pathFilter: FullPathFilter,
        maxFileSizeBytes: number = 1024 * 1024
    ) {
        super();
        this._logger = getLogger("FilesystemChangeTracker");
        this._workspaceRoot = workspaceRoot;
        this._pathFilter = pathFilter;
        this._maxFileSizeBytes = maxFileSizeBytes;

        this._currentState = new Map<string, FileMetadata>();

        this._isGitRepository = this._checkIsGitRepository();

        if (this._isGitRepository) {
            this._baselineCommit = this._getCurrentCommitHash();
            this._logger.debug(`Using git baseline commit: ${this._baselineCommit}`);
            // Initialize cache for dirty files in git repository
            this._initializeDirtyFileCache();
        } else {
            this._baselineCommit = "HEAD";
            this._logger.debug("Not in a git repository, using file cache fallback");
            this._initializeFileCache();
        }

        this._initializeWatcher();
    }

    /**
     * Safely read file content as string, returning empty string if file cannot be read or exceeds size limit
     */
    private async _readFileContent(filePath: string): Promise<string> {
        try {
            const stats = await fs.promises.lstat(filePath);

            if (stats.isDirectory()) {
                this._logger.debug(
                    `Skipping directory ${filePath} - cannot read directory content`
                );
                return "";
            }

            if (stats.isSymbolicLink()) {
                // Never follow symlinks - return empty content
                return "";
            } else {
                if (stats.size > this._maxFileSizeBytes) {
                    this._logger.debug(
                        `File ${filePath} exceeds size limit (${stats.size} > ${this._maxFileSizeBytes}), skipping content tracking`
                    );
                    return "";
                }
            }

            const content = await fs.promises.readFile(filePath, "utf8");
            return content;
        } catch (error) {
            this._logger.error(`Could not read file content for ${filePath}: ${getErrmsg(error)}`);
            return "";
        }
    }

    /**
     * Check if the workspace is a git repository
     */
    private _checkIsGitRepository(): boolean {
        try {
            execSync("git rev-parse --git-dir", {
                cwd: this._workspaceRoot,
                stdio: "pipe",
            });
            return true;
        } catch (error) {
            return false;
        }
    }

    /**
     * Get the current git commit hash for baseline content retrieval
     * Should only be called when we know we're in a git repository
     */
    private _getCurrentCommitHash(): string {
        try {
            const commit = execSync("git rev-parse HEAD", {
                cwd: this._workspaceRoot,
                encoding: "utf8",
                stdio: "pipe",
            }).trim();
            this._logger.debug(`Stored baseline commit: ${commit}`);
            return commit;
        } catch (error) {
            this._logger.error(`Failed to get git commit hash: ${getErrmsg(error)}`);
            return "HEAD";
        }
    }

    /**
     * Initialize file cache by scanning all workspace files when git is not available
     */
    private _initializeFileCache(): void {
        this._logger.debug("Initializing file cache for non-git repository");
        try {
            this._scanDirectoryForCache(".");
            this._logger.debug(`Cached ${this._currentState.size} files for baseline comparison`);
        } catch (error) {
            this._logger.error(`Failed to initialize file cache: ${getErrmsg(error)}`);
        }
    }

    /**
     * Recursively scan directory and cache all files
     */
    private _scanDirectoryForCache(relativePath: string): void {
        const fullPath = path.join(this._workspaceRoot, relativePath);

        try {
            const entries = fs.readdirSync(fullPath, { withFileTypes: true });

            for (const entry of entries) {
                const entryRelativePath = path.join(relativePath, entry.name).replace(/\\/g, "/");
                const entryFullPath = path.join(fullPath, entry.name);

                if (
                    !this._pathFilter.acceptsPath(
                        entryRelativePath,
                        entry.isFile() ? FileType.file : FileType.directory
                    )
                ) {
                    continue;
                }

                if (entry.isFile() || entry.isSymbolicLink()) {
                    try {
                        const lStats = fs.lstatSync(entryFullPath);
                        let fileSize = lStats.size;

                        if (lStats.isSymbolicLink()) {
                            // Never follow symlinks - use empty size and continue
                            fileSize = 0;
                        }

                        if (fileSize <= this._maxFileSizeBytes) {
                            const content = fs.readFileSync(entryFullPath);
                            const fileMetadata: FileMetadata = {
                                path: entryRelativePath,
                                mtime: lStats.mtimeMs,
                                content: content.toString("utf8"),
                            };
                            this._currentState.set(entryRelativePath, fileMetadata);
                        }
                    } catch (error) {
                        this._logger.error(
                            `Error caching file ${entryRelativePath}: ${getErrmsg(error)}`
                        );
                    }
                } else if (entry.isDirectory()) {
                    this._scanDirectoryForCache(entryRelativePath);
                }
            }
        } catch (error) {
            this._logger.error(`Error scanning directory ${relativePath}: ${getErrmsg(error)}`);
        }
    }

    /**
     * Initialize cache for dirty files in git repository
     * This method finds all files that are modified or untracked compared to HEAD
     * and caches their current working directory content
     */
    private _initializeDirtyFileCache(): void {
        this._logger.debug("Initializing dirty file cache for git repository");
        try {
            const dirtyFiles = this._getDirtyFiles();
            if (dirtyFiles.length === 0) {
                this._logger.debug("No dirty files found in git repository");
                return;
            }

            this._cacheDirtyFiles(dirtyFiles);
        } catch (error) {
            this._logger.error(`Failed to initialize dirty file cache: ${getErrmsg(error)}`);
        }
    }

    /**
     * Get list of dirty files including untracked files
     * Uses git status --porcelain and extracts just the file paths
     */
    private _getDirtyFiles(): string[] {
        const gitStatus = execSync("git status --porcelain | cut -c4-", {
            cwd: this._workspaceRoot,
            encoding: "utf8",
            stdio: "pipe",
            shell: "/bin/bash", // Required for pipe command
        }).trim();

        if (!gitStatus) {
            return [];
        }

        return gitStatus.split("\n").filter((line) => line.trim());
    }

    /**
     * Cache dirty files for baseline comparison
     */
    private _cacheDirtyFiles(dirtyFiles: string[]): void {
        for (const filePath of dirtyFiles) {
            if (this._shouldCacheDirtyFile(filePath)) {
                this._cacheSingleDirtyFile(filePath);
            }
        }
    }

    /**
     * Check if a dirty file should be cached
     * Note: git diff only shows files, never directories, so we always use FileType.file
     */
    private _shouldCacheDirtyFile(filePath: string): boolean {
        return this._pathFilter.acceptsPath(filePath, FileType.file);
    }

    /**
     * Cache a single dirty file
     */
    private _cacheSingleDirtyFile(filePath: string): void {
        const fullPath = path.join(this._workspaceRoot, filePath);

        try {
            const stats = fs.lstatSync(fullPath);

            // Skip directories
            if (stats.isDirectory()) {
                return;
            }

            const fileSize = this._getEffectiveFileSize(fullPath, stats);
            if (fileSize === null) {
                return; // Broken symlink or other issue
            }

            if (fileSize <= this._maxFileSizeBytes) {
                const content = fs.readFileSync(fullPath);
                const fileMetadata: FileMetadata = {
                    path: filePath,
                    mtime: stats.mtimeMs,
                    content: content.toString("utf8"),
                };
                this._currentState.set(filePath, fileMetadata);
                this._logger.debug(`Cached dirty file: ${filePath}`);
            } else {
                this._logger.debug(
                    `Skipping large dirty file ${filePath} (${fileSize} > ${this._maxFileSizeBytes})`
                );
            }
        } catch (error) {
            // File doesn't exist in working tree - it's deleted, get content from git
            this._cacheDeletedFile(filePath);
        }
    }

    /**
     * Cache a deleted file by getting its content from git HEAD
     */
    private _cacheDeletedFile(filePath: string): void {
        try {
            // Store content in unified cache and mark as deleted
            const fileMetadata: FileMetadata = {
                path: filePath,
                mtime: 0, // Deleted files don't have meaningful mtime
                content: undefined,
                deleted: true, // Mark as deleted to prevent git fallback
            };
            this._currentState.set(filePath, fileMetadata);
            this._logger.debug(`Cached deleted file from git: ${filePath}`);
        } catch (error) {
            this._logger.error(
                `Error caching deleted file ${filePath} from git: ${getErrmsg(error)}`
            );
        }
    }

    /**
     * Get effective file size, handling symlinks
     */
    private _getEffectiveFileSize(_fullPath: string, stats: fs.Stats): number | null {
        if (stats.isSymbolicLink()) {
            // Never follow symlinks - return 0 size
            return 0;
        }
        return stats.size;
    }

    /**
     * Check if a file existed in the baseline commit
     */
    private _fileExistedInBaseline(relativePath: string): boolean {
        if (!this._isGitRepository) {
            return this._currentState.has(relativePath);
        }

        try {
            execSync(`git cat-file -e ${this._baselineCommit}:"${relativePath}"`, {
                cwd: this._workspaceRoot,
                stdio: "pipe",
            });
            return true;
        } catch (error) {
            return false;
        }
    }

    /**
     * Get file content from cache or git baseline - used for "before" content when we don't have it cached
     * Always checks file cache first (for dirty files and non-git repos), then falls back to git if available
     */
    private async _getFileContentFromCache(
        relativePath: string,
        snapshot?: Map<string, FileMetadata>
    ): Promise<string | undefined> {
        // Check unified cache first (contains all cached content)
        if (!snapshot) {
            snapshot = this._currentState;
        }
        const existingFile = snapshot.get(relativePath);
        if (existingFile?.content !== undefined) {
            this._logger.debug(`Retrieved content for ${relativePath} from unified cache`);
            return existingFile.content;
        }

        // If file is explicitly marked as deleted in cache, don't fall back to git
        if (existingFile?.deleted) {
            this._logger.debug(
                `File ${relativePath} is marked as deleted in cache, returning undefined`
            );
            return undefined;
        }

        // If not in git repository, we can't get content from git
        if (!this._isGitRepository) {
            return undefined;
        }

        // Try to get content from git as fallback

        // Check if file exists in baseline commit before trying to retrieve content
        if (!this._fileExistedInBaseline(relativePath)) {
            this._logger.debug(
                `File ${relativePath} did not exist in baseline commit ${this._baselineCommit}, skipping content retrieval`
            );
            return undefined;
        }

        // Try to get content from git as fallback
        try {
            const gitCommand = `git show ${this._baselineCommit}:"${relativePath}"`;
            const result = await execAsync(gitCommand, {
                cwd: this._workspaceRoot,
                maxBuffer: this._maxFileSizeBytes,
            });
            const content = result.stdout;
            this._logger.debug(
                `Retrieved content for ${relativePath} from baseline commit ${this._baselineCommit}`
            );
            return content;
        } catch (error) {
            const errorMsg = getErrmsg(error);
            // Don't log ENOBUFS errors as errors - they're expected for very large files
            if (errorMsg.includes("ENOBUFS") || errorMsg.includes("maxBuffer")) {
                this._logger.debug(
                    `File ${relativePath} too large for git content retrieval (${errorMsg}), skipping content`
                );
            } else {
                this._logger.error(
                    `Could not read file content from baseline commit for ${relativePath}: ${errorMsg}`
                );
            }
            return undefined;
        }
    }

    /**
     * Create a ChangedFile with appropriate content based on change type
     */
    private async _createChangeEvent(
        relativePath: string,
        changeType: "create" | "modify" | "delete",
        _mtime?: number
    ): Promise<ChangedFile> {
        const absPath = path.join(this._workspaceRoot, relativePath);

        let fileChangeType: FileChangeType;
        switch (changeType) {
            case "create":
                fileChangeType = FileChangeType.added;
                break;
            case "modify":
                fileChangeType = FileChangeType.modified;
                break;
            case "delete":
                fileChangeType = FileChangeType.deleted;
                break;
        }

        let oldContents = "";
        let newContents = "";

        if (changeType === "create") {
            oldContents = "";
            newContents = await this._readFileContent(absPath);
        } else if (changeType === "modify") {
            oldContents = (await this._getFileContentFromCache(relativePath)) || "";
            newContents = await this._readFileContent(absPath);
        } else if (changeType === "delete") {
            oldContents = (await this._getFileContentFromCache(relativePath)) || "";
            newContents = "";
        }

        return {
            old_path: relativePath,
            new_path: relativePath,
            old_contents: oldContents,
            new_contents: newContents,
            change_type: fileChangeType,
        };
    }

    /**
     * Event that fires when file changes are processed
     */
    public get onFileChange(): vscode.Event<ChangedFile> {
        return this._onFileChangeEmitter.event;
    }

    /**
     * Create a snapshot of the current filesystem state for change tracking
     * @returns Snapshot ID that can be used with getChangesSince()
     */
    public createSnapshot(): number {
        const timestamp = Number(process.hrtime.bigint() / 1000000n);

        // Only capture snapshot if _currentState is initialized (not during construction)
        if (this._currentState) {
            const snapshotFiles = new Map<string, FileMetadata>();

            for (const [filePath, metadata] of this._currentState) {
                // Deep copy the metadata
                snapshotFiles.set(filePath, {
                    path: metadata.path,
                    content: metadata.content,
                    mtime: metadata.mtime,
                    deleted: metadata.deleted,
                });
            }

            this._snapshots.set(timestamp, snapshotFiles);
            this._logger.debug(
                `Captured snapshot at timestamp ${timestamp} with ${snapshotFiles.size} files`
            );
        }

        return timestamp;
    }

    /**
     * Wait for all pending changes to be processed
     * @returns Promise that resolves when all pending changes have been processed
     */
    public async waitForPendingChanges(): Promise<void> {
        if (this._pendingChanges.size === 0) {
            return;
        }

        if (this._debounceTimer) {
            return new Promise<void>((resolve) => {
                const originalTimer = this._debounceTimer;
                if (originalTimer) {
                    clearTimeout(originalTimer);
                    this._debounceTimer = setTimeout(() => {
                        void this._processChanges().then(() => resolve());
                    }, 0);
                } else {
                    resolve();
                }
            });
        }

        await this._processChanges();
    }

    /**
     * Get changes since a specific timestamp using snapshot-based comparison
     * @param snapshotId The timestamp to get changes since
     * @returns Array of changed files since the timestamp
     */
    public async getChangesSince(snapshotId: number): Promise<ChangedFile[]> {
        await this.waitForPendingChanges();

        const snapshot = this._snapshots.get(snapshotId);
        if (!snapshot) {
            return [];
        }

        const changes: ChangedFile[] = [];
        const allFiles = new Set<string>();

        // Collect all files from both current state and snapshot
        for (const filePath of this._currentState.keys()) {
            allFiles.add(filePath);
        }
        for (const filePath of snapshot.keys()) {
            allFiles.add(filePath);
        }

        // Compare each file between snapshot and current state
        for (const filePath of allFiles) {
            const oldContent = await this._getFileContentFromCache(filePath, snapshot);
            const newContent = await this._getFileContentFromCache(filePath);

            if (oldContent !== newContent) {
                let changeType: FileChangeType;
                if (oldContent === undefined) {
                    changeType = FileChangeType.added;
                } else if (newContent === undefined) {
                    changeType = FileChangeType.deleted;
                } else {
                    changeType = FileChangeType.modified;
                }

                changes.push({
                    old_path: filePath,
                    new_path: filePath,
                    old_contents: oldContent || "",
                    new_contents: newContent || "",
                    change_type: changeType,
                });
            }
        }

        // Clean up this specific snapshot since it's been used
        this._snapshots.delete(snapshotId);
        this._logger.debug(
            `Used and cleaned up snapshot for timestamp ${snapshotId}, found ${changes.length} changes`
        );

        return changes;
    }

    /**
     * Initialize the filesystem watcher with robust error handling
     */
    private _initializeWatcher(): void {
        if (this._watcherInitialized) {
            return;
        }

        this._initializeSelectiveWatcher();
    }

    /**
     * Initialize a selective filesystem watcher that avoids problematic directories
     */
    private _initializeSelectiveWatcher(): void {
        try {
            const rootWatcher = watch(
                this._workspaceRoot,
                { recursive: false, persistent: true },
                (eventType, filename) => {
                    this._safeHandleFileSystemEvent(eventType, filename);
                }
            );

            rootWatcher.on("error", (error: any) => {
                this._logger.error(`Filesystem watcher error (non-critical): ${getErrmsg(error)}`);
            });

            this._watchers.set(".", rootWatcher);
            this._addSelectiveSubdirectoryWatchers();

            this._watcherInitialized = true;
            this._logger.debug(
                `Selective filesystem watcher initialized for ${this._workspaceRoot}`
            );
        } catch (error) {
            this._logger.error(
                `Failed to initialize selective filesystem watcher: ${getErrmsg(error)}`
            );
            this._initializePollingFallback();
        }
    }

    /**
     * Add watchers for specific subdirectories, avoiding problematic ones like node_modules
     */
    private _addSelectiveSubdirectoryWatchers(): void {
        try {
            const entries = fs.readdirSync(this._workspaceRoot, { withFileTypes: true });

            for (const entry of entries) {
                if (!entry.isDirectory()) {
                    continue;
                }

                const dirName = entry.name;
                const dirPath = path.join(this._workspaceRoot, dirName);

                const dirAccepted = this._pathFilter.acceptsPath(dirName, FileType.directory);
                if (!dirAccepted) {
                    this._logger.debug(`Skipping watcher for filtered directory: ${dirName}`);
                    continue;
                }

                this._addDirectoryWatcher(dirName, dirPath);
            }
        } catch (error) {
            this._logger.error(
                `Failed to add selective subdirectory watchers: ${getErrmsg(error)}`
            );
        }
    }

    /**
     * Add a watcher for a specific directory using non-recursive watching
     * to avoid issues with broken symlinks in subdirectories
     */
    private _addDirectoryWatcher(dirName: string, dirPath: string): void {
        if (this._watchers.has(dirName)) {
            return;
        }

        try {
            const stats = fs.lstatSync(dirPath);
            if (!stats.isDirectory()) {
                this._logger.debug(`Skipping watcher for non-directory: ${dirName}`);
                return;
            }
        } catch (error) {
            this._logger.debug(
                `Skipping watcher for inaccessible directory ${dirName}: ${getErrmsg(error)}`
            );
            return;
        }

        const dirAccepted = this._pathFilter.acceptsPath(dirName, FileType.directory);
        if (!dirAccepted) {
            this._logger.debug(`Skipping watcher for filtered directory: ${dirName}`);
            return;
        }

        try {
            const subWatcher = watch(
                dirPath,
                { recursive: false, persistent: true },
                (eventType, filename) => {
                    const fullPath = filename ? path.join(dirName, filename) : dirName;
                    this._safeHandleFileSystemEvent(eventType, fullPath);
                }
            );

            subWatcher.on("error", (error: any) => {
                this._logger.error(
                    `Subdirectory watcher error for ${dirName}: ${getErrmsg(error)}`
                );
                subWatcher.close();
                this._watchers.delete(dirName);
            });

            this._watchers.set(dirName, subWatcher);
            this._logger.debug(`Added filesystem watcher for directory: ${dirName}`);

            this._addWatchersForSubdirectories(dirName, dirPath);
        } catch (error) {
            this._logger.error(`Failed to watch subdirectory ${dirName}: ${getErrmsg(error)}`);
        }
    }

    /**
     * Add watchers for subdirectories that pass the path filter
     */
    private _addWatchersForSubdirectories(parentDirName: string, parentDirPath: string): void {
        try {
            const entries = fs.readdirSync(parentDirPath, { withFileTypes: true });

            for (const entry of entries) {
                if (!entry.isDirectory()) {
                    continue;
                }

                const subDirName = path.join(parentDirName, entry.name);
                const subDirPath = path.join(parentDirPath, entry.name);

                const subDirAccepted = this._pathFilter.acceptsPath(subDirName, FileType.directory);
                if (subDirAccepted) {
                    this._addDirectoryWatcher(subDirName, subDirPath);
                } else {
                    this._logger.debug(`Skipping watcher for filtered subdirectory: ${subDirName}`);
                }
            }
        } catch (error) {
            this._logger.debug(
                `Error reading subdirectories of ${parentDirName}: ${getErrmsg(error)}`
            );
        }
    }

    /**
     * Scan a directory for existing files and generate events for them
     */
    private async _scanDirectoryForExistingFiles(dirName: string, dirPath: string): Promise<void> {
        try {
            const entries = await fs.promises.readdir(dirPath, { withFileTypes: true });

            for (const entry of entries) {
                const entryPath = path.join(dirPath, entry.name);
                const relativePath = path.join(dirName, entry.name);

                if (entry.isFile() || entry.isSymbolicLink()) {
                    const pathInfo = this._pathFilter.getPathInfo(
                        relativePath,
                        entry.isFile() ? FileType.file : FileType.other
                    );

                    if (pathInfo.accepted) {
                        try {
                            const stats = await fs.promises.lstat(entryPath);

                            const changeEvent = await this._createChangeEvent(
                                relativePath,
                                "create",
                                Math.floor(stats.mtimeMs)
                            );

                            this._pendingChanges.set(relativePath, changeEvent);
                            this._logger.debug(
                                `Found existing file in new directory: ${relativePath}`
                            );
                        } catch (error) {
                            this._logger.error(
                                `Error reading existing file ${relativePath}: ${getErrmsg(error)}`
                            );
                        }
                    }
                } else if (entry.isDirectory()) {
                    const subDirAccepted = this._pathFilter.acceptsPath(
                        relativePath,
                        FileType.directory
                    );
                    if (subDirAccepted) {
                        void this._addDirectoryWatcher(relativePath, entryPath);
                        void this._scanDirectoryForExistingFiles(relativePath, entryPath);
                    } else {
                        this._logger.debug(
                            `Skipping watcher for filtered subdirectory: ${relativePath}`
                        );
                    }
                }
            }

            if (this._pendingChanges.size > 0) {
                this._debouncedProcessChanges();
            }
        } catch (error) {
            this._logger.error(`Error scanning directory ${dirName}: ${getErrmsg(error)}`);
        }
    }

    /**
     * Handle directory deletion by generating deletion events for any files that were in the directory
     * but weren't detected by individual file deletion events (due to race conditions)
     */
    private async _handleDirectoryDeletion(deletedDirPath: string): Promise<void> {
        try {
            const filesToDelete: string[] = [];

            for (const [filePath] of this._currentState) {
                // Only process files that are inside the deleted directory, not the directory itself
                if (filePath.startsWith(deletedDirPath + "/")) {
                    if (
                        !this._pendingChanges.has(filePath) ||
                        this._pendingChanges.get(filePath)?.change_type !== FileChangeType.deleted
                    ) {
                        filesToDelete.push(filePath);
                    }
                }
            }

            this._logger.debug(
                `Directory ${deletedDirPath} deleted, generating deletion events for ${filesToDelete.length} files: ${filesToDelete.join(", ")}`
            );

            for (const filePath of filesToDelete) {
                const fileMetadata = this._currentState.get(filePath);
                if (fileMetadata) {
                    const changeEvent = await this._createChangeEvent(filePath, "delete");

                    this._pendingChanges.set(filePath, changeEvent);
                    this._logger.debug(
                        `Generated deletion event for file in deleted directory: ${filePath}`
                    );
                }
            }

            if (filesToDelete.length > 0) {
                this._debouncedProcessChanges();
            }
        } catch (error) {
            this._logger.error(
                `Error handling directory deletion for ${deletedDirPath}: ${getErrmsg(error)}`
            );
        }
    }

    /**
     * Fallback when filesystem watching fails - just log and continue
     */
    private _initializePollingFallback(): void {
        this._logger.warn("Filesystem watching failed - change detection disabled");
        this._watcherInitialized = true;
    }

    /**
     * Safely handle filesystem events with proper error handling
     */
    private _safeHandleFileSystemEvent(eventType: WatchEventType, filename: string | null): void {
        this._logger.debug(`Raw filesystem event: ${eventType} ${filename}`);
        try {
            void this._handleFileSystemEvent(eventType, filename);
        } catch (error) {
            this._logger.error(
                `Error handling filesystem event for ${filename}: ${getErrmsg(error)}`
            );
        }
    }

    /**
     * Handle filesystem events from the watcher
     */
    private async _handleFileSystemEvent(
        eventType: WatchEventType,
        filename: string | null
    ): Promise<void> {
        if (!filename) {
            return;
        }

        // Early filtering check
        if (!this._shouldProcessPath(filename)) {
            return;
        }

        // Debouncing and rate limiting
        if (!this._shouldProcessEvent(filename)) {
            return;
        }

        const absPath = path.join(this._workspaceRoot, filename);

        // Get file info once
        const fileInfo = await this._getFileInfo(absPath, filename);
        if (!fileInfo) {
            return; // Error already logged in _getFileInfo
        }

        // Handle directory events
        if (fileInfo.fileType === FileType.directory) {
            this._handleDirectoryEvent(eventType, filename, absPath, fileInfo.exists);
            return; // Don't report directory changes as file events
        }

        // Handle file events
        const changeEvent = await this._createFileChangeEvent(eventType, filename, fileInfo);
        if (changeEvent) {
            this._addPendingChange(filename, changeEvent);
        }
    }

    /**
     * Check if we should process this path based on filters
     */
    private _shouldProcessPath(filename: string): boolean {
        const fileAccepted = this._pathFilter.acceptsPath(filename, FileType.file);
        const dirAccepted = this._pathFilter.acceptsPath(filename, FileType.directory);

        if (!fileAccepted && !dirAccepted) {
            this._logger.debug(`Skipping event for filtered path: ${filename}`);
            return false;
        }
        return true;
    }

    /**
     * Check if we should process this event based on debouncing and rate limiting
     */
    private _shouldProcessEvent(filename: string): boolean {
        // Rate limiting
        if (this._pendingChanges.size >= this._maxPendingChanges) {
            this._logger.warn(
                `Too many pending changes (${this._pendingChanges.size}), processing immediately`
            );
            void this._processChanges();
        }

        // Debouncing
        const now = Number(process.hrtime.bigint() / 1000000n);
        const lastChange = this._recentChanges.get(filename);
        if (lastChange && now - lastChange < this._rapidChangeDebounceMs) {
            this._logger.debug(`Debouncing rapid change for ${filename}`);
            return false;
        }

        this._recentChanges.set(filename, now);
        this._cleanupChangeHistory(now);
        return true;
    }

    /**
     * Get file information (type, existence, stats) with proper error handling
     */
    private async _getFileInfo(
        absPath: string,
        filename: string
    ): Promise<{
        fileType: FileType;
        exists: boolean;
        stats?: fs.Stats;
        acceptance: PathAcceptance;
    } | null> {
        let fileType: FileType = FileType.file;
        let exists = false;
        let stats: fs.Stats | undefined;

        try {
            // Use lstat instead of existsSync to detect broken symlinks
            try {
                stats = await fs.promises.lstat(absPath);
                exists = true;
                fileType = stats.isDirectory() ? FileType.directory : FileType.file;
            } catch (lstatError) {
                // File doesn't exist or can't be accessed
                exists = false;
            }

            const acceptance = this._pathFilter.getPathInfo(filename, fileType);
            if (!acceptance.accepted) {
                return null;
            }

            return { fileType, exists, stats, acceptance };
        } catch (error) {
            const errorMsg = getErrmsg(error);
            if (errorMsg.includes("ENOENT")) {
                // File was deleted between existsSync check and lstat - race condition, ignore
                this._logger.debug(`File ${filename} deleted during processing, ignoring`);
            } else {
                // Other errors are worth logging
                this._logger.error(`Error checking file ${filename}: ${errorMsg}`);
            }
            return null;
        }
    }

    /**
     * Handle directory-specific events
     */
    private _handleDirectoryEvent(
        eventType: WatchEventType,
        filename: string,
        absPath: string,
        exists: boolean
    ): void {
        if (eventType === "rename") {
            if (exists) {
                void this._addDirectoryWatcher(filename, absPath);
                void this._scanDirectoryForExistingFiles(filename, absPath);
            } else {
                void this._handleDirectoryDeletion(filename);
            }
        }
    }

    /**
     * Create a file change event based on the event type and file info
     */
    private async _createFileChangeEvent(
        eventType: WatchEventType,
        filename: string,
        fileInfo: { exists: boolean; stats?: fs.Stats }
    ): Promise<ChangedFile | null> {
        this._logger.debug(
            `Filesystem event: ${eventType} ${filename} (exists: ${fileInfo.exists})`
        );

        // Handle special case: rename event for non-existent file that might be a directory
        if (eventType === "rename" && !fileInfo.exists && this._watchers.has(filename)) {
            void this._handleDirectoryDeletion(filename);
            return null;
        }

        // Create change event based on existence and event type
        if (fileInfo.exists && fileInfo.stats) {
            const mtime = Math.floor(fileInfo.stats.mtimeMs);

            if (eventType === "rename") {
                // For rename events, determine if it's create or modify based on baseline content
                const beforeContent = await this._getFileContentFromCache(filename);
                const changeType: "create" | "modify" =
                    beforeContent !== undefined ? "modify" : "create";

                this._logger.debug(`Rename event for ${filename}: changeType=${changeType}`);
                return this._createChangeEvent(filename, changeType, mtime);
            } else if (eventType === "change") {
                return this._createChangeEvent(filename, "modify", mtime);
            }
        } else {
            // File doesn't exist or can't be read → delete
            this._logger.debug(`File ${filename} doesn't exist, creating delete event`);
            return this._createChangeEvent(filename, "delete");
        }

        return null;
    }

    /**
     * Add a change event to pending changes
     */
    private _addPendingChange(filename: string, changeEvent: ChangedFile): void {
        this._logger.debug(
            `Adding to pending changes: ${FileChangeType[changeEvent.change_type]}:${filename}`
        );
        this._pendingChanges.set(filename, changeEvent);
        this._debouncedProcessChanges();
    }

    /**
     * Clean up old change history to prevent memory leaks
     */
    private _cleanupChangeHistory(now: number): void {
        for (const [path, timestamp] of this._recentChanges.entries()) {
            if (now - timestamp > this._changeHistoryMs) {
                this._recentChanges.delete(path);
            }
        }
    }

    /**
     * Debounced processing of pending changes
     */
    private _debouncedProcessChanges(): void {
        if (this._debounceTimer) {
            clearTimeout(this._debounceTimer);
        }

        this._debounceTimer = setTimeout(() => {
            void this._processChanges();
        }, this._debounceMs);
    }

    /**
     * Process all pending changes and update the current snapshot
     */
    private async _processChanges(): Promise<void> {
        if (this._pendingChanges.size === 0) {
            return;
        }

        const changes = Array.from(this._pendingChanges.values());
        this._pendingChanges.clear();

        this._logger.debug(
            `Processing ${changes.length} file changes: ${changes.map((c) => `${FileChangeType[c.change_type]}:${c.new_path || c.old_path}`).join(", ")}`
        );

        let hasChanges = false;

        for (const change of changes) {
            try {
                await this._applyChange(change);
                hasChanges = true;
            } catch (error) {
                this._logger.error(
                    `Error applying change for ${change.new_path || change.old_path}: ${getErrmsg(error)}`
                );
            }
        }

        if (hasChanges) {
            // Fire the file change event for each change
            for (const change of changes) {
                this._onFileChangeEmitter.fire(change);
            }
        }
    }

    /**
     * Apply a single file change to the current snapshot
     */
    private async _applyChange(change: ChangedFile): Promise<void> {
        const filePath = change.new_path || change.old_path;
        const changeType = change.change_type;

        if (changeType === FileChangeType.deleted) {
            // Mark file as deleted instead of removing it from cache
            // This prevents git fallback when checking if file existed in current state
            const deletedFileMetadata: FileMetadata = {
                path: filePath,
                mtime: 0,
                content: undefined,
                deleted: true,
            };
            this._currentState.set(filePath, deletedFileMetadata);
            this._logger.debug(`Marked ${filePath} as deleted in state`);
            return;
        }

        const absPath = path.join(this._workspaceRoot, filePath);
        try {
            const lStats = await fs.promises.lstat(absPath);
            let actualMtime = Math.floor(lStats.mtimeMs);
            const fileMetadata: FileMetadata = {
                path: filePath,
                mtime: actualMtime,
                content: change.new_contents,
            };

            this._currentState.set(filePath, fileMetadata);
            this._logger.debug(`Updated ${filePath} in state (${FileChangeType[changeType]})`);
        } catch (error) {
            const errorMsg = getErrmsg(error);
            if (errorMsg.includes("ENOENT")) {
                // File was deleted between existence check and lstat - this is a race condition
                if (this._currentState.has(filePath)) {
                    this._currentState.delete(filePath);
                    this._logger.debug(
                        `Removed file ${filePath} from state (deleted during processing)`
                    );
                }
            } else {
                // Other errors are worth logging
                this._logger.error(`Error reading file ${filePath}: ${errorMsg}`);
            }
        }
    }

    /**
     * Dispose of resources
     */
    public dispose(): void {
        if (this._debounceTimer) {
            clearTimeout(this._debounceTimer);
        }

        for (const [dirName, watcher] of this._watchers.entries()) {
            try {
                watcher.close();
            } catch (error) {
                this._logger.error(`Error closing watcher for ${dirName}: ${getErrmsg(error)}`);
            }
        }
        this._watchers.clear();

        super.dispose();
    }
}
