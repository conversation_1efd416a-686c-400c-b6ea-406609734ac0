local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';

function(cloud, env, namespace, namespace_config)
  local appName = 'audit-exporter';

  local config = {
    LogSourceProjectID: cloudInfo[cloud].projectId,
    LogSourceBucketLocation: 'global',
    LogSourceBucketID: if env == 'PROD' then 'au-app-audit' else 'app-audit-log-bucket',
    LogSourceViewID: '_AllLogs',
    TenantName: if env == 'PROD' then 'rubrik-cmk' else 'ysecurity-cmk',
    GCSDestinationBucketName: if env == 'PROD' then 'augment-us' else 'augment-audit-logs',
    GCSSecretName: if env == 'PROD' then 'audit-log-exporter-rubrik-cmk' else 'audit-log-exporter-ysecurity-cmk',
  };

  local serviceAccount = gcpLib.createServiceAccount(
    app=appName, cloud=cloud, env=env, namespace=namespace, iam=true
  );

  // For specific secret access (more granular approach)
  local secretForGcsBucketAccess = gcpLib.grantAccess(
    name='%s-specific-secret-policy' % appName,
    env=env,
    namespace=namespace,
    appName=appName,
    resourceRef={
      kind: 'SecretManagerSecret',
      external: 'projects/%s/secrets/%s' % [cloudInfo[cloud].projectId, config.GCSSecretName],
    },
    bindings=[
      {
        role: 'roles/secretmanager.secretAccessor',
        members: [
          {
            memberFrom: {
              serviceAccountRef: {
                name: serviceAccount.iamServiceAccountName,
              },
            },
          },
        ],
      },
    ],
  );

  // Grant Log Viewer permissions to the service account
  local logViewerAccess = gcpLib.grantAccess(
    name='%s-log-viewer-policy' % appName,
    env=env,
    namespace=namespace,
    appName=appName,
    resourceRef={
      kind: 'Project',
      external: 'project/%s' % cloudInfo[cloud].projectId,
    },
    bindings=[
      {
        role: 'roles/logging.viewer',
        members: [
          {
            memberFrom: {
              serviceAccountRef: {
                name: serviceAccount.iamServiceAccountName,
              },
            },
          },
        ],
      },
      {
        role: 'roles/logging.privateLogViewer',
        members: [
          {
            memberFrom: {
              serviceAccountRef: {
                name: serviceAccount.iamServiceAccountName,
              },
            },
          },
        ],
      },
    ],
  );


  local configMap = configMapLib.createConfigMap(appName=appName, namespace=namespace, config=config);

  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=null);

  local container = {
    name: appName,
    target: {
      name: '//services/security/audit-exporter:image',
      dst: appName,
    },
    volumeMounts: [
      configMap.volumeMountDef,
    ],
    args: [
      '--config-file',
      '/config/config.json',
    ],
    env: [
      {
        name: 'POD_NAME',
        valueFrom: {
          fieldRef: {
            fieldPath: 'metadata.name',
          },
        },
      },
      {
        name: 'POD_NAMESPACE',
        valueFrom: {
          fieldRef: {
            fieldPath: 'metadata.namespace',
          },
        },
      },
    ],
    resources: {
      limits: {
        cpu: 0.5,
        memory: '512Mi',
      },
    },
  };

  local pod = {
    serviceAccountName: serviceAccount.name,
    restartPolicy: 'Never',
    tolerations: tolerations,
    affinity: affinity,
    priorityClassName: cloudInfo.envToPriorityClass(env),
    containers: [
      container,
    ],
    volumes: [
      configMap.podVolumeDef,
    ],
  };

  local cronjob = {
    apiVersion: 'batch/v1',
    kind: 'CronJob',
    metadata: {
      name: appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    spec: {
      schedule: '0 5,17 * * *',
      // Don't start jobs that were skipped when this job was suspended. Set to 2 minutes to give
      // the scheduler some slack.
      startingDeadlineSeconds: 120,
      successfulJobsHistoryLimit: 2,
      failedJobsHistoryLimit: 4,
      concurrencyPolicy: 'Forbid',
      timeZone: 'America/Los_Angeles',
      jobTemplate: {
        metadata: {
          labels: {
            app: appName,
          },
        },
        spec: {
          // Disable backoff
          backoffLimit: 0,
          template: {
            metadata: {
              labels: {
                app: appName,
              },
            },
            spec: pod,
          },
        },
      },
    },
  };

  lib.flatten([
    configMap.objects,
    serviceAccount.objects,
    logViewerAccess,
    secretForGcsBucketAccess,
    cronjob,
  ])
