load("//tools/bzl:go.bzl", "go_binary", "go_library", "go_oci_image", "go_test")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:metadata.bzl", "metadata_test")

go_library(
    name = "audit-exporter-lib",
    srcs = ["main.go"],
    importpath = "github.com/augmentcode/augment/services/security/audit-exporter",
    visibility = ["//visibility:private"],
    deps = [
        "//base/logging:logging_go",
        "@com_github_rs_zerolog//log",
        "@com_google_cloud_go_logging//logadmin",
        "@com_google_cloud_go_secretmanager//apiv1",
        "@com_google_cloud_go_secretmanager//apiv1/secretmanagerpb",
        "@com_google_cloud_go_storage//:storage",
        "@org_golang_google_api//iterator",
        "@org_golang_google_api//option",
    ],
)

go_binary(
    name = "audit-exporter",
    embed = [":audit-exporter-lib"],
)

go_test(
    name = "audit-exporter-test",
    srcs = ["main_test.go"],
    embed = [":audit-exporter-lib"],
    deps = [
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//credentials/insecure",
        "@org_golang_google_grpc//status",
    ],
)

go_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":audit-exporter",
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        ":image",
    ],
    visibility = [
        "//services/deploy:__subpackages__",
    ],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:config-map-lib",
        "//deploy/common:dynamic-feature-flags-lib",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/gcp:gcp-lib",
        "//services/deploy:endpoints",
    ],
)

metadata_test(
    name = "metadata_test",
    timeout = "moderate",
    src = "METADATA.jsonnet",
    deps = [
        ":kubecfg",
    ],
)
