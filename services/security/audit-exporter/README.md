# Audit Log Exporter

This tool exports audit logs from Google Cloud Platform Log Analytics to a GCP bucket.

#### Please note, this is a temporary stop-gap solution

This is a temporary stop-gap solution until we build the full-featured audit log API endpoint for consumption by Enterprise Customers into their local SIEM. Latest the full-audit log API may be delivered is October, for <PERSON><PERSON><PERSON>. When this happens, we may remote this implementation. Please, do not use this stop-gap unless absolutely nessesary.

## Features

- Queries GCP Log Analytics for audit logs
- Filters logs by tenant name
- Writes logs to a local file
- Uploads the file to a GCP bucket
- Deletes the local file after upload

## Setting up Secrets

### One Secret Per Tenant

Each tenant requires its own secret in Google Secret Manager. This secret corresponds to the Customer's service account key used to authenticate with the GCP bucket for that tenant. The secret name should follow the convention:

```
audit-log-exporter-[NAME OF THE TENANT]
```

For example, for a tenant named "rubrik-cmk", the secret name would be:

```
audit-log-exporter-rubrik-cmk
```

## Command-Line Arguments

- `--config-file`: Path to configuration file (default: config.json)
- `--log-source-project-id`: GCP project ID for Log Analytics source
- `--log-source-bucket-location`: Location of the Log Analytics bucket (default: global)
- `--log-source-bucket-id`: ID of the Log Analytics bucket (default: au-app-audit)
- `--log-source-view-id`: ID of the view within the Log Analytics bucket (default: _AllLogs)
- `--tenant-name`: Tenant name to filter logs
- `--gcs-destination-bucket-name`: GCS bucket name to upload the exported logs to
- `--gcs-secret-name`: Name of the secret in Google Secret Manager for GCS credentials


## Troubleshooting
Common issues:
- Insufficient permissions for GCP resources
- Invalid credentials
