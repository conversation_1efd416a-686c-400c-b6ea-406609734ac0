package main

import (
	"context"
	"encoding/json"
	"errors"
	"flag"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"time"

	"cloud.google.com/go/logging/logadmin"
	secretmanager "cloud.google.com/go/secretmanager/apiv1"
	secretmanagerpb "cloud.google.com/go/secretmanager/apiv1/secretmanagerpb"
	"cloud.google.com/go/storage"
	"github.com/augmentcode/augment/base/logging"
	"github.com/rs/zerolog/log"
	"google.golang.org/api/iterator"
	"google.golang.org/api/option"
)

// Config holds all configuration parameters for the exporter.
type Config struct {
	LogSourceProjectID       string `json:"LogSourceProjectID"`
	LogSourceBucketLocation  string `json:"LogSourceBucketLocation"`
	LogSourceBucketID        string `json:"LogSourceBucketID"`
	LogSourceViewID          string `json:"LogSourceViewID"`
	TenantName               string `json:"TenantName"`
	GCSDestinationBucketName string `json:"GCSDestinationBucketName"`
	GCSSecretName            string `json:"GCSSecretName"`
	// OutputFile is configured via a flag only, not in config.json
}

func getConfiguration() (*Config, error) {
	// Define flags
	configFileFlag := flag.String("config-file", "config.json", "Path to configuration file.")
	logSourceProjectIDFlag := flag.String("log-source-project-id", "", "GCP project ID for Log Analytics source.")
	logSourceBucketLocationFlag := flag.String("log-source-bucket-location", "", "Location of the Log Analytics bucket (e.g., global, us-central1).")
	logSourceBucketIDFlag := flag.String("log-source-bucket-id", "", "ID of the Log Analytics bucket.")
	logSourceViewIDFlag := flag.String("log-source-view-id", "", "ID of the view within the Log Analytics bucket.")
	tenantNameFlag := flag.String("tenant-name", "", "Tenant name to filter logs.")
	gcsDestinationBucketNameFlag := flag.String("gcs-destination-bucket-name", "", "GCS bucket name to upload the exported logs to.")
	gcsSecretNameFlag := flag.String("gcs-secret-name", "", "Name of the secret in Google Secret Manager for GCS credentials.")

	flag.Parse()

	// Initialize config with defaults that can be overridden
	cfg := Config{
		LogSourceBucketLocation: "global",       // Default
		LogSourceBucketID:       "au-app-audit", // Default
		LogSourceViewID:         "_AllLogs",     // Default
	}

	// Load from config file if it exists
	loadedCfg, err := loadConfig(*configFileFlag)
	if err == nil {
		cfg = loadedCfg // config.json values override hardcoded defaults
	} else {
		if *configFileFlag != "config.json" || !os.IsNotExist(err) {
			return nil, fmt.Errorf("error loading config file %s: %w", *configFileFlag, err)
		}
		log.Info().Str("configFile", *configFileFlag).Msg("Config file not found or not specified, using defaults and command-line flags")
	}

	// Override with flags - flags take highest precedence
	if *logSourceProjectIDFlag != "" {
		cfg.LogSourceProjectID = *logSourceProjectIDFlag
	}
	if *logSourceBucketLocationFlag != "" {
		cfg.LogSourceBucketLocation = *logSourceBucketLocationFlag
	}
	if *logSourceBucketIDFlag != "" {
		cfg.LogSourceBucketID = *logSourceBucketIDFlag
	}
	if *logSourceViewIDFlag != "" {
		cfg.LogSourceViewID = *logSourceViewIDFlag
	}
	if *tenantNameFlag != "" {
		cfg.TenantName = *tenantNameFlag
	}
	if *gcsDestinationBucketNameFlag != "" {
		cfg.GCSDestinationBucketName = *gcsDestinationBucketNameFlag
	}
	if *gcsSecretNameFlag != "" {
		cfg.GCSSecretName = *gcsSecretNameFlag
	}

	// Validate required parameters
	if cfg.LogSourceProjectID == "" {
		return nil, errors.New("log source project ID is required. Set via --log-source-project-id flag or 'LogSourceProjectID' in config file")
	}
	if cfg.LogSourceBucketLocation == "" {
		return nil, errors.New("log source bucket location is required. Set via --log-source-bucket-location flag or 'LogSourceBucketLocation' in config file")
	}
	if cfg.LogSourceBucketID == "" {
		return nil, errors.New("log source bucket ID is required. Set via --log-source-bucket-id flag or 'LogSourceBucketID' in config file")
	}
	if cfg.LogSourceViewID == "" {
		return nil, errors.New("log source view ID is required. Set via --log-source-view-id flag or 'LogSourceViewID' in config file")
	}
	if cfg.TenantName == "" {
		return nil, errors.New("tenant name is required. Set via --tenant-name flag or 'TenantName' in config file")
	}
	if cfg.GCSDestinationBucketName == "" {
		return nil, errors.New("GCS destination bucket name is required. Set via --gcs-destination-bucket-name flag or 'GCSDestinationBucketName' in config file")
	}
	if cfg.GCSSecretName == "" {
		return nil, errors.New("GCS secret name is required. Set via --gcs-secret-name flag or 'GCSSecretName' in config file")
	}

	log.Info().
		Str("LogSourceProjectID", cfg.LogSourceProjectID).
		Str("LogSourceBucketLocation", cfg.LogSourceBucketLocation).
		Str("LogSourceBucketID", cfg.LogSourceBucketID).
		Str("LogSourceViewID", cfg.LogSourceViewID).
		Str("TenantName", cfg.TenantName).
		Msg("Log Source Configuration")

	return &cfg, nil
}

// loadConfig loads configuration from a JSON file.
func loadConfig(filePath string) (Config, error) {
	var cfg Config
	configFile, err := os.Open(filePath)
	if err != nil {
		return cfg, fmt.Errorf("failed to open config file %s: %w", filePath, err)
	}
	defer configFile.Close()

	decoder := json.NewDecoder(configFile)
	if err = decoder.Decode(&cfg); err != nil {
		return cfg, fmt.Errorf("failed to decode config file %s: %w", filePath, err)
	}
	return cfg, nil
}

// getSecret gets a secret from Google Secret Manager.
func getSecret(ctx context.Context, client *secretmanager.Client, projectID, secretID string) ([]byte, error) {
	if secretID == "" {
		log.Info().Msg("No GCS Secret specified, using Application Default Credentials for GCS")
		return nil, nil
	}
	log.Info().
		Str("GCSSecretName", secretID).
		Msg("Fetching GCS credentials from Secret Manager")

	accessRequest := &secretmanagerpb.AccessSecretVersionRequest{
		Name: fmt.Sprintf("projects/%s/secrets/%s/versions/latest", projectID, secretID),
	}

	result, err := client.AccessSecretVersion(ctx, accessRequest)
	if err != nil {
		return nil, fmt.Errorf("failed to access secret version '%s' in project '%s': %w", secretID, projectID, err)
	}
	return result.Payload.Data, nil
}

// uploadToGCS handles uploading the given source file to the specified GCS bucket.
// It uses provided credentialsJSON if not nil, otherwise falls back to ADC.
func uploadToGCS(ctx context.Context, bucketName, sourceFilePath string, credentialsJSON []byte) (string, error) {
	var client *storage.Client
	var err error

	log.Info().
		Str("outputFile", sourceFilePath).
		Str("bucketName", bucketName).
		Msg("Attempting to upload to GCS destination bucket")
	storageOpts := []option.ClientOption{}
	if credentialsJSON != nil {
		storageOpts = append(storageOpts, option.WithCredentialsJSON(credentialsJSON))
	}

	client, err = storage.NewClient(ctx, storageOpts...)
	if err != nil {
		return "", fmt.Errorf("storage.NewClient: %w", err)
	}
	defer client.Close()

	sourceFile, err := os.Open(sourceFilePath)
	if err != nil {
		return "", fmt.Errorf("os.Open source file %s: %w", sourceFilePath, err)
	}
	defer sourceFile.Close()

	objectName := getGcsObjectName(sourceFilePath)

	wc := client.Bucket(bucketName).Object(objectName).NewWriter(ctx)
	wc.ContentType = "application/json"

	if _, err = io.Copy(wc, sourceFile); err != nil {
		wc.Close()
		client.Bucket(bucketName).Object(objectName).Delete(ctx)
		return "", fmt.Errorf("io.Copy to GCS object %s: %w", objectName, err)
	}
	if err := wc.Close(); err != nil {
		return "", fmt.Errorf("GCS Writer.Close for object %s: %w", objectName, err)
	}

	gcsPath := fmt.Sprintf("gs://%s/%s", bucketName, objectName)
	log.Info().
		Str("filename", filepath.Base(sourceFilePath)).
		Str("gcsPath", gcsPath).
		Msg("Successfully uploaded file to GCS")
	return gcsPath, nil
}

func getGcsObjectName(sourceFilePath string) string {
	timestamp := time.Now().Format("20060102_150405")
	filename := filepath.Base(sourceFilePath)

	// Get current date components
	now := time.Now()
	year := now.Format("2006")
	month := now.Format("01")
	day := now.Format("02")

	// Per Rubrik's request, format the object name with date partitioning.
	// Ex.: audit_logs/year=2025/month=05/day=30/20250530_150000_<sourceFilePath>.json
	return fmt.Sprintf("audit_logs/year=%s/month=%s/day=%s/%s_%s",
		year, month, day, timestamp, filename)
}

func gatherLogEntries(ctx context.Context, cfg *Config, adminClient *logadmin.Client) (string, error) {
	// Determine output file path
	timestamp := time.Now().Format("20060102150405")
	outputFileName := fmt.Sprintf("%s-logs-%s.json", cfg.TenantName, timestamp)

	// always get to the start of the hour
	endTime := time.Now().UTC().Truncate(time.Hour)
	startTime := endTime.Add(-1 * 12 * time.Hour)
	endTimeFormatted := endTime.Format(time.RFC3339)
	startTimeFormatted := startTime.Format(time.RFC3339)
	filter := fmt.Sprintf("jsonPayload.tenant.name=\"%s\" AND timestamp >= \"%s\" AND timestamp <= \"%s\"", cfg.TenantName, startTimeFormatted, endTimeFormatted)
	log.Info().Str("filter", filter).Msg("Extracting logs from Cloud Logging using filter")

	iter := adminClient.Entries(ctx, logadmin.Filter(filter), logadmin.NewestFirst())

	log.Info().Str("outputFileName", outputFileName).Msg("Exporting logs to file")
	entryCount := 0

	file, err := os.OpenFile(outputFileName, os.O_CREATE|os.O_WRONLY|os.O_TRUNC, 0o644)
	if err != nil {
		return "", fmt.Errorf("failed to open export file '%s' for writing: %w", outputFileName, err)
	}
	defer file.Close()

	for {
		entry, err := iter.Next()
		if err == iterator.Done {
			break
		}
		if err != nil {
			return "", fmt.Errorf("failed during log iteration: %w", err)
		}

		// Original audkit log payload does not have timestamp.
		// Create a copy of the payload and add timestamp information
		var payloadWithTimestamp interface{}

		// Handle different payload types
		switch p := entry.Payload.(type) {
		case map[string]interface{}:
			// For map payloads, make a copy and add timestamp
			payloadCopy := make(map[string]interface{}, len(p)+1)
			for k, v := range p {
				payloadCopy[k] = v
			}
			payloadCopy["timestamp"] = entry.Timestamp
			payloadWithTimestamp = payloadCopy
		default:
			// For other types, try to marshal and unmarshal to add timestamp
			tempData, err := json.Marshal(entry.Payload)
			if err != nil {
				log.Warn().Err(err).Msg("Failed to marshal payload for timestamp addition, using original payload")
				payloadWithTimestamp = entry.Payload
			} else {
				var tempMap map[string]interface{}
				if err := json.Unmarshal(tempData, &tempMap); err != nil {
					log.Warn().Err(err).Msg("Failed to unmarshal payload for timestamp addition, using original payload")
					payloadWithTimestamp = entry.Payload
				} else {
					tempMap["timestamp"] = entry.Timestamp
					payloadWithTimestamp = tempMap
				}
			}
		}

		jsonData, err := json.Marshal(payloadWithTimestamp)
		if err != nil {
			log.Warn().
				Err(err).
				Str("insertID", entry.InsertID).
				Msg("Failed to marshal log entry to JSON, skipping entry")
			continue
		}

		if _, err = file.Write(jsonData); err != nil {
			log.Fatal().
				Err(err).
				Str("outputFileName", outputFileName).
				Msg("Failed writing JSON data to file")
		}
		if _, err = file.WriteString("\n"); err != nil {
			log.Fatal().
				Err(err).
				Str("outputFileName", outputFileName).
				Msg("Failed writing newline to file")
		}
		entryCount++
	}

	if entryCount > 0 {
		log.Info().
			Int("entryCount", entryCount).
			Str("outputFileName", outputFileName).
			Msg("Successfully saved log entries to file")
	} else {
		log.Info().Msg("No log entries found matching the filter. Nothing to upload")
		if _, err := os.Stat(outputFileName); err == nil {
			if err := os.Remove(outputFileName); err != nil {
				log.Warn().
					Err(err).
					Str("outputFileName", outputFileName).
					Msg("Failed to remove empty or pre-existing output file")
			}
		}
		return "", nil
	}

	log.Info().
		Int("entryCount", entryCount).
		Str("outputFileName", outputFileName).
		Msg("Log export complete")

	// Print the content of the file before uploading
	if entryCount > 0 { // Only print if there are entries
		log.Info().Str("outputFileName", outputFileName).Msg("Content of file to be uploaded")
		fileData, err := os.ReadFile(outputFileName)
		if err != nil {
			log.Warn().
				Err(err).
				Str("outputFileName", outputFileName).
				Msg("Could not read file to print content")
		} else {
			// Limit printing to avoid flooding console for very large files
			// Print first 10KB or whole file if smaller
			const maxPrintSize = 1024
			if len(fileData) > maxPrintSize {
				log.Info().
					Str("content", string(fileData[:maxPrintSize])).
					Int("totalSize", len(fileData)).
					Msg("File content (truncated)")
			} else {
				log.Info().
					Str("content", string(fileData)).
					Msg("File content")
			}
		}
		log.Info().Str("outputFileName", outputFileName).Msg("End of content")
	}

	return outputFileName, nil
}

func main() {
	logging.SetupServerLogging()

	log.Info().Msg("Starting audit log exporter")
	ctx := context.Background()

	// 1. get configuration (config file or command line args)
	cfg, err := getConfiguration()
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to get configuration")
		return
	}

	// 2. get the logs
	adminClient, err := logadmin.NewClient(ctx, cfg.LogSourceProjectID)
	if err != nil {
		log.Fatal().
			Err(err).
			Str("projectID", cfg.LogSourceProjectID).
			Msg("Failed to create logadmin client")
	}
	defer adminClient.Close()
	outputFile, err := gatherLogEntries(ctx, cfg, adminClient)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to gather log entries")
		return
	}
	if outputFile == "" {
		// no file was generated - nothing to do
		return
	} else {
		defer func() {
			log.Info().Str("outputFile", outputFile).Msg("Deleting file after successful upload")
			if err := os.Remove(outputFile); err != nil {
				log.Warn().
					Err(err).
					Str("outputFile", outputFile).
					Msg("Failed to delete local file after upload")
			}
		}()
	}

	// 3. Get GCS credentials
	var gcsCredentialsJSON []byte
	client, err := secretmanager.NewClient(ctx)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to create secretmanager client")
		return
	}
	defer client.Close()
	creds, err := getSecret(ctx, client, cfg.LogSourceProjectID, cfg.GCSSecretName)
	if err != nil {
		log.Warn().
			Err(err).
			Msg("Failed to get GCS credentials from Secret Manager. Will attempt GCS upload with Application Default Credentials (ADC)")
	} else {
		gcsCredentialsJSON = creds
		log.Info().Msg("Successfully fetched GCS credentials from Secret Manager")
	}

	// 4. Upload the logs
	gcsPath, err := uploadToGCS(ctx, cfg.GCSDestinationBucketName, outputFile, gcsCredentialsJSON)
	if err != nil {
		log.Error().
			Err(err).
			Str("outputFile", outputFile).
			Str("bucketName", cfg.GCSDestinationBucketName).
			Msg("Failed to upload to GCS destination bucket")
	} else {
		log.Info().
			Str("outputFile", outputFile).
			Str("gcsPath", gcsPath).
			Msg("Successfully uploaded to GCS")
	}

	log.Info().Msg("Audit log exporter finished")
}
