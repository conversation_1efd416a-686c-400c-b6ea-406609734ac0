local cloudInfo = import 'deploy/common/cloud_info.jsonnet';

{
  deployment: [
    {
      name: 'security-export-auditlogs',
      kubecfg: {
        target: '//services/security/audit-exporter:kubecfg',
        task: [
          {
            cloud: std.asciiUpper(centralNamespace.cloud),
            env: centralNamespace.env,
            namespace: centralNamespace.namespace,
          }
          for centralNamespace in cloudInfo.centralNamespaces
          if cloudInfo.isLeadCluster(centralNamespace.cloud) && centralNamespace.env == 'PROD'
        ],
      },
      health: {
        tier: 'TIER_2',
        experts: {
          users: ['miroslav'],
          slack_channel: '#augment-security',
        },
      },
    },
  ],
}
