package main

import (
	"context"
	"fmt"
	"net"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"testing"
	"time"

	secretmanager "cloud.google.com/go/secretmanager/apiv1"
	secretmanagerpb "cloud.google.com/go/secretmanager/apiv1/secretmanagerpb"
	"google.golang.org/api/option"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/status"
)

// mockSecretManagerServer implements the Secret Manager API for testing
type mockSecretManagerServer struct {
	secretmanagerpb.UnimplementedSecretManagerServiceServer
	// Map of secret name to secret data
	secrets map[string][]byte
}

func (m *mockSecretManagerServer) AccessSecretVersion(
	ctx context.Context,
	req *secretmanagerpb.AccessSecretVersionRequest,
) (*secretmanagerpb.AccessSecretVersionResponse, error) {
	secretData, exists := m.secrets[req.Name]
	if !exists {
		return nil, status.Errorf(codes.NotFound, "secret %s not found", req.Name)
	}

	return &secretmanagerpb.AccessSecretVersionResponse{
		Name: req.Name,
		Payload: &secretmanagerpb.SecretPayload{
			Data: secretData,
		},
	}, nil
}

func TestLoadConfig(t *testing.T) {
	// Create a temporary config file
	tempFile, err := os.CreateTemp("", "test-config-*.json")
	if err != nil {
		t.Fatalf("Failed to create temporary file: %v", err)
	}
	defer os.Remove(tempFile.Name())

	// Write test config to the file with all expected fields
	// Using the correct JSON field names that match the struct tags
	testConfig := `{
		"LogSourceProjectID": "test-log-project",
		"LogSourceBucketLocation": "global",
		"LogSourceBucketID": "test-log-bucket",
		"LogSourceViewID": "_AllLogs",
		"TenantName": "test-tenant",
		"GCSDestinationBucketName": "test-destination-bucket",
		"GCSSecretName": "test-secret"
	}`
	if _, err := tempFile.Write([]byte(testConfig)); err != nil {
		t.Fatalf("Failed to write to temporary file: %v", err)
	}
	if err := tempFile.Close(); err != nil {
		t.Fatalf("Failed to close temporary file: %v", err)
	}

	// Load the config
	config, err := loadConfig(tempFile.Name())
	if err != nil {
		t.Fatalf("Failed to load config: %v", err)
	}

	// Verify the config values
	testCases := []struct {
		fieldName string
		got       string
		want      string
	}{
		{"LogSourceProjectID", config.LogSourceProjectID, "test-log-project"},
		{"LogSourceBucketLocation", config.LogSourceBucketLocation, "global"},
		{"LogSourceBucketID", config.LogSourceBucketID, "test-log-bucket"},
		{"LogSourceViewID", config.LogSourceViewID, "_AllLogs"},
		{"TenantName", config.TenantName, "test-tenant"},
		{"GCSDestinationBucketName", config.GCSDestinationBucketName, "test-destination-bucket"},
		{"GCSSecretName", config.GCSSecretName, "test-secret"},
	}

	for _, tc := range testCases {
		if tc.got != tc.want {
			t.Errorf("Expected %s to be '%s', got '%s'", tc.fieldName, tc.want, tc.got)
		}
	}

	// Test with missing file
	_, err = loadConfig("non-existent-file.json")
	if err == nil {
		t.Error("Expected error when loading non-existent file, got nil")
	}

	// Test with invalid JSON
	invalidFile, err := os.CreateTemp("", "invalid-config-*.json")
	if err != nil {
		t.Fatalf("Failed to create temporary file: %v", err)
	}
	defer os.Remove(invalidFile.Name())

	if _, err := invalidFile.Write([]byte("this is not valid json")); err != nil {
		t.Fatalf("Failed to write to temporary file: %v", err)
	}
	if err := invalidFile.Close(); err != nil {
		t.Fatalf("Failed to close temporary file: %v", err)
	}

	_, err = loadConfig(invalidFile.Name())
	if err == nil {
		t.Error("Expected error when loading invalid JSON, got nil")
	}
}

func TestGetSecret(t *testing.T) {
	ctx := context.Background()
	// Set up a mock gRPC server
	mockServer := &mockSecretManagerServer{
		secrets: map[string][]byte{
			"projects/test-project/secrets/test-secret/versions/latest": []byte("test-secret-value"),
		},
	}
	l, err := net.Listen("tcp", "localhost:0")
	if err != nil {
		t.Fatal(err)
	}

	// Create a local test server
	server := grpc.NewServer()
	secretmanagerpb.RegisterSecretManagerServiceServer(server, mockServer)
	fakeServerAddr := l.Addr().String()
	go func() {
		if err := server.Serve(l); err != nil {
			panic(err)
		}
	}()
	defer server.Stop()

	// Create a client that uses the mock connection
	client, err := secretmanager.NewClient(ctx,
		option.WithEndpoint(fakeServerAddr),
		option.WithoutAuthentication(),
		option.WithGRPCDialOption(grpc.WithTransportCredentials(insecure.NewCredentials())))
	if err != nil {
		t.Fatalf("Failed to create client: %v", err)
	}
	defer client.Close()

	// Mock the secretmanager.NewClient function to return our test client
	originalNewClient := secretmanagerNewClient
	secretmanagerNewClient = func(ctx context.Context, opts ...option.ClientOption) (*secretmanager.Client, error) {
		return client, nil
	}
	defer func() { secretmanagerNewClient = originalNewClient }()

	// Test successful secret retrieval
	t.Run("Success", func(t *testing.T) {
		secretData, err := getSecret(ctx, client, "test-project", "test-secret")
		if err != nil {
			t.Fatalf("getSecret failed: %v", err)
		}

		if string(secretData) != "test-secret-value" {
			t.Errorf("Expected secret value 'test-secret-value', got '%s'", string(secretData))
		}
	})

	// Test non-existent secret
	t.Run("NonExistentSecret", func(t *testing.T) {
		_, err := getSecret(ctx, client, "test-project", "non-existent-secret")
		if err == nil {
			t.Error("Expected error for non-existent secret, got nil")
		}

		if status.Code(err) != codes.NotFound {
			t.Errorf("Expected NotFound error, got %v", err)
		}
	})
}

// Variable to allow mocking in tests
var secretmanagerNewClient = secretmanager.NewClient

func TestGetGcsObjectName(t *testing.T) {
	tests := []struct {
		name           string
		sourceFilePath string
		wantPattern    string
		description    string
	}{
		{
			name:           "simple filename",
			sourceFilePath: "audit.json",
			wantPattern:    `^audit_logs/year=\d{4}/month=\d{2}/day=\d{2}/\d{8}_\d{6}_audit\.json$`,
			description:    "should handle simple filename",
		},
		{
			name:           "full path",
			sourceFilePath: "/tmp/logs/audit_export.json",
			wantPattern:    `^audit_logs/year=\d{4}/month=\d{2}/day=\d{2}/\d{8}_\d{6}_audit_export\.json$`,
			description:    "should extract filename from full path",
		},
		{
			name:           "nested path",
			sourceFilePath: "data/exports/2024/audit_logs_final.json",
			wantPattern:    `^audit_logs/year=\d{4}/month=\d{2}/day=\d{2}/\d{8}_\d{6}_audit_logs_final\.json$`,
			description:    "should handle nested paths",
		},
		{
			name:           "filename with underscores",
			sourceFilePath: "my_audit_file_v2.json",
			wantPattern:    `^audit_logs/year=\d{4}/month=\d{2}/day=\d{2}/\d{8}_\d{6}_my_audit_file_v2\.json$`,
			description:    "should handle filenames with underscores",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := getGcsObjectName(tt.sourceFilePath)

			// Check if the result matches the expected pattern
			matched, err := regexp.MatchString(tt.wantPattern, got)
			if err != nil {
				t.Fatalf("Invalid regex pattern: %v", err)
			}
			if !matched {
				t.Errorf("getGcsObjectName() = %v, want pattern %v", got, tt.wantPattern)
			}

			// Verify the structure components
			parts := strings.Split(got, "/")
			if len(parts) != 5 {
				t.Errorf("Expected 5 path components, got %d: %v", len(parts), parts)
			}

			// Check prefix
			if parts[0] != "audit_logs" {
				t.Errorf("Expected prefix 'audit_logs', got '%s'", parts[0])
			}

			// Check year format
			if !strings.HasPrefix(parts[1], "year=") || len(parts[1]) != 9 {
				t.Errorf("Expected year=YYYY format, got '%s'", parts[1])
			}

			// Check month format
			if !strings.HasPrefix(parts[2], "month=") || len(parts[2]) != 8 {
				t.Errorf("Expected month=MM format, got '%s'", parts[2])
			}

			// Check day format
			if !strings.HasPrefix(parts[3], "day=") || len(parts[3]) != 6 {
				t.Errorf("Expected day=DD format, got '%s'", parts[3])
			}

			// Check filename component contains original filename
			expectedFilename := filepath.Base(tt.sourceFilePath)
			if !strings.Contains(parts[4], expectedFilename) {
				t.Errorf("Expected filename component to contain '%s', got '%s'", expectedFilename, parts[4])
			}
		})
	}
}

func TestGetGcsObjectNameDateConsistency(t *testing.T) {
	// Test that multiple calls within a short time frame produce consistent date components
	sourceFilePath := "test.json"

	// Get the current time for comparison
	now := time.Now()
	expectedYear := now.Format("2006")
	expectedMonth := now.Format("01")
	expectedDay := now.Format("02")

	// Call the function multiple times
	for i := 0; i < 5; i++ {
		result := getGcsObjectName(sourceFilePath)
		parts := strings.Split(result, "/")

		if len(parts) != 5 {
			t.Fatalf("Expected 5 path components, got %d", len(parts))
		}

		// Extract date components
		year := strings.TrimPrefix(parts[1], "year=")
		month := strings.TrimPrefix(parts[2], "month=")
		day := strings.TrimPrefix(parts[3], "day=")

		// Verify they match current date (allowing for potential day rollover)
		if year != expectedYear {
			// Allow for year rollover at midnight
			if !(expectedYear == "2024" && year == "2025") {
				t.Errorf("Expected year %s, got %s", expectedYear, year)
			}
		}

		if month != expectedMonth {
			// Allow for month rollover
			if !((expectedMonth == "12" && month == "01") || (expectedMonth == "01" && month == "12")) {
				t.Errorf("Expected month %s, got %s", expectedMonth, month)
			}
		}

		if day != expectedDay {
			// Allow for day rollover (previous or next day)
			dayInt := 0
			expectedDayInt := 0
			if _, err := fmt.Sscanf(day, "%d", &dayInt); err != nil {
				t.Errorf("Invalid day format: %s", day)
			}
			if _, err := fmt.Sscanf(expectedDay, "%d", &expectedDayInt); err != nil {
				t.Errorf("Invalid expected day format: %s", expectedDay)
			}

			// Allow for ±1 day difference (for midnight rollover during test)
			diff := dayInt - expectedDayInt
			if diff < -1 || diff > 1 {
				t.Errorf("Expected day %s, got %s (difference too large)", expectedDay, day)
			}
		}

		// Small delay to ensure timestamp differences
		time.Sleep(10 * time.Millisecond)
	}
}
