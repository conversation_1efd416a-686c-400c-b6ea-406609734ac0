load("@aspect_rules_ts//ts:defs.bzl", "ts_config", "ts_project")
load("@aspect_rules_js//js:defs.bzl", "js_library")
load("@npm//:defs.bzl", "npm_link_all_packages")

npm_link_all_packages()

ts_config(
    name = "tsconfig",
    src = "tsconfig.json",
    visibility = ["//visibility:public"],
)

ts_project(
    name = "grpc_debug_frontend",
    srcs = [
        "grpc_debug.ts",
        "grpc_debug_component.tsx",
    ],
    allow_js = True,
    composite = True,
    declaration = True,
    incremental = True,
    resolve_json_module = True,
    tsconfig = ":tsconfig",
    visibility = ["//visibility:public"],
    deps = [
        ":node_modules/@ant-design/icons",
        ":node_modules/@types/react",
        ":node_modules/antd",
        ":node_modules/axios",
        ":node_modules/react",
        ":node_modules/react-dom",
        ":node_modules/react-json-view",
    ],
)

js_library(
    name = "pkg",
    srcs = [
        "package.json",
        ":grpc_debug_frontend",
    ],
    visibility = ["//visibility:public"],
)
