import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Checkbox,
  Divider,
  Form,
  Select,
  Spin,
  Typography,
  Space,
} from "antd";
import { CopyOutlined } from "@ant-design/icons";
import {
  Endpoint,
  getEndpoints,
  getServiceMethods,
  getTokenScopes,
  invokeMethod,
  InvokeMethodResponse,
  ServiceMethod,
  Tenant,
} from "./grpc_debug";
import { useEffect, useState } from "react";
import TextArea from "antd/es/input/TextArea";
import React<PERSON><PERSON> from "react-json-view";
import React from "react";
const { Text } = Typography;

type GrpcDebugFormForm = {
  endpoint: string | undefined;
  method: string | undefined;
  tenant: string | undefined;
  scopes: string[] | undefined;
  request: string;
};

function isValidJson(jsonString: string): boolean {
  try {
    JSON.parse(jsonString);
    return true;
  } catch {
    return false;
  }
}

function validateConcatenatedJson(jsonString: string): boolean {
  const jsonObjects: string[] = [];

  let openBraces = 0;
  let startIdx = 0;

  // Walk through the string character by character
  for (let i = 0; i < jsonString.length; i++) {
    if (jsonString[i] === "{") {
      if (openBraces === 0) {
        startIdx = i;
      }
      openBraces++;
    } else if (jsonString[i] === "}") {
      openBraces--;
      if (openBraces === 0) {
        // Extract JSON document and validate it
        const jsonDoc = jsonString.slice(startIdx, i + 1);
        if (!isValidJson(jsonDoc)) {
          return false;
        }
        jsonObjects.push(jsonDoc);
      }
    }
  }

  // If there are unbalanced braces, it’s not a valid concatenation
  return openBraces === 0;
}

function isValidJsonOrConcatenation(jsonString: string): boolean {
  // First, check if it's a single valid JSON document
  if (isValidJson(jsonString)) {
    return true;
  }

  // If not, check if it's a concatenation of multiple JSON documents
  return validateConcatenatedJson(jsonString);
}

function GrpcDebugComponent({
  tenantsInfo,
}: {
  tenantsInfo: Tenant[] | undefined;
}) {
  const [endpointData, setEndpointData] = useState<Endpoint[] | undefined>(
    undefined,
  );
  const [selectedEndpoint, setSelectedEndpoint] = useState<
    Endpoint | undefined
  >(undefined);
  const [tokenScopes, setTokenScopes] = useState<string[] | undefined>(
    undefined,
  );
  const [errorData, setErrorData] = useState<string | undefined>(undefined);
  const [response, setResponse] = useState<InvokeMethodResponse | undefined>(
    undefined,
  );
  const [methodsData, setMethodsData] = useState<ServiceMethod[] | undefined>(
    undefined,
  );
  const [busy, setBusy] = useState(false);
  const [grpcDebugForm] = Form.useForm();
  const [selectedTenant, setSelectedTenant] = useState<Tenant | undefined>(
    undefined,
  );
  const [copiedTenantId, setCopiedTenantId] = useState(false);

  useEffect(() => {
    const fetchTokenScopes = async () => {
      try {
        const scopes = await getTokenScopes();
        console.log(`scopes ${JSON.stringify(scopes)}`);
        setTokenScopes(scopes);
      } catch (e) {
        console.log(`Error while loading the token scopes: ${e}`);
      }
    };
    fetchTokenScopes();
  }, []);

  useEffect(() => {
    const fetchEndpoints = async () => {
      try {
        const endpoints = await getEndpoints();
        console.log(`endpoints ${JSON.stringify(endpoints)}`);
        setEndpointData(endpoints);
      } catch (e) {
        console.log(`Error while loading the endpoints data: ${e}`);
      }
    };
    fetchEndpoints();
  }, []);

  useEffect(() => {
    if (selectedEndpoint === undefined) {
      return;
    }
    const fetchMethods = async () => {
      try {
        const methods = await getServiceMethods(selectedEndpoint?.url);
        console.log(`methods ${JSON.stringify(methods)}`);
        setMethodsData(methods);
      } catch (e) {
        console.log(`Error while loading the methods data: ${e}`);
      }
    };
    fetchMethods();
  }, [endpointData, selectedEndpoint]);

  if (endpointData === undefined) {
    return <Spin />;
  }
  if (tokenScopes === undefined) {
    return <Spin />;
  }
  if (errorData !== undefined) {
    return <Alert message={errorData} type="error" />;
  }

  const onFormFinish = (values: GrpcDebugFormForm) => {
    setBusy(true);
    setResponse(undefined);
    console.log("Success:", values);
    let tenantId = "";
    if (values.tenant !== undefined) {
      const tid = tenantsInfo?.find((t) => t.name === values.tenant)?.tenantId;
      if (tid === undefined) {
        console.log(`Error while getting tenant id for ${values.tenant}`);
        return;
      }
      tenantId = tid;
    }
    let service = "";
    let method = "";
    if (values.method !== undefined) {
      const methodData = methodsData?.find(
        (m) => m.fullyQualifiedName === values.method,
      );
      if (methodData !== undefined) {
        service = methodData.serviceName;
        method = methodData.methodName;
      }
    }
    if (values.endpoint === undefined) {
      return;
    }
    const endpointUrl = endpointData.find(
      (e) => e.name === values.endpoint,
    )?.url;
    if (endpointUrl === undefined) {
      return;
    }
    invokeMethod(
      endpointUrl,
      service,
      method,
      tenantId,
      values.scopes || [],
      values.request,
      300 * 1000, // 300 seconds timeout
    )
      .then((response) => {
        console.log(`response ${JSON.stringify(response)}`);
        setResponse(response);
        setBusy(false);
      })
      .catch((e) => {
        if (e.response?.status === 403) {
          setErrorData("You do not have access to this request");
        }
        console.log(`Error while invoking the method: ${e}`);
        setBusy(false);
      });
  };

  const validateJson = (_: unknown, value: string) => {
    try {
      if (!isValidJsonOrConcatenation(value)) {
        return Promise.reject(new Error("Invalid JSON format"));
      }
      return Promise.resolve();
    } catch (err) {
      return Promise.reject(new Error("Invalid JSON format"));
    }
  };

  const onMethodChange = (value: string) => {
    const m = methodsData?.find((m) => m.fullyQualifiedName === value);
    grpcDebugForm.setFieldsValue({
      request: m?.emptyRequestJson,
    });
  };

  const copyTenantId = async () => {
    if (selectedTenant?.tenantId) {
      await navigator.clipboard.writeText(selectedTenant.tenantId);
      setCopiedTenantId(true);
      setTimeout(() => setCopiedTenantId(false), 1500);
    }
  };

  const tenantFormItem = (
    <>
      <Form.Item
        label="Tenant"
        name="tenant"
        rules={[{ required: false, message: "Please input the tenant" }]}
      >
        <Select
          disabled={tenantsInfo === undefined}
          options={tenantsInfo?.map((tenant) => {
            return { label: tenant.name, value: tenant.name };
          })}
          showSearch
          placeholder="Search tenant"
          filterOption={(
            input: string,
            option: { label: string; value: string },
          ) =>
            (option?.label ?? "").toLowerCase().includes(input.toLowerCase())
          }
          onChange={(value: string) => {
            const tenant = tenantsInfo?.find((t) => t.name === value);
            setSelectedTenant(tenant);
          }}
        ></Select>
      </Form.Item>

      {selectedTenant && (
        <Form.Item wrapperCol={{ offset: 8, span: 16 }}>
          <Space>
            <Text keyboard>{selectedTenant.tenantId}</Text>
            <Button
              size="small"
              type="text"
              onClick={copyTenantId}
              title={copiedTenantId ? "Copied!" : "Copy tenant ID"}
              icon={<CopyOutlined />}
            />
          </Space>
        </Form.Item>
      )}
    </>
  );

  const form = (
    <Form
      name="grpc_debug"
      form={grpcDebugForm}
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 16 }}
      style={{ maxWidth: 600 }}
      initialValues={{ remember: true }}
      onFinish={onFormFinish}
      autoComplete="off"
    >
      <Form.Item
        label="Endpoint"
        name="endpoint"
        rules={[{ required: true, message: "Please input the endpoint" }]}
      >
        <Select
          value={selectedEndpoint?.name}
          onChange={(value: string) => {
            setSelectedEndpoint(endpointData.find((e) => e.name === value));
          }}
          showSearch
          placeholder="Search endpoint"
          filterOption={(
            input: string,
            option: { label: string; value: string },
          ) =>
            (option?.label ?? "").toLowerCase().includes(input.toLowerCase())
          }
          options={endpointData.map((endpoint) => {
            return { label: endpoint.name, value: endpoint.name };
          })}
        ></Select>
      </Form.Item>

      <Form.Item
        label="Method"
        name="method"
        rules={[{ required: false, message: "Please input the service" }]}
      >
        <Select
          disabled={selectedEndpoint === undefined}
          onChange={onMethodChange}
          showSearch
          placeholder="Search method"
          filterOption={(
            input: string,
            option: { label: string; value: string },
          ) =>
            (option?.label ?? "").toLowerCase().includes(input.toLowerCase())
          }
          options={methodsData?.map((method) => {
            return {
              label: method.fullyQualifiedName,
              value: method.fullyQualifiedName,
            };
          })}
        ></Select>
      </Form.Item>

      <Form.Item
        label="Request"
        name="request"
        rules={[
          { required: true, message: "Please input the request" },
          { validator: validateJson },
        ]}
      >
        <TextArea rows={8} className="keyboard-textarea" />
      </Form.Item>

      {tenantFormItem}

      <Form.Item label="Scopes" name="scopes">
        <Checkbox.Group disabled={selectedEndpoint === undefined}>
          {tokenScopes?.map((scope) => {
            return (
              <Checkbox key={scope} value={scope}>
                <Text keyboard>{scope}</Text>
              </Checkbox>
            );
          })}
        </Checkbox.Group>
      </Form.Item>

      <Form.Item wrapperCol={{ offset: 8, span: 16 }}>
        <Button type="primary" htmlType="submit" loading={busy}>
          Submit
        </Button>
      </Form.Item>
    </Form>
  );
  let responseElement = <></>;
  if (busy === true) {
    responseElement = <Spin />;
  } else if (response !== undefined) {
    if (response.error?.code) {
      if (response.error?.code === 7) {
        responseElement = (
          <Alert message={response.error?.message} type="error" />
        );
      } else {
        responseElement = (
          <Alert message={response.error?.message} type="error" />
        );
      }
    } else {
      const jsonResponse = JSON.parse(response?.responseJson);
      responseElement = <ReactJson src={jsonResponse} theme="rjv-default" />;
    }
  }
  return (
    <>
      {form}
      <Divider />
      {responseElement}
    </>
  );
}

export default GrpcDebugComponent;
