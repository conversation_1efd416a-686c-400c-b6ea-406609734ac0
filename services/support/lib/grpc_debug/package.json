{"name": "@augment-internal/grpc_debug", "private": true, "version": "0.0.0", "type": "module", "scripts": {"prettier": "git ls-files -- . | xargs pre-commit run prettier --files", "prettier:fix": "git ls-files -- . | xargs pre-commit run prettier --hook-stage=manual --files"}, "main": "grpc_debug.js", "types": "grpc_debug.d.ts", "dependencies": {"@ant-design/icons": "^6.0.0", "antd": "^5.19.0", "axios": "^1.8.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-json-view": "^1.21.3"}, "devDependencies": {"@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "prettier": "^3.2.5"}}