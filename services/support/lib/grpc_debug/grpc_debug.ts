import axios from "axios";

export type Tenant = {
  name: string;
  tenantId: string;
};

export type Endpoint = {
  name: string;
  url: string;
};

export async function getEndpoints(): Promise<Endpoint[]> {
  const { data: response }: { data: any } =
    await axios.post(`/api/grpc_endpoints`);
  return response;
}

export type ServiceMethod = {
  serviceName: string;
  methodName: string;
  fullyQualifiedName: string;
  emptyRequestJson: string;
};

export async function getServiceMethods(
  endpoint: string,
): Promise<ServiceMethod[]> {
  const { data: response }: { data: any } = await axios.post(
    `/api/grpc_service_methods`,
    { endpoint },
  );
  return response;
}

export type InvokeMethodResponse = {
  responseJson: string;
  error?: {
    code: number;
    message: string;
  };
};

export async function invokeMethod(
  endpoint: string,
  service: string,
  method: string,
  tenant_id: string,
  scopes: string[],
  request: string,
  timeout?: number,
): Promise<InvokeMethodResponse> {
  const { data: response }: { data: any } = await axios.post(
    `/api/grpc_invoke_method`,
    { endpoint, service, method, request, tenant_id, scopes },
    timeout? { timeout: timeout } : undefined
  );
  return response;
}

export async function getTokenScopes(): Promise<string[]> {
  const { data: response }: { data: any } = await axios.get(
    `/api/grpc_token_scopes`,
  );
  return response;
}
