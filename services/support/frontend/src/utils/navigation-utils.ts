/**
 * Utility functions for page navigation and UI interactions
 */

/**
 * Collapses all collapsible panels on the page
 */
export function collapseAll(): void {
  // Find all Collapse panels and collapse them
  const collapseHeaders = document.querySelectorAll(".ant-collapse-header");
  collapseHeaders.forEach((header) => {
    const headerElement = header as HTMLElement;
    const isExpanded = headerElement.getAttribute("aria-expanded") === "true";
    if (isExpanded) {
      headerElement.click();
    }
  });
}

/**
 * Expands all collapsible panels on the page
 */
export function expandAll(): void {
  // Find all Collapse panels and expand them
  const collapseHeaders = document.querySelectorAll(".ant-collapse-header");
  collapseHeaders.forEach((header) => {
    const headerElement = header as HTMLElement;
    const isCollapsed = headerElement.getAttribute("aria-expanded") === "false";
    if (isCollapsed) {
      headerElement.click();
    }
  });
}

/**
 * Navigates to the previous section on the page
 */
export function navigateToPreviousSection(): void {
  // Find all section headers - use a simple selector that works with our components
  const headers = Array.from(
    document.querySelectorAll("h5.ant-typography"),
  ) as HTMLElement[];
  if (headers.length === 0) return;

  const scrollY = window.scrollY;
  const offset = 10; // Small offset to avoid boundary issues

  // Find the index of the first header below the current scroll position
  const currentHeaderIndex = headers.findIndex((header) => {
    const rect = header.getBoundingClientRect();
    return rect.top + scrollY > scrollY - offset;
  });

  let targetHeader: HTMLElement | null = null;

  // Case 1: We found a header below the current position, go to the one before it
  if (currentHeaderIndex > 0) {
    targetHeader = headers[currentHeaderIndex - 1];
  }
  // Case 2: We're at the first header or no header is below current position, go to the last header
  else if (currentHeaderIndex <= 0 && headers.length > 0) {
    // If currentHeaderIndex is 0, we're at the first header, so go to the last one
    // If currentHeaderIndex is -1, no header is below current position, also go to the last one
    targetHeader = headers[headers.length - 1];
  }

  // Scroll to the target header if we found one
  if (targetHeader) {
    targetHeader.scrollIntoView({ behavior: "smooth" });
  }
}

/**
 * Navigates to the next section on the page
 */
export function navigateToNextSection(): void {
  // Find all section headers - use a simple selector that works with our components
  const headers = Array.from(
    document.querySelectorAll("h5.ant-typography"),
  ) as HTMLElement[];
  if (headers.length === 0) return;

  const scrollY = window.scrollY;
  const offset = 50; // Larger offset for next to avoid selecting the current header

  // Find the index of the first header below the current scroll position plus offset
  const nextHeaderIndex = headers.findIndex((header) => {
    const rect = header.getBoundingClientRect();
    return rect.top + scrollY > scrollY + offset;
  });

  let targetHeader: HTMLElement | null = null;

  // Case 1: We found a header below the current position, go to it
  if (nextHeaderIndex !== -1) {
    targetHeader = headers[nextHeaderIndex];
  }
  // Case 2: No header is below current position, go to the first header (loop back to top)
  else if (headers.length > 0) {
    targetHeader = headers[0];
  }

  // Scroll to the target header if we found one
  if (targetHeader) {
    targetHeader.scrollIntoView({ behavior: "smooth" });
  }
}
