import React, { useCallback } from "react";
import { MagnifyingGlassIcon } from "@radix-ui/react-icons";
import { Flex, Box, TextField, Button } from "@radix-ui/themes";

interface UserSearchInputProps {
  placeholder: string;
  onSubmit: (value: string) => void;
  searchValue: string;
  onSearchValueChange: (value: string) => void;
  isLoading?: boolean;
}

export default function UserSearchInput({
  placeholder,
  onSubmit,
  searchValue,
  onSearchValueChange,
  isLoading,
}: UserSearchInputProps) {
  const handleSubmit = useCallback(
    (e: React.FormEvent) => {
      e.preventDefault();
      onSubmit(searchValue);
    },
    [onSubmit, searchValue],
  );

  return (
    <form onSubmit={handleSubmit}>
      <Flex gap="3" align="center">
        <Box flexGrow="1">
          <TextField.Root
            placeholder={placeholder}
            value={searchValue}
            onChange={(e) => onSearchValueChange(e.target.value)}
          >
            <TextField.Slot>
              <MagnifyingGlassIcon height="16" width="16" />
            </TextField.Slot>
          </TextField.Root>
        </Box>

        <Button type="submit" disabled={isLoading}>
          {isLoading ? "Searching..." : "Search"}
        </Button>
      </Flex>
    </form>
  );
}
