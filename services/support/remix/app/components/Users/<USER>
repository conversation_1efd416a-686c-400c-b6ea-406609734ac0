import React, { useState } from "react";
import {
  Table,
  Box,
  Text,
  Flex,
  Badge,
  <PERSON><PERSON>,
  Card,
  Heading,
} from "@radix-ui/themes";
import { ChevronDownIcon, ChevronRightIcon } from "@radix-ui/react-icons";
import { type User } from "../../schemas/users";
import TenantRemovalButton from "./TenantRemovalButton";

interface UserTableProps {
  users: User[];
  isLoading: boolean;
  hasMoreResults: boolean;
  onLoadMore: () => void;
  onUserRemovalSuccess?: (userId: string, tenantId: string) => void;
}

export default function UserTable({
  users,
  isLoading,
  hasMoreResults,
  onLoadMore,
  onUserRemovalSuccess,
}: UserTableProps) {
  const [expandedRows, setExpandedRows] = useState<Record<string, boolean>>({});

  const toggleRow = (userId: string) => {
    setExpandedRows((prev) => ({
      ...prev,
      [userId]: !prev[userId],
    }));
  };

  if (users.length === 0 && !isLoading) {
    return (
      <Box py="4">
        <Text>No users found. Try a different search query.</Text>
      </Box>
    );
  }

  return (
    <Box>
      <Table.Root variant="surface">
        <Table.Header>
          <Table.Row>
            <Table.ColumnHeaderCell width="40px"></Table.ColumnHeaderCell>
            <Table.ColumnHeaderCell>Email</Table.ColumnHeaderCell>
            <Table.ColumnHeaderCell>ID</Table.ColumnHeaderCell>
            <Table.ColumnHeaderCell>Tenants</Table.ColumnHeaderCell>
            <Table.ColumnHeaderCell>Status</Table.ColumnHeaderCell>
          </Table.Row>
        </Table.Header>

        <Table.Body>
          {users.map((user) => (
            <React.Fragment key={user.id}>
              <Table.Row>
                <Table.Cell>
                  <Button
                    variant="ghost"
                    onClick={() => toggleRow(user.id)}
                    aria-label={
                      expandedRows[user.id] ? "Collapse row" : "Expand row"
                    }
                  >
                    {expandedRows[user.id] ? (
                      <ChevronDownIcon />
                    ) : (
                      <ChevronRightIcon />
                    )}
                  </Button>
                </Table.Cell>
                <Table.Cell>{user.email}</Table.Cell>
                <Table.Cell>
                  <Text size="2" as="span">
                    {user.id}
                  </Text>
                </Table.Cell>
                <Table.Cell>
                  <Flex gap="2" wrap="wrap">
                    {user.tenants.map((tenant) => (
                      <Flex key={tenant} align="center" gap="1">
                        <Badge size="1">{tenant}</Badge>
                        <TenantRemovalButton
                          user={user}
                          tenantId={tenant}
                          onRemovalSuccess={onUserRemovalSuccess}
                        />
                      </Flex>
                    ))}
                  </Flex>
                </Table.Cell>
                <Table.Cell>
                  {user.blocked ? (
                    <Badge color="red">Blocked</Badge>
                  ) : user.suspensions && user.suspensions.length > 0 ? (
                    <Badge color="orange">Suspended</Badge>
                  ) : (
                    <Badge color="green">Active</Badge>
                  )}
                </Table.Cell>
              </Table.Row>
              {expandedRows[user.id] && (
                <Table.Row>
                  <Table.Cell colSpan={5}>
                    <Card size="1">
                      <Flex direction="column" gap="3">
                        <Box>
                          <Heading size="3">User Details</Heading>
                        </Box>
                        <Flex gap="4">
                          <Box>
                            <Text as="div" weight="bold" size="2">
                              Created At
                            </Text>
                            <Text as="div" size="2">
                              {user.createdAt
                                ? new Date(user.createdAt).toLocaleString()
                                : "N/A"}
                            </Text>
                          </Box>
                          <Box>
                            <Text as="div" weight="bold" size="2">
                              Billing Method
                            </Text>
                            <Text as="div" size="2">
                              {user.billingMethod}
                            </Text>
                          </Box>
                        </Flex>

                        {user.suspensions && user.suspensions.length > 0 && (
                          <Box>
                            <Text as="div" weight="bold" size="2">
                              Suspensions
                            </Text>
                            {user.suspensions.map((suspension) => (
                              <Box
                                key={suspension.suspensionId}
                                mt="2"
                                style={{
                                  borderLeft: "2px solid var(--orange-9)",
                                  paddingLeft: "8px",
                                }}
                              >
                                <Text as="div" size="2">
                                  Type: {suspension.suspensionType}
                                </Text>
                                <Text as="div" size="2">
                                  Created:{" "}
                                  {suspension.createdTime
                                    ? new Date(
                                        suspension.createdTime,
                                      ).toLocaleString()
                                    : "N/A"}
                                </Text>
                                <Text as="div" size="2">
                                  Evidence: {suspension.evidence}
                                </Text>
                              </Box>
                            ))}
                          </Box>
                        )}

                        <Flex gap="4">
                          {user.stripeCustomerId && (
                            <Box>
                              <Text as="div" weight="bold" size="2">
                                Stripe Customer ID
                              </Text>
                              <Text as="div" size="2">
                                {user.stripeCustomerId}
                              </Text>
                            </Box>
                          )}
                          {user.orbCustomerId && (
                            <Box>
                              <Text as="div" weight="bold" size="2">
                                Orb Customer ID
                              </Text>
                              <Text as="div" size="2">
                                {user.orbCustomerId}
                              </Text>
                            </Box>
                          )}
                        </Flex>

                        <Flex gap="4">
                          {user.subscriptionId && (
                            <Box>
                              <Text as="div" weight="bold" size="2">
                                Subscription ID
                              </Text>
                              <Text as="div" size="2">
                                {user.subscriptionId}
                              </Text>
                            </Box>
                          )}
                          {user.orbSubscriptionId && (
                            <Box>
                              <Text as="div" weight="bold" size="2">
                                Orb Subscription ID
                              </Text>
                              <Text as="div" size="2">
                                {user.orbSubscriptionId}
                              </Text>
                            </Box>
                          )}
                        </Flex>
                      </Flex>
                    </Card>
                  </Table.Cell>
                </Table.Row>
              )}
            </React.Fragment>
          ))}
        </Table.Body>
      </Table.Root>

      {hasMoreResults && (
        <Flex justify="center" mt="4">
          <Button onClick={onLoadMore} disabled={isLoading}>
            {isLoading ? "Loading..." : "Load More"}
          </Button>
        </Flex>
      )}
    </Box>
  );
}
