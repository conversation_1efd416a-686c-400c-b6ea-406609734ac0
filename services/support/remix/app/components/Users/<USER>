import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Button, Dialog, Flex, Text, Badge, Box } from "@radix-ui/themes";
import { Cross2Icon } from "@radix-ui/react-icons";
import { useToast } from "../ui/Toast";
import { type User, ErrorResponseSchema } from "../../schemas/users";

interface TenantRemovalButtonProps {
  user: User;
  tenantId: string;
  onRemovalSuccess?: (userId: string, tenantId: string) => void;
}

export default function TenantRemovalButton({
  user,
  tenantId,
  onRemovalSuccess,
}: TenantRemovalButtonProps) {
  if (!user.tenants.includes(tenantId)) {
    throw new Error(`User ${user.id} is not in tenant ${tenantId}`);
  }

  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false);
  const queryClient = useQueryClient();
  const toast = useToast();

  const removeUserMutation = useMutation({
    mutationFn: async (): Promise<void> => {
      const response = await fetch(
        `/api/users/${user.id}/tenants/${tenantId}`,
        {
          method: "DELETE",
          headers: {
            "Content-Type": "application/json",
          },
        },
      );

      const data = await response.json();

      if (!response.ok) {
        const errorData = ErrorResponseSchema.safeParse(data);
        const errorMessage = errorData.success
          ? errorData.data.error
          : `Failed to remove user: ${response.statusText}`;
        throw new Error(errorMessage);
      }
    },
    onSuccess: () => {
      // Show success toast
      toast.success({
        title: "User Removed",
        description: `${user.email} has been successfully removed from tenant ${tenantId}`,
        duration: 5000,
      });

      // Invalidate and refetch users query to update the UI
      queryClient.invalidateQueries({ queryKey: ["users"] });

      // Call the success callback if provided
      onRemovalSuccess?.(user.id, tenantId);

      // Close the dialog
      setIsConfirmDialogOpen(false);
    },
    onError: (error) => {
      console.error(
        `Failed to remove user ${user.id} from tenant ${tenantId}:`,
        error,
      );
      // Keep the dialog open so user can see the error and try again
    },
  });

  const handleRemoveClick = () => {
    setIsConfirmDialogOpen(true);
  };

  const handleConfirmRemoval = () => {
    removeUserMutation.mutate();
  };

  return (
    <>
      <Button
        size="1"
        variant="ghost"
        color="red"
        onClick={handleRemoveClick}
        disabled={removeUserMutation.isPending}
        aria-label={`Remove user from tenant ${tenantId}`}
      >
        <Cross2Icon />
      </Button>

      {/* Confirmation Dialog */}
      <Dialog.Root
        open={isConfirmDialogOpen}
        onOpenChange={setIsConfirmDialogOpen}
      >
        <Dialog.Content style={{ maxWidth: 450 }}>
          <Dialog.Title>Remove User from Tenant</Dialog.Title>
          <Dialog.Description size="2" mb="4">
            Are you sure you want to remove this user from the tenant? This
            action cannot be undone.
          </Dialog.Description>

          <Box mb="4">
            <Flex direction="column" gap="2">
              <Text size="2" weight="bold">
                User:
              </Text>
              <Text size="2">{user.email}</Text>

              <Text size="2" weight="bold">
                Tenant:
              </Text>
              <Badge size="1">{tenantId}</Badge>

              <Text size="2" weight="bold">
                Remaining Tenants:
              </Text>
              <Flex gap="1" wrap="wrap">
                {user.tenants
                  .filter((t) => t !== tenantId)
                  .map((tenant) => (
                    <Badge key={tenant} size="1" color="gray">
                      {tenant}
                    </Badge>
                  ))}
                {user.tenants.filter((t) => t !== tenantId).length === 0 && (
                  <Text size="2" color="gray" style={{ fontStyle: "italic" }}>
                    None
                  </Text>
                )}
              </Flex>
            </Flex>
          </Box>

          {removeUserMutation.error && (
            <Box
              mb="4"
              p="3"
              style={{
                backgroundColor: "var(--red-3)",
                borderRadius: "6px",
              }}
            >
              <Text size="2" color="red" weight="bold">
                Error: {removeUserMutation.error.message}
              </Text>
            </Box>
          )}

          <Flex gap="3" mt="4" justify="end">
            <Dialog.Close>
              <Button variant="soft" color="gray">
                Cancel
              </Button>
            </Dialog.Close>
            <Button
              color="red"
              onClick={handleConfirmRemoval}
              disabled={removeUserMutation.isPending}
            >
              {removeUserMutation.isPending ? "Removing..." : "Remove User"}
            </Button>
          </Flex>
        </Dialog.Content>
      </Dialog.Root>
    </>
  );
}
