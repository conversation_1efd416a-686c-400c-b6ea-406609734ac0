import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { Flex } from "@radix-ui/themes";
import TenantRemovalButton from "./TenantRemovalButton";
import type { User } from "../../schemas/users";

const meta: Meta<typeof TenantRemovalButton> = {
  title: "Users/TenantRemovalButton",
  component: TenantRemovalButton,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof meta>;

// Mock user data
const mockUserWithMultipleTenants: User = {
  id: "user-123",
  email: "<EMAIL>",
  tenants: ["tenant-1", "tenant-2", "tenant-3"],
  blocked: false,
  createdAt: "2023-01-01T00:00:00Z",
  suspensions: [],
  billingMethod: "STRIPE",
  stripeCustomerId: "cus_123",
  orbCustomerId: undefined,
  subscriptionId: "sub_123",
  orbSubscriptionId: undefined,
};

const mockUserWithSingleTenant: User = {
  id: "user-456",
  email: "<EMAIL>",
  tenants: ["tenant-1"],
  blocked: false,
  createdAt: "2023-01-01T00:00:00Z",
  suspensions: [],
  billingMethod: "STRIPE",
  stripeCustomerId: "cus_456",
  orbCustomerId: undefined,
  subscriptionId: "sub_456",
  orbSubscriptionId: undefined,
};

// Default story - user with multiple tenants
export const Default: Story = {
  args: {
    user: mockUserWithMultipleTenants,
    tenantId: "tenant-1",
    onRemovalSuccess: (userId, tenantId) => {
      console.log(`User ${userId} removed from tenant ${tenantId}`);
    },
  },
  render: (args) => (
    <Flex align="center" gap="2">
      <span>tenant-1</span>
      <TenantRemovalButton {...args} />
    </Flex>
  ),
};

// User with single tenant (will remove from all tenants)
export const LastTenant: Story = {
  args: {
    user: mockUserWithSingleTenant,
    tenantId: "tenant-1",
    onRemovalSuccess: (userId, tenantId) => {
      console.log(`User ${userId} removed from tenant ${tenantId}`);
    },
  },
  render: (args) => (
    <Flex align="center" gap="2">
      <span>tenant-1</span>
      <TenantRemovalButton {...args} />
    </Flex>
  ),
};

// Multiple removal buttons for different tenants
export const MultipleTenants: Story = {
  render: () => (
    <Flex direction="column" gap="3">
      {mockUserWithMultipleTenants.tenants.map((tenantId) => (
        <Flex key={tenantId} align="center" gap="2">
          <span style={{ minWidth: "80px" }}>{tenantId}</span>
          <TenantRemovalButton
            user={mockUserWithMultipleTenants}
            tenantId={tenantId}
            onRemovalSuccess={(userId, tenantId) => {
              console.log(`User ${userId} removed from tenant ${tenantId}`);
            }}
          />
        </Flex>
      ))}
    </Flex>
  ),
};
