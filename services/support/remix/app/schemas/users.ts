import { z } from "zod";

// Schema for query parameters
export const UserQueryParamsSchema = z.object({
  searchString: z.string().default(""),
  pageToken: z.string().default(""),
  pageSize: z.coerce
    .number()
    .int()
    .min(1)
    .max(2000)
    .default(200)
    .describe("Number of users to return per page (1-200)"),
});

export type UserQueryParams = z.infer<typeof UserQueryParamsSchema>;

// Schema for user suspension data
export const UserSuspensionSchema = z.object({
  suspensionId: z.string(),
  suspensionType: z.string(),
  evidence: z.string(),
  createdTime: z.string().datetime().optional(),
});

export type UserSuspension = z.infer<typeof UserSuspensionSchema>;

// Schema for individual user data
export const UserSchema = z.object({
  id: z.string(),
  email: z.string().email(),
  tenants: z.array(z.string()),
  blocked: z.boolean(),
  createdAt: z.string().datetime().optional(),
  suspensions: z.array(UserSuspensionSchema),
  billingMethod: z.string().optional(),
  stripeCustomerId: z.string().optional(),
  orbCustomerId: z.string().optional(),
  subscriptionId: z.string().optional(),
  orbSubscriptionId: z.string().optional(),
});

export type User = z.infer<typeof UserSchema>;

// Schema for successful response
export const UsersResponseSchema = z.object({
  users: z.array(UserSchema),
  nextPageToken: z.string(),
});

export type UsersResponse = z.infer<typeof UsersResponseSchema>;

// Schema for error response
export const ErrorResponseSchema = z.object({
  error: z.string(),
});

export type ErrorResponse = z.infer<typeof ErrorResponseSchema>;
