// Helper jsonnet for getting the objects for a Redis container.
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';

// Redis configuration for in-memory only operation.
local getRedisExtraConfigs(port=6379) = {
  'redis.conf': |||
    # Disable persistence
    save ""
    appendonly no

    # Technically protected-mode is not needed if we only bind localhost, but
    # we'll leave this as is.
    protected-mode yes

    # Network settings
    bind 127.0.0.1 -::1
    port %d

    # Memory settings - adjust as needed
    maxmemory 512mb
    maxmemory-policy allkeys-lru
  ||| % port,
};

// Creates a Redis container definition.
// configMap: the config map object for the pod. Should contain the redis.conf file from getRedisExtraConfigs.
// appName: the name of the app that the Redis container is for
// port: the port that <PERSON><PERSON> should listen on. Should match the port in the config map.
local getRedisContainer(configMap, appName='memstore', port=6379) =
  // Redis container definition.
  {
    name: 'redis',
    target: {
      name: '//tools/docker:redis_base_image',
      dst: 'redis',
    },
    args: [
      'redis-server',
      configMap.volumeMountDef.mountPath + '/redis.conf',  // Use our custom config with correct path
    ],
    ports: [
      {
        containerPort: port,
        name: 'redis',
      },
    ],
    volumeMounts: [
      configMap.volumeMountDef,
    ],
    resources: {
      limits: {
        cpu: 1,
        memory: '512Mi',
      },
    },
    readinessProbe: {
      exec: {
        command: [
          'redis-cli',
          '-p',
          std.toString(port),
          'ping',
        ],
      },
      initialDelaySeconds: 1,
      periodSeconds: 5,
    },
    livenessProbe: {
      exec: {
        command: [
          'redis-cli',
          '-p',
          std.toString(port),
          'ping',
        ],
      },
      initialDelaySeconds: 15,
      periodSeconds: 20,
    },
  };

{
  // These should be added to the `extraConfigs` of the config map for the pod.
  getRedisExtraConfigs:: getRedisExtraConfigs,
  // The function that creates the Redis container.
  getRedisContainer:: getRedisContainer,
}
