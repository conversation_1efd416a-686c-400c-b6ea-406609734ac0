// see https://www.notion.so/Bazel-based-testing-and-deployment-638c55d03c9a446c884fd0b0d0b25447?pvs=4#74fd85baf83f43fd97c47bb4485d218e for details
local tenantNamespaces = import 'deploy/tenants/namespaces.jsonnet';

{
  deployment: [
    {
      name: 'memstore',
      kubecfg: {
        target: '//services/memstore/server:kubecfg',
        task: tenantNamespaces.namespaces,
      },
      health: {
        tier: 'TIER_1_B',
        experts: {
          users: ['tenzin', 'rich', 'igor'],
          slack_channel: '#system-services',
        },
      },
    },
  ],
}
