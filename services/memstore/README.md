# Memstore

An in-memory key-value store service. This service is intended for ephemeral
storage of **NON-PII** data.

Some main use-cases for this service are:
- Sharing a consistent counter amongst replicas of a service.
- Caching data that is expensive to compute.

e.g.: One of the main use cases that drove the creation of this service was
counting the number of times users from a specific IP address were hitting
API-Proxy to enforce the fair-use policy. Within every UTC day, a user should
only be able to make 1000 agents requests.

This service helped by:
- Having one consistent counter shared between replicas of API-Proxy
- Not being redeployed as often as API-proxy (so the counters weren't being
  reset earlier than they were supposed to).
