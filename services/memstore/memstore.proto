syntax = "proto3";

package memstore;

// An in-memory key-value store. Values stored in the memstore are ephemeral and lost
// when the server is restarted. As a general policy we should not store any
// sensitive information within the memstore as any service can access any key
// within the memstore.
//
// Keys are separated by tenant based on the tenant ID in the request context.
// Similarly, subscription channels are also separate by tenant.
// Currently service tokens with no tenant ID are not allowed to access the memstore.
//
// Memstore is essentially a proxy to Redis.
service Memstore {
  // Get retrieves the value for a key.
  //
  // Returns an empty value (unset value) if key doesn't exist.
  rpc Get(GetRequest) returns (GetResponse) {}

  // Set sets the value for a key. Integers should be encoded as their
  // base-10 string representation and will then be incrementable.
  //
  // Notes: the empty string is a valid key (following Redis API).
  // If EX is set, the key will expire after the specified number of seconds.
  //
  // Because the stored value are just bytes, the client is responsible
  // for maintaining forward compatibility as the code changes. A simple
  // way to loudly fail when the type is unexpected is to store
  // serialized `google.protobuf.Any` messages (as they have attached
  // type strings).
  //
  // Errors:
  // - INVALID_ARGUMENT: If the value is empty.
  rpc Set(SetRequest) returns (SetResponse) {}

  // Del deletes a key from the memstore.
  //
  // Returns: the number of actually deleted items. (e.g.: if 3 keys were
  //   requested to be deleted and only 2 existed, then 2 is returned).
  rpc Del(DelRequest) returns (DelResponse) {}

  // Mark the key as expired at the specified time in UTC.
  // If the time is in the past the key is deleted immediately.
  //
  // Returns: true if the key was successfully marked as expired.
  //
  // Errors:
  // - INVALID_ARGUMENT: If incompatible options are passed
  //   together. (NX is mutually exclusive with all other options,
  //   LT and GT are mutually exclusive as well).
  rpc ExpireAt(ExpireAtRequest) returns (ExpireAtResponse) {}

  // Increment the integer value of a key by one.
  //
  // The requested key must already exist and the stored value
  // must be an instance of the well-known protobuf Int64Value type.
  // https://protobuf.dev/reference/protobuf/google.protobuf/#int64-value
  // On error no updates are performed to the memstore.
  //
  // Returns: the new value of the key after incrementing.
  //
  // Errors:
  // - NOT_FOUND: If the key does not exist.
  // - FAILED_PRECONDITION: If the value stored is not an integer.
  rpc Incr(IncrRequest) returns (IncrResponse) {}

  // Increment the integer value of a key by the requested value.
  // Decrementing can be performed by passing a negative increment.
  //
  // The requested key must already exist and the stored value
  // must be an instance of the well-known protobuf Int64Value type.
  // On error no updates are performed to the memstore.
  //
  // Returns: the new value of the key after incrementing.
  //
  // Errors:
  // - NOT_FOUND: If the key does not exist.
  // - FAILED_PRECONDITION: If the key is not an integer.
  rpc IncrBy(IncrByRequest) returns (IncrByResponse) {}

  // Publish a message to a channel.
  //
  // This is a simple wrapper around Redis Pub/Sub functionality.
  // Messages are published to a channel and can be received by subscribers.
  //
  // Returns: the number of clients that received the message.
  //
  // Errors:
  // - INVALID_ARGUMENT: If the channel or message is empty.
  rpc Publish(PublishRequest) returns (PublishResponse) {}

  // Subscribe to messages on a channel.
  //
  // This is a simple wrapper around Redis Pub/Sub functionality.
  // The server will stream messages as they are published to the channel.
  //
  // Errors:
  // - INVALID_ARGUMENT: If the channel is empty.
  rpc Subscribe(SubscribeRequest) returns (stream SubscribeResponse) {}
}

message GetRequest {
  string key = 1;
}

message GetResponse {
  // The value for the key, empty if key doesn't exist.
  optional bytes value = 1;
}

message SetRequest {
  // The key to set
  string key = 1;
  // The value to set. MUST be set to some value.
  bytes value = 2;
  // Optional expiry time in seconds from now.
  optional int64 EX = 3;
}

message SetResponse {}

message DelRequest {
  // The keys to delete
  repeated string keys = 1;
}

message DelResponse {
  // The number of deleted keys.
  int64 num_deleted = 1;
}

message ExpireAtRequest {
  // The key to expire
  string key = 1;
  // The Unix timestamp (seconds since UTC epoch) at which the key should expire.
  uint64 expire_at = 2;

  // Set expiry only when the key has no expiry.
  bool NX = 3;
  // Set expiry only when the key has an existing expiry.
  bool XX = 4;
  // Set expiry only when the new expiry is greater than current one.
  bool GT = 5;
  // Set expiry only when the new expiry is less than current one.
  bool LT = 6;
}

message ExpireAtResponse {
  // True iff the key was successfully marked as expired.
  // False if either the key did not exist, or the operation
  // was skipped because of the provided arguments.
  bool success = 1;
}

message IncrRequest {
  // The key to increment.
  string key = 1;
}

message IncrResponse {
  // The new value of the key after incrementing.
  int64 new_value = 1;
}

message IncrByRequest {
  // The key to increment.
  string key = 1;
  // The amount to increment by.
  int64 increment = 2;
}

message IncrByResponse {
  // The new value of the key after incrementing.
  int64 new_value = 1;
}

message PublishRequest {
  // The channel to publish to
  string channel = 1;
  // The message to publish
  bytes message = 2;
}

message PublishResponse {
  // The number of clients that received the message
  int64 receivers = 1;
}

message SubscribeRequest {
  // The channel to subscribe to
  string channel = 1;
}

message SubscribeResponse {
  // The channel the message was published to
  string channel = 1;
  // The message that was published
  bytes message = 2;
}
