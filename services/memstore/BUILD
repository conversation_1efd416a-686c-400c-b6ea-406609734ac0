load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:go.bzl", "go_grpc_library")
load("//tools/bzl:python.bzl", "py_grpc_library")

proto_library(
    name = "memstore_proto",
    srcs = ["memstore.proto"],
    visibility = ["//services:__subpackages__"],
)

go_grpc_library(
    name = "memstore_go_proto",
    importpath = "github.com/augmentcode/augment/services/memstore/proto",
    proto = ":memstore_proto",
    visibility = [
        "//services:__subpackages__",
    ],
)

py_grpc_library(
    name = "memstore_py_proto",
    protos = [":memstore_proto"],
    visibility = ["//services:__subpackages__"],
)
