"""conftest.py is a special file that pytest will automatically load.

pytest will automatically load and execute before any other test files. This is a
good place to put fixtures that are used by multiple test files.
"""

import argparse
import time
from pathlib import Path
from typing import Generator, Optional

import grpc
import kubernetes
import pytest

import base.python.k8s_test_helper.k8s_test_helper as k8s_test_helper
from base.python.cloud import cloud as cloud_lib
from base.python.grpc.health_check import HealthChecker
from services.memstore.client.client import MemstoreClient
from services.lib.request_context.request_context import RequestContext
from services.token_exchange.client.client import (
    GrpcTokenExchangeClient,
    TokenExchangeClient,
)
from services.token_exchange import token_exchange_pb2


def pytest_addoption(parser):
    """Add command line options to pytest."""
    parser.addoption(
        "--skip-deployment",
        action="store_true",
        help="skip deploy and delete of the models",
        default=False,
    )
    parser.addoption(
        "--teardown-deployment",
        action=argparse.BooleanOptionalAction,
        help="kubectl delete all deployed resources after the run completes.",
        default=k8s_test_helper.is_running_in_test_infra(),
    )
    parser.addoption(
        "--skip-deployment-check",
        action="store_true",
        help="skip checking if the deployments are settled",
        default=False,
    )
    parser.addoption(
        "--namespace",
        help="Namespace to use for the test. If not specified, existing kubecfg black magic does what it does.",
        default=None,
    )
    parser.addoption(
        "--cloud",
        help="Cloud to use",
        default=cloud_lib.get_default_cloud(),
        choices=cloud_lib.get_cloud_list(gcp_only=True),
    )
    # Our test relies on fixtures which should automatically check if their needed
    # services are up before proceeding. So this built-in wait can be short.
    parser.addoption(
        "--settle-time",
        help="Time in seconds to wait for the deployment to settle before proceeding with tests.",
        default=5,
        type=int,
    )


# Helper variables for choosing ports on local machine to port forward
# remote ports to.
NEXT_FORWARDED_PORT = 50052


def get_next_forwarded_port() -> int:
    """Return a fresh local port."""
    global NEXT_FORWARDED_PORT  # pylint: disable=global-statement
    port = NEXT_FORWARDED_PORT
    NEXT_FORWARDED_PORT += 1
    return port


# Use the gRPC health check to see if the service is up.
def _test_response(
    url: str,
    credentials: Optional[grpc.ChannelCredentials],
    service_name: str,
    options: list[tuple[str, str]] | None = None,
) -> bool:
    try:
        checker = HealthChecker(url, credentials, options=options)
        status = checker.is_serving(service_name)
        return status
    except grpc.RpcError as ex:
        print(ex, flush=True)
        return False


@pytest.fixture(scope="session")
def application_deploy(
    request,
) -> Generator[k8s_test_helper.DeployInfo, None, None]:
    """Deploys the application as pytest fixture."""
    k8s_test_helper.print_link_to_logs()
    skip_deploy = request.config.getoption("--skip-deployment")
    skip_deploy_check = request.config.getoption("--skip-deployment-check")
    skip_deploy_check_teardown = not request.config.getoption("--teardown-deployment")
    namespace = request.config.getoption("--namespace")
    cloud = request.config.getoption("--cloud")
    settle_time = request.config.getoption("--settle-time")
    assert cloud

    with k8s_test_helper.deploy(
        skip_deploy=skip_deploy,
        skip_deploy_check=skip_deploy_check,
        skip_deploy_check_teardown=skip_deploy_check_teardown,
        cloud=cloud,
        namespace=namespace,
        kubecfg_binaries=[
            Path("services/memstore/test/test_kubecfg.sh"),
        ],
        settle_time=settle_time,
    ) as deploy_info:
        yield deploy_info


@pytest.fixture(scope="session")
def memstore_client(
    application_deploy: k8s_test_helper.DeployInfo,
) -> Generator[MemstoreClient, None, None]:
    """Return an GRPC stub to access the memstore service.

    If applicable, it will update or create the application in Kubernetes.
    """
    pod_name = _get_pod(
        application_deploy.kubectl.api_client,
        application_deploy.namespace,
        "memstore",
    )
    assert pod_name, "Failed to find pod"
    print("Using pod", pod_name, flush=True)

    credentials = application_deploy.kubectl.load_grpc_credentials()
    # change the endpoint name when verifying the TLS certificate as "localhost" is not
    # on the certificate.
    target_name_override_endpoint = "memstore-svc"
    options: list[tuple[str, str]] = [
        ("grpc.ssl_target_name_override", target_name_override_endpoint)
    ]

    url = ""
    for _ in range(20):
        with application_deploy.kubectl.port_forward(pod_name, 50051, 50051) as port:
            url = f"localhost:{port}"
            if not _test_response(
                url, credentials=credentials, options=options, service_name=""
            ):
                time.sleep(5)
                continue
            else:
                # Sleep for a short period just to make sure DNS comes up.
                time.sleep(2)
                print(f"Response from {url} is up")
                yield MemstoreClient(
                    url,
                    credentials=credentials,
                    options=options,
                )
                break
    else:
        print(f"TIMEOUT testing response from {url}")
        # Yield the non-responsive memstore client, even if test will probably fail.
        yield MemstoreClient(
            url,
            credentials=credentials,
            options=options,
        )


def _get_pod(
    api_client: kubernetes.client.ApiClient | None, namespace: str, deployment_name: str
) -> Optional[str]:
    v1 = kubernetes.client.CoreV1Api(api_client)
    ret = v1.list_namespaced_pod(namespace)
    for service in ret.items:
        name = service.metadata.name
        if name.startswith(deployment_name):
            return name


@pytest.fixture(scope="session")
def token_exchange_client(
    application_deploy: k8s_test_helper.DeployInfo,
) -> Generator[TokenExchangeClient, None, None]:
    """
    Return an GRPC stub to access the token exchange server.

    The client authenticates to the server using the support certificate,
    allowing it to fetch user tokens and service tokens with pretty much
    any desired scope.
    """
    credentials = application_deploy.kubectl.load_grpc_credentials()
    # change the endpoint name when verifying the TLS certificate as "localhost" is not
    # on the certificate.
    target_name_override_endpoint = "token-exchange-central-svc"
    options: list[tuple[str, str]] = [
        ("grpc.ssl_target_name_override", target_name_override_endpoint)
    ]

    url = ""
    for _ in range(20):
        with application_deploy.kubectl.port_forward(
            "deployment/token-exchange-central", 50051, get_next_forwarded_port()
        ) as port:
            url = f"localhost:{port}"
            if not _test_response(
                url, credentials=credentials, options=options, service_name=""
            ):
                time.sleep(5)
                continue
            else:
                # Sleep for a short period even after health check is up. There've been cases
                # where even though this machine is able to access the deployment, DNS from
                # within the Kubernetes cluster is still taking a short bit to update.

                # Ideally we would use a smarter wait here to wait check the DNS explicitly,
                # but whatever, a small wait is easy to understand + use.
                time.sleep(2)
                yield GrpcTokenExchangeClient(
                    url,
                    credentials=credentials,
                    options=options,
                    namespace=application_deploy.namespace,
                )
                break
    else:
        print(f"TIMEOUT testing response from {url}")
        # the test will likely fail
        yield GrpcTokenExchangeClient(
            url,
            credentials=credentials,
            options=options,
            namespace=application_deploy.namespace,
        )


def _service_request_context_for_tenant(
    token_exchange_client: TokenExchangeClient, tenant_id: str
) -> RequestContext:
    """Return a request context with service token for the given tenant."""
    # Technically we don't need any scopes for accessing memstore,
    # but we need to request at least one scope or the token exchange server
    # will reject the request.
    token = token_exchange_client.get_signed_token_for_service(
        tenant_id=tenant_id, scopes=[token_exchange_pb2.Scope.CONTENT_RW]
    )
    return RequestContext(
        request_id="test_request_id",
        request_session_id="test_request_session_id",
        request_source="test_request_source",
        auth_token=token,
    )


@pytest.fixture(scope="session")
def tenant_a_id(
    application_deploy: k8s_test_helper.DeployInfo,
) -> Generator[str, None, None]:
    """Create tenant A, return the ID of the tenant."""
    with k8s_test_helper.new_tenant(
        application_deploy, "test-tenant-a", wait_time=1
    ) as tenant_id:
        yield tenant_id


@pytest.fixture()
def tenant_a_request_context(
    tenant_a_id: str, token_exchange_client: TokenExchangeClient
) -> Generator[RequestContext, None, None]:
    """Return a request context for tenant A."""
    yield _service_request_context_for_tenant(token_exchange_client, tenant_a_id)


@pytest.fixture(scope="session")
def tenant_b_id(
    application_deploy: k8s_test_helper.DeployInfo,
) -> Generator[str, None, None]:
    """Create tenant B, return the ID of the tenant."""
    with k8s_test_helper.new_tenant(
        application_deploy, "test-tenant-b", wait_time=1
    ) as tenant_id:
        yield tenant_id


@pytest.fixture()
def tenant_b_request_context(
    tenant_b_id: str, token_exchange_client: TokenExchangeClient
) -> Generator[RequestContext, None, None]:
    """Return a request context for tenant B."""
    yield _service_request_context_for_tenant(token_exchange_client, tenant_b_id)
