# Memstore Integration Tests

Integration tests for the memstore service. Run with
```
bazel run services/memstore/test:memstore_test
```

For faster re-testing when in dev deploy (i.e.: when you're running this
manually and the services are already deployed):
```
bazel run services/memstore/test:memstore_test -- --skip-deployment
```

The tests are written to be idempotent, so even if the memory store's
state isn't clean the test should work. All of the keys the test
writes to should be prefixed with `test_`, so ideally this shouldn't
trample on any existing data if you're running this against your
dev-deployed instance of memstore.
