"""
Integration tests of the memstore service (tested by making calls against an actual deploy
of the service).
"""

import grpc
import pytest
import time
from pydantic import SecretStr
import queue
import threading
import uuid
from services.lib.request_context.request_context import RequestContext
import base.python.k8s_test_helper.k8s_test_helper as k8s_test_helper
from services.memstore.client.client import MemstoreClient
from services.token_exchange.client.client import TokenExchangeClient
from services.token_exchange import token_exchange_pb2


def _get_service_token(
    token_exchange_client: TokenExchangeClient,
    *,
    scopes: list[token_exchange_pb2.Scope.ValueType] = [],
    tenant_id: str = "test-tenant-id",
) -> SecretStr:
    """Get a service token from the token exchange server."""
    token = token_exchange_client.get_signed_token_for_service(
        tenant_id=tenant_id, scopes=scopes
    )
    return token


def test_memstore_operations(
    application_deploy: k8s_test_helper.DeployInfo,
    memstore_client: MemstoreClient,
    tenant_a_request_context: RequestContext,
):
    """Test that basic memstore operations work as expected.

    The test aims to very simply probe the commands in increasing order of complexity.
    Ideally it would be nice to separate out these tests into separate tests,
    but the issue is that we want the tests to not assume that the memstore state
    is clean to allow for idempotent behavior on dev deploys (and doing that
    requires using multiple commands in conjunction).
    """
    rq = tenant_a_request_context

    # ===== GET and SET Simple =====
    # Set a string value and see that the same result is returned. We set before deletion
    # to not assume that the kvstore is fully fresh (making dev deploy testing easier).
    memstore_client.set("test_string_key", "test_string_value", request_context=rq)

    # When comparing output remember to encode the string to bytes, since under the
    # hood we're encoding the string when sending it over.
    assert (
        memstore_client.get("test_string_key", request_context=rq)
        == "test_string_value".encode()
    )

    # ===== DELETE Simple =====

    # Deleting the key should clear it
    num_deleted = memstore_client.delete(["test_string_key"], request_context=rq)
    assert memstore_client.get("test_string_key", request_context=rq) is None
    assert num_deleted == 1

    # Deleting a non-existent key should not error, and should return 0 deleted items.
    num_deleted = memstore_client.delete(["test_string_key"], request_context=rq)
    assert num_deleted == 0

    # ===== SET Integers =====

    # Validate setting integers through Python client behaves as expected.
    memstore_client.set("test_int_key", 1, request_context=rq)
    assert memstore_client.get("test_int_key", request_context=rq) == "1".encode()

    # ===== INCR Simple =====

    # Make sure that we can increment integer values.
    assert memstore_client.incr("test_int_key", request_context=rq) == 2
    assert memstore_client.get("test_int_key", request_context=rq) == "2".encode()

    # Incrementing a non-existent key should create it and set it to 1.
    memstore_client.delete(["test_int_key_2"], request_context=rq)
    assert memstore_client.incr("test_int_key_2", request_context=rq) == 1

    # Incrementing a non-integer should error with a gRPC error.
    memstore_client.set("test_string_key", "test_string_value", request_context=rq)

    with pytest.raises(grpc.RpcError) as e:
        memstore_client.incr("test_string_key", request_context=rq)
    assert e.value.code() == grpc.StatusCode.FAILED_PRECONDITION  # type: ignore

    # ===== INCRBY =====
    # Make sure that we can increment by arbitrary values.
    assert memstore_client.incr_by("test_int_key_2", 5, request_context=rq) == 6
    assert memstore_client.get("test_int_key_2", request_context=rq) == "6".encode()

    # Check that we can decrement and have negative keys.
    assert memstore_client.incr_by("test_int_key_2", -7, request_context=rq) == -1
    assert memstore_client.get("test_int_key_2", request_context=rq) == "-1".encode()

    # INCRBY on a non-integer should error with a gRPC error.
    memstore_client.set("test_string_key", "test_string_value", request_context=rq)
    try:
        memstore_client.incr_by("test_string_key", 10, request_context=rq)
        assert False, "Expected incr_by to fail"
    except grpc.RpcError as e:
        assert e.code() == grpc.StatusCode.FAILED_PRECONDITION  # type: ignore

    # ===== Type Edge Cases =====

    # Show that Redis strings with our client can handle weird characters.
    memstore_client.set("test_weird_key", "test_string_value_💻", request_context=rq)
    assert (
        memstore_client.get("test_weird_key", request_context=rq)
        == "test_string_value_💻".encode()
    )

    # Make sure that RESP special characters like * for arrays don't break serialization.
    memstore_client.set("test_resp_key", "*1\r\n$3\r\nfoo\r\n", request_context=rq)
    assert (
        memstore_client.get("test_resp_key", request_context=rq)
        == "*1\r\n$3\r\nfoo\r\n".encode()
    )


def test_client_decode_as(
    memstore_client: MemstoreClient,
    tenant_a_request_context: RequestContext,
):
    """Test that the client can decode values as the specified type."""
    rq = tenant_a_request_context

    # Set an integer value and make sure it can be decoded as an int.
    memstore_client.set("test_int_key", 1, request_context=rq)
    assert memstore_client.get("test_int_key", request_context=rq, decode_as="int") == 1

    # Check that strings decode as expected.
    memstore_client.set("test_string_key", "test_string_value", request_context=rq)
    assert (
        memstore_client.get("test_string_key", request_context=rq, decode_as="str")
        == "test_string_value"
    )

    # Check that floats decode as expected.
    memstore_client.set("test_float_key", 1.1, request_context=rq)
    assert (
        memstore_client.get("test_float_key", request_context=rq, decode_as="float")
        == 1.1
    )


def test_memstore_tenant_isolation(
    application_deploy: k8s_test_helper.DeployInfo,
    memstore_client: MemstoreClient,
    tenant_a_request_context: RequestContext,
    tenant_b_request_context: RequestContext,
):
    """Test that memstore properly isolates tenants from each other."""
    rq_a = tenant_a_request_context
    rq_b = tenant_b_request_context

    # Make sure to clear the keys first (idempotency)
    memstore_client.delete(
        ["test_tenant_a_key", "test_tenant_b_key"], request_context=rq_a
    )
    memstore_client.delete(
        ["test_tenant_a_key", "test_tenant_b_key"], request_context=rq_b
    )

    # Set a key in tenant A and make sure it's not accessible from tenant B.
    memstore_client.set(
        "test_tenant_a_key", "test_tenant_a_value", request_context=rq_a
    )
    assert memstore_client.get("test_tenant_a_key", request_context=rq_b) is None

    # Set a key in tenant B and make sure it's not accessible from tenant A.
    memstore_client.set(
        "test_tenant_b_key", "test_tenant_b_value", request_context=rq_b
    )
    assert memstore_client.get("test_tenant_b_key", request_context=rq_a) is None

    # Set the same key in both with different values, and make sure they don't overwrite each other.
    memstore_client.set(
        "test_tenant_shared_key", "test_tenant_a_value", request_context=rq_a
    )
    memstore_client.set(
        "test_tenant_shared_key", "test_tenant_b_value", request_context=rq_b
    )
    assert (
        memstore_client.get("test_tenant_shared_key", request_context=rq_a)
        == "test_tenant_a_value".encode()
    )
    assert (
        memstore_client.get("test_tenant_shared_key", request_context=rq_b)
        == "test_tenant_b_value".encode()
    )


def test_memstore_pubsub(
    memstore_client: MemstoreClient,
    tenant_a_request_context: RequestContext,
):
    """Test that memstore pub/sub functionality works as expected."""
    rq = tenant_a_request_context
    channel_name = f"test_pubsub_channel_{uuid.uuid4().hex}"
    test_message = b"hello world"

    received_event = threading.Event()
    message_queue = queue.Queue()

    # Keep track of the subscription to cancel it later
    subscription_iterator = None

    def subscriber_thread_func():
        nonlocal subscription_iterator
        try:
            subscription_iterator = memstore_client.subscribe(
                channel_name,
                request_context=rq,
                timeout=10,  # Short timeout for stream setup
            )
            for response in subscription_iterator:
                assert response.channel == channel_name
                assert response.message == test_message
                message_queue.put(response.message)
                received_event.set()
                break  # Stop after receiving the first message
        except grpc.RpcError as e:
            # Allow specific errors that might occur during shutdown or cancellation
            if e.code() not in (grpc.StatusCode.CANCELLED, grpc.StatusCode.UNAVAILABLE):  # type: ignore
                # Put the exception in the queue so the main thread can see it
                message_queue.put(e)
                received_event.set()  # Signal main thread about the error
        except Exception as e:
            message_queue.put(e)  # Catch any other unexpected error
            received_event.set()
        finally:
            # Attempt to cancel the subscription if it was initiated
            if hasattr(subscription_iterator, "cancel") and callable(
                subscription_iterator.cancel  # type: ignore
            ):
                subscription_iterator.cancel()  # type: ignore

    subscriber_thread = threading.Thread(target=subscriber_thread_func)
    subscriber_thread.daemon = True  # So it doesn't block program exit
    subscriber_thread.start()

    # Give the subscriber a moment to start up and subscribe
    # A more robust way would be to use another Event signaled by the subscriber
    # once it's ready to receive, but time.sleep is simpler for this test.
    time.sleep(1)

    # Publish the message
    publish_response_receivers = memstore_client.publish(
        channel_name, test_message, request_context=rq
    )
    # In a clean test environment, we expect 1 receiver.
    assert publish_response_receivers == 1

    # Wait for the subscriber to receive the message
    event_was_set = received_event.wait(timeout=10)  # Wait up to 10 seconds
    assert event_was_set, "Subscriber did not receive the message in time."

    # Check for errors from the subscriber thread
    try:
        received_item = message_queue.get_nowait()
        if isinstance(received_item, Exception):
            raise AssertionError(
                f"Subscriber thread raised an exception: {received_item}"
            ) from received_item
        assert received_item == test_message
    except queue.Empty:
        assert False, "Message queue was empty after event was set."

    # Ensure the subscriber thread finishes
    subscriber_thread.join(timeout=5)
    assert not subscriber_thread.is_alive(), "Subscriber thread did not terminate."


def test_memstore_set_with_expiry(
    application_deploy: k8s_test_helper.DeployInfo,
    memstore_client: MemstoreClient,
    tenant_a_request_context: RequestContext,
):
    """Test that key expiration works as expected."""
    rq = tenant_a_request_context

    # Set a key with expiry
    memstore_client.set("test_expiry_key", "test_value", request_context=rq, ex=3)
    set_time = time.time()

    # Verify the key exists immediately after setting
    result = memstore_client.get("test_expiry_key", request_context=rq)
    get_time = time.time()
    elapsed_ms = (get_time - set_time) * 1000

    # In practice elapsed_ms should be like 3ms, if it's close to a second something
    # else funky is afoot.
    assert (
        elapsed_ms < 800
    ), f"Time between set and get too long! Can't assert key exists: {elapsed_ms:.2f}ms"
    assert (
        result == "test_value".encode()
    ), f"Key should exist, elapsed time: {elapsed_ms:.2f}ms"

    # Wait a bit, but not longer than expiry, key should still exist
    time.sleep(1.5)
    assert (
        memstore_client.get("test_expiry_key", request_context=rq)
        == "test_value".encode()
    )

    # Wait for the key to expire (slightly more than 1 second)
    time.sleep(1.5)

    # Verify the key no longer exists after expiry
    assert memstore_client.get("test_expiry_key", request_context=rq) is None


def test_memstore_expireat_simple(
    application_deploy: k8s_test_helper.DeployInfo,
    memstore_client: MemstoreClient,
    tenant_a_request_context: RequestContext,
):
    """Test that expire_at works as expected."""
    rq = tenant_a_request_context

    # Test expire_at with future timestamp
    memstore_client.set("test_expire_at_key", "test_value", request_context=rq)

    # Set expiry to 2 seconds from now
    future_timestamp = int(time.time()) + 2
    success = memstore_client.expireat(
        "test_expire_at_key", future_timestamp, request_context=rq
    )
    assert success, "expire_at should return True for existing key"

    # Verify key still exists immediately
    assert (
        memstore_client.get("test_expire_at_key", request_context=rq)
        == "test_value".encode()
    )

    # Wait for expiry
    time.sleep(2.5)

    # Verify key has expired
    assert memstore_client.get("test_expire_at_key", request_context=rq) is None

    # Test expire_at with past timestamp (should delete immediately)
    memstore_client.set("test_expire_at_past", "test_value", request_context=rq)
    past_timestamp = int(time.time()) - 10  # 10 seconds ago
    success = memstore_client.expireat(
        "test_expire_at_past", past_timestamp, request_context=rq
    )
    assert success, "expire_at should return True for existing key with past timestamp"

    # Key should be deleted immediately
    assert memstore_client.get("test_expire_at_past", request_context=rq) is None

    # Test expire_at on non-existent key
    success = memstore_client.expireat(
        "non_existent_key", int(time.time()) + 10, request_context=rq
    )
    assert not success, "expire_at should return False for non-existent key"

    # Test expire_at with NX flag (set expiry only when key has no expiry)
    memstore_client.set("test_nx_key", "test_value", request_context=rq)

    # First expire_at with NX should succeed
    success = memstore_client.expireat(
        "test_nx_key", int(time.time()) + 10, request_context=rq, nx=True
    )
    assert success, "expire_at with NX should succeed on key with no expiry"

    # Second expire_at with NX should fail (key already has expiry)
    success = memstore_client.expireat(
        "test_nx_key", int(time.time()) + 20, request_context=rq, nx=True
    )
    assert not success, "expire_at with NX should fail on key that already has expiry"

    # Clean up
    memstore_client.delete(["test_nx_key"], request_context=rq)

    # Test expire_at with XX flag (set expiry only when key has existing expiry)
    memstore_client.set("test_xx_key", "test_value", request_context=rq)

    # expire_at with XX should fail (key has no expiry)
    success = memstore_client.expireat(
        "test_xx_key", int(time.time()) + 10, request_context=rq, xx=True
    )
    assert not success, "expire_at with XX should fail on key with no expiry"

    # Set expiry first
    memstore_client.expireat("test_xx_key", int(time.time()) + 5, request_context=rq)

    # Now expire_at with XX should succeed
    success = memstore_client.expireat(
        "test_xx_key", int(time.time()) + 10, request_context=rq, xx=True
    )
    assert success, "expire_at with XX should succeed on key that already has expiry"

    # Clean up
    memstore_client.delete(["test_xx_key"], request_context=rq)


def test_memstore_expireat_lt_gt(
    application_deploy: k8s_test_helper.DeployInfo,
    memstore_client: MemstoreClient,
    tenant_a_request_context: RequestContext,
):
    """Test that expireat's LT and GT flags work as expected."""
    rq = tenant_a_request_context

    # ===== Test GT (only set expiry if new expiry is greater than current one) =====

    test_gt_key = "test_gt_key"
    memstore_client.set(test_gt_key, "test_value", request_context=rq, ex=1)

    success = memstore_client.expireat(
        test_gt_key, int(time.time()) + 1, request_context=rq, gt=True
    )
    assert (
        not success
    ), "expireat with GT should fail when new expiry is not greater than current"

    success = memstore_client.expireat(
        test_gt_key, int(time.time()) + 3, request_context=rq, gt=True
    )
    assert (
        success
    ), "expireat with GT should succeed when new expiry is greater than current"

    time.sleep(2)
    assert (
        memstore_client.get(test_gt_key, request_context=rq) == b"test_value"
    ), "Key should still exist after 2 seconds"

    time.sleep(2)
    assert (
        memstore_client.get(test_gt_key, request_context=rq) is None
    ), "Key should be deleted after 4 seconds"

    # ===== Test LT (only set expiry if new expiry is less than current one) =====

    # Create a test_lt_key key which expires in 1 second
    test_lt_key = "test_lt_key"
    memstore_client.set(test_lt_key, "test_value", request_context=rq, ex=1)

    # expireat with LT should fail when new expiry is not less than current. This
    # is essential for how API-proxy's rollover counting will work.
    success = memstore_client.expireat(
        test_lt_key, int(time.time()) + 20, request_context=rq, lt=True
    )
    assert (
        not success
    ), "expireat with LT should fail when new expiry is not less than current"

    time.sleep(1.5)
    assert (
        memstore_client.get(test_lt_key, request_context=rq) is None
    ), "Key should be deleted after original 1 second expiry"

    # Updating the expiration time with LT should still work when setting an earlier
    # expiry time.
    memstore_client.set(test_lt_key, "test_value", request_context=rq, ex=20)

    success = memstore_client.expireat(
        test_lt_key, int(time.time()) + 1, request_context=rq, lt=True
    )
    assert (
        success
    ), "expireat with LT should succeed when new expiry is less than current"

    time.sleep(1.5)
    assert (
        memstore_client.get(test_lt_key, request_context=rq) is None
    ), "Key should be deleted after new 1 second expiry"


def test_memstore_expireat_exceptions(
    application_deploy: k8s_test_helper.DeployInfo,
    memstore_client: MemstoreClient,
    tenant_a_request_context: RequestContext,
):
    """Test that expireat handles exceptional input properly."""
    rq = tenant_a_request_context

    # Passing NX and XX at the same time should fail with INVALID_ARGUMENT.
    key_nx_xx = f"test_expireat_nx_xx_{uuid.uuid4().hex}"
    memstore_client.set(key_nx_xx, "value", request_context=rq)
    with pytest.raises(grpc.RpcError) as e:
        memstore_client.expireat(
            key_nx_xx, int(time.time()) + 10, request_context=rq, nx=True, xx=True
        )
    assert e.value.code() == grpc.StatusCode.INVALID_ARGUMENT  # type: ignore
    memstore_client.delete([key_nx_xx], request_context=rq)

    # Passing GT and LT at the same time should also fail with INVALID_ARGUMENT.
    key_gt_lt = f"test_expireat_gt_lt_{uuid.uuid4().hex}"
    memstore_client.set(key_gt_lt, "value", request_context=rq)
    # Set an initial expiry for GT/LT to make sense
    memstore_client.expireat(key_gt_lt, int(time.time()) + 5, request_context=rq)
    with pytest.raises(grpc.RpcError) as e:
        memstore_client.expireat(
            key_gt_lt, int(time.time()) + 10, request_context=rq, gt=True, lt=True
        )
    assert e.value.code() == grpc.StatusCode.INVALID_ARGUMENT  # type: ignore
    memstore_client.delete([key_gt_lt], request_context=rq)
