load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:kubecfg.bzl", "kubecfg_multi")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

# These are not services under test, but they are in the
# transitive closure of the dependencies of the services
# under test, or the test code itself.
kubecfg_multi(
    name = "base_services",
    deps = [
        # Tests rely on support certificate to get tokens.
        # This target adds support certificate.
        "//services/deploy:shard_namespace_base_kubecfg",
        # Token exchange server container doesn't create without
        # the request insight publisher config deployed.
        "//services/request_insight:core_kubecfg",
        # Token exchange server crash loops without tenant watcher.
        "//services/tenant_watcher/server:kubecfg",
        # The memstore server instantiates a launch darkly client
        # and needs fake feature flags to receive the connection.
        "//services/test/fake_feature_flags:kubecfg",
        # The memstore server needs a token exchange server to
        # authenticate requests, and the tests interact with
        # this test server to get tokens.
        "//services/token_exchange/server:kubecfg",
    ],
)

# Lots of annoying trial and error to figure out the needed things. When
# iterating on this: use a fresh dev namespace to replicate test runner
# environment, be aware that the test helper deployment teardown does NOT fully
# clear the environment and thus doesn't perfectly recreate test runner
# environment, test runner seems to do a better job.
kubecfg_multi(
    name = "test_kubecfg",
    deps = [
        ":base_services",
        "//services/memstore/server:kubecfg",
    ],
)

pytest_test(
    name = "memstore_test",
    size = "enormous",
    timeout = "eternal",
    srcs = [
        "conftest.py",
        "memstore_test.py",
    ],
    data = [
        ":test_kubecfg",
        "@k8s_binary//file:kubectl",
    ],
    tags = [
        "exclusive",
        "postmerge-test",
        "system-test",
    ],
    deps = [
        "//base/python/k8s_test_helper",
        "//base/python/k8s_test_helper:k8s_resource",
        "//services/lib/request_context:request_context_py",
        "//services/memstore/client:client_py",
        "//services/token_exchange:token_exchange_py_proto",
        "//services/token_exchange/client:client_py",
        requirement("grpcio"),
        requirement("requests"),
        requirement("kubernetes"),
        requirement("pyyaml"),
    ],
)
