"""A Python client library for the memstore service."""

from __future__ import annotations

import logging
from typing import Iterable, Literal, Sequence, assert_never

import grpc

from base.python.grpc import client_options
from services.lib.request_context.request_context import RequestContext
from services.memstore import memstore_pb2, memstore_pb2_grpc


class EncodeError(ValueError):
    pass


EncodableT = bytes | memoryview | bool | int | float | str


# Encoding function taken from the official Python Redis client with
# minor naming/typing modifications.
# https://github.com/redis/redis-py/blob/2732a8553e58d9e77f16566b9132fc7205614a53/redis/_parsers/encoders.py#L14-L35
def encode(value: EncodableT) -> bytes:
    "Return a bytestring or bytes-like representation of the value"
    if isinstance(value, (bytes, memoryview)):
        return value
    elif isinstance(value, bool):
        # special case bool since it is a subclass of int
        raise EncodeError(
            "Invalid input of type: 'bool'. Convert to a "
            "bytes, string, int or float first."
        )
    elif isinstance(value, (int, float)):
        return repr(value).encode()
    elif isinstance(value, str):
        return value.encode()
    else:
        # A value we don't know how to deal with. Throw an error.
        typename = type(value).__name__
        raise EncodeError(
            f"Invalid input of type: '{typename}'. "
            f"Convert to a bytes, string, int or float first."
        )

    # Make sure this function is exhaustive on value's type.
    assert_never(value)


DecodeTName = Literal["int", "float", "str"]
DecodeT = int | float | str


def decode(value: bytes, as_type: DecodeTName) -> DecodeT:
    """Decode a bytestring into the specified type.

    Args:
        value: The bytestring to decode.
        type: The type to decode to (bytes, bool, int, float, or str).

    Returns:
        The decoded value of the specified type.

    Raises:
        EncodeError: If the value cannot be decoded to the specified type.
    """
    if as_type == "int":
        try:
            return int(value.decode())
        except (ValueError, UnicodeDecodeError) as e:
            raise EncodeError(f"Failed to decode to int: {e}")
    elif as_type == "float":
        try:
            return float(value.decode())
        except (ValueError, UnicodeDecodeError) as e:
            raise EncodeError(f"Failed to decode to float: {e}")
    elif as_type == "str":
        try:
            return value.decode()
        except UnicodeDecodeError as e:
            raise EncodeError(f"Failed to decode to string: {e}")

    assert_never(as_type)


def setup_stub(
    endpoint: str,
    credentials: grpc.ChannelCredentials | None,
    options: client_options.OptionsList | None = None,
) -> memstore_pb2_grpc.MemstoreStub:
    """Setup the client stub for the memstore service.

    Args:
        endpoint: The endpoint to connect to.
        credentials: The credentials to use for the connection.
        options: Additional options for the connection.

    Returns:
        A memstore stub.
    """
    logging.info("Creating grpc client to %s with options %s", endpoint, options or [])
    if not credentials:
        channel = grpc.insecure_channel(
            endpoint, options=client_options.create(options)
        )
    else:
        channel = grpc.secure_channel(
            endpoint, credentials, options=client_options.create(options)
        )
    stub = memstore_pb2_grpc.MemstoreStub(channel)
    return stub


class MemstoreClient:
    """Client for the Memstore service."""

    def __init__(
        self,
        endpoint: str,
        credentials: grpc.ChannelCredentials | None,
        options: client_options.OptionsList | None = None,
    ):
        """Initialize the Memstore client.

        Args:
            endpoint: The endpoint to connect to.
            credentials: The credentials to use for the connection.
            options: Additional options for the connection.
        """
        self.stub = setup_stub(endpoint, credentials, options=options)

    def get(
        self,
        key: str,
        request_context: RequestContext,
        *,
        timeout: float = 30,
        decode_as: DecodeTName | None = None,
    ) -> bytes | DecodeT | None:
        """Get the value for a key.

        Args:
            key: The key to get the value for.
            request_context: The request context to use.
            timeout: The timeout to use for the request.
            decode_as: The type to decode the value as. The decoding function
                assumes the values were encoded by passing them through `set` as
                the unencoded type. If not specified, the value is returned as
                bytes.

        Returns:
            The value for the key, or None if the key doesn't exist.

        Raises:
            grpc.RpcError: If there's an error communicating with the service.
        """
        request = memstore_pb2.GetRequest(key=key)
        response = self.stub.Get(
            request,
            timeout=timeout,
            metadata=request_context.to_metadata(),
        )
        if not response.HasField("value"):
            return None
        if decode_as is not None:
            return decode(response.value, decode_as)
        return response.value

    def set(
        self,
        key: str,
        value: EncodableT,
        request_context: RequestContext,
        *,
        timeout: float = 30,
        ex: int | None = None,
    ) -> None:
        """Set the value for a key.

        Args:
            key: The key to set the value for.
            value: The value to set. NOTE: When a non-bytes value is passed in
                it is encoded following standard Redis rules (e.g.: ints are encoded
                as their base-10 string representation). This means `get` returns
                bytes by default, although you can use `decode_as` to reverse the
                automatic encoding implemented by this function for convenience.
            request_context: The request context to use.
            timeout: The timeout to use for the request.
            ex: Optional number of seconds after which the key will expire.

        Raises:
            grpc.RpcError: If there's an error communicating with the service or
                if the value is empty.
        """
        encoded_value = encode(value)
        request = memstore_pb2.SetRequest(key=key, value=encoded_value)
        if ex is not None:
            request.EX = ex
        self.stub.Set(
            request,
            timeout=timeout,
            metadata=request_context.to_metadata(),
        )

    def delete(
        self,
        keys: Sequence[str],
        request_context: RequestContext,
        timeout: float = 30,
    ) -> int:
        """Delete keys from the memstore. The name of this function doesn't
        match the protobuf method name because 'del' is a reserved word in Python.

        Args:
            keys: The keys to delete.
            request_context: The request context to use.
            timeout: The timeout to use for the request.

        Returns:
            The number of keys that were actually deleted.

        Raises:
            grpc.RpcError: If there's an error communicating with the service.
        """
        request = memstore_pb2.DelRequest(keys=keys)
        response = self.stub.Del(
            request,
            timeout=timeout,
            metadata=request_context.to_metadata(),
        )
        return response.num_deleted

    def incr(
        self,
        key: str,
        request_context: RequestContext,
        timeout: float = 30,
    ) -> int:
        """Increment the integer value of a key by one.

        The key must already exist and the stored value must be an Int64Value.

        Args:
            key: The key to increment.
            request_context: The request context to use.
            timeout: The timeout to use for the request.

        Returns:
            The new value of the key after incrementing.

        Raises:
            grpc.RpcError: If there's an error communicating with the service,
                if the key doesn't exist, or if the value is not an integer.
        """
        request = memstore_pb2.IncrRequest(key=key)
        response = self.stub.Incr(
            request,
            timeout=timeout,
            metadata=request_context.to_metadata(),
        )
        return response.new_value

    def incr_by(
        self,
        key: str,
        increment: int,
        request_context: RequestContext,
        timeout: float = 30,
    ) -> int:
        """Increment the integer value of a key by the specified amount.

        The key must already exist and the stored value must be an Int64Value.

        Args:
            key: The key to increment.
            increment: The amount to increment by. Can be negative to decrement.
            request_context: The request context to use.
            timeout: The timeout to use for the request.

        Returns:
            The new value of the key after incrementing.

        Raises:
            grpc.RpcError: If there's an error communicating with the service,
                if the key doesn't exist, or if the value is not an integer.
        """
        request = memstore_pb2.IncrByRequest(key=key, increment=increment)
        response = self.stub.IncrBy(
            request,
            timeout=timeout,
            metadata=request_context.to_metadata(),
        )
        return response.new_value

    def expireat(
        self,
        key: str,
        expire_at: int,
        request_context: RequestContext,
        *,
        timeout: float = 30,
        nx: bool = False,
        xx: bool = False,
        gt: bool = False,
        lt: bool = False,
    ) -> bool:
        """Set the expiry time for a key to the specified Unix timestamp.

        Args:
            key: The key to set expiry for.
            expire_at: Unix timestamp (seconds since epoch) when the key should expire.
            request_context: The request context to use.
            timeout: The timeout to use for the request.
            nx: Set expiry only when the key has no expiry.
            xx: Set expiry only when the key has an existing expiry.
            gt: Set expiry only when the new expiry is greater than current one.
            lt: Set expiry only when the new expiry is less than current one.

        Returns:
            True if the expiry was successfully set, False otherwise.

        Raises:
            grpc.RpcError: If there's an error communicating with the service.
        """
        request = memstore_pb2.ExpireAtRequest(
            key=key,
            expire_at=expire_at,
            NX=nx,
            XX=xx,
            GT=gt,
            LT=lt,
        )
        response = self.stub.ExpireAt(
            request,
            timeout=timeout,
            metadata=request_context.to_metadata(),
        )
        return response.success

    def publish(
        self,
        channel: str,
        message: bytes,
        request_context: RequestContext,
        timeout: float = 30,
    ) -> int:
        """Publish a message to a channel.

        Args:
            channel: The channel to publish to.
            message: The message to publish.
            request_context: The request context to use.
            timeout: The timeout to use for the request.

        Returns:
            The number of clients that received the message.

        Raises:
            grpc.RpcError: If there's an error communicating with the service.
        """
        request = memstore_pb2.PublishRequest(channel=channel, message=message)
        response = self.stub.Publish(
            request,
            timeout=timeout,
            metadata=request_context.to_metadata(),
        )
        return response.receivers

    def subscribe(
        self,
        channel: str,
        request_context: RequestContext,
        timeout: float = 30,
    ) -> Iterable[memstore_pb2.SubscribeResponse]:
        """Subscribe to messages on a channel.

        Args:
            channel: The channel to subscribe to.
            request_context: The request context to use.
            timeout: The timeout to use for the request.

        Yields:
            memstore_pb2.SubscribeResponse: Messages from the channel.

        Raises:
            grpc.RpcError: If there's an error communicating with the service.
        """
        request = memstore_pb2.SubscribeRequest(channel=channel)
        # The timeout here applies to the establishment of the stream,
        # not to individual messages.
        for response in self.stub.Subscribe(
            request,
            timeout=timeout,
            metadata=request_context.to_metadata(),
        ):
            yield response
