package client

// Package client provides a client for the Memstore service.

import (
	"context"
	"strings"
	"time"

	"github.com/rs/zerolog/log"
	"go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"
	"google.golang.org/grpc/metadata"

	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	memstoreproto "github.com/augmentcode/augment/services/memstore/proto"
)

const (
	// Initial reconnect delay for Subscribe. Grow exponentially with each retry.
	InitialReconnectDelay = 500 * time.Millisecond

	// Max reconnect delay for Subscribe.
	MaxReconnectDelay = 10 * time.Second
)

// MemstoreClient interface for the Memstore service. See memstore.proto for details.
type MemstoreClient interface {
	Get(ctx context.Context, requestContext *requestcontext.RequestContext, key string) ([]byte, error)

	// Set sets the value for a key with optional configuration.
	// Example: client.Set(ctx, rc, "key", value, WithExpiry(60)) // expires in 60 seconds
	Set(ctx context.Context, requestContext *requestcontext.RequestContext, key string, value []byte, opts ...SetOption) error

	Del(ctx context.Context, requestContext *requestcontext.RequestContext, keys []string) (int64, error)

	Incr(ctx context.Context, requestContext *requestcontext.RequestContext, key string) (int64, error)

	IncrBy(ctx context.Context, requestContext *requestcontext.RequestContext, key string, increment int64) (int64, error)

	// Publish publishes a message to a channel.
	Publish(ctx context.Context, requestContext *requestcontext.RequestContext, channel string, message []byte) (int64, error)

	// Subscribe subscribes to messages on a channel and returns a stream of messages.
	// If reconnect is true, it will automatically reconnect if the connection is lost,
	// using exponential backoff starting at InitialReconnectDelay and capped at MaxReconnectDelay.
	Subscribe(ctx context.Context, requestContext *requestcontext.RequestContext, channel string, reconnect bool) (chan []byte, func(), error)

	// This should be called when done with the client to cleanup resources for this client.
	Close()
}

// MemstoreClientImpl implementation of the MemstoreClient interface.
type MemstoreClientImpl struct {
	// gRPC channel.
	Conn *grpc.ClientConn

	// gRPC client to use to make requests.
	Client memstoreproto.MemstoreClient
}

// NewMemstoreClient creates a new MemstoreClient.
//
// endpoint: The endpoint of the Memstore service.
// credentials: The credentials to use for the channel (optional)
// additionalOpts: Additional dial options to use (optional, can be nil)
//
// Returns: The client stub for the Memstore or an error if the client could not be created.
func NewMemstoreClient(endpoint string, credentials credentials.TransportCredentials, additionalOpts ...grpc.DialOption) (MemstoreClient, error) {
	opts := []grpc.DialOption{
		grpc.WithTransportCredentials(credentials),
		grpc.WithStatsHandler(otelgrpc.NewClientHandler()),
	}

	// Apply round-robin load balancing only for headless services
	if strings.Contains(endpoint, "headless") {
		opts = append(opts, grpc.WithDefaultServiceConfig(`{"loadBalancingConfig": [{"round_robin":{}}]}`))
	}

	// Add any additional options
	if len(additionalOpts) > 0 {
		opts = append(opts, additionalOpts...)
	}

	conn, err := grpc.NewClient(endpoint, opts...)
	if err != nil {
		return nil, err
	}
	client := memstoreproto.NewMemstoreClient(conn)
	return &MemstoreClientImpl{Conn: conn, Client: client}, nil
}

// Get retrieves the value for a key.
func (c *MemstoreClientImpl) Get(ctx context.Context, requestContext *requestcontext.RequestContext, key string) ([]byte, error) {
	req := &memstoreproto.GetRequest{
		Key: key,
	}
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	resp, err := c.Client.Get(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp.Value, nil
}

// SetOption defines options for the Set operation
type SetOption func(*memstoreproto.SetRequest)

// WithExpiry returns a SetOption that sets the expiry time in seconds
func WithExpiry(seconds int64) SetOption {
	return func(req *memstoreproto.SetRequest) {
		req.EX = &seconds
	}
}

// Set sets the value for a key with optional configuration.
func (c *MemstoreClientImpl) Set(
	ctx context.Context,
	requestContext *requestcontext.RequestContext,
	key string,
	value []byte,
	opts ...SetOption,
) error {
	req := &memstoreproto.SetRequest{
		Key:   key,
		Value: value,
	}

	// Apply all options
	for _, opt := range opts {
		opt(req)
	}

	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	_, err := c.Client.Set(ctx, req)
	return err
}

// Del deletes a key from the Memstore.
func (c *MemstoreClientImpl) Del(ctx context.Context, requestContext *requestcontext.RequestContext, keys []string) (int64, error) {
	req := &memstoreproto.DelRequest{
		Keys: keys,
	}
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	resp, err := c.Client.Del(ctx, req)
	if err != nil {
		return resp.GetNumDeleted(), err
	}
	return resp.GetNumDeleted(), nil
}

// Incr increments the integer value of a key by one.
func (c *MemstoreClientImpl) Incr(ctx context.Context, requestContext *requestcontext.RequestContext, key string) (int64, error) {
	req := &memstoreproto.IncrRequest{
		Key: key,
	}
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	resp, err := c.Client.Incr(ctx, req)
	if err != nil {
		return resp.GetNewValue(), err
	}
	return resp.GetNewValue(), nil
}

// IncrBy increments the integer value of a key by the requested value.
func (c *MemstoreClientImpl) IncrBy(ctx context.Context, requestContext *requestcontext.RequestContext, key string, increment int64) (int64, error) {
	req := &memstoreproto.IncrByRequest{
		Key:       key,
		Increment: increment,
	}
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	resp, err := c.Client.IncrBy(ctx, req)
	if err != nil {
		return resp.GetNewValue(), err
	}
	return resp.GetNewValue(), nil
}

// Publish publishes a message to a channel.
func (c *MemstoreClientImpl) Publish(ctx context.Context, requestContext *requestcontext.RequestContext, channel string, message []byte) (int64, error) {
	req := &memstoreproto.PublishRequest{
		Channel: channel,
		Message: message,
	}
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	resp, err := c.Client.Publish(ctx, req)
	if err != nil {
		return 0, err
	}
	return resp.GetReceivers(), nil
}

// Subscribe subscribes to messages on a channel and returns a stream of messages.
// If reconnect is true, it will automatically reconnect if the connection is lost,
// using exponential backoff starting at InitialReconnectDelay and capped at MaxReconnectDelay.
//
// Returns:
//
//	channel which yields the messages,
//	a cancellation function for ending the subscription,
//	error on failure
func (c *MemstoreClientImpl) Subscribe(ctx context.Context, requestContext *requestcontext.RequestContext, channel string, reconnect bool) (chan []byte, func(), error) {
	req := &memstoreproto.SubscribeRequest{
		Channel: channel,
	}
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())

	// Create a channel to send messages to the caller
	messageChan := make(chan []byte, 100) // Buffer size of 100
	// internalCtx is used to signal the goroutine to stop.
	// We derive it from the original ctx so that if the original ctx is cancelled, internalCtx is also cancelled.
	internalCtx, internalCancel := context.WithCancel(ctx)

	// Create a cancellation function for the caller
	cancelFunc := func() {
		internalCancel() // Signal the goroutine to stop
	}

	go func() {
		// Ensure messageChan is closed only when this goroutine truly exits.
		// This is important because the caller might still be reading from messageChan
		// if cancelFunc hasn't been called yet.
		defer func() {
			log.Ctx(ctx).Info().Str("channel", channel).Msg("Memstore Subscribe: Goroutine exiting, closing messageChan")
			close(messageChan)
		}()

		currentDelay := InitialReconnectDelay
		var stream memstoreproto.Memstore_SubscribeClient // Declare stream here to be available in the loop

		// Outer loop for reconnection
		for {
			// Check for cancellation before attempting to connect or after a disconnect.
			select {
			case <-internalCtx.Done():
				log.Ctx(ctx).Info().Str("channel", channel).Msg("Memstore Subscribe: Context cancelled, exiting goroutine")
				return
			default:
				// Proceed with attempting to connect and receive
			}

			log.Ctx(ctx).Debug().Str("channel", channel).Msg("Memstore Subscribe: Attempting to connect")
			var err error
			// We use internalCtx here for the stream, so if cancelFunc is called (which cancels internalCtx),
			// the stream establishment or Recv() call will be interrupted.
			// The requestContext provides the metadata (headers) for the gRPC call.
			streamCtx := metadata.NewOutgoingContext(internalCtx, requestContext.ToMetadata())
			stream, err = c.Client.Subscribe(streamCtx, req)
			if err != nil {
				if !reconnect {
					log.Ctx(ctx).Error().Err(err).Str("channel", channel).Msg("Memstore Subscribe: Error creating stream and reconnect is disabled")
					return
				}

				log.Ctx(ctx).Error().Err(err).Str("channel", channel).Dur("delay", currentDelay).Msg("Memstore Subscribe: Error creating stream, will retry")
				// Wait for the delay or until context is cancelled
				select {
				case <-time.After(currentDelay):
					currentDelay *= 2
					if currentDelay > MaxReconnectDelay {
						currentDelay = MaxReconnectDelay
					}
					continue // Try to reconnect
				case <-internalCtx.Done():
					log.Ctx(ctx).Info().Str("channel", channel).Msg("Memstore Subscribe: Context cancelled during retry wait")
					return
				}
			}

			log.Ctx(ctx).Debug().Str("channel", channel).Msg("Memstore Subscribe: Successfully connected")
			currentDelay = InitialReconnectDelay // Reset delay on successful connection

			// Inner loop for receiving messages
			for {
				resp, err := stream.Recv()
				if err != nil {
					// Check if the context was cancelled (which might cause Recv to error)
					select {
					case <-internalCtx.Done():
						log.Ctx(ctx).Info().Err(err).Str("channel", channel).Msg("Memstore Subscribe: Context cancelled, Recv() error")
						return // Exit goroutine
					default:
						// Context not cancelled, so it's a genuine Recv error (disconnect, etc.)
						log.Ctx(ctx).Error().Err(err).Str("channel", channel).Msg("Memstore Subscribe: Error receiving message")
						if !reconnect {
							log.Ctx(ctx).Info().Str("channel", channel).Msg("Memstore Subscribe: Reconnect is disabled, exiting")
							return
						}
						// Break inner loop to trigger reconnection in the outer loop.
						// No need for an explicit delay here, the outer loop's delay will handle it.
						goto ReconnectLoop // Break from inner loop to reconnect
					}
				}

				// Send the message to the channel
				select {
				case messageChan <- resp.Message:
					// Message sent successfully
				case <-internalCtx.Done():
					log.Ctx(ctx).Info().Str("channel", channel).Msg("Memstore Subscribe: Context cancelled while sending message")
					return // Exit goroutine
				}
			}
		ReconnectLoop: // Label for goto to break out of inner loop and continue outer
		}
	}()

	return messageChan, cancelFunc, nil
}

// Close closes the connection to the server.
func (c *MemstoreClientImpl) Close() {
	c.Conn.Close()
}
