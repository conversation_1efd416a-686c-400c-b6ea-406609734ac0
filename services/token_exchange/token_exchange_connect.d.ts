// @generated by protoc-gen-connect-es v1.4.0 with parameter "keep_empty_files=true,target=js+dts"
// @generated from file services/token_exchange/token_exchange.proto (package token_exchange, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { GetSignedTokenForIAPTokenRequest, GetSignedTokenForIAPTokenResponse, GetSignedTokenForServiceRequest, GetSignedTokenForServiceResponse, GetSignedTokenForUserRequest, GetSignedTokenForUserResponse, GetVerificationKeyRequest, GetVerificationKeyResponse } from "./token_exchange_pb.js";
import { MethodKind } from "@bufbuild/protobuf";

/**
 * @generated from service token_exchange.TokenExchange
 */
export declare const TokenExchange: {
  readonly typeName: "token_exchange.TokenExchange",
  readonly methods: {
    /**
     * @generated from rpc token_exchange.TokenExchange.GetSignedTokenForUser
     */
    readonly getSignedTokenForUser: {
      readonly name: "GetSignedTokenForUser",
      readonly I: typeof GetSignedTokenForUserRequest,
      readonly O: typeof GetSignedTokenForUserResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc token_exchange.TokenExchange.GetSignedTokenForService
     */
    readonly getSignedTokenForService: {
      readonly name: "GetSignedTokenForService",
      readonly I: typeof GetSignedTokenForServiceRequest,
      readonly O: typeof GetSignedTokenForServiceResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc token_exchange.TokenExchange.GetSignedTokenForIAPToken
     */
    readonly getSignedTokenForIAPToken: {
      readonly name: "GetSignedTokenForIAPToken",
      readonly I: typeof GetSignedTokenForIAPTokenRequest,
      readonly O: typeof GetSignedTokenForIAPTokenResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc token_exchange.TokenExchange.GetVerificationKey
     */
    readonly getVerificationKey: {
      readonly name: "GetVerificationKey",
      readonly I: typeof GetVerificationKeyRequest,
      readonly O: typeof GetVerificationKeyResponse,
      readonly kind: MethodKind.Unary,
    },
  }
};

