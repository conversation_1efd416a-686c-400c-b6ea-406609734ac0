syntax = "proto3";

package third_party_arbiter;

option go_package = "github.com/augmentcode/augment/services/third_party_arbiter/proto";

service ThirdPartyArbiter {
  rpc GetTarget(GetTargetRequest) returns (GetTargetResponse) {}
  rpc ReportResponse(ReportResponseRequest) returns (ReportResponseResponse) {}
}

enum ThirdPartyClient {
  UNKNOWN = 0; // Use for errors only
  ANTHROPIC_DIRECT = 1;
  ANTHROPIC_VERTEXAI_US_E5 = 2;
  ANTHROPIC_VERTEXAI_EU_W1 = 3;
  ANTHROPIC_VERTEXAI_AS_SE1 = 4;
}

enum Model {
  UNKNOWN_MODEL = 0; // Use for errors only
  CLAUDE_3_5_SONNET_V2 = 1;
  CLAUDE_3_7_SONNET = 2;
  CLAUDE_4_0_SONNET = 3;
  CLAUDE_4_0_OPUS = 4;
}

message GetTargetRequest {
  Model model = 1;
  repeated ThirdPartyClient supported_targets = 2;
}

message GetTargetResponse {
  repeated ThirdPartyClient targets = 1;
  int32 delay_ms = 2;
}

message ReportResponseRequest {
  ThirdPartyClient target = 1;
  Model model = 2;
  bool is_successful = 3;
  optional int32 retry_after_seconds = 4; // Often reported with 429
}

message ReportResponseResponse {}
