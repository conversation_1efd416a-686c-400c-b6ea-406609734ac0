# Third Party Arbiter Service

The Third Party Arbiter service is a central deployment service that determines which region and service should handle third party requests.

## Overview

This service provides a simple gRPC API with methods to determine the target region and service for routing third party requests, and to report response information for load balancing.

## API

The service exposes the following gRPC methods:

```protobuf
service ThirdPartyArbiter {
  rpc GetTarget(GetTargetRequest) returns (GetTargetResponse) {}
  rpc ReportResponse(ReportResponseRequest) returns (ReportResponseResponse) {}
}
```

The service uses feature flags to determine routing and implements adaptive load balancing based on response history.

## Building

To build the service:

```bash
bazel build //services/third_party_arbiter/server:server
```

To build the Docker image:

```bash
bazel build //services/third_party_arbiter/server:image
```

## Testing

To run the tests:

```bash
bazel test //services/third_party_arbiter/server:third_party_arbiter_server_test
```

## Deployment

The service is deployed to central namespaces only. To deploy:

```bash
bazel run //services/third_party_arbiter/server:kubecfg
```

## Monitoring

The service exposes Prometheus metrics on port 9090. Monitoring configuration is available in `monitoring.jsonnet`.

## Clients

Both Go and Python clients are available:

- Go: `//services/third_party_arbiter/client:client_go`
- Python: `//services/third_party_arbiter/client:client_py`
