"""Tests for the ThirdPartyArbiter client."""

import unittest
from unittest.mock import MagicMock, patch

from services.third_party_arbiter import third_party_arbiter_pb2
from services.lib.request_context.request_context import RequestContext
from services.third_party_arbiter.client.client import GrpcThirdPartyArbiterClientImpl


class ThirdPartyArbiterClientTest(unittest.TestCase):
    """Tests for the ThirdPartyArbiter client."""

    def setUp(self):
        """Set up the test."""
        self.mock_stub = MagicMock()
        self.client = GrpcThirdPartyArbiterClientImpl("localhost:50051", None)
        self.client.stub = self.mock_stub
        self.request_context = RequestContext.create()

    def test_get_target(self):
        """Test the get_target method."""
        # Set up the mock response
        expected_response = third_party_arbiter_pb2.GetTargetResponse(
            targets=[
                third_party_arbiter_pb2.ThirdPartyClient.ANTHROPIC_DIRECT,
                third_party_arbiter_pb2.ThirdPartyClient.ANTHROPIC_VERTEXAI_US_E5,
            ],
            delay_ms=0,
        )
        self.mock_stub.GetTarget.return_value = expected_response

        # Call the method without supported_targets
        response = self.client.get_target(
            self.request_context,
            third_party_arbiter_pb2.Model.CLAUDE_3_5_SONNET_V2,
        )

        # Verify the response
        self.assertEqual(len(response.targets), 2)
        self.assertEqual(
            response.targets[0],
            third_party_arbiter_pb2.ThirdPartyClient.ANTHROPIC_DIRECT,
        )
        self.assertEqual(
            response.targets[1],
            third_party_arbiter_pb2.ThirdPartyClient.ANTHROPIC_VERTEXAI_US_E5,
        )
        self.assertEqual(response.delay_ms, 0)

        # Verify the stub was called correctly
        self.mock_stub.GetTarget.assert_called_once()
        call_args = self.mock_stub.GetTarget.call_args
        self.assertIsInstance(call_args[0][0], third_party_arbiter_pb2.GetTargetRequest)
        self.assertEqual(
            call_args[0][0].model, third_party_arbiter_pb2.Model.CLAUDE_3_5_SONNET_V2
        )
        self.assertEqual(len(call_args[0][0].supported_targets), 0)
        self.assertEqual(call_args[1]["metadata"], self.request_context.to_metadata())

        # Reset the mock
        self.mock_stub.GetTarget.reset_mock()

        # Call the method with supported_targets
        supported_targets = [
            third_party_arbiter_pb2.ThirdPartyClient.ANTHROPIC_DIRECT,
            third_party_arbiter_pb2.ThirdPartyClient.ANTHROPIC_VERTEXAI_US_E5,
        ]
        response = self.client.get_target(
            self.request_context,
            third_party_arbiter_pb2.Model.CLAUDE_3_7_SONNET,
            supported_targets,
        )

        # Verify the stub was called correctly
        self.mock_stub.GetTarget.assert_called_once()
        call_args = self.mock_stub.GetTarget.call_args
        self.assertIsInstance(call_args[0][0], third_party_arbiter_pb2.GetTargetRequest)
        self.assertEqual(len(call_args[0][0].supported_targets), 2)
        self.assertEqual(
            call_args[0][0].supported_targets[0],
            third_party_arbiter_pb2.ThirdPartyClient.ANTHROPIC_DIRECT,
        )
        self.assertEqual(
            call_args[0][0].supported_targets[1],
            third_party_arbiter_pb2.ThirdPartyClient.ANTHROPIC_VERTEXAI_US_E5,
        )
        self.assertEqual(call_args[1]["metadata"], self.request_context.to_metadata())

    def test_report_response(self):
        """Test the report_response method."""
        # Set up the mock response
        expected_response = third_party_arbiter_pb2.ReportResponseResponse()
        self.mock_stub.ReportResponse.return_value = expected_response

        # Call the method without retry_after_seconds
        response = self.client.report_response(
            self.request_context,
            third_party_arbiter_pb2.Model.CLAUDE_3_5_SONNET_V2,
            third_party_arbiter_pb2.ThirdPartyClient.ANTHROPIC_VERTEXAI_US_E5,
            False,
        )

        # Verify the response
        self.assertEqual(response, expected_response)

        # Verify the stub was called correctly
        self.mock_stub.ReportResponse.assert_called_once()
        call_args = self.mock_stub.ReportResponse.call_args
        request = call_args[0][0]
        self.assertIsInstance(request, third_party_arbiter_pb2.ReportResponseRequest)
        self.assertEqual(
            request.model,
            third_party_arbiter_pb2.Model.CLAUDE_3_5_SONNET_V2,
        )
        self.assertEqual(
            request.target,
            third_party_arbiter_pb2.ThirdPartyClient.ANTHROPIC_VERTEXAI_US_E5,
        )
        self.assertEqual(request.is_successful, False)
        self.assertFalse(request.HasField("retry_after_seconds"))
        self.assertEqual(call_args[1]["metadata"], self.request_context.to_metadata())

        # Reset the mock
        self.mock_stub.ReportResponse.reset_mock()

        # Call the method with retry_after_seconds
        response = self.client.report_response(
            self.request_context,
            third_party_arbiter_pb2.Model.CLAUDE_3_7_SONNET,
            third_party_arbiter_pb2.ThirdPartyClient.ANTHROPIC_VERTEXAI_US_E5,
            False,
            1,
        )

        # Verify the response
        self.assertEqual(response, expected_response)

        # Verify the stub was called correctly
        self.mock_stub.ReportResponse.assert_called_once()
        call_args = self.mock_stub.ReportResponse.call_args
        request = call_args[0][0]
        self.assertIsInstance(request, third_party_arbiter_pb2.ReportResponseRequest)
        self.assertEqual(
            request.model,
            third_party_arbiter_pb2.Model.CLAUDE_3_7_SONNET,
        )
        self.assertEqual(
            request.target,
            third_party_arbiter_pb2.ThirdPartyClient.ANTHROPIC_VERTEXAI_US_E5,
        )
        self.assertEqual(request.is_successful, False)
        self.assertTrue(request.HasField("retry_after_seconds"))
        self.assertEqual(request.retry_after_seconds, 1)
        self.assertEqual(call_args[1]["metadata"], self.request_context.to_metadata())


if __name__ == "__main__":
    unittest.main()
