package client

import (
	"context"

	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	proto "github.com/augmentcode/augment/services/third_party_arbiter/proto"
)

type MockThirdPartyArbiterClient struct {
	GetTargetFunc      func(ctx context.Context, requestContext requestcontext.RequestContext, supportedTargets []proto.ThirdPartyClient) (*proto.GetTargetResponse, error)
	ReportResponseFunc func(ctx context.Context, requestContext requestcontext.RequestContext, target proto.ThirdPartyClient, isSuccessful bool, retryAfterSeconds *int32) (*proto.ReportResponseResponse, error)
}

func (m *MockThirdPartyArbiterClient) GetTarget(ctx context.Context, requestContext requestcontext.RequestContext, supportedTargets []proto.ThirdPartyClient) (*proto.GetTargetResponse, error) {
	if m.GetTargetFunc != nil {
		return m.GetTargetFunc(ctx, requestContext, supportedTargets)
	}
	return &proto.GetTargetResponse{
		Targets: []proto.ThirdPartyClient{proto.ThirdPartyClient_UNKNOWN},
		DelayMs: 0,
	}, nil
}

func (m *MockThirdPartyArbiterClient) ReportResponse(ctx context.Context, requestContext requestcontext.RequestContext, target proto.ThirdPartyClient, isSuccessful bool, retryAfterSeconds *int32) (*proto.ReportResponseResponse, error) {
	if m.ReportResponseFunc != nil {
		return m.ReportResponseFunc(ctx, requestContext, target, isSuccessful, retryAfterSeconds)
	}
	return &proto.ReportResponseResponse{}, nil
}
