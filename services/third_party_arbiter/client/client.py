import typing
from typing import Optional

import grpc

from services.third_party_arbiter import third_party_arbiter_pb2
from services.third_party_arbiter import third_party_arbiter_pb2_grpc
from services.lib.request_context.request_context import RequestContext


class ThirdPartyArbiterClient(typing.Protocol):
    """Interface for the Third Party Arbiter service."""

    def get_target(
        self,
        request_context: RequestContext,
        model: third_party_arbiter_pb2.Model.ValueType,
        supported_targets: typing.Optional[
            typing.List[third_party_arbiter_pb2.ThirdPartyClient.ValueType]
        ] = None,
    ) -> third_party_arbiter_pb2.GetTargetResponse:
        """Get the target client for routing third-party requests.

        Args:
            request_context: The request context.
            model: The model to get targets for.
            supported_targets: Optional list of supported targets. If provided, only these targets
                will be considered for selection.

        Returns:
            A response with a list of targets. The first target in the list
            is the primary target, and the second is the secondary target.
        """
        raise NotImplementedError()

    def report_response(
        self,
        request_context: RequestContext,
        model: third_party_arbiter_pb2.Model.ValueType,
        target: third_party_arbiter_pb2.ThirdPartyClient.ValueType,
        is_successful: bool,
        retry_after_seconds: Optional[int] = None,
    ) -> third_party_arbiter_pb2.ReportResponseResponse:
        """Report a response from a third-party service.

        Args:
            request_context: The request context.
            model: The model that was used.
            target: The target that was used.
            is_successful: Whether the request was successful.
            retry_after_seconds: Optional number of seconds to wait before retrying.
        """
        raise NotImplementedError()


class GrpcThirdPartyArbiterClientImpl:
    """Class to call Third Party Arbiter APIs remotely."""

    def __init__(self, endpoint: str, credentials: grpc.ChannelCredentials | None):
        self.endpoint = endpoint
        if credentials:
            channel = grpc.secure_channel(endpoint, credentials)
        else:
            channel = grpc.insecure_channel(endpoint)
        self.stub = third_party_arbiter_pb2_grpc.ThirdPartyArbiterStub(channel)

    def get_target(
        self,
        request_context: RequestContext,
        model: third_party_arbiter_pb2.Model.ValueType,
        supported_targets: typing.Optional[
            typing.List[third_party_arbiter_pb2.ThirdPartyClient.ValueType]
        ] = None,
    ) -> third_party_arbiter_pb2.GetTargetResponse:
        """Get the target client for routing third-party requests.

        Args:
            request_context: The request context.
            model: The model to get targets for.
            supported_targets: Optional list of supported targets. If provided, only these targets
                will be considered for selection.

        Returns:
            A response with a list of targets. The first target in the list
            is the primary target, and the second is the secondary target.
        """
        request = third_party_arbiter_pb2.GetTargetRequest(model=model)
        if supported_targets:
            request.supported_targets.extend(supported_targets)
        response = self.stub.GetTarget(request, metadata=request_context.to_metadata())
        return response

    def report_response(
        self,
        request_context: RequestContext,
        model: third_party_arbiter_pb2.Model.ValueType,
        target: third_party_arbiter_pb2.ThirdPartyClient.ValueType,
        is_successful: bool,
        retry_after_seconds: Optional[int] = None,
    ) -> third_party_arbiter_pb2.ReportResponseResponse:
        """Report a response from a third-party service.

        Args:
            request_context: The request context.
            model: The model that was used.
            target: The target that was used.
            is_successful: Whether the request was successful.
            retry_after_seconds: Optional number of seconds to wait before retrying.
        """
        # We need to use the raw value of the enum for the proto
        request = third_party_arbiter_pb2.ReportResponseRequest(
            model=model,
            target=target,
            is_successful=is_successful,
        )

        if retry_after_seconds is not None:
            request.retry_after_seconds = retry_after_seconds

        response = self.stub.ReportResponse(
            request, metadata=request_context.to_metadata()
        )
        return response
