from typing import Optional, List

from services.third_party_arbiter import third_party_arbiter_pb2
from services.lib.request_context.request_context import RequestContext


class MockThirdPartyArbiterClient:
    """Mock implementation of the Third Party Arbiter client for testing."""

    def __init__(self):
        self.get_target_response = third_party_arbiter_pb2.GetTargetResponse(
            targets=[third_party_arbiter_pb2.ThirdPartyClient.UNKNOWN],
            delay_ms=0,
        )
        self.report_response_response = third_party_arbiter_pb2.ReportResponseResponse()

    def get_target(
        self,
        request_context: RequestContext,  # pylint: disable=unused-argument
        supported_targets: Optional[
            list[third_party_arbiter_pb2.ThirdPartyClient.ValueType]
        ] = None,  # pylint: disable=unused-argument
    ) -> third_party_arbiter_pb2.GetTargetResponse:
        """Mock implementation of get_target."""
        return self.get_target_response

    def report_response(
        self,
        request_context: RequestContext,  # pylint: disable=unused-argument
        target: third_party_arbiter_pb2.ThirdPartyClient,  # pylint: disable=unused-argument
        is_successful: bool,  # pylint: disable=unused-argument
        retry_after_seconds: Optional[int] = None,  # pylint: disable=unused-argument
    ) -> third_party_arbiter_pb2.ReportResponseResponse:
        """Mock implementation of report_response."""
        return self.report_response_response
