package client

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"google.golang.org/grpc"

	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	proto "github.com/augmentcode/augment/services/third_party_arbiter/proto"
)

// MockThirdPartyArbiterStub is a mock implementation of the ThirdPartyArbiterClient interface
type MockThirdPartyArbiterStub struct {
	mock.Mock
}

func (m *MockThirdPartyArbiterStub) GetTarget(ctx context.Context, in *proto.GetTargetRequest, opts ...grpc.CallOption) (*proto.GetTargetResponse, error) {
	args := m.Called(ctx, in, opts)
	return args.Get(0).(*proto.GetTargetResponse), args.Error(1)
}

func (m *MockThirdPartyArbiterStub) ReportResponse(ctx context.Context, in *proto.ReportResponseRequest, opts ...grpc.CallOption) (*proto.ReportResponseResponse, error) {
	args := m.Called(ctx, in, opts)
	return args.Get(0).(*proto.ReportResponseResponse), args.Error(1)
}

func TestGetTarget(t *testing.T) {
	// Create a mock stub
	mockStub := new(MockThirdPartyArbiterStub)

	// Create a client with the mock stub
	client := &thirdPartyArbiterClientImpl{
		endpoint: "localhost:50051",
		stub:     mockStub,
	}

	// Create a request context
	requestCtx := requestcontext.RequestContext{
		RequestId:        "test-request-id",
		RequestSessionId: "test-session-id",
	}

	// Set up the expected response
	expectedResponse := &proto.GetTargetResponse{
		Targets: []proto.ThirdPartyClient{
			proto.ThirdPartyClient_ANTHROPIC_DIRECT,
			proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_US_E5,
		},
		DelayMs: 0,
	}

	// Set up the mock to return the expected response
	mockStub.On("GetTarget", mock.Anything, &proto.GetTargetRequest{
		Model:            proto.Model_CLAUDE_3_5_SONNET_V2,
		SupportedTargets: []proto.ThirdPartyClient{},
	}, mock.Anything).Return(expectedResponse, nil)

	// Call the method
	response, err := client.GetTarget(context.Background(), requestCtx, proto.Model_CLAUDE_3_5_SONNET_V2, []proto.ThirdPartyClient{})

	// Assert that there was no error
	assert.NoError(t, err)

	// Assert that the response is as expected
	assert.Equal(t, expectedResponse, response)

	// Assert that the stub was called with the correct arguments
	mockStub.AssertCalled(t, "GetTarget", mock.Anything, &proto.GetTargetRequest{
		Model:            proto.Model_CLAUDE_3_5_SONNET_V2,
		SupportedTargets: []proto.ThirdPartyClient{},
	}, mock.Anything)

	// We can't easily assert on the context with metadata, so we'll just check that the stub was called
	mockStub.AssertNumberOfCalls(t, "GetTarget", 1)
}

func TestReportResponse(t *testing.T) {
	// Create a mock stub
	mockStub := new(MockThirdPartyArbiterStub)

	// Create a client with the mock stub
	client := &thirdPartyArbiterClientImpl{
		endpoint: "localhost:50051",
		stub:     mockStub,
	}

	// Create a request context
	requestCtx := requestcontext.RequestContext{
		RequestId:        "test-request-id",
		RequestSessionId: "test-session-id",
	}

	// Set up the expected response
	expectedResponse := &proto.ReportResponseResponse{}

	// Test without retry_after_seconds
	target := proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_US_E5
	isSuccessful := false

	// Set up the mock to return the expected response
	mockStub.On("ReportResponse", mock.Anything, &proto.ReportResponseRequest{
		Model:        proto.Model_CLAUDE_3_5_SONNET_V2,
		Target:       target,
		IsSuccessful: isSuccessful,
	}, mock.Anything).Return(expectedResponse, nil)

	// Call the method
	response, err := client.ReportResponse(context.Background(), requestCtx, proto.Model_CLAUDE_3_5_SONNET_V2, target, isSuccessful, nil)

	// Assert that there was no error
	assert.NoError(t, err)

	// Assert that the response is as expected
	assert.Equal(t, expectedResponse, response)

	// Assert that the stub was called with the correct arguments
	mockStub.AssertCalled(t, "ReportResponse", mock.Anything, &proto.ReportResponseRequest{
		Model:        proto.Model_CLAUDE_3_5_SONNET_V2,
		Target:       target,
		IsSuccessful: isSuccessful,
	}, mock.Anything)

	// Reset the mock
	mockStub.Calls = nil

	// Test with retry_after_seconds
	retryAfterSeconds := int32(1)

	// Set up the mock to return the expected response
	mockStub.On("ReportResponse", mock.Anything, &proto.ReportResponseRequest{
		Model:             proto.Model_CLAUDE_3_5_SONNET_V2,
		Target:            target,
		IsSuccessful:      isSuccessful,
		RetryAfterSeconds: &retryAfterSeconds,
	}, mock.Anything).Return(expectedResponse, nil)

	// Call the method
	response, err = client.ReportResponse(context.Background(), requestCtx, proto.Model_CLAUDE_3_5_SONNET_V2, target, isSuccessful, &retryAfterSeconds)

	// Assert that there was no error
	assert.NoError(t, err)

	// Assert that the response is as expected
	assert.Equal(t, expectedResponse, response)

	// Assert that the stub was called with the correct arguments
	mockStub.AssertCalled(t, "ReportResponse", mock.Anything, &proto.ReportResponseRequest{
		Model:             proto.Model_CLAUDE_3_5_SONNET_V2,
		Target:            target,
		IsSuccessful:      isSuccessful,
		RetryAfterSeconds: &retryAfterSeconds,
	}, mock.Anything)
}
