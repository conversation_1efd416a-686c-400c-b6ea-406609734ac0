load("//tools/bzl:go.bzl", "go_library", "go_test")
load("//tools/bzl:python.bzl", "py_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

go_library(
    name = "client_go",
    srcs = [
        "client.go",
        "mock_client.go",
    ],
    importpath = "github.com/augmentcode/augment/services/third_party_arbiter/client",
    visibility = ["//services:__subpackages__"],
    deps = [
        "//services/lib/request_context:request_context_go",
        "//services/third_party_arbiter:third_party_arbiter_go_proto",
        "@io_opentelemetry_go_contrib_instrumentation_google_golang_org_grpc_otelgrpc//:otelgrpc",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//credentials",
        "@org_golang_google_grpc//metadata",
    ],
)

py_library(
    name = "client_py",
    srcs = [
        "client.py",
        "mock_client.py",
    ],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//base/python/grpc:client_options",
        "//services/lib/request_context:request_context_py",
        "//services/third_party_arbiter:third_party_arbiter_py_proto",
    ],
)

pytest_test(
    name = "client_test",
    srcs = ["client_test.py"],
    deps = [
        ":client_py",
        "//services/lib/request_context:request_context_py",
        "//services/third_party_arbiter:third_party_arbiter_py_proto",
    ],
)

go_test(
    name = "client_go_test",
    srcs = ["client_test.go"],
    embed = [":client_go"],
    deps = [
        "//services/lib/request_context:request_context_go",
        "//services/third_party_arbiter:third_party_arbiter_go_proto",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//mock",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//metadata",
    ],
)
