package client

import (
	"context"
	"fmt"

	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	proto "github.com/augmentcode/augment/services/third_party_arbiter/proto"
	"go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"
	"google.golang.org/grpc/metadata"
)

type ThirdPartyArbiterClient interface {
	// GetTarget returns the targets to use for a third party request.
	// The first target in the slice is the primary target, and the second is the secondary target.
	// If supportedTargets is provided, only those targets will be considered for selection.
	GetTarget(ctx context.Context, requestContext requestcontext.RequestContext, model proto.Model, supportedTargets []proto.ThirdPartyClient) (*proto.GetTargetResponse, error)

	ReportResponse(ctx context.Context, requestContext requestcontext.RequestContext, model proto.Model, target proto.ThirdPartyClient, isSuccessful bool, retryAfterSeconds *int32) (*proto.ReportResponseResponse, error)
}

type thirdPartyArbiterClientImpl struct {
	endpoint string
	stub     proto.ThirdPartyArbiterClient
}

func New(endpoint string, creds credentials.TransportCredentials) (ThirdPartyArbiterClient, error) {
	conn, err := grpc.NewClient(endpoint,
		grpc.WithTransportCredentials(creds),
		grpc.WithStatsHandler(otelgrpc.NewClientHandler()),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to dial third party arbiter service: %w", err)
	}

	return &thirdPartyArbiterClientImpl{
		endpoint: endpoint,
		stub:     proto.NewThirdPartyArbiterClient(conn),
	}, nil
}

func (c *thirdPartyArbiterClientImpl) GetTarget(ctx context.Context, requestContext requestcontext.RequestContext, model proto.Model, supportedTargets []proto.ThirdPartyClient) (*proto.GetTargetResponse, error) {
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())

	req := &proto.GetTargetRequest{
		Model:            model,
		SupportedTargets: supportedTargets,
	}
	return c.stub.GetTarget(ctx, req)
}

func (c *thirdPartyArbiterClientImpl) ReportResponse(ctx context.Context, requestContext requestcontext.RequestContext, model proto.Model, target proto.ThirdPartyClient, isSuccessful bool, retryAfterSeconds *int32) (*proto.ReportResponseResponse, error) {
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())

	req := &proto.ReportResponseRequest{
		Model:        model,
		Target:       target,
		IsSuccessful: isSuccessful,
	}

	if retryAfterSeconds != nil {
		req.RetryAfterSeconds = retryAfterSeconds
	}

	return c.stub.ReportResponse(ctx, req)
}
