load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:go.bzl", "go_grpc_library")
load("//tools/bzl:python.bzl", "py_grpc_library")

proto_library(
    name = "third_party_arbiter_proto",
    srcs = ["third_party_arbiter.proto"],
    visibility = ["//services:__subpackages__"],
)

go_grpc_library(
    name = "third_party_arbiter_go_proto",
    importpath = "github.com/augmentcode/augment/services/third_party_arbiter/proto",
    proto = ":third_party_arbiter_proto",
    visibility = [
        "//services:__subpackages__",
    ],
)

py_grpc_library(
    name = "third_party_arbiter_py_proto",
    protos = [":third_party_arbiter_proto"],
    visibility = [
        "//services:__subpackages__",
    ],
)
