local monitoringLib = import 'deploy/gcp/monitoring-lib.jsonnet';

function(cloud)
  local highErrorRateSpec = {
    displayName: 'Third Party Arbiter High Error Rate',
    conditionPrometheusQueryLanguage: {
      duration: '300s',
      evaluationInterval: '60s',
      labels: { severity: 'warning' },
      query: |||
        sum(rate(grpc_server_handled_total{grpc_service="third_party_arbiter.ThirdPartyArbiter",grpc_code!="OK"}[5m])) /
        sum(rate(grpc_server_handled_total{grpc_service="third_party_arbiter.ThirdPartyArbiter"}[5m])) > 0.05
      |||,
    },
  };

  local highLatencySpec = {
    displayName: 'Third Party Arbiter High Latency',
    conditionPrometheusQueryLanguage: {
      duration: '300s',
      evaluationInterval: '60s',
      labels: { severity: 'warning' },
      query: |||
        histogram_quantile(0.95, sum(rate(grpc_server_handling_seconds_bucket{grpc_service="third_party_arbiter.ThirdPartyArbiter"}[5m])) by (le)) > 1
      |||,
    },
  };

  [
    monitoringLib.alertPolicy(
      cloud,
      highErrorRateSpec,
      'third-party-arbiter-high-error-rate',
      'Third Party Arbiter service is experiencing high error rate (>5%) for more than 5 minutes.'
    ),
    monitoringLib.alertPolicy(
      cloud,
      highLatencySpec,
      'third-party-arbiter-high-latency',
      'Third Party Arbiter service is experiencing high latency (p95 > 1s) for more than 5 minutes.'
    ),
  ]
