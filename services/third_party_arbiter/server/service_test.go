package main

import (
	"context"
	"encoding/json"
	"fmt"
	"testing"
	"time"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/go/clock"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	proto "github.com/augmentcode/augment/services/third_party_arbiter/proto"
	"github.com/stretchr/testify/assert"
)

func TestGetPseudoRandomFromSessionID(t *testing.T) {
	// Test that the same session ID always produces the same random value
	sessionID := "test-session-123"
	value1 := getPseudoRandomFromSessionID(sessionID)
	value2 := getPseudoRandomFromSessionID(sessionID)
	assert.Equal(t, value1, value2, "Same session ID should produce same random value")

	// Test that different session IDs produce different values
	differentSessionID := "test-session-456"
	value3 := getPseudoRandomFromSessionID(differentSessionID)
	assert.NotEqual(t, value1, value3, "Different session IDs should produce different random values")

	// Test that the value is between 0 and 1
	assert.GreaterOrEqual(t, value1, 0.0, "Random value should be >= 0")
	assert.LessOrEqual(t, value1, 1.0, "Random value should be <= 1")
}

// TestCase represents a test case for GetTarget with different configurations
type GetTargetTestCase struct {
	name           string
	sessionID      string
	weights        string
	expectedTarget proto.ThirdPartyClient
}

func TestGetTarget_TargetSelection(t *testing.T) {
	// Define test cases for different targets
	testCases := []GetTargetTestCase{
		{
			name:           "DirectAnthropic",
			sessionID:      "test-session-anthropic-direct!",
			weights:        `{"anthropic_direct": 0.7, "anthropic_vertexai_us_e5": 0.1, "anthropic_vertexai_eu_w1": 0.1, "anthropic_vertexai_as_se1": 0.1}`,
			expectedTarget: proto.ThirdPartyClient_ANTHROPIC_DIRECT,
		},
		{
			name:           "VertexAI_US",
			sessionID:      "test-session-vertexai-us-2",
			weights:        `{"anthropic_direct": 0.1, "anthropic_vertexai_us_e5": 0.7, "anthropic_vertexai_eu_w1": 0.1, "anthropic_vertexai_as_se1": 0.1}`,
			expectedTarget: proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_US_E5,
		},
		{
			name:           "VertexAI_Europe",
			sessionID:      "test-session-vertexai-europe",
			weights:        `{"anthropic_direct": 0.1, "anthropic_vertexai_us_e5": 0.1, "anthropic_vertexai_eu_w1": 0.7, "anthropic_vertexai_as_se1": 0.1}`,
			expectedTarget: proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_EU_W1,
		},
		{
			name:           "VertexAI_Asia",
			sessionID:      "test-session-asia",
			weights:        `{"anthropic_direct": 0.1, "anthropic_vertexai_us_e5": 0.1, "anthropic_vertexai_eu_w1": 0.1, "anthropic_vertexai_as_se1": 0.7}`,
			expectedTarget: proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_AS_SE1,
		},
	}

	// Run each test case
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			featureFlagHandler := featureflags.NewLocalFeatureFlagHandler()

			// Parse weights from JSON string
			var weightsForModel map[string]float64
			err := json.Unmarshal([]byte(tc.weights), &weightsForModel)
			assert.NoError(t, err, "Failed to parse weights JSON")

			// Create a map of model -> weights
			weights := map[string]map[string]float64{
				"claude_3_5_sonnet_v2": weightsForModel,
				"claude_3_7_sonnet":    weightsForModel,
				"claude_4_0_sonnet":    weightsForModel,
				"claude_4_0_opus":      weightsForModel,
			}

			service := NewThirdPartyArbiterService(featureFlagHandler, weights)

			// Set up feature flags for history management (common to all test cases)
			featureFlagHandler.Set("third_party_arbiter_load_balance_vacate_history_seconds", 300)
			featureFlagHandler.Set("third_party_arbiter_load_balance_min_records", 10)

			// Create a context with the test case's session ID
			ctx := context.Background()
			reqCtx := &requestcontext.RequestContext{
				RequestSessionId: requestcontext.RequestSessionId(tc.sessionID),
			}
			ctx = requestcontext.NewIncomingContext(ctx, reqCtx)

			req := &proto.GetTargetRequest{
				Model: proto.Model_CLAUDE_3_5_SONNET_V2,
			}
			resp, err := service.GetTarget(ctx, req)

			assert.NoError(t, err)
			// Verify that the target is valid
			assert.Contains(t, []proto.ThirdPartyClient{
				proto.ThirdPartyClient_ANTHROPIC_DIRECT,
				proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_US_E5,
				proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_EU_W1,
				proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_AS_SE1,
			}, resp.Targets[0])
			// Check that we have secondary and tertiary targets too
			assert.Equal(t, 3, len(resp.Targets))
			assert.Equal(t, int32(0), resp.DelayMs)
			// Check that the expected target is selected
			assert.Equal(t, tc.expectedTarget, resp.Targets[0],
				"Expected target %s for session ID %s with weights %s",
				tc.expectedTarget.String(), tc.sessionID, tc.weights)
		})
	}
}

func TestGetTarget_NoSessionID(t *testing.T) {
	featureFlagHandler := featureflags.NewLocalFeatureFlagHandler()

	// Set up balanced weights
	weightsForModel := map[string]float64{
		"anthropic_direct":          0.25,
		"anthropic_vertexai_us_e5":  0.25,
		"anthropic_vertexai_eu_w1":  0.25,
		"anthropic_vertexai_as_se1": 0.25,
	}

	// Create a map of model -> weights
	weights := map[string]map[string]float64{
		"claude_3_5_sonnet_v2": weightsForModel,
		"claude_3_7_sonnet":    weightsForModel,
		"claude_4_0_sonnet":    weightsForModel,
		"claude_4_0_opus":      weightsForModel,
	}

	service := NewThirdPartyArbiterService(featureFlagHandler, weights)

	// Set up feature flags for history management
	featureFlagHandler.Set("third_party_arbiter_load_balance_vacate_history_seconds", 300)
	featureFlagHandler.Set("third_party_arbiter_load_balance_min_records", 10)

	// Create a context without a session ID
	ctx := context.Background()

	req := &proto.GetTargetRequest{
		Model: proto.Model_CLAUDE_3_5_SONNET_V2,
	}
	resp, err := service.GetTarget(ctx, req)

	assert.NoError(t, err)
	// We can't predict the exact result since it's random, but we can check it's valid
	assert.Contains(t, []proto.ThirdPartyClient{
		proto.ThirdPartyClient_ANTHROPIC_DIRECT,
		proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_US_E5,
		proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_EU_W1,
		proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_AS_SE1,
	}, resp.Targets[0])
	// Check that we have secondary and tertiary targets too
	assert.Equal(t, 3, len(resp.Targets))
	assert.Equal(t, int32(0), resp.DelayMs)
}

func TestGetTarget_RateNormalization(t *testing.T) {
	featureFlagHandler := featureflags.NewLocalFeatureFlagHandler()

	// Create weights map that doesn't sum to 1.0
	weightsForModel := map[string]float64{
		"anthropic_direct":          30.0,
		"anthropic_vertexai_us_e5":  10.0,
		"anthropic_vertexai_eu_w1":  60.0,
		"anthropic_vertexai_as_se1": 60.0,
	}

	// Create a map of model -> weights
	weights := map[string]map[string]float64{
		"claude_3_5_sonnet_v2": weightsForModel,
		"claude_3_7_sonnet":    weightsForModel,
		"claude_4_0_sonnet":    weightsForModel,
		"claude_4_0_opus":      weightsForModel,
	}

	service := NewThirdPartyArbiterService(featureFlagHandler, weights)

	// Set up feature flags for history management
	featureFlagHandler.Set("third_party_arbiter_load_balance_vacate_history_seconds", 300)
	featureFlagHandler.Set("third_party_arbiter_load_balance_min_records", 10)

	// Create a context with a session ID
	ctx := context.Background()
	reqCtx := &requestcontext.RequestContext{
		RequestSessionId: requestcontext.RequestSessionId("test-session-normalization"),
	}
	ctx = requestcontext.NewIncomingContext(ctx, reqCtx)

	req := &proto.GetTargetRequest{
		Model: proto.Model_CLAUDE_3_5_SONNET_V2,
	}
	resp, err := service.GetTarget(ctx, req)

	assert.NoError(t, err)
	// The weights should be normalized, so we should get a valid response
	assert.Contains(t, []proto.ThirdPartyClient{
		proto.ThirdPartyClient_ANTHROPIC_DIRECT,
		proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_US_E5,
		proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_EU_W1,
		proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_AS_SE1,
	}, resp.Targets[0])
	// Check that we have secondary and tertiary targets too
	assert.Equal(t, 3, len(resp.Targets))
	assert.Equal(t, int32(0), resp.DelayMs)
}

func TestReportResponse(t *testing.T) {
	featureFlagHandler := featureflags.NewLocalFeatureFlagHandler()
	weightsForModel := map[string]float64{
		"anthropic_direct":          22.0,
		"anthropic_vertexai_us_e5":  20.0,
		"anthropic_vertexai_eu_w1":  20.0,
		"anthropic_vertexai_as_se1": 38.0,
	}

	// Create a map of model -> weights
	weights := map[string]map[string]float64{
		"claude_3_5_sonnet_v2": weightsForModel,
		"claude_3_7_sonnet":    weightsForModel,
		"claude_4_0_sonnet":    weightsForModel,
		"claude_4_0_opus":      weightsForModel,
	}

	service := NewThirdPartyArbiterService(featureFlagHandler, weights)
	ctx := context.Background()

	// Set up feature flags for history management
	featureFlagHandler.Set("third_party_arbiter_load_balance_vacate_history_seconds", 300)
	featureFlagHandler.Set("third_party_arbiter_load_balance_bucket_size_seconds", 10)

	// Define the model to use for testing
	model := proto.Model_CLAUDE_3_5_SONNET_V2

	// Test successful response
	successReq := &proto.ReportResponseRequest{
		IsSuccessful: true,
		Target:       proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_US_E5,
		Model:        model,
	}
	_, err := service.ReportResponse(ctx, successReq)
	assert.NoError(t, err)

	// Test rate-limited response
	retryAfterSeconds := int32(1)
	rateLimitReq := &proto.ReportResponseRequest{
		IsSuccessful:      false,
		Target:            proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_US_E5,
		RetryAfterSeconds: &retryAfterSeconds, // Add retry_after_seconds for 429 responses
		Model:             model,
	}
	_, err = service.ReportResponse(ctx, rateLimitReq)
	assert.NoError(t, err)

	// Test failed response
	serverErrorReq := &proto.ReportResponseRequest{
		IsSuccessful: false,
		Target:       proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_EU_W1,
		Model:        model,
	}
	_, err = service.ReportResponse(ctx, serverErrorReq)
	assert.NoError(t, err)

	// Test multiple failures
	for i := 0; i < 4; i++ {
		req := &proto.ReportResponseRequest{
			IsSuccessful: false,
			Target:       proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_AS_SE1,
			Model:        model,
		}
		_, err = service.ReportResponse(ctx, req)
		assert.NoError(t, err)
	}

	// Get record stats to verify the records were stored correctly

	// First, check the US target key
	key1 := proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_US_E5

	// Should have 2 records (1 success and 1 failure)
	usStats := service.GetRecordStats(model, key1)
	assert.Equal(t, 2, usStats.TotalCount, "Should have 2 records for ANTHROPIC_VERTEXAI_US_E5")
	assert.Equal(t, 1, usStats.SuccessCount, "Should have 1 success for ANTHROPIC_VERTEXAI_US_E5")
	assert.Equal(t, 1, usStats.FailureCount, "Should have 1 failure for ANTHROPIC_VERTEXAI_US_E5")

	// Check the EU target key
	key2 := proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_EU_W1

	// Should have 1 failed record
	euStats := service.GetRecordStats(model, key2)
	assert.Equal(t, 1, euStats.TotalCount, "Should have 1 record for ANTHROPIC_VERTEXAI_EU_W1")
	assert.Equal(t, 0, euStats.SuccessCount, "Should have 0 successes for ANTHROPIC_VERTEXAI_EU_W1")
	assert.Equal(t, 1, euStats.FailureCount, "Should have 1 failure for ANTHROPIC_VERTEXAI_EU_W1")

	// Check the Asia target key for errors
	key3 := proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_AS_SE1

	// Should have 4 failures
	asiaStats := service.GetRecordStats(model, key3)
	assert.Equal(t, 4, asiaStats.TotalCount, "Should have 4 records for ANTHROPIC_VERTEXAI_AS_SE1")
	assert.Equal(t, 0, asiaStats.SuccessCount, "Should have 0 successes for ANTHROPIC_VERTEXAI_AS_SE1")
	assert.Equal(t, 4, asiaStats.FailureCount, "Should have 4 failures for ANTHROPIC_VERTEXAI_AS_SE1")

	// The total number of records should be 7 (2 for vertexai/us-east5, 1 for vertexai/europe-west1, and 4 for vertexai/asia-southeast1)
	totalRecords := usStats.TotalCount + euStats.TotalCount + asiaStats.TotalCount
	assert.Equal(t, 7, totalRecords, "Should have 7 total records")
}

func TestGetTarget_AdaptiveLoadBalancing(t *testing.T) {
	featureFlagHandler := featureflags.NewLocalFeatureFlagHandler()

	// Set up weights that favor Asia region
	weightsForModel := map[string]float64{
		"anthropic_direct":          0.22,
		"anthropic_vertexai_us_e5":  0.19,
		"anthropic_vertexai_eu_w1":  0.23,
		"anthropic_vertexai_as_se1": 0.36,
	}

	// Create a map of model -> weights
	weights := map[string]map[string]float64{
		"claude_3_5_sonnet_v2": weightsForModel,
		"claude_3_7_sonnet":    weightsForModel,
		"claude_4_0_sonnet":    weightsForModel,
		"claude_4_0_opus":      weightsForModel,
	}

	service := NewThirdPartyArbiterService(featureFlagHandler, weights)

	// Set up feature flags for history management
	featureFlagHandler.Set("third_party_arbiter_load_balance_vacate_history_seconds", 300)
	featureFlagHandler.Set("third_party_arbiter_load_balance_bucket_size_seconds", 10)
	featureFlagHandler.Set("third_party_arbiter_load_balance_min_records", 10)

	// Create a context with a session ID that will select Asia initially
	ctx := context.Background()
	reqCtx := &requestcontext.RequestContext{
		RequestSessionId: requestcontext.RequestSessionId("test-session-asia"),
	}
	ctx = requestcontext.NewIncomingContext(ctx, reqCtx)

	// Define the model to use for testing
	model := proto.Model_CLAUDE_3_5_SONNET_V2

	// First, make a request without any error history
	req := &proto.GetTargetRequest{
		Model: model,
	}
	resp, err := service.GetTarget(ctx, req)
	assert.NoError(t, err)
	// The test is sensitive to the random value used for selection
	// We'll check that the primary target is one of the valid targets
	assert.Contains(t, []proto.ThirdPartyClient{
		proto.ThirdPartyClient_ANTHROPIC_DIRECT,
		proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_US_E5,
		proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_EU_W1,
		proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_AS_SE1,
	}, resp.Targets[0])
	// Check that we have secondary and tertiary targets too
	assert.Equal(t, 3, len(resp.Targets))
	assert.Equal(t, int32(0), resp.DelayMs)
	primaryTarget := resp.Targets[0]

	// Now add enough error history for Asia to trigger adaptive load balancing
	// We need at least minRecords (10) to calculate error rate
	for i := 0; i < 15; i++ {
		reportReq := &proto.ReportResponseRequest{
			IsSuccessful: false, // Error
			Target:       proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_AS_SE1,
			Model:        model,
		}
		_, err = service.ReportResponse(ctx, reportReq)
		assert.NoError(t, err)
	}

	// Make another request - it should potentially redirect due to high error rate
	resp, err = service.GetTarget(ctx, req)
	assert.NoError(t, err)

	// The response might be redirected to another region/service
	// We can't predict exactly where due to the hash, but it should be valid
	assert.Contains(t, []proto.ThirdPartyClient{
		proto.ThirdPartyClient_ANTHROPIC_DIRECT,
		proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_US_E5,
		proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_EU_W1,
		proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_AS_SE1,
	}, resp.Targets[0])
	// Check that we have secondary and tertiary targets too
	assert.Equal(t, 3, len(resp.Targets))
	assert.Equal(t, int32(0), resp.DelayMs)
	assert.NotEqual(t, primaryTarget, resp.Targets[0])
}

func TestCalculateErrorRate(t *testing.T) {
	featureFlagHandler := featureflags.NewLocalFeatureFlagHandler()

	// Set up weights
	weightsForModel := map[string]float64{
		"anthropic_direct":          22.0,
		"anthropic_vertexai_us_e5":  20.0,
		"anthropic_vertexai_eu_w1":  20.0,
		"anthropic_vertexai_as_se1": 38.0,
	}

	// Create a map of model -> weights
	weights := map[string]map[string]float64{
		"claude_3_5_sonnet_v2": weightsForModel,
		"claude_3_7_sonnet":    weightsForModel,
		"claude_4_0_sonnet":    weightsForModel,
		"claude_4_0_opus":      weightsForModel,
	}

	service := NewThirdPartyArbiterService(featureFlagHandler, weights)
	ctx := context.Background()

	// Define the model to use for testing
	model := proto.Model_CLAUDE_3_5_SONNET_V2

	// Set up feature flags
	featureFlagHandler.Set("third_party_arbiter_load_balance_vacate_history_seconds", 300)
	featureFlagHandler.Set("third_party_arbiter_load_balance_bucket_size_seconds", 10)
	featureFlagHandler.Set("third_party_arbiter_load_balance_min_records", 10)

	// Test with no history
	errorRate := service.calculateErrorRate(ctx, model, proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_US_E5)
	assert.Equal(t, 0.0, errorRate)

	// Test with insufficient history (less than minRecords)
	for i := 0; i < 5; i++ {
		req := &proto.ReportResponseRequest{
			IsSuccessful: false,
			Target:       proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_US_E5,
			Model:        model,
		}
		_, err := service.ReportResponse(ctx, req)
		assert.NoError(t, err)
	}

	// Should still return 0.0 because we have less than minRecords
	errorRate = service.calculateErrorRate(ctx, model, proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_US_E5)
	assert.Equal(t, 0.0, errorRate)

	// Add more responses to reach minRecords
	for i := 0; i < 5; i++ {
		req := &proto.ReportResponseRequest{
			IsSuccessful: true,
			Target:       proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_US_E5,
			Model:        model,
		}
		_, err := service.ReportResponse(ctx, req)
		assert.NoError(t, err)
	}

	// Now we have 10 records (5 errors + 5 successes)
	// Calculate error rate (should be 5/10 = 0.5)
	errorRate = service.calculateErrorRate(ctx, model, proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_US_E5)
	assert.Equal(t, 0.5, errorRate)

	// Add more successful responses
	for i := 0; i < 10; i++ {
		req := &proto.ReportResponseRequest{
			IsSuccessful: true,
			Target:       proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_US_E5,
			Model:        model,
		}
		_, err := service.ReportResponse(ctx, req)
		assert.NoError(t, err)
	}

	// Now we have 20 records (5 errors + 15 successes)
	// Calculate error rate (should be 5/20 = 0.25)
	errorRate = service.calculateErrorRate(ctx, model, proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_US_E5)
	assert.Equal(t, 0.25, errorRate)
}

func TestAllEnumValuesAreCovered(t *testing.T) {
	// This test verifies that all values of the ThirdPartyClient enum are covered in the code

	// Test thirdPartyClientToString function
	// We should have a case for each enum value
	for _, enumValue := range []proto.ThirdPartyClient{
		proto.ThirdPartyClient_ANTHROPIC_DIRECT,
		proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_US_E5,
		proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_EU_W1,
		proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_AS_SE1,
	} {
		// Call the function for each enum value
		result := thirdPartyClientToString(enumValue)

		// Verify that we get a non-empty string for valid enum values
		assert.NotEqual(t, "", result, "Enum value should map to a non-empty string")
	}

	// Test that UNKNOWN returns an empty string (error case)
	result := thirdPartyClientToString(proto.ThirdPartyClient_UNKNOWN)
	assert.Equal(t, "", result, "UNKNOWN enum value should return empty string (error case)")

	// Test stringToThirdPartyClient function
	// We should have a case for each enum value's string representation
	stringValues := []string{
		"anthropic_direct",
		"anthropic_vertexai_us_e5",
		"anthropic_vertexai_eu_w1",
		"anthropic_vertexai_as_se1",
	}

	expectedEnumValues := []proto.ThirdPartyClient{
		proto.ThirdPartyClient_ANTHROPIC_DIRECT,
		proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_US_E5,
		proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_EU_W1,
		proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_AS_SE1,
	}

	for i, stringValue := range stringValues {
		// Call the function for each string value
		result := stringToThirdPartyClient(stringValue)

		// Verify that we get the expected enum value
		assert.Equal(t, expectedEnumValues[i], result,
			"String value %s should map to enum value %s",
			stringValue, expectedEnumValues[i].String())
	}

	// Test that unknown string returns UNKNOWN enum value
	unknownResult := stringToThirdPartyClient("some_unknown_value")
	assert.Equal(t, proto.ThirdPartyClient_UNKNOWN, unknownResult,
		"Unknown string value should map to UNKNOWN enum value")
}

func TestHistoryCleanup(t *testing.T) {
	// Create a mock clock for testing
	mockClock := clock.NewMockClock(time.Now())
	featureFlagHandler := featureflags.NewLocalFeatureFlagHandler()

	// Set up weights
	weightsForModel := map[string]float64{
		"anthropic_direct":          22.0,
		"anthropic_vertexai_us_e5":  20.0,
		"anthropic_vertexai_eu_w1":  20.0,
		"anthropic_vertexai_as_se1": 38.0,
	}

	// Create a map of model -> weights
	weights := map[string]map[string]float64{
		"claude_3_5_sonnet_v2": weightsForModel,
		"claude_3_7_sonnet":    weightsForModel,
		"claude_4_0_sonnet":    weightsForModel,
		"claude_4_0_opus":      weightsForModel,
	}

	service := NewThirdPartyArbiterServiceWithClock(featureFlagHandler, mockClock, weights)
	ctx := context.Background()

	// Define the model to use for testing
	model := proto.Model_CLAUDE_3_5_SONNET_V2

	// Set up feature flags for bucket-based history
	featureFlagHandler.Set("third_party_arbiter_load_balance_vacate_history_seconds", 30)
	featureFlagHandler.Set("third_party_arbiter_load_balance_bucket_size_seconds", 10)

	key := proto.ThirdPartyClient_ANTHROPIC_DIRECT

	// Add records to the current bucket
	for i := 0; i < 5; i++ {
		req := &proto.ReportResponseRequest{
			IsSuccessful: true,
			Target:       proto.ThirdPartyClient_ANTHROPIC_DIRECT,
			Model:        model,
		}
		_, err := service.ReportResponse(ctx, req)
		assert.NoError(t, err)
	}

	// Check that we have 5 success records
	keyStats := service.GetRecordStats(model, key)
	assert.Equal(t, 5, keyStats.TotalCount, "Should have 5 total records")
	assert.Equal(t, 5, keyStats.SuccessCount, "Should have 5 successes")
	assert.Equal(t, 0, keyStats.FailureCount, "Should have no failures")

	// Advance the clock, enough to make sure we're in a new bucket
	mockClock.(*clock.MockClock).MockNow = mockClock.Now().Add(11 * time.Second)

	// Add a failure record to the new bucket
	req := &proto.ReportResponseRequest{
		IsSuccessful: false,
		Target:       proto.ThirdPartyClient_ANTHROPIC_DIRECT,
		Model:        model,
	}
	_, err := service.ReportResponse(ctx, req)
	assert.NoError(t, err)

	// Advance the clock, not enough to trigger any cleanup
	mockClock.(*clock.MockClock).MockNow = mockClock.Now().Add(10 * time.Second)

	// Check that we now have 6 records (5 successes + 1 failure)
	keyStats = service.GetRecordStats(model, key)
	assert.Equal(t, 6, keyStats.TotalCount, "Should have 6 total records")
	assert.Equal(t, 5, keyStats.SuccessCount, "Should have 5 successes")
	assert.Equal(t, 1, keyStats.FailureCount, "Should have 1 failure")

	// Advance the clock to trigger cleanup of oldest records
	mockClock.(*clock.MockClock).MockNow = mockClock.Now().Add(10 * time.Second)

	// Check that old records were cleaned up
	keyStats = service.GetRecordStats(model, key)
	// With the new bucketed approach, we'll have all records in the current bucket
	assert.Equal(t, 1, keyStats.TotalCount, "Should have only one record 30s+ after first report")
	assert.Equal(t, 0, keyStats.SuccessCount, "Should have 6 successes after cleanup")
	assert.Equal(t, 1, keyStats.FailureCount, "Should have 1 failure after cleanup")

	// Advance the clock past the vacate history seconds to finish cleanp
	mockClock.(*clock.MockClock).MockNow = mockClock.Now().Add(10 * time.Second)

	// Check that all records were cleaned up
	keyStats = service.GetRecordStats(model, key)
	// With the new bucketed approach, we'll have all records in the current bucket
	assert.Equal(t, 0, keyStats.TotalCount, "Should have only one record 30s after first report")
	assert.Equal(t, 0, keyStats.SuccessCount, "Should have 6 successes after cleanup")
	assert.Equal(t, 0, keyStats.FailureCount, "Should have 1 failure after cleanup")
}

func TestHistoryCleanupSkipBuckets(t *testing.T) {
	featureFlagHandler := featureflags.NewLocalFeatureFlagHandler()

	// Create a mock clock with the current time
	mockClock := clock.NewMockClock(time.Now())

	// Set up weights
	weightsForModel := map[string]float64{
		"anthropic_direct":          22.0,
		"anthropic_vertexai_us_e5":  20.0,
		"anthropic_vertexai_eu_w1":  20.0,
		"anthropic_vertexai_as_se1": 38.0,
	}

	// Create a map of model -> weights
	weights := map[string]map[string]float64{
		"claude_3_5_sonnet_v2": weightsForModel,
		"claude_3_7_sonnet":    weightsForModel,
		"claude_4_0_sonnet":    weightsForModel,
		"claude_4_0_opus":      weightsForModel,
	}

	service := NewThirdPartyArbiterServiceWithClock(featureFlagHandler, mockClock, weights)

	// Define the model to use for testing
	model := proto.Model_CLAUDE_3_5_SONNET_V2

	// Set up feature flags
	featureFlagHandler.Set("third_party_arbiter_load_balance_vacate_history_seconds", 300)
	featureFlagHandler.Set("third_party_arbiter_load_balance_bucket_size_seconds", 10)
	featureFlagHandler.Set("third_party_arbiter_load_balance_min_records", 10)

	key := proto.ThirdPartyClient_ANTHROPIC_DIRECT

	ctx := context.Background()

	// Add "old" records first (these will be ignored in error rate calculation)
	for i := 0; i < 3; i++ {
		isSuccess := false
		if i != 1 { // Make only the second record a failure
			isSuccess = true
		}

		req := &proto.ReportResponseRequest{
			IsSuccessful: isSuccess,
			Target:       proto.ThirdPartyClient_ANTHROPIC_DIRECT,
			Model:        model,
		}
		_, err := service.ReportResponse(ctx, req)
		assert.NoError(t, err)
	}

	// Calculate error rate - this should only consider recent records
	errorRate := service.calculateErrorRate(ctx, model, proto.ThirdPartyClient_ANTHROPIC_DIRECT)

	// Under minimum of 10 records, no error rate yet
	assert.Equal(t, 0.0, errorRate)

	// Check that we have all records (old + recent)
	keyStats := service.GetRecordStats(model, key)

	// GetRecordStats counts all records in the current buckets
	assert.Equal(t, 3, keyStats.TotalCount, "Should have 3 records")
	assert.Equal(t, 2, keyStats.SuccessCount, "Should have 2 successes")
	assert.Equal(t, 1, keyStats.FailureCount, "Should have 1 failures")

	// Advance the clock by 30 seconds and add more records
	mockClock.(*clock.MockClock).MockNow = mockClock.Now().Add(time.Duration(30) * time.Second)
	// Add recent records
	for i := 0; i < 10; i++ {
		var isSuccess bool
		if i < 2 { // Make the first two records failures
			isSuccess = false
		} else {
			isSuccess = true
		}

		req := &proto.ReportResponseRequest{
			IsSuccessful: isSuccess,
			Target:       proto.ThirdPartyClient_ANTHROPIC_DIRECT,
			Model:        model,
		}
		_, err := service.ReportResponse(ctx, req)
		assert.NoError(t, err)
	}

	// Calculate error rate - this should only consider recent records
	errorRate = service.calculateErrorRate(ctx, model, proto.ThirdPartyClient_ANTHROPIC_DIRECT)

	// With the new bucketed approach, we'll have all records in the current bucket
	// Error rate will be 3/13 ~= 0.23077
	assert.InDelta(t, 0.23, errorRate, 0.01)

	// Check that we have all records (old + recent)
	keyStats = service.GetRecordStats(model, key)

	// GetRecordStats counts all records in the current buckets
	assert.Equal(t, 13, keyStats.TotalCount, "Should have 13 records")
	assert.Equal(t, 10, keyStats.SuccessCount, "Should have 10 successes")
	assert.Equal(t, 3, keyStats.FailureCount, "Should have 3 failures")

	// Advance the clock enough to expire the first 3 records
	mockClock.(*clock.MockClock).MockNow = mockClock.Now().Add(time.Duration(286) * time.Second)

	// Calculate error rate - this should only consider recent records
	errorRate = service.calculateErrorRate(ctx, model, proto.ThirdPartyClient_ANTHROPIC_DIRECT)

	// With the new bucketed approach, we'll have all records in the current bucket
	// Error rate will be 2/10 = 0.2
	assert.InDelta(t, 0.2, errorRate, 0.01)

	// Check that we have all records (old + recent)
	keyStats = service.GetRecordStats(model, key)
	assert.Equal(t, 10, keyStats.TotalCount, "Should have 10 records")
	assert.Equal(t, 8, keyStats.SuccessCount, "Should have 8 successes")
	assert.Equal(t, 2, keyStats.FailureCount, "Should have 2 failures")
}

func TestRetryAfterSeconds(t *testing.T) {
	jitterMaxMs := 100
	// Create a mock clock for testing
	mockClock := clock.NewMockClock(time.Now())
	featureFlagHandler := featureflags.NewLocalFeatureFlagHandler()
	featureFlagHandler.Set("third_party_arbiter_jitter_max_ms", jitterMaxMs)

	// Set up balanced weights
	weightsForModel := map[string]float64{
		"anthropic_direct":          0.25,
		"anthropic_vertexai_us_e5":  0.25,
		"anthropic_vertexai_eu_w1":  0.25,
		"anthropic_vertexai_as_se1": 0.25,
	}

	// Create a map of model -> weights
	weights := map[string]map[string]float64{
		"claude_3_5_sonnet_v2": weightsForModel,
		"claude_3_7_sonnet":    weightsForModel,
		"claude_4_0_sonnet":    weightsForModel,
		"claude_4_0_opus":      weightsForModel,
	}

	service := NewThirdPartyArbiterServiceWithClock(featureFlagHandler, mockClock, weights)

	ctx := context.Background()
	reqCtx := &requestcontext.RequestContext{
		RequestSessionId: requestcontext.RequestSessionId("test-session-retry"),
	}
	ctx = requestcontext.NewIncomingContext(ctx, reqCtx)

	// Define the model to use for testing
	model := proto.Model_CLAUDE_3_5_SONNET_V2

	// Step 1: Report a rate-limited response for US target
	retryAfterSeconds := int32(1) // 1 second
	rateLimitReq := &proto.ReportResponseRequest{
		IsSuccessful:      false,
		Target:            proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_US_E5,
		RetryAfterSeconds: &retryAfterSeconds,
		Model:             model,
	}
	_, err := service.ReportResponse(ctx, rateLimitReq)
	assert.NoError(t, err)

	// Step 2: Get target - should return a delay for US target
	req := &proto.GetTargetRequest{
		Model: model,
	}
	resp, err := service.GetTarget(ctx, req)
	assert.NoError(t, err)

	// If the primary target is US, we should have a delay
	if resp.Targets[0] == proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_US_E5 {
		assert.Greater(t, resp.DelayMs, int32(0), "Should have a delay for rate-limited target")
		assert.LessOrEqual(t, resp.DelayMs, retryAfterSeconds*1000, "Delay should not exceed retry_after_seconds*1000")
	}

	// Step 3: Report a rate-limited response for EU target with a longer delay
	longerRetryAfterSeconds := int32(2) // 2 seconds
	euRateLimitReq := &proto.ReportResponseRequest{
		IsSuccessful:      false,
		Target:            proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_EU_W1,
		RetryAfterSeconds: &longerRetryAfterSeconds,
		Model:             model,
	}
	_, err = service.ReportResponse(ctx, euRateLimitReq)
	assert.NoError(t, err)

	// Step 4: Advance the clock by 500ms (half of the US delay)
	mockClock.(*clock.MockClock).MockNow = mockClock.Now().Add(500 * time.Millisecond)

	// Step 5: Get target again - should still return a delay for US target if it's selected
	resp, err = service.GetTarget(ctx, req)
	assert.NoError(t, err)

	// If the primary target is US, we should have a reduced delay
	if resp.Targets[0] == proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_US_E5 {
		assert.Greater(t, resp.DelayMs, int32(0), "Should still have a delay for rate-limited target")
		assert.Less(t, resp.DelayMs, retryAfterSeconds*1000, "Delay should be reduced after time passes")
		assert.LessOrEqual(t, resp.DelayMs, int32(500), "Delay should be around 500ms or less")
	}

	// If the primary target is EU, we should have a longer delay
	if resp.Targets[0] == proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_EU_W1 {
		assert.Greater(t, resp.DelayMs, int32(1000), "Should have a longer delay for EU target")
		assert.LessOrEqual(t, resp.DelayMs, longerRetryAfterSeconds*1000+int32(jitterMaxMs), "Delay should not exceed retry_after_seconds*1000 + jitter")
	}

	previousDelay := resp.DelayMs

	// Step 6: Advance the clock past the US delay but before the EU delay, and more than jitter
	mockClock.(*clock.MockClock).MockNow = mockClock.Now().Add(500 * time.Millisecond)

	// Step 7: Get target again - US delay should be cleared, EU should still have delay
	resp, err = service.GetTarget(ctx, req)
	assert.NoError(t, err)

	// If the primary target is US, we should have no delay (timestamp cleared)
	if resp.Targets[0] == proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_US_E5 {
		assert.Equal(t, int32(0), resp.DelayMs, "Delay should be cleared after timestamp expires")
	}

	// If the primary target is EU, we should still have a delay, and it should be shorter than before
	if resp.Targets[0] == proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_EU_W1 {
		assert.Greater(t, resp.DelayMs, int32(0), "Should still have a delay for EU target")
		assert.Less(t, resp.DelayMs, previousDelay+int32(jitterMaxMs), "Delay should be reduced after time passes (accounting for jitter)")
	}

	// Step 8: Report a new rate-limited response for US with a shorter delay
	shorterRetryAfterSeconds := int32(0) // 0.5 seconds
	usRateLimitReq := &proto.ReportResponseRequest{
		IsSuccessful:      false,
		Target:            proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_US_E5,
		RetryAfterSeconds: &shorterRetryAfterSeconds,
		Model:             model,
	}
	_, err = service.ReportResponse(ctx, usRateLimitReq)
	assert.NoError(t, err)

	// Step 9: Get target again - US should have a new delay
	resp, err = service.GetTarget(ctx, req)
	assert.NoError(t, err)

	// If the primary target is US, we should have a new delay
	if resp.Targets[0] == proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_US_E5 {
		assert.Greater(t, resp.DelayMs, int32(0), "Should have a new delay for US target")
		assert.LessOrEqual(t, resp.DelayMs, shorterRetryAfterSeconds*1000, "Delay should not exceed new retry_after_seconds*1000")
	}

	// Step 10: Report a new rate-limited response for US with a longer delay
	longerUsRetryAfterSeconds := int32(3) // 3 seconds
	usLongerRateLimitReq := &proto.ReportResponseRequest{
		IsSuccessful:      false,
		Target:            proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_US_E5,
		RetryAfterSeconds: &longerUsRetryAfterSeconds,
		Model:             model,
	}
	_, err = service.ReportResponse(ctx, usLongerRateLimitReq)
	assert.NoError(t, err)

	// Step 11: Get target again - US should have the longer delay (later timestamp)
	resp, err = service.GetTarget(ctx, req)
	assert.NoError(t, err)

	// If the primary target is US, we should have the longer delay
	if resp.Targets[0] == proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_US_E5 {
		assert.Greater(t, resp.DelayMs, shorterRetryAfterSeconds*1000, "Should use the longer delay for US target")
		assert.LessOrEqual(t, resp.DelayMs, longerUsRetryAfterSeconds*1000, "Delay should not exceed longer retry_after_seconds*1000")
	}

	// Step 12: Advance the clock past all delays
	mockClock.(*clock.MockClock).MockNow = mockClock.Now().Add(5 * time.Second)

	// Step 13: Get target again - all delays should be cleared
	resp, err = service.GetTarget(ctx, req)
	assert.NoError(t, err)

	// All targets should have no delay
	assert.Equal(t, int32(0), resp.DelayMs, "All delays should be cleared after timestamps expire")
}

func TestGetTarget_WithSupportedTargets(t *testing.T) {
	featureFlagHandler := featureflags.NewLocalFeatureFlagHandler()

	// Set up balanced weights
	weightsForModel := map[string]float64{
		"anthropic_direct":          0.25,
		"anthropic_vertexai_us_e5":  0.25,
		"anthropic_vertexai_eu_w1":  0.25,
		"anthropic_vertexai_as_se1": 0.25,
	}

	// Create a map of model -> weights
	weights := map[string]map[string]float64{
		"claude_3_5_sonnet_v2": weightsForModel,
		"claude_3_7_sonnet":    weightsForModel,
		"claude_4_0_sonnet":    weightsForModel,
		"claude_4_0_opus":      weightsForModel,
	}

	service := NewThirdPartyArbiterService(featureFlagHandler, weights)

	// Set up feature flags for history management
	featureFlagHandler.Set("third_party_arbiter_load_balance_vacate_history_seconds", 300)
	featureFlagHandler.Set("third_party_arbiter_load_balance_min_records", 10)

	// Create a context with a request context
	ctx := context.Background()
	reqCtx := &requestcontext.RequestContext{
		RequestSessionId: requestcontext.RequestSessionId("test-session-supported-targets"),
	}
	ctx = requestcontext.NewIncomingContext(ctx, reqCtx)

	// Define the model to use for testing
	model := proto.Model_CLAUDE_3_5_SONNET_V2

	// Test with supported targets
	req := &proto.GetTargetRequest{
		SupportedTargets: []proto.ThirdPartyClient{
			proto.ThirdPartyClient_ANTHROPIC_DIRECT,
			proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_US_E5,
		},
		Model: model,
	}
	resp, err := service.GetTarget(ctx, req)

	assert.NoError(t, err)
	// We should only get targets from the supported list
	assert.Contains(t, []proto.ThirdPartyClient{
		proto.ThirdPartyClient_ANTHROPIC_DIRECT,
		proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_US_E5,
	}, resp.Targets[0])
	// Check that we have exactly two targets
	assert.Equal(t, 2, len(resp.Targets))
	// All targets should be from the supported list
	for _, target := range resp.Targets {
		assert.Contains(t, []proto.ThirdPartyClient{
			proto.ThirdPartyClient_ANTHROPIC_DIRECT,
			proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_US_E5,
		}, target)
	}
	// Targets should be different
	assert.NotEqual(t, resp.Targets[0], resp.Targets[1])
	assert.Equal(t, int32(0), resp.DelayMs)
}

func TestGetTarget_WithSingleSupportedTarget(t *testing.T) {
	featureFlagHandler := featureflags.NewLocalFeatureFlagHandler()

	// Set up balanced weights
	weightsForModel := map[string]float64{
		"anthropic_direct":          0.25,
		"anthropic_vertexai_us_e5":  0.25,
		"anthropic_vertexai_eu_w1":  0.25,
		"anthropic_vertexai_as_se1": 0.25,
	}

	// Create a map of model -> weights
	weights := map[string]map[string]float64{
		"claude_3_5_sonnet_v2": weightsForModel,
		"claude_3_7_sonnet":    weightsForModel,
		"claude_4_0_sonnet":    weightsForModel,
		"claude_4_0_opus":      weightsForModel,
	}

	service := NewThirdPartyArbiterService(featureFlagHandler, weights)

	// Set up feature flags for history management
	featureFlagHandler.Set("third_party_arbiter_load_balance_vacate_history_seconds", 300)
	featureFlagHandler.Set("third_party_arbiter_load_balance_min_records", 10)

	// Create a context with a request context
	ctx := context.Background()
	reqCtx := &requestcontext.RequestContext{
		RequestSessionId: requestcontext.RequestSessionId("test-session-single-target"),
	}
	ctx = requestcontext.NewIncomingContext(ctx, reqCtx)

	// Define the model to use for testing
	model := proto.Model_CLAUDE_3_5_SONNET_V2

	// Test with only one supported target
	req := &proto.GetTargetRequest{
		SupportedTargets: []proto.ThirdPartyClient{
			proto.ThirdPartyClient_ANTHROPIC_DIRECT,
		},
		Model: model,
	}
	resp, err := service.GetTarget(ctx, req)

	assert.NoError(t, err)
	// Primary target should be the only supported target
	assert.Equal(t, proto.ThirdPartyClient_ANTHROPIC_DIRECT, resp.Targets[0])
	// Check that we only have one target in the response
	assert.Equal(t, 1, len(resp.Targets))
	assert.Equal(t, int32(0), resp.DelayMs)
}

func TestGetTarget_WithTwoSupportedTargets(t *testing.T) {
	featureFlagHandler := featureflags.NewLocalFeatureFlagHandler()

	// Set up balanced weights
	weightsForModel := map[string]float64{
		"anthropic_direct":          0.25,
		"anthropic_vertexai_us_e5":  0.25,
		"anthropic_vertexai_eu_w1":  0.25,
		"anthropic_vertexai_as_se1": 0.25,
	}

	// Create a map of model -> weights
	weights := map[string]map[string]float64{
		"claude_3_5_sonnet_v2": weightsForModel,
		"claude_3_7_sonnet":    weightsForModel,
		"claude_4_0_sonnet":    weightsForModel,
		"claude_4_0_opus":      weightsForModel,
	}

	service := NewThirdPartyArbiterService(featureFlagHandler, weights)

	// Set up feature flags for history management
	featureFlagHandler.Set("third_party_arbiter_load_balance_vacate_history_seconds", 300)
	featureFlagHandler.Set("third_party_arbiter_load_balance_min_records", 10)

	// Create a context with a request context
	ctx := context.Background()
	reqCtx := &requestcontext.RequestContext{
		RequestSessionId: requestcontext.RequestSessionId("test-session-two-targets"),
	}
	ctx = requestcontext.NewIncomingContext(ctx, reqCtx)

	// Define the model to use for testing
	model := proto.Model_CLAUDE_3_5_SONNET_V2

	// Test with two supported targets
	req := &proto.GetTargetRequest{
		SupportedTargets: []proto.ThirdPartyClient{
			proto.ThirdPartyClient_ANTHROPIC_DIRECT,
			proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_US_E5,
		},
		Model: model,
	}
	resp, err := service.GetTarget(ctx, req)

	assert.NoError(t, err)
	// We should only get targets from the supported list
	assert.Contains(t, []proto.ThirdPartyClient{
		proto.ThirdPartyClient_ANTHROPIC_DIRECT,
		proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_US_E5,
	}, resp.Targets[0])
	// Check that we have exactly two targets
	assert.Equal(t, 2, len(resp.Targets))
	// All targets should be from the supported list
	for _, target := range resp.Targets {
		assert.Contains(t, []proto.ThirdPartyClient{
			proto.ThirdPartyClient_ANTHROPIC_DIRECT,
			proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_US_E5,
		}, target)
	}
	// Targets should be different
	assert.NotEqual(t, resp.Targets[0], resp.Targets[1])
	assert.Equal(t, int32(0), resp.DelayMs)
}

func TestGetTarget_WithClaude40Model(t *testing.T) {
	featureFlagHandler := featureflags.NewLocalFeatureFlagHandler()

	// Set up balanced weights
	weightsForModel := map[string]float64{
		"anthropic_direct":          0.25,
		"anthropic_vertexai_us_e5":  0.25,
		"anthropic_vertexai_eu_w1":  0.25,
		"anthropic_vertexai_as_se1": 0.25,
	}

	// Create a map of model -> weights including Claude 4.0
	weights := map[string]map[string]float64{
		"claude_3_5_sonnet_v2": weightsForModel,
		"claude_3_7_sonnet":    weightsForModel,
		"claude_4_0_sonnet":    weightsForModel,
		"claude_4_0_opus":      weightsForModel,
	}

	service := NewThirdPartyArbiterService(featureFlagHandler, weights)

	// Set up feature flags for history management
	featureFlagHandler.Set("third_party_arbiter_load_balance_vacate_history_seconds", 300)
	featureFlagHandler.Set("third_party_arbiter_load_balance_min_records", 10)

	// Create a context with a request context
	ctx := context.Background()
	reqCtx := &requestcontext.RequestContext{
		RequestSessionId: requestcontext.RequestSessionId("test-session-claude-4-0"),
	}
	ctx = requestcontext.NewIncomingContext(ctx, reqCtx)

	// Test with Claude 4.0 model
	req := &proto.GetTargetRequest{
		SupportedTargets: []proto.ThirdPartyClient{
			proto.ThirdPartyClient_ANTHROPIC_DIRECT,
			proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_US_E5,
		},
		Model: proto.Model_CLAUDE_4_0_SONNET,
	}
	resp, err := service.GetTarget(ctx, req)

	assert.NoError(t, err)
	// We should only get targets from the supported list
	assert.Contains(t, []proto.ThirdPartyClient{
		proto.ThirdPartyClient_ANTHROPIC_DIRECT,
		proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_US_E5,
	}, resp.Targets[0])
	// Check that we have exactly two targets
	assert.Equal(t, 2, len(resp.Targets))
	// All targets should be from the supported list
	for _, target := range resp.Targets {
		assert.Contains(t, []proto.ThirdPartyClient{
			proto.ThirdPartyClient_ANTHROPIC_DIRECT,
			proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_US_E5,
		}, target)
	}
	// Targets should be different
	assert.NotEqual(t, resp.Targets[0], resp.Targets[1])
	assert.Equal(t, int32(0), resp.DelayMs)
}

func TestGetTarget_WithClaude40OpusModel(t *testing.T) {
	featureFlagHandler := featureflags.NewLocalFeatureFlagHandler()

	// Set up balanced weights
	weightsForModel := map[string]float64{
		"anthropic_direct":          0.25,
		"anthropic_vertexai_us_e5":  0.25,
		"anthropic_vertexai_eu_w1":  0.25,
		"anthropic_vertexai_as_se1": 0.25,
	}

	// Create a map of model -> weights including Claude 4.0 Opus
	weights := map[string]map[string]float64{
		"claude_3_5_sonnet_v2": weightsForModel,
		"claude_3_7_sonnet":    weightsForModel,
		"claude_4_0_sonnet":    weightsForModel,
		"claude_4_0_opus":      weightsForModel,
	}

	service := NewThirdPartyArbiterService(featureFlagHandler, weights)

	// Set up feature flags for history management
	featureFlagHandler.Set("third_party_arbiter_load_balance_vacate_history_seconds", 300)
	featureFlagHandler.Set("third_party_arbiter_load_balance_min_records", 10)

	// Create a context with a request context
	ctx := context.Background()
	reqCtx := &requestcontext.RequestContext{
		RequestSessionId: requestcontext.RequestSessionId("test-session-claude-4-0-opus"),
	}
	ctx = requestcontext.NewIncomingContext(ctx, reqCtx)

	// Test with Claude 4.0 Opus model
	req := &proto.GetTargetRequest{
		SupportedTargets: []proto.ThirdPartyClient{
			proto.ThirdPartyClient_ANTHROPIC_DIRECT,
			proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_US_E5,
		},
		Model: proto.Model_CLAUDE_4_0_OPUS,
	}
	resp, err := service.GetTarget(ctx, req)

	assert.NoError(t, err)
	// We should only get targets from the supported list
	assert.Contains(t, []proto.ThirdPartyClient{
		proto.ThirdPartyClient_ANTHROPIC_DIRECT,
		proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_US_E5,
	}, resp.Targets[0])
	// Check that we have exactly two targets
	assert.Equal(t, 2, len(resp.Targets))
	// All targets should be from the supported list
	for _, target := range resp.Targets {
		assert.Contains(t, []proto.ThirdPartyClient{
			proto.ThirdPartyClient_ANTHROPIC_DIRECT,
			proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_US_E5,
		}, target)
	}
	// Targets should be different
	assert.NotEqual(t, resp.Targets[0], resp.Targets[1])
	assert.Equal(t, int32(0), resp.DelayMs)
}

func TestGetTarget_ZeroWeightNoTraffic(t *testing.T) {
	featureFlagHandler := featureflags.NewLocalFeatureFlagHandler()

	// Set up weights where one target has 0% weight
	weightsForModel := map[string]float64{
		"anthropic_direct":          0.0, // 0% weight - should never be selected
		"anthropic_vertexai_us_e5":  0.4, // 40% weight
		"anthropic_vertexai_eu_w1":  0.3, // 30% weight
		"anthropic_vertexai_as_se1": 0.3, // 30% weight
	}

	// Create a map of model -> weights
	weights := map[string]map[string]float64{
		"claude_3_5_sonnet_v2": weightsForModel,
		"claude_3_7_sonnet":    weightsForModel,
		"claude_4_0_sonnet":    weightsForModel,
		"claude_4_0_opus":      weightsForModel,
	}

	service := NewThirdPartyArbiterService(featureFlagHandler, weights)

	// Set up feature flags for history management
	featureFlagHandler.Set("third_party_arbiter_load_balance_vacate_history_seconds", 300)
	featureFlagHandler.Set("third_party_arbiter_load_balance_min_records", 10)

	// Test with many different session IDs to ensure anthropic_direct is never selected
	for i := 0; i < 100; i++ {
		ctx := context.Background()
		reqCtx := &requestcontext.RequestContext{
			RequestSessionId: requestcontext.RequestSessionId(fmt.Sprintf("test-session-zero-weight-%d", i)),
		}
		ctx = requestcontext.NewIncomingContext(ctx, reqCtx)

		req := &proto.GetTargetRequest{
			Model: proto.Model_CLAUDE_3_5_SONNET_V2,
		}
		resp, err := service.GetTarget(ctx, req)

		assert.NoError(t, err)
		// anthropic_direct should never be selected as primary target since it has 0% weight
		assert.NotEqual(t, proto.ThirdPartyClient_ANTHROPIC_DIRECT, resp.Targets[0],
			"anthropic_direct should never be selected as primary target with 0%% weight (iteration %d)", i)

		// Verify that the selected target is one of the valid non-zero weight targets
		assert.Contains(t, []proto.ThirdPartyClient{
			proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_US_E5,
			proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_EU_W1,
			proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_AS_SE1,
		}, resp.Targets[0], "Primary target should be one of the non-zero weight targets (iteration %d)", i)

		// Check that we have the expected number of targets (should be 3 since we have 4 total targets)
		assert.Equal(t, 3, len(resp.Targets), "Should have 3 targets in response (iteration %d)", i)
		assert.Equal(t, int32(0), resp.DelayMs, "Should have no delay (iteration %d)", i)

		// Verify that anthropic_direct can still appear as secondary or tertiary target
		// (since it's removed from weights only after being selected as primary)
		// But since it has 0 weight, it should never be primary
		for j, target := range resp.Targets {
			if target == proto.ThirdPartyClient_ANTHROPIC_DIRECT {
				assert.NotEqual(t, 0, j, "anthropic_direct should not be the primary target (position 0) with 0%% weight (iteration %d)", i)
			}
		}
		// anthropic_direct might appear as secondary/tertiary, but that's acceptable
		// The key test is that it's never the primary target
	}
}
