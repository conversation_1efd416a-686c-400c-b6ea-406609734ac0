package main

import (
	proto "github.com/augmentcode/augment/services/third_party_arbiter/proto"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
)

var (
	// ReportResponse endpoint metrics

	// Counter for tracking success/failure status in ReportResponse
	reportResponseTotal = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "au_third_party_arbiter_report_response_total",
			Help: "Total number of responses reported by status",
		},
		[]string{"model", "target", "status"},
	)

	// Gauge for current retry_after_seconds values
	retryAfterMs = promauto.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "au_third_party_arbiter_retry_after_seconds",
			Help: "Current retry_after delay in seconds for each model and target",
		},
		[]string{"model", "target"},
	)

	// Histogram for ReportResponse latency
	reportResponseLatency = promauto.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "au_third_party_arbiter_report_response_latency",
			Help:    "Latency of ReportResponse requests in seconds",
			Buckets: prometheus.DefBuckets,
		},
		[]string{"model", "target"},
	)

	// GetTarget endpoint metrics

	// Counter for primary target selections
	primaryTargetSelectionsTotal = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "au_third_party_arbiter_primary_target_selections_total",
			Help: "Total number of primary target selections",
		},
		[]string{"model", "primary_target"},
	)

	// Histogram for number of targets returned
	targetsReturned = promauto.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "au_third_party_arbiter_targets_returned",
			Help:    "Number of targets returned in GetTarget response",
			Buckets: []float64{1, 2, 3, 4, 5},
		},
		[]string{"model"},
	)

	// Gauge for delay returned
	delayReturnedMs = promauto.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "au_third_party_arbiter_delay_returned_ms",
			Help: "Delay returned in GetTarget response in milliseconds",
		},
		[]string{"model", "target_count"},
	)

	// Histogram for GetTarget latency
	getTargetLatency = promauto.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "au_third_party_arbiter_get_target_latency",
			Help:    "Latency of GetTarget requests in seconds",
			Buckets: prometheus.DefBuckets,
		},
		[]string{"model"},
	)

	// Additional recommended metrics

	// Gauge for active connections
	activeConnections = promauto.NewGauge(
		prometheus.GaugeOpts{
			Name: "au_third_party_arbiter_active_connections",
			Help: "Number of currently active connections",
		},
	)

	// Counter for request rate by endpoint
	requestsTotal = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "au_third_party_arbiter_requests_total",
			Help: "Total number of requests by endpoint and method",
		},
		[]string{"endpoint", "method"},
	)
)

// Helper functions to convert enums to strings for metrics labels

func modelToString(model proto.Model) string {
	switch model {
	case proto.Model_CLAUDE_3_5_SONNET_V2:
		return "claude_3_5_sonnet_v2"
	case proto.Model_CLAUDE_3_7_SONNET:
		return "claude_3_7_sonnet"
	case proto.Model_CLAUDE_4_0_SONNET:
		return "claude_4_0_sonnet"
	case proto.Model_CLAUDE_4_0_OPUS:
		return "claude_4_0_opus"
	default:
		return "unknown"
	}
}

func targetToString(target proto.ThirdPartyClient) string {
	switch target {
	case proto.ThirdPartyClient_ANTHROPIC_DIRECT:
		return "anthropic_direct"
	case proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_US_E5:
		return "anthropic_vertexai_us_e5"
	case proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_EU_W1:
		return "anthropic_vertexai_eu_w1"
	case proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_AS_SE1:
		return "anthropic_vertexai_as_se1"
	default:
		return "unknown"
	}
}
