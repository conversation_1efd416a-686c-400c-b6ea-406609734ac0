package main

import (
	"context"
	"testing"
	"time"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/go/clock"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	proto "github.com/augmentcode/augment/services/third_party_arbiter/proto"
	"github.com/prometheus/client_golang/prometheus/testutil"
	"github.com/stretchr/testify/assert"
)

// TestMetricsInitialization tests that metrics are properly initialized
func TestMetricsInitialization(t *testing.T) {
	// Create a new service with test weights
	weights := map[string]map[string]float64{
		"claude_3_5_sonnet_v2": {
			"anthropic_direct":          0.5,
			"anthropic_vertexai_us_e5":  0.3,
			"anthropic_vertexai_eu_w1":  0.1,
			"anthropic_vertexai_as_se1": 0.1,
		},
		"claude_3_7_sonnet": {
			"anthropic_direct":          0.5,
			"anthropic_vertexai_us_e5":  0.3,
			"anthropic_vertexai_eu_w1":  0.1,
			"anthropic_vertexai_as_se1": 0.1,
		},
		"claude_4_0_sonnet": {
			"anthropic_direct": 1,
		},
		"claude_4_0_opus": {
			"anthropic_direct": 1,
		},
	}

	featureFlagHandler := featureflags.NewLocalFeatureFlagHandler()
	service := NewThirdPartyArbiterService(featureFlagHandler, weights)

	// Verify that the service was created
	assert.NotNil(t, service)

	// Verify that active connections metric is initialized to 0
	assert.Equal(t, 0.0, testutil.ToFloat64(activeConnections))
}

// TestGetTargetMetrics tests that GetTarget endpoint metrics are properly recorded
func TestGetTargetMetrics(t *testing.T) {
	// Create a new service with test weights
	weights := map[string]map[string]float64{
		"claude_3_5_sonnet_v2": {
			"anthropic_direct":          0.5,
			"anthropic_vertexai_us_e5":  0.3,
			"anthropic_vertexai_eu_w1":  0.1,
			"anthropic_vertexai_as_se1": 0.1,
		},
		"claude_3_7_sonnet": {
			"anthropic_direct":          0.5,
			"anthropic_vertexai_us_e5":  0.3,
			"anthropic_vertexai_eu_w1":  0.1,
			"anthropic_vertexai_as_se1": 0.1,
		},
		"claude_4_0_sonnet": {
			"anthropic_direct": 1,
		},
		"claude_4_0_opus": {
			"anthropic_direct": 1,
		},
	}

	featureFlagHandler := featureflags.NewLocalFeatureFlagHandler()
	service := NewThirdPartyArbiterServiceWithClock(featureFlagHandler, clock.NewMockClock(time.Now()), weights)

	// Create a context with a session ID
	ctx := context.Background()
	reqCtx := &requestcontext.RequestContext{
		RequestSessionId: requestcontext.RequestSessionId("test-session-id"),
	}
	ctx = requestcontext.NewIncomingContext(ctx, reqCtx)

	// Call GetTarget
	req := &proto.GetTargetRequest{
		Model: proto.Model_CLAUDE_3_5_SONNET_V2,
	}
	resp, err := service.GetTarget(ctx, req)

	// Verify that the call succeeded
	assert.NoError(t, err)
	assert.NotNil(t, resp)

	// Verify that the request counter was incremented
	assert.Equal(t, 1.0, testutil.ToFloat64(requestsTotal.WithLabelValues("GetTarget", "GET")))

	// Verify that the primary target selection counter was incremented
	// Note: We can't easily test the exact label value since it depends on the session ID hash
	// But we can verify that the total count across all labels is 1
	assert.Equal(t, 1, testutil.CollectAndCount(primaryTargetSelectionsTotal))

	// Verify that the targets returned histogram was updated
	assert.Equal(t, 1, testutil.CollectAndCount(targetsReturned))

	// Verify that the active connections gauge was incremented and then decremented
	assert.Equal(t, 0.0, testutil.ToFloat64(activeConnections))
}

// TestReportResponseMetrics tests that ReportResponse endpoint metrics are properly recorded
func TestReportResponseMetrics(t *testing.T) {
	// Create a new service with test weights
	weights := map[string]map[string]float64{
		"claude_3_5_sonnet_v2": {
			"anthropic_direct":          0.5,
			"anthropic_vertexai_us_e5":  0.3,
			"anthropic_vertexai_eu_w1":  0.1,
			"anthropic_vertexai_as_se1": 0.1,
		},
		"claude_3_7_sonnet": {
			"anthropic_direct":          0.5,
			"anthropic_vertexai_us_e5":  0.3,
			"anthropic_vertexai_eu_w1":  0.1,
			"anthropic_vertexai_as_se1": 0.1,
		},
		"claude_4_0_sonnet": {
			"anthropic_direct": 1,
		},
		"claude_4_0_opus": {
			"anthropic_direct": 1,
		},
	}

	featureFlagHandler := featureflags.NewLocalFeatureFlagHandler()
	service := NewThirdPartyArbiterServiceWithClock(featureFlagHandler, clock.NewMockClock(time.Now()), weights)

	// Set up feature flags for history management
	featureFlagHandler.Set("third_party_arbiter_load_balance_vacate_history_seconds", 300)
	featureFlagHandler.Set("third_party_arbiter_load_balance_bucket_size_seconds", 10)
	featureFlagHandler.Set("third_party_arbiter_load_balance_min_records", 10)

	// Create a context
	ctx := context.Background()

	// Test successful response
	successReq := &proto.ReportResponseRequest{
		Model:        proto.Model_CLAUDE_3_5_SONNET_V2,
		Target:       proto.ThirdPartyClient_ANTHROPIC_DIRECT,
		IsSuccessful: true,
	}
	_, err := service.ReportResponse(ctx, successReq)
	assert.NoError(t, err)

	// Verify that the success counter was incremented
	assert.Equal(t, 1.0, testutil.ToFloat64(reportResponseTotal.WithLabelValues(
		modelToString(proto.Model_CLAUDE_3_5_SONNET_V2),
		targetToString(proto.ThirdPartyClient_ANTHROPIC_DIRECT),
		StatusSuccess,
	)))

	// Test error response
	errorReq := &proto.ReportResponseRequest{
		Model:        proto.Model_CLAUDE_3_5_SONNET_V2,
		Target:       proto.ThirdPartyClient_ANTHROPIC_DIRECT,
		IsSuccessful: false,
	}
	_, err = service.ReportResponse(ctx, errorReq)
	assert.NoError(t, err)

	// Verify that the error counter was incremented
	assert.Equal(t, 1.0, testutil.ToFloat64(reportResponseTotal.WithLabelValues(
		modelToString(proto.Model_CLAUDE_3_5_SONNET_V2),
		targetToString(proto.ThirdPartyClient_ANTHROPIC_DIRECT),
		StatusFailure,
	)))

	// Test rate limit response
	retryAfterSeconds := int32(30)
	rateLimitReq := &proto.ReportResponseRequest{
		Model:             proto.Model_CLAUDE_3_5_SONNET_V2,
		Target:            proto.ThirdPartyClient_ANTHROPIC_DIRECT,
		IsSuccessful:      false,
		RetryAfterSeconds: &retryAfterSeconds,
	}
	_, err = service.ReportResponse(ctx, rateLimitReq)
	assert.NoError(t, err)

	// Verify that the retry_after gauge was set
	assert.Equal(t, float64(retryAfterSeconds), testutil.ToFloat64(retryAfterMs.WithLabelValues(
		modelToString(proto.Model_CLAUDE_3_5_SONNET_V2),
		targetToString(proto.ThirdPartyClient_ANTHROPIC_DIRECT),
	)))
}
