local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local dynamicFeatureFlagsLib = import 'deploy/common/dynamic-feature-flags-lib.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local telemetryLib = import 'deploy/common/telemetry-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local endpointsLib = import 'services/deploy/endpoints.jsonnet';

function(env, namespace, cloud, namespace_config)
  local appName = 'third-party-arbiter';
  local shortAppName = 'tp-arbiter';
  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
  local isCentralService = env != 'DEV' && cloudInfo.isCentralNamespace(env, namespace, cloud);
  local services = lib.flatten([
    grpcLib.grpcService(appName=appName, namespace=namespace),
    if isCentralService then grpcLib.globalGrpcService(cloud=cloud, appName=appName, namespace=namespace) else [],
  ]);
  local serverDnsNames = lib.flatten([
    grpcLib.grpcServiceNames(appName),
    if isCentralService then grpcLib.globalGrpcServiceHostname(cloud, appName, namespace) else [],
  ]);
  local serverCert = if isCentralService
  then
    certLib.createCentralServerCert(
      name='%s-central-server-certificate' % appName,
      namespace=namespace,
      env=env,
      appName=appName,
      dnsNames=serverDnsNames,
    )
  else
    certLib.createServerCert(
      name='%s-server-certificate' % appName,
      namespace=namespace,
      appName=appName,
      dnsNames=grpcLib.grpcServiceNames(appName),
    );

  local serviceAccount = gcpLib.createServiceAccount(
    appName, env, cloud, namespace, iam=true, overridePrefix=shortAppName
  );

  local dynamicFeatureFlags = dynamicFeatureFlagsLib.mountLaunchDarklySecret(env=env, namespace=namespace, cloud=cloud, appName=appName, serviceAccount=serviceAccount);

  // Always need central client cert for token exchange, tenant watcher, etc
  local centralClientCert = certLib.createCentralClientCert(
    name='%s-central-client-certificate' % appName,
    namespace=namespace,
    env=env,
    appName=appName,
    dnsNames=grpcLib.grpcServiceNames(appName, namespace=namespace),
  );
  // Need namespace client cert for health checks on non-central services
  local clientCert = certLib.createClientCert(
    name='%s-client-certificate' % appName,
    namespace=namespace,
    appName=appName,
  );

  local config = {
    port: 50051,
    serverMtls: if mtls then serverCert.config else null,
    centralClientMtls: if mtls then centralClientCert.config else null,
    tokenExchangeEndpoint: endpointsLib.getTokenExchangeGrpcUrl(env=env, namespace=namespace, cloud=cloud),
    tenantWatcherEndpoint: endpointsLib.getTenantWatcherGrpcUrl(env=env, namespace=namespace, cloud=cloud),
    namespace: namespace,
    promPort: 9090,

    FeatureFlagsSdkKeyPath: dynamicFeatureFlags.filePath,
    dynamicFeatureFlagsEndpoint: if namespace_config.flags.useFakeFeatureFlags then 'http://fake-feature-flags-svc' else null,
    // ThirdPartyArbiterLoadBalanceWeights is now a map of model -> target -> weight
    // This allows different weights for different models
    ThirdPartyArbiterLoadBalanceWeights: {
      // Claude 3.5 Sonnet v2 weights
      claude_3_5_sonnet_v2: {
        anthropic_direct: 20.0,
        anthropic_vertexai_us_e5: 60.0,
        anthropic_vertexai_eu_w1: 20.0,
      },
      // Claude 3.7 Sonnet weights
      claude_3_7_sonnet: {
        anthropic_direct: 25.0,
        anthropic_vertexai_us_e5: 25.0,
        anthropic_vertexai_eu_w1: 25.0,
        anthropic_vertexai_as_se1: 25.0,
      },
      // Claude 4.0 Sonnet weights
      claude_4_0_sonnet: {
        anthropic_direct: 42.0,
        anthropic_vertexai_us_e5: 10.0,
        anthropic_vertexai_eu_w1: 10.0,
        anthropic_vertexai_as_se1: 38.0,
      },
      // Claude 4.0 Opus weights
      claude_4_0_opus: {
        anthropic_direct: 100.0,
      },
    },
  };

  local configMap = {
    apiVersion: 'v1',
    kind: 'ConfigMap',
    metadata: {
      name: '%s-config' % appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    data: {
      'config.json': std.manifestJsonEx(config, '  '),
    },
  };

  local configMapVolumeMountDef = {
    name: 'config',
    mountPath: '/config',
  };

  local configMapVolumeDef = {
    name: 'config',
    configMap: {
      name: '%s-config' % appName,
    },
  };

  local configFilename = '/config/config.json';

  local container = {
    name: appName,
    target: {
      name: '//services/third_party_arbiter/server:image',
      dst: 'third_party_arbiter',
    },
    ports: [
      {
        containerPort: 50051,
        name: 'grpc-svc',
      },
    ],
    env: [
      {
        name: 'POD_NAME',
        valueFrom: {
          fieldRef: {
            fieldPath: 'metadata.name',
          },
        },
      },
      {
        name: 'POD_NAMESPACE',
        valueFrom: {
          fieldRef: {
            fieldPath: 'metadata.namespace',
          },
        },
      },
    ] + telemetryLib.telemetryEnv(appName, telemetryLib.collectorUri(env, namespace, cloud)) + dynamicFeatureFlags.env,
    volumeMounts: [
      configMapVolumeMountDef,
      serverCert.volumeMountDef,
      clientCert.volumeMountDef,
      centralClientCert.volumeMountDef,
      dynamicFeatureFlags.volumeMountDef,
    ],
    resources: {
      limits: {
        cpu: 1,
        memory: '1Gi',
      },
    },
    args: [
      '--config',
      configFilename,
    ],
    readinessProbe: grpcLib.grpcHealthCheck(
      serviceName=appName + '-svc',
      tls=mtls,
      serverCerts=serverCert.volumeMountDef.mountPath,
      clientCerts=if isCentralService
      then centralClientCert.volumeMountDef.mountPath
      else clientCert.volumeMountDef.mountPath
    ) + {
      periodSeconds: 30,
    },
    livenessProbe: grpcLib.grpcHealthCheck(
      serviceName=appName + '-svc',
      tls=mtls,
      serverCerts=serverCert.volumeMountDef.mountPath,
      clientCerts=if isCentralService
      then centralClientCert.volumeMountDef.mountPath
      else clientCert.volumeMountDef.mountPath
    ) + {
      periodSeconds: 30,
    },
  };

  local deployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    spec: {
      minReadySeconds: if env == 'DEV' then 0 else 60,
      replicas: if env == 'DEV' then 1 else 4,
      selector: {
        matchLabels: {
          app: appName,
        },
      },
      template: {
        metadata: {
          labels: {
            app: appName,
          },
        },
        spec: {
          serviceAccountName: serviceAccount.name,
          priorityClassName: cloudInfo.envToPriorityClass(env),
          containers: [container],
          volumes: [
            configMapVolumeDef,
            serverCert.podVolumeDef,
            clientCert.podVolumeDef,
            centralClientCert.podVolumeDef,
            dynamicFeatureFlags.podVolumeDef,
          ],
        },
      },
    },
  };

  lib.flatten([
    configMap,
    deployment,
    serverCert.objects,
    clientCert.objects,
    centralClientCert.objects,
    dynamicFeatureFlags.objects,
    services,
    serviceAccount.objects,
  ])
