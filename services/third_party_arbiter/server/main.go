package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"net"
	"net/http"
	"os"
	"os/signal"
	"sync"
	"syscall"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/logging"
	tracing "github.com/augmentcode/augment/base/tracing/go"
	"github.com/augmentcode/augment/services/lib/grpc/auth"
	"github.com/augmentcode/augment/services/lib/grpc/recovery"
	tlsconfig "github.com/augmentcode/augment/services/lib/grpc/tls_config"
	tenantwatcherclient "github.com/augmentcode/augment/services/tenant_watcher/client"
	proto "github.com/augmentcode/augment/services/third_party_arbiter/proto"
	tokenexchange "github.com/augmentcode/augment/services/token_exchange/client"
	grpcprom "github.com/grpc-ecosystem/go-grpc-middleware/providers/prometheus"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/rs/zerolog/log"
	"go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc"
	_ "go.uber.org/automaxprocs"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"
	"google.golang.org/grpc/health"
	healthgrpc "google.golang.org/grpc/health/grpc_health_v1"
	"google.golang.org/grpc/reflection"
)

var configFile = flag.String("config", "", "Path to the config file")

type Config struct {
	// the port the grpc server will listen on
	Port int

	// TLS configs
	ServerMtls        *tlsconfig.ServerConfig
	CentralClientMtls *tlsconfig.ClientConfig

	TokenExchangeEndpoint string
	TenantWatcherEndpoint string

	Namespace string
	PromPort  int

	// Path to the feature flag SDK key file
	FeatureFlagsSdkKeyPath string
	// Optional endpoint for fake feature flags in dev/test
	DynamicFeatureFlagsEndpoint string

	// Load balance weights for third party clients
	// Maps model name to a map of client name to weight value
	ThirdPartyArbiterLoadBalanceWeights map[string]map[string]float64
}

func loadConfig(configFile string) *Config {
	if configFile == "" {
		log.Fatal().Msg("Missing config file")
	}

	f, err := os.Open(configFile)
	if err != nil {
		log.Fatal().Err(err).Msg("Error opening config file")
	}
	defer f.Close()

	var config Config
	decoder := json.NewDecoder(f)
	decoder.DisallowUnknownFields()
	if err := decoder.Decode(&config); err != nil {
		log.Fatal().Err(err).Msg("Error decoding config file")
	}

	return &config
}

func setupServer(ctx context.Context, config *Config,
	srvMetrics *grpcprom.ServerMetrics, serverTls credentials.TransportCredentials,
	tokenExchangeClient tokenexchange.TokenExchangeClient,
	tenantWatcherClient tenantwatcherclient.TenantWatcherClient,
	featureFlags featureflags.FeatureFlagHandle,
) (*grpc.Server, func(), error) {
	var opts []grpc.ServerOption
	opts = append(opts, grpc.Creds(serverTls))
	opts = append(opts, grpc.StatsHandler(otelgrpc.NewServerHandler()))
	opts = append(opts, grpc.ChainUnaryInterceptor(
		recovery.UnaryServerInterceptor(),
		srvMetrics.UnaryServerInterceptor(),
	))
	opts = append(opts, grpc.ChainStreamInterceptor(
		recovery.StreamingServerInterceptor(),
		srvMetrics.StreamServerInterceptor(),
	))

	// Set up service token auth.
	serviceTokenAuth := auth.NewServiceTokenAuth(tokenExchangeClient)
	authInterceptor := auth.NewAuthServerInterceptor(serviceTokenAuth.ValidateAccess)
	opts = append(opts, grpc.ChainUnaryInterceptor(authInterceptor.Intercept))
	opts = append(opts, grpc.ChainStreamInterceptor(authInterceptor.StreamIntercept))

	grpcServer := grpc.NewServer(opts...)
	// setup prometheus metrics for GRPC calls
	srvMetrics.InitializeMetrics(grpcServer)

	// setup reflection for debugging
	reflection.Register(grpcServer)
	// setup health service
	healthgrpc.RegisterHealthServer(grpcServer, health.NewServer())

	thirdPartyArbiterServer := NewThirdPartyArbiterService(featureFlags, config.ThirdPartyArbiterLoadBalanceWeights)
	if thirdPartyArbiterServer == nil {
		return nil, nil, fmt.Errorf("failed to create third party arbiter server")
	}
	proto.RegisterThirdPartyArbiterServer(grpcServer, thirdPartyArbiterServer)

	done := func() {
		// no need to shut down the grpc server here since that's done in main
		log.Info().Msg("Shutting down Third Party Arbiter service")
	}
	return grpcServer, done, nil
}

func main() {
	logging.SetupServerLogging()
	log.Info().Msg("Starting new Third Party Arbiter")
	ctx := context.Background()

	// Parse flags.
	flag.Parse()
	log.Info().Msgf("Config file: %s", *configFile)

	// Load config.
	config := loadConfig(*configFile)

	tracingShutdown := tracing.Init()
	defer tracingShutdown()

	// Create a channel to catch OS signals
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGTERM)
	wg := sync.WaitGroup{}

	// Set up prometheus metrics.
	srvMetrics := grpcprom.NewServerMetrics(
		grpcprom.WithServerHandlingTimeHistogram(
			grpcprom.WithHistogramBuckets([]float64{0.001, 0.01, 0.1, 0.3, 0.6, 1, 3, 6, 9, 20, 30, 60, 90, 120}),
		),
	)
	prometheus.MustRegister(srvMetrics)

	// Set up TLS.
	serverTls, err := tlsconfig.GetServerTls([]*tlsconfig.ServerConfig{config.ServerMtls})
	if err != nil {
		log.Fatal().Err(err).Msgf("Error getting server credentials")
	}
	centralClientCreds, err := tlsconfig.GetClientTls(config.CentralClientMtls)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error getting central client credentials")
	}

	// Set up service token auth.
	tokenExchangeClient, err := tokenexchange.New(
		config.TokenExchangeEndpoint, config.Namespace, grpc.WithTransportCredentials(centralClientCreds),
	)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating token exchange client")
		os.Exit(1)
	}
	defer tokenExchangeClient.Close()

	// Setup tenant watcher client.
	tenantWatcherClient := tenantwatcherclient.New(
		config.TenantWatcherEndpoint, grpc.WithTransportCredentials(centralClientCreds),
	)

	// Set up feature flags.
	var featureFlags featureflags.FeatureFlagHandle
	if config.FeatureFlagsSdkKeyPath != "" {
		var err error
		featureFlags, err = featureflags.NewFeatureFlagHandleFromFile(
			config.FeatureFlagsSdkKeyPath,
			config.DynamicFeatureFlagsEndpoint,
		)
		if err != nil {
			log.Fatal().Err(err).Msg("Error creating feature flag handle")
		}
	} else {
		log.Info().Msg("Feature flags disabled: using local implementation")
		featureFlags = featureflags.NewLocalFeatureFlagHandler()
	}

	grpcServer, done, err := setupServer(ctx, config, srvMetrics, serverTls, tokenExchangeClient, tenantWatcherClient, featureFlags)
	if err != nil {
		log.Fatal().Err(err).Msg("Error setting up server")
	}

	lis, err := net.Listen("tcp", fmt.Sprintf(":%d", config.Port))
	if err != nil {
		log.Fatal().Err(err).Msgf("Failed to listen")
	}
	log.Info().Msgf("Listening on %v", lis.Addr())

	go func() {
		wg.Add(1)
		defer wg.Done()
		err = grpcServer.Serve(lis)
		if err != nil && err != grpc.ErrServerStopped {
			log.Fatal().Err(err).Msg("Error serving")
		}
		log.Info().Msg("gRPC server closed")
	}()

	// Start prometheus metrics server.
	go func() {
		wg.Add(1)
		defer wg.Done()
		http.Handle("/metrics", promhttp.Handler())
		err := http.ListenAndServe(fmt.Sprintf(":%d", config.PromPort), nil)
		if err != nil && err != http.ErrServerClosed {
			log.Fatal().Err(err).Msg("Error serving prometheus metrics")
		}
		log.Info().Msg("Prometheus metrics server closed")
	}()

	// Wait for either a shutdown signal or an OS signal
	sig := <-sigChan
	log.Info().Msgf("Received signal: %v", sig)
	grpcServer.GracefulStop()
	done()
	wg.Wait()
	log.Info().Msg("Server stopped")
}
