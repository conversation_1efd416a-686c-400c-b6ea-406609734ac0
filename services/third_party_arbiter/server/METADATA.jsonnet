local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local tenantNamespaces = import 'deploy/tenants/namespaces.jsonnet';

{
  deployment: [
    {
      name: 'third-party-arbiter',
      kubecfg: {
        target: '//services/third_party_arbiter/server:kubecfg',
        task: [c for c in cloudInfo.centralNamespaces if c.cloud == 'GCP_US_CENTRAL1_PROD'],
      },
      health: {
        tier: 'TIER_1_A',
        experts: {
          users: ['ran', 'zheren'],
          slack_channel: '#system-services',
        },
      },
    },
    {
      name: 'third-party-arbiter-monitoring',
      kubecfg: {
        target: '//services/third_party_arbiter/server:kubecfg_monitoring',
        task: [
          {
            cloud: 'ALL_LEADS',
          },
        ],
      },
      health: {
        tier: 'TIER_2',
        experts: {
          users: ['ran', 'zheren'],
          slack_channel: '#system-services',
        },
      },
    },
  ],
}
