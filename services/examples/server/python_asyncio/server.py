"""Server for the route guide service using asyncio."""

import argparse
import asyncio
import logging
import os
import pathlib
import signal
import typing
from dataclasses import dataclass

import grpc
import grpc.aio
import opentelemetry
import opentelemetry.instrumentation.grpc
import prometheus_client
import structlog
from dataclasses_json import dataclass_json
from grpc_health.v1 import health, health_pb2_grpc
from grpc_reflection.v1alpha import reflection

import base.feature_flags
import base.tracing
import services.lib.grpc.tls_config.tls_config as tls_config
from base.logging.struct_logging import setup_struct_logging
from base.python.signal_handler.signal_handler import <PERSON>fulSignalHandler
from services.examples import route_guide_pb2, route_guide_pb2_grpc
from services.lib.grpc.auth.service_auth_interceptor import (
    ServiceAuthAsyncInterceptor,
    get_auth_info_from_grpc_context,
)
from services.lib.grpc.auth.service_token_auth import (
    GrpcPublicKeySource,
    ServiceTokenAuth,
)
from services.lib.grpc.metrics.metrics import AsyncMetricsServerInterceptor
from services.lib.request_context.request_context import RequestContext
from services.token_exchange.client import client as token_exchange_client

log = structlog.get_logger()


tracer = base.tracing.setup_opentelemetry()


@dataclass_json
@dataclass
class AuthConfig:
    """Configuration for the token authentication."""

    # the endpoint for the token exchange service
    token_exchange_endpoint: str


@dataclass_json
@dataclass
class Config:
    """Configuration for the example server."""

    # the port the grpc server will listen on
    port: int

    # the path to the feature flag sdk key
    feature_flags_sdk_key_path: typing.Optional[str]

    # the endpoint for the dynamic feature flags service or None if not used
    dynamic_feature_flags_endpoint: typing.Optional[str]

    # the configuration for the token authentication
    auth_config: AuthConfig

    # TLS configuration for the central clients
    central_client_mtls: typing.Optional[tls_config.ClientConfig] = None

    # TLS configuration for the client to talk to GRPC services in the same namespace
    client_mtls: typing.Optional[tls_config.ClientConfig] = None

    # TLS configuration for the server
    server_mtls: typing.Optional[tls_config.ServerConfig] = None

    # Grace period for the server to shutdown
    shutdown_grace_period_s: int = 25

    @classmethod
    def load_config(cls, config_file: pathlib.Path):
        """Loads the configuration from a file."""
        return cls.schema().loads(  # pylint: disable=no-member # type: ignore
            config_file.read_text()
        )


class AsyncRouteGuideServicer(route_guide_pb2_grpc.RouteGuideServicer):
    """RouteGuideServicer RPC server using asyncio."""

    def __init__(
        self,
        config: Config,
    ):
        self.config = config

    async def GetFeature(
        self,
        request: route_guide_pb2.GetFeatureRequest,
        context: grpc.aio.ServicerContext,
    ) -> route_guide_pb2.GetFeatureResponse:
        """Get a feature.

        Args:
            request: The request.
            context: The context.

        Returns:
            The response or None if context.abort() was called.
        """
        # Extract the request context and auth info
        # Note: We're using the context as-is, even though the type is different in asyncio
        request_context = RequestContext.from_grpc_context(context)  # type: ignore
        auth_info = get_auth_info_from_grpc_context(context)  # type: ignore

        # Bind the context logging to the request context and auth info
        with request_context.with_context_logging(), auth_info.with_context_logging():
            try:
                point = request.point
                # Note: be careful to never log restricted information
                logging.info("GetFeature: %s/%s", point.latitude, point.longitude)
                if point.latitude != 374219983 or point.longitude != -1220851590:
                    await context.abort(
                        grpc.StatusCode.NOT_FOUND, "Failed to find feature"
                    )
                    return route_guide_pb2.GetFeatureResponse()

                feature = route_guide_pb2.Feature()
                feature.name = "Augment HQ"
                feature.location.latitude = 374219983
                feature.location.longitude = -1220851590
                return route_guide_pb2.GetFeatureResponse(feature=feature)

            # Handle errors
            except grpc.RpcError as ex:
                logging.error("GetFeature failed: %s", ex)
                logging.exception(ex)
                await context.abort(
                    code=ex.code(),  # pylint: disable=no-member # type: ignore
                    details=ex.details(),  # pylint: disable=no-member # type: ignore
                )
                return route_guide_pb2.GetFeatureResponse()
            except Exception as ex:  # pylint: disable=broad-exception-caught
                logging.error("GetFeature failed: %s", ex)
                logging.exception(ex)
                raise

    async def ListFeatures(
        self,
        request: route_guide_pb2.ListFeaturesRequest,
        context: grpc.aio.ServicerContext,
    ):
        """List features.

        Args:
            request: The request.
            context: The context.

        Returns:
            The responses as an async iterator.
        """

        # Extract the request context and auth info
        request_context = RequestContext.from_grpc_context(context)  # type: ignore
        auth_info = get_auth_info_from_grpc_context(context)  # type: ignore

        # Bind the context logging to the request context and auth info
        with request_context.with_context_logging(), auth_info.with_context_logging():
            try:
                logging.info("ListFeatures: %s", request)

                # yield the responses
                feature = route_guide_pb2.Feature()
                feature.name = "Augment HQ"
                feature.location.latitude = 374219983
                feature.location.longitude = -1220851590
                yield route_guide_pb2.ListFeaturesResponse(feature=feature)

                feature = route_guide_pb2.Feature()
                feature.name = "Sutter Hill HQ"
                feature.location.latitude = 374265693
                feature.location.longitude = -1221412065
                yield route_guide_pb2.ListFeaturesResponse(feature=feature)
            except grpc.RpcError as ex:
                logging.error("ListFeatures failed: %s", ex)
                logging.exception(ex)
                await context.abort(
                    code=ex.code(),  # pylint: disable=no-member # type: ignore
                    details=ex.details(),  # pylint: disable=no-member # type: ignore
                )
            except Exception as ex:  # pylint: disable=broad-exception-caught
                logging.error("ListFeatures failed: %s", ex)
                logging.exception(ex)
                raise

    async def RecordRoute(
        self,
        request_iterator,
        context: grpc.aio.ServicerContext,
    ):
        """Record a route.

        Args:
            request_iterator: An async iterator of points.
            context: The context.

        Returns:
            The route summary.
        """
        request_context = RequestContext.from_grpc_context(context)  # type: ignore
        with request_context.with_context_logging():
            try:
                point_count = 0
                async for point in request_iterator:
                    logging.info("RecordRoute: %s", point)
                    point_count += 1

                return route_guide_pb2.RouteSummary(point_count=point_count)
            except grpc.RpcError as ex:
                logging.error("RecordRoute failed: %s", ex)
                logging.exception(ex)
                await context.abort(
                    code=ex.code(),  # pylint: disable=no-member # type: ignore
                    details=ex.details(),  # pylint: disable=no-member # type: ignore
                )
            except Exception as ex:  # pylint: disable=broad-exception-caught
                logging.error("RecordRoute failed: %s", ex)
                logging.exception(ex)
                raise

    async def RouteChat(
        self,
        request_iterator: typing.AsyncIterable[route_guide_pb2.RouteNote],
        context: grpc.aio.ServicerContext,
    ) -> typing.AsyncIterator[route_guide_pb2.RouteNote]:
        """Route chat.

        Args:
            request_iterator: An async iterator of route notes.
            context: The context.

        Returns:
            An async iterator of route notes.
        """
        request_context = RequestContext.from_grpc_context(context)  # type: ignore
        with request_context.with_context_logging():
            try:
                async for note in request_iterator:
                    logging.info("RouteChat: %s", note)
                    yield note
            except grpc.RpcError as ex:
                logging.error("RouteChat failed: %s", ex)
                logging.exception(ex)
                await context.abort(
                    code=ex.code(),  # pylint: disable=no-member # type: ignore
                    details=ex.details(),  # pylint: disable=no-member # type: ignore
                )
            except Exception as ex:  # pylint: disable=broad-exception-caught
                logging.error("RouteChat failed: %s", ex)
                logging.exception(ex)
                raise


async def _serve(config: Config, shutdown_event: asyncio.Event):
    """Start the server and wait for shutdown."""
    path = None
    if config.feature_flags_sdk_key_path is not None:
        path = pathlib.Path(config.feature_flags_sdk_key_path)
    custom_endpoint = None
    if config.dynamic_feature_flags_endpoint is not None:
        custom_endpoint = config.dynamic_feature_flags_endpoint

    context = base.feature_flags.Context.setup(path, custom_endpoint)
    base.feature_flags.set_global_context(context)

    namespace = os.environ["POD_NAMESPACE"]
    token_client = token_exchange_client.GrpcTokenExchangeClient.create(
        config.auth_config.token_exchange_endpoint,
        namespace,
        tls_config.get_client_tls_creds(config.central_client_mtls),
    )
    service_auth = ServiceTokenAuth(
        GrpcPublicKeySource(token_client),
        required_scopes=["CONTENT_R"],
    )
    auth_interceptor = ServiceAuthAsyncInterceptor(service_auth)

    # Create the async gRPC server
    server = grpc.aio.server(
        interceptors=[
            opentelemetry.instrumentation.grpc.aio_server_interceptor(),
            AsyncMetricsServerInterceptor(),
            auth_interceptor,
        ],
    )

    # Reply to health check RPCs
    health_servicer = health.aio.HealthServicer()
    health_pb2_grpc.add_HealthServicer_to_server(health_servicer, server)

    # Add the route guide servicer
    # Note: We need to use the async version of the servicer
    route_guide_servicer = AsyncRouteGuideServicer(config)
    route_guide_pb2_grpc.add_RouteGuideServicer_to_server(route_guide_servicer, server)

    # Enable reflection
    service_names = (
        route_guide_pb2.DESCRIPTOR.services_by_name["RouteGuide"].full_name,
        reflection.SERVICE_NAME,
    )
    reflection.enable_server_reflection(service_names, server)

    # Start the server
    server_credentials = tls_config.get_server_tls_creds(config.server_mtls)
    if server_credentials:
        actual_port = server.add_secure_port(f"[::]:{config.port}", server_credentials)
    else:
        actual_port = server.add_insecure_port(f"[::]:{config.port}")

    await server.start()
    logging.info("Listening on %s", actual_port)

    # Wait for shutdown
    await shutdown_event.wait()
    logging.info("Shutting down server")

    # Graceful shutdown
    await server.stop(grace=config.shutdown_grace_period_s)
    logging.info("Server shutdown complete")


async def main_async():
    """Run the server asynchronously."""
    # Set up the logging
    setup_struct_logging()

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--config-file", default=pathlib.Path("/config/config.json"), type=pathlib.Path
    )
    args = parser.parse_args()
    logging.info("Args %s", args)

    config = Config.load_config(args.config_file)
    logging.info("Config %s", config)

    # This goes out and overrides the grpc package so the opentelemetry
    # interceptor is automatically added during client creation.
    grpc_client_instrumentor = (
        opentelemetry.instrumentation.grpc.GrpcInstrumentorClient()
    )
    grpc_client_instrumentor.instrument()

    # Start Prometheus metrics server
    prometheus_client.start_http_server(9090)

    # Create an asyncio event for shutdown
    shutdown_event = asyncio.Event()

    # Set up signal handlers for graceful shutdown
    loop = asyncio.get_running_loop()

    def signal_handler():
        logging.info("Received shutdown signal")
        shutdown_event.set()

    # Register signal handlers
    for sig in [signal.SIGINT, signal.SIGTERM]:
        loop.add_signal_handler(sig, signal_handler)

    # Run the server
    await _serve(config, shutdown_event)


def main():
    """Run the server."""
    # Run the async main function
    asyncio.run(main_async())


if __name__ == "__main__":
    main()
