load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:python.bzl", "py_binary", "py_oci_image")

py_binary(
    name = "server",
    srcs = [
        "server.py",
    ],
    deps = [
        "//base/logging:struct_logging",
        "//base/python/signal_handler",
        "//base/tracing:tracing_py",
        "//services/examples:route_guide_py_proto",
        "//services/lib/grpc/auth:service_auth_interceptor",
        "//services/lib/grpc/auth:service_token_auth",
        "//services/lib/grpc/metrics",
        "//services/lib/grpc/tls_config:grpc_tls_config_py",
        "//services/lib/request_context:request_context_py",
        requirement("grpcio-health-checking"),
        requirement("dataclasses_json"),
        requirement("grpcio"),
        requirement("grpcio-reflection"),
        requirement("opentelemetry-instrumentation-grpc"),
        requirement("prometheus-client"),
        requirement("protobuf"),
    ],
)

py_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":server",
    tars = ["//tools/docker:grpc_health_probe_tar"],
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    # the examples should only be deployed to DEV
    # remove to be able to deploy to PROD
    cloud = [
        "GCP_US_CENTRAL1_DEV",
    ],
    data = [
        ":image",
    ],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:config-map-lib",
        "//deploy/common:dynamic-feature-flags-lib",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/common:telemetry-lib",
        "//deploy/gcp:gcp-lib",
        "//services/deploy:endpoints",
    ],
)
