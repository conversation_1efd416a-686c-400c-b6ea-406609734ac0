import { logger } from "@augment-internal/logging";
import type { SessionUser } from "../.server/auth";

/**
 * Creates a structured logging context for API routes.
 *
 * @param user - The authenticated user from the session
 * @param request - The incoming HTTP request
 * @returns Logging helper with info, warn, and error methods that include consistent metadata
 */
export function createRouteLogContext(user: SessionUser, request: Request) {
  const url = new URL(request.url);

  const baseContext = {
    user_id: user.userId,
    tenant_id: user.tenantId,
    method: request.method,
    path: url.pathname,
  };

  return {
    info: (message: string, extra?: Record<string, any>) =>
      logger.info({ message, ...baseContext, ...extra }),

    warn: (message: string, error?: unknown, extra?: Record<string, any>) => {
      const errorInfo = error
        ? {
            error: error instanceof Error ? error.message : String(error),
            stack: error instanceof Error ? error.stack : undefined,
          }
        : {};
      logger.warn({ message, ...baseContext, ...errorInfo, ...extra });
    },

    error: (message: string, error?: unknown, extra?: Record<string, any>) => {
      const errorInfo = error
        ? {
            error: error instanceof Error ? error.message : String(error),
            stack: error instanceof Error ? error.stack : undefined,
          }
        : {};
      logger.error({ message, ...baseContext, ...errorInfo, ...extra });
    },
  };
}
