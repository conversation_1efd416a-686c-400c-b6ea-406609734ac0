import { json } from "@remix-run/node";
import {
  fromUnixTime,
  minutesToMilliseconds,
  secondsToMilliseconds,
} from "date-fns";
import { withAuthApi } from "../.server/auth";
import {
  TeamSchema,
  UpdateTeamSchema,
  type TeamMemberSchema,
  type TeamInvitationSchema,
  type UpdateTeamRequestSchema,
  type UpdateTeamResponseSchema,
} from "../schemas/team";
import { TenantWatcherCachingClient } from "../.server/grpc/tenant-watcher";
import { AuthCentralClient } from "../.server/grpc/auth-central";
import { CustomerUiRole } from "~services/auth/central/server/auth_entities_pb";
import {
  isUserInSelfServeTeam,
  ensureTeamForUser,
  teamsAllowed,
} from "../utils/team.server";
import { updateSessionWithTenantAndRoles } from "app/utils/session.server";
import { waitUntil } from "@augment-internal/ts-utils/timer";
import type { GetSubscriptionResponse } from "~services/auth/central/server/auth_pb";
import { isNonNullable } from "app/utils/guards";
import { createRouteLogContext } from "../utils/logging.server";

// Endpoint to check the status of a team creation or get team info
// If the tenant has is_self_serve_team=false, it will return status "none"
// If the tenant has is_self_serve_team=true or the property is not set, it will return status "active" with team data
export const loader = withAuthApi(async ({ user, request }) => {
  const start = Date.now();
  const log = createRouteLogContext(user, request);

  log.info("Loading team data");

  try {
    const authCentralClient = AuthCentralClient.getInstance();

    if (!(await teamsAllowed(user))) {
      const status = 403;
      log.info("Team management not enabled for user", { status_code: status });
      return json({ message: "Team management is not enabled" }, { status });
    }

    const { tenantId } = user;
    if (!tenantId) {
      const status = 200;
      log.info("User has no tenant, returning status none", {
        status_code: status,
      });
      return json({ status: "none" }, { status });
    }

    // User has a tenant, check if it's a self-serve team
    try {
      const tenantWatcher = TenantWatcherCachingClient.getInstance();
      const tenant = await tenantWatcher.tenantFor(tenantId);

      // Check if the tenant has is_self_serve_team property set to "false"
      if (tenant?.config?.configs?.is_self_serve_team === "false") {
        const status = 200;
        log.info("Tenant is not a self-serve team, returning status none", {
          status_code: status,
        });
        return json(
          {
            status: "none",
          },
          { status },
        );
      }
    } catch (error) {
      log.warn("Failed to get tenant information", error);
    }

    // User has a tenant and it's a self-serve team, return the team data
    // Get subscription information to get the current seats
    let seats = 0;
    try {
      const subscriptionResponse =
        await authCentralClient.getSubscriptionFromDatabase(user);
      seats = subscriptionResponse.subscription?.seats ?? 0;
    } catch (error) {
      log.warn("Failed to get subscription for tenant", error);
    }

    // Get list of users in the tenant
    let usersWithRoles: Array<TeamMemberSchema> = [];
    try {
      const listUsersResponse = await authCentralClient.listTenantUsers(
        user,
        tenantId,
      );

      // Fetch roles for each user
      usersWithRoles = await Promise.all(
        listUsersResponse.users.map(async (userInfo) => {
          // Convert Timestamp to ISO string if it exists
          const joinedAt = userInfo.createdAt
            ? typeof userInfo.createdAt === "string"
              ? userInfo.createdAt
              : fromUnixTime(Number(userInfo.createdAt.seconds)).toISOString()
            : new Date().toISOString();

          let role: "ADMIN" | "MEMBER" = "MEMBER";
          try {
            const userOnTenantResponse =
              await authCentralClient.getUserOnTenant(
                user,
                userInfo.id,
                tenantId,
              );

            if (
              userOnTenantResponse.customerUiRoles.includes(
                CustomerUiRole.ADMIN,
              )
            ) {
              role = "ADMIN";
            }
          } catch (error) {
            log.warn("Failed to get roles for user", error, {
              target_user_id: userInfo.id,
            });
          }

          return {
            id: userInfo.id,
            email: userInfo.email,
            role,
            joinedAt,
          };
        }),
      );
    } catch (error) {
      log.error("Failed to list users for tenant", error);
    }

    // Get invitations for the tenant
    let invitations: Array<TeamInvitationSchema> = [];
    try {
      const invitationsResponse =
        await authCentralClient.getTenantInvitations(user);
      invitations = invitationsResponse.invitations.map((invitation) => ({
        id: invitation.id,
        email: invitation.inviteeEmail,
        invitedAt: fromUnixTime(
          Number(invitation.createdAt?.seconds),
        ).toISOString(),
      }));
    } catch (error) {
      log.warn("Failed to get invitations for tenant", error);
    }

    // If we couldn't get seats from subscription, use the number of users
    if (seats === 0) {
      seats = usersWithRoles.length;
      log.warn(
        "Failed to get subscription for tenant, using number of users as seats",
      );
    }

    const teamData: TeamSchema = {
      id: tenantId,
      seats,
      users: usersWithRoles,
      invitations,
    };

    // This is where the validation error might occur in the test
    let validatedTeam;
    try {
      validatedTeam = TeamSchema.parse(teamData);
    } catch (validationError) {
      const status = 500;
      log.error("Validation error", validationError, { status_code: status });
      return json({ message: "Internal Server Error" }, { status });
    }

    const status = 200;
    log.info("Team data loaded successfully", {
      duration_ms: Date.now() - start,
      status_code: status,
    });

    return json(
      {
        status: "active",
        team: validatedTeam,
      },
      { status },
    );
  } catch (error) {
    const status = 500;
    log.error("Error fetching team data", error, { status_code: status });
    return json(
      {
        message: "Internal Server Error",
      },
      { status },
    );
  }
});

export const action = withAuthApi(async ({ request, user }) => {
  const start = Date.now();
  const log = createRouteLogContext(user, request);
  const authCentralClient = AuthCentralClient.getInstance();

  log.info("Processing team action");

  if (request.method === "POST") {
    // Handle POST request to create a team
    // This will create a new tenant with is_self_serve_team=true by default
    // Users can only create a team if they have a tenant with is_self_serve_team=false
    // Users without a tenant cannot create a team
    try {
      // Only allow team creation if the current tenant has is_self_serve_team=false
      if (await isUserInSelfServeTeam(user)) {
        const status = 400;
        log.info("User already associated with a self-serve team", {
          status_code: status,
        });
        return json(
          { message: "User already associated with a self-serve team" },
          { status },
        );
      }

      // If is_self_serve_team=false, continue with team creation
      log.info("User is on a non-self-serve team, allowing team creation");
    } catch (error) {
      const status = 500;
      log.error("Failed to get tenant information for tenant", error, {
        status_code: status,
      });
      return json({ message: "Failed to check tenant status" }, { status });
    }

    // Create a new tenant for the team
    try {
      const createResponse = await authCentralClient.createTenantForTeam(user);
      const tenantCreationId = createResponse.tenantCreationId;

      const status = 200;
      log.info("Team creation started successfully", {
        tenant_creation_id: tenantCreationId,
        duration_ms: Date.now() - start,
        status_code: status,
      });

      return json({ tenant_creation_id: tenantCreationId }, { status });
    } catch (error) {
      const status = 500;
      log.error("Failed to start creating team", error, {
        status_code: status,
      });
      return json({ message: "Failed to start creating team" }, { status });
    }
  } else if (request.method === "PATCH") {
    // Handle PATCH request to update team seats
    // The auth service will check if the user is an admin and reject the request if not
    try {
      const tenantId = await ensureTeamForUser(user);

      // Update session and get headers if needed
      const { headers } = await updateSessionWithTenantAndRoles(
        request,
        user,
        tenantId,
      );

      const authCentralClient = AuthCentralClient.getInstance();

      let requestData: UpdateTeamRequestSchema;
      try {
        const body = await request.json();
        requestData = UpdateTeamSchema.parse(body);
      } catch (error) {
        const status = 400;
        log.error("Invalid request body", error, { status_code: status });
        return json({ message: "Invalid request format" }, { status });
      }

      // Get the current subscription to get the subscription id
      let subscription;
      try {
        const subscriptionResponse =
          await authCentralClient.getSubscriptionFromDatabase(user);
        subscription = subscriptionResponse.subscription;
        if (!subscription) {
          throw new Error("No subscription found");
        }
      } catch (error) {
        const status = 500;
        log.error("Failed to get subscription for tenant", error, {
          status_code: status,
        });
        return json(
          { message: "Failed to retrieve current subscription" },
          { status },
        );
      }

      try {
        await authCentralClient.updateSubscription(
          user,
          requestData.seats,
          subscription.subscriptionId,
        );
        const waitForSubscriptionChangeToComplete =
          waitUntil<GetSubscriptionResponse>(
            (subscriptionResponse) =>
              isNonNullable(subscriptionResponse.subscription) &&
              subscriptionResponse.subscription?.subscriptionChangeId == null,
            minutesToMilliseconds(2),
            secondsToMilliseconds(1),
          );
        await waitForSubscriptionChangeToComplete(async () =>
          authCentralClient.getSubscriptionFromDatabase(user),
        );

        const status = 200;
        log.info("Team seats updated successfully", {
          seats: requestData.seats,
          duration_ms: Date.now() - start,
          status_code: status,
        });

        return json(
          { seats: requestData.seats } satisfies UpdateTeamResponseSchema,
          { headers, status },
        );
      } catch (error) {
        const status = 500;
        log.error("Failed to update subscription seats for tenant", error, {
          status_code: status,
        });

        return json(
          { message: "Failed to update subscription seats" },
          { status },
        );
      }
    } catch (error) {
      const status = 500;
      log.error("Error updating team seats", error, { status_code: status });
      return json({ message: "Internal Server Error" }, { status });
    }
  }

  // Default response for unsupported methods
  const status = 405;
  log.info("Method not allowed", { status_code: status });
  return json({ message: "Method not allowed" }, { status });
});
