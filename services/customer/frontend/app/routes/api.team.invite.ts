import { json } from "@remix-run/node";
import { withAuth<PERSON><PERSON> } from "../.server/auth";
import { AuthCentralClient } from "../.server/grpc/auth-central";
import { InviteUsersToTenantResponse_InvitationCreationStatus_Status } from "~services/auth/central/server/auth_pb";
import {
  TeamInviteRequestSchema,
  type TeamInviteSuccessSchema,
  type TeamInvitePartialSuccessSchema,
  type TeamInviteErrorSchema,
} from "../schemas/team-invite";
import { ensureTeamForUser, teamsAllowed } from "../utils/team.server";
import { updateSessionWithTenantAndRoles } from "app/utils/session.server";
import { createRouteLogContext } from "../utils/logging.server";

export const action = withAuthApi(async ({ request, user }) => {
  const start = Date.now();
  const log = createRouteLogContext(user, request);

  log.info("Processing team invite action");

  if (request.method !== "POST") {
    const status = 405;
    log.info("Method not allowed", { status_code: status });
    return json<TeamInviteErrorSchema>(
      { message: "Method not allowed" },
      { status },
    );
  }

  if (!(await teamsAllowed(user))) {
    const status = 403;
    log.info("Team management not enabled for user", { status_code: status });
    return json<TeamInviteErrorSchema>(
      { message: "Team management is not enabled" },
      { status },
    );
  }

  try {
    const tenantId = await ensureTeamForUser(user);

    // Update session and get headers if needed
    const { headers } = await updateSessionWithTenantAndRoles(
      request,
      user,
      tenantId,
    );

    const authCentralClient = AuthCentralClient.getInstance();

    // Parse and validate the request body
    let requestData;
    try {
      const body = await request.json();
      requestData = TeamInviteRequestSchema.parse(body);
    } catch (error) {
      const status = 400;
      log.error("Invalid request body", error, { status_code: status });
      return json<TeamInviteErrorSchema>(
        { message: "Invalid request format" },
        { status },
      );
    }

    if (requestData.emails.length === 0) {
      const status = 400;
      log.info("No emails provided", { status_code: status });
      return json<TeamInviteErrorSchema>(
        { message: "No emails provided" },
        { status },
      );
    }

    // Invite users to the tenant
    try {
      const response = await authCentralClient.inviteUsersToTenant(
        user,
        requestData.emails,
        tenantId,
      );

      const failedInvitations = response.invitationStatuses.filter(
        (status) =>
          status.status !==
          InviteUsersToTenantResponse_InvitationCreationStatus_Status.SUCCESS,
      );

      if (failedInvitations.length === 0) {
        const status = 200;
        log.info("All invitations sent successfully", {
          email_count: requestData.emails.length,
          duration_ms: Date.now() - start,
          status_code: status,
        });
        return json<TeamInviteSuccessSchema>(
          {},
          {
            status,
            headers,
          },
        );
      } else if (failedInvitations.length < requestData.emails.length) {
        const failedEmails = failedInvitations.map((status) => status.email);
        const status = 207;
        log.warn("Some invitations failed", undefined, {
          failed_count: failedInvitations.length,
          total_count: requestData.emails.length,
          failed_emails: failedEmails,
          duration_ms: Date.now() - start,
          status_code: status,
        });
        return json<TeamInvitePartialSuccessSchema>(
          { failed: failedEmails },
          {
            status,
            headers,
          },
        );
      } else {
        const failedEmails = failedInvitations.map((status) => status.email);
        const status = 400;
        log.error("All invitations failed", undefined, {
          failed_count: failedInvitations.length,
          failed_emails: failedEmails,
          duration_ms: Date.now() - start,
          status_code: status,
        });
        return json<TeamInvitePartialSuccessSchema>(
          { failed: failedEmails },
          {
            status,
            headers,
          },
        );
      }
    } catch (error) {
      const status = 500;
      log.error("Failed to invite users to tenant", error, {
        status_code: status,
      });
      return json<TeamInviteErrorSchema>(
        { message: "Internal Server Error" },
        { status },
      );
    }
  } catch (error) {
    const status = 500;
    log.error("Error inviting users to tenant", error, { status_code: status });
    return json<TeamInviteErrorSchema>(
      { message: "Internal Server Error" },
      { status },
    );
  }
});
