package customerio

import (
	"context"

	"github.com/stretchr/testify/mock"
)

// MockCustomerioClient is a mock implementation of the CustomerioClient interface
type MockCustomerioClient struct {
	mock.Mock
}

// NewMockCustomerioClient creates a new mock CustomerioClient
func NewMockCustomerioClient() *MockCustomerioClient {
	return new(MockCustomerioClient)
}

// SendEmail is a mock implementation of the SendEmail method
func (m *MockCustomerioClient) SendEmail(ctx context.Context, email CustomerioEmail) error {
	retVals := m.Called(ctx, email)
	return retVals.Error(0)
}
