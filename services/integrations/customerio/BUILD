load("//tools/bzl:go.bzl", "go_library")

go_library(
    name = "customerio_lib",
    srcs = [
        "customerio.go",
        "mock_customerio_client.go",
    ],
    importpath = "github.com/augmentcode/augment/services/integrations/customerio",
    visibility = ["//visibility:public"],
    deps = [
        "@com_github_customerio_go_customerio_v3//:go-customerio",
        "@com_github_google_uuid//:uuid",
        "@com_github_rs_zerolog//log",
        "@com_github_stretchr_testify//mock",
    ],
)
