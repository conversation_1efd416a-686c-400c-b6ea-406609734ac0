package customerio

import (
	"context"
	"fmt"

	customerio "github.com/customerio/go-customerio/v3"
	"github.com/google/uuid"
	"github.com/rs/zerolog/log"
)

// CustomerioClient is the interface for interacting with the customer.io API
type CustomerioClient interface {
	SendEmail(ctx context.Context, email CustomerioEmail) error
}

// CustomerioEmail represents an email to be sent via customer.io
type CustomerioEmail struct {
	TransactionMessageID string
	MessageData          map[string]interface{}
	To                   string
	From                 string
	Subject              string
	Body                 string
}

// CustomerioClientImpl is the implementation of the CustomerioClient interface
type CustomerioClientImpl struct {
	client *customerio.APIClient
}

// NewCustomerioClient creates a new CustomerioClient with the given API key
func NewCustomerioClient(apiKey string) CustomerioClient {
	return &CustomerioClientImpl{
		client: customerio.NewAPIClient(apiKey),
	}
}

// SendEmail sends an email using customer.io
func (c *CustomerioClientImpl) SendEmail(ctx context.Context, email CustomerioEmail) error {
	if c.client == nil {
		return fmt.Errorf("CustomerioClient is not initialized")
	}

	request := customerio.SendEmailRequest{
		Identifiers: map[string]string{
			"id": uuid.New().String(),
		},
		To:   email.To,
		From: email.From,
	}

	if email.TransactionMessageID != "" {
		request.TransactionalMessageID = email.TransactionMessageID
		request.MessageData = email.MessageData
	} else {
		request.Subject = email.Subject
		request.Body = email.Body
	}

	// Send the email
	_, err := c.client.SendEmail(ctx, &request)
	if err != nil {
		log.Error().Err(err).Str("to", email.To).Msg("Failed to send email via customer.io")
		return fmt.Errorf("failed to send email: %w", err)
	}

	log.Info().Str("to", email.To).Msg("Successfully sent email via customer.io")
	return nil
}
