"""A Python client library call a working set service."""

import logging
import typing


import grpc

import services.lib.grpc.tls_config.tls_config as tls_config
from base.blob_names import blob_names_pb2
from base.python.grpc import client_options
from services.working_set import working_set_pb2, working_set_pb2_grpc
from services.lib.request_context.request_context import RequestContext


def setup_stub(
    endpoint: str,
    credentials: grpc.ChannelCredentials | None,
    options: client_options.OptionsList | None = None,
) -> working_set_pb2_grpc.WorkingSetManagerStub:
    """Setup the client stub for the working set service.

    Args:
        endpoint: The endpoint of the working set service.
        credentials: The credentials to use for the channel (optional)

    Returns:
        The client stub for the working set.
    """
    logging.info("Creating grpc client to %s with options %s", endpoint, [])
    if not credentials:
        channel = grpc.insecure_channel(
            endpoint, options=client_options.create(options)
        )
    else:
        channel = grpc.secure_channel(
            endpoint, credentials, options=client_options.create(options)
        )
    stub = working_set_pb2_grpc.WorkingSetManagerStub(channel)
    return stub


class WorkingSetClient(typing.Protocol):
    """Class to call working set APIs remotely."""

    def register_working_set(
        self,
        blobs: blob_names_pb2.Blobs,
        request_context: RequestContext,
        timeout: float = 30,
    ):
        """Register a working set.

        Args:
            blobs: The blobs to register.
            request_context: The request context to use.
            timeout: The timeout to use.
        """
        ...

    def create_ann_index_for_checkpoint(
        self,
        checkpoint_id: str,
        transformation_key: str,
        request_context: RequestContext,
        timeout: float = 30,
    ):
        """Create an ANN index for a checkpoint.

        Args:
            checkpoint_id: The checkpoint to create the index for.
            transformation_key: The transformation key to use.
            request_context: The request context to use.
            timeout: The timeout to use.
        """
        ...

    def get_active_checkpoints(
        self,
        request_context: RequestContext,
        timeout: float = 30,
    ) -> list[str]:
        """Get the active checkpoints.

        Args:
            request_context: The request context to use.
            timeout: The timeout to use.
        """
        ...

    def get_active_transformation_keys(
        self,
        request_context: RequestContext,
        timeout: float = 30,
    ) -> list[str]:
        """Get the active transformation keys.

        Args:
            request_context: The request context to use.
            timeout: The timeout to use.
        """
        ...


class GrpcWorkingSetClient(WorkingSetClient):
    """Class to call working set APIs remotely."""

    def __init__(self, stub: working_set_pb2_grpc.WorkingSetManagerStub):
        """Constructs a new working set client."""
        self.stub = stub

    @classmethod
    def create_for_endpoint(
        cls,
        endpoint: str,
        credentials: grpc.ChannelCredentials | None,
        options: client_options.OptionsList | None = None,
    ):
        """Constructs a new working set client from endpoint and credentials.

        Args:
            endpoint: The endpoint of the  working set  service.
            credentials: The credentials to use for the channel (optional)
            options: The options to use for the channel (optional)

        Returns:
            The client stub for the  working set
        """
        stub = setup_stub(endpoint, credentials, options=options)
        return cls(stub)

    def register_working_set(
        self,
        blobs: blob_names_pb2.Blobs,
        request_context: RequestContext,
        timeout: float = 30,
    ):
        """Register a working set.

        Args:
            blobs: The blobs to register.
            request_context: The request context to use.
            timeout: The timeout to use.
        """
        try:
            self.stub.RegisterWorkingSet(
                working_set_pb2.RegisterWorkingSetRequest(blobs=[blobs]),
                timeout=timeout,
                metadata=request_context.to_metadata(),
            )
        except grpc.RpcError as ex:
            status = ex.status()  # pylint: disable=no-member # type: ignore
            if status and status.code == grpc.StatusCode.UNAVAILABLE:
                # it is okay if the working set service is not available
                pass
            raise

    def create_ann_index_for_checkpoint(
        self,
        checkpoint_id: str,
        transformation_key: str,
        request_context: RequestContext,
        timeout: float = 30,
    ):
        """Create an ANN index for a checkpoint.

        Args:
            checkpoint_id: The checkpoint to create the index for.
            transformation_key: The transformation key to use.
            request_context: The request context to use.
            timeout: The timeout to use.
        """
        try:
            self.stub.CreateAnnIndexForCheckpoint(
                working_set_pb2.CreateAnnIndexForCheckpointRequest(
                    checkpoint_id=checkpoint_id,
                    transformation_key=transformation_key,
                ),
                timeout=timeout,
                metadata=request_context.to_metadata(),
            )
        except grpc.RpcError as ex:
            status = ex.status()  # pylint: disable=no-member # type: ignore
            if status and status.code == grpc.StatusCode.UNAVAILABLE:
                # it is okay if the working set service is not available
                pass
            raise

    def get_active_checkpoints(
        self,
        request_context: RequestContext,
        timeout: float = 30,
    ) -> list[str]:
        """Get the active checkpoints.

        Args:
            request_context: The request context to use.
            timeout: The timeout to use.
        """
        response = self.stub.GetActiveCheckpoints(
            working_set_pb2.GetActiveCheckpointsRequest(),
            timeout=timeout,
            metadata=request_context.to_metadata(),
        )
        return response.checkpoint_ids

    def get_active_transformation_keys(
        self,
        request_context: RequestContext,
        timeout: float = 30,
    ) -> list[str]:
        """Get the active transformation keys.

        Args:
            request_context: The request context to use.
            timeout: The timeout to use.
        """
        response = self.stub.GetActiveTransformationKeys(
            working_set_pb2.GetActiveTransformationKeysRequest(),
            timeout=timeout,
            metadata=request_context.to_metadata(),
        )
        return response.transformation_keys


class NullWorkingSetClient(WorkingSetClient):
    """A null working set client that does nothing."""

    def register_working_set(
        self,
        blobs: blob_names_pb2.Blobs,
        request_context: RequestContext,
        timeout: float = 30,
    ):
        """Register a working set.

        Args:
            blobs: The blobs to register.
            request_context: The request context to use.
            timeout: The timeout to use.
        """
        del blobs, request_context, timeout
        pass

    def create_ann_index_for_checkpoint(
        self,
        checkpoint_id: str,
        transformation_key: str,
        request_context: RequestContext,
        timeout: float = 30,
    ):
        """Create an ANN index for a checkpoint.

        Args:
            checkpoint_id: The checkpoint to create the index for.
            transformation_key: The transformation key to use.
            request_context: The request context to use.
            timeout: The timeout to use.
        """
        del checkpoint_id, transformation_key, request_context, timeout

    def get_active_checkpoints(
        self,
        request_context: RequestContext,
        timeout: float = 30,
    ):
        """Get the active checkpoints.

        Args:
            request_context: The request context to use.
            timeout: The timeout to use.
        """
        del request_context, timeout
        return []

    def get_active_transformation_keys(
        self,
        request_context: RequestContext,
        timeout: float = 30,
    ):
        """Get the active transformation keys.

        Args:
            request_context: The request context to use.
            timeout: The timeout to use.
        """
        del request_context, timeout
        return []


class WorkingSetClientConfigProtocol(typing.Protocol):
    """
    structually typed object that matches any class with the requisite props to configure
    a WorkingSetClient
    """

    working_set_endpoint: str | None
    client_mtls: typing.Optional[tls_config.ClientConfig]


def get_working_set_client(config: WorkingSetClientConfigProtocol) -> WorkingSetClient:
    """Returns a content manger client created using a compatible config object"""
    if config.working_set_endpoint is None:
        return NullWorkingSetClient()
    options = client_options.get_grpc_client_options(
        client_options.GrpcClientOptions(
            load_balancing="headless" in config.working_set_endpoint,
        )
    )
    return GrpcWorkingSetClient.create_for_endpoint(
        config.working_set_endpoint,
        credentials=tls_config.get_client_tls_creds(config.client_mtls),
        options=options,
    )
