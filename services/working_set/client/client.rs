use std::sync::Arc;
use std::time::Duration;

use async_lock::Mutex;
use async_trait::async_trait;
use grpc_client::create_channel;
use request_context::RequestContext;
use tonic::transport::ClientTlsConfig;
use tracing_tonic::client::TracingService;

pub mod working_set {
    tonic::include_proto!("working_set");
}

pub mod base {
    pub mod blob_names {
        tonic::include_proto!("base.blob_names");
    }
}

/// Trait for making gRPC calls to the WorkingSet service.
#[async_trait]
pub trait WorkingSetClient {
    /// Register a working set.
    async fn register_working_set(
        &self,
        request_context: &RequestContext,
        blobs: base::blob_names::Blobs,
        timeout: Duration,
    ) -> Result<(), tonic::Status>;

    /// Create an ANN index for a checkpoint.
    async fn create_ann_index_for_checkpoint(
        &self,
        request_context: &RequestContext,
        checkpoint_id: String,
        transformation_key: String,
        timeout: Duration,
    ) -> Result<(), tonic::Status>;

    async fn get_active_checkpoints(
        &self,
        request_context: &RequestContext,
        timeout: Duration,
    ) -> Result<Vec<String>, tonic::Status>;

    async fn get_active_transformation_keys(
        &self,
        request_context: &RequestContext,
        timeout: Duration,
    ) -> Result<Vec<String>, tonic::Status>;
}

/// Implementation of the WorkingSetClient trait that communicates with a real WorkingSet service.
pub struct WorkingSetClientImpl {
    endpoint: String,
    tls_config: Option<ClientTlsConfig>,
    client: Arc<
        Mutex<
            Option<
                working_set::working_set_manager_client::WorkingSetManagerClient<TracingService>,
            >,
        >,
    >,
}

impl WorkingSetClientImpl {
    /// Create a new WorkingSetClientImpl.
    pub fn new(endpoint: &str, tls_config: Option<ClientTlsConfig>) -> Self {
        Self {
            endpoint: endpoint.to_string(),
            tls_config,
            client: Arc::new(Mutex::new(None)),
        }
    }

    /// Get the client, setting it up if necessary.
    async fn get_client(
        &self,
    ) -> Result<
        working_set::working_set_manager_client::WorkingSetManagerClient<TracingService>,
        tonic::transport::Error,
    > {
        let mut m = self.client.lock().await;
        match m.as_ref() {
            None => {
                let channel =
                    create_channel(self.endpoint.to_string(), None, &self.tls_config).await?;
                let client =
                    working_set::working_set_manager_client::WorkingSetManagerClient::new(channel);
                *m = Some(client.clone());
                Ok(client)
            }
            Some(c) => Ok(c.clone()),
        }
    }
}

#[async_trait]
impl WorkingSetClient for WorkingSetClientImpl {
    /// Register a working set.
    async fn register_working_set(
        &self,
        request_context: &RequestContext,
        blobs: base::blob_names::Blobs,
        timeout: Duration,
    ) -> Result<(), tonic::Status> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("working set client to {} not ready: {}", self.endpoint, e);
            tonic::Status::unavailable("working set not ready")
        })?;

        let request = working_set::RegisterWorkingSetRequest { blobs: vec![blobs] };

        let mut tonic_request = tonic::Request::new(request);
        tonic_request.set_timeout(timeout);
        request_context.annotate(tonic_request.metadata_mut());

        let _response = client.register_working_set(tonic_request).await?;
        Ok(())
    }

    /// Create an ANN index for a checkpoint.
    async fn create_ann_index_for_checkpoint(
        &self,
        request_context: &RequestContext,
        checkpoint_id: String,
        transformation_key: String,
        timeout: Duration,
    ) -> Result<(), tonic::Status> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("working set client to {} not ready: {}", self.endpoint, e);
            tonic::Status::unavailable("working set not ready")
        })?;

        let request = working_set::CreateAnnIndexForCheckpointRequest {
            checkpoint_id,
            transformation_key,
        };

        let mut tonic_request = tonic::Request::new(request);
        tonic_request.set_timeout(timeout);
        request_context.annotate(tonic_request.metadata_mut());

        let _response = client
            .create_ann_index_for_checkpoint(tonic_request)
            .await?;
        Ok(())
    }

    async fn get_active_checkpoints(
        &self,
        request_context: &RequestContext,
        timeout: Duration,
    ) -> Result<Vec<String>, tonic::Status> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("working set client to {} not ready: {}", self.endpoint, e);
            tonic::Status::unavailable("working set not ready")
        })?;

        let request = working_set::GetActiveCheckpointsRequest {};

        let mut tonic_request = tonic::Request::new(request);
        tonic_request.set_timeout(timeout);
        request_context.annotate(tonic_request.metadata_mut());

        let response = client.get_active_checkpoints(tonic_request).await?;
        Ok(response.into_inner().checkpoint_ids)
    }

    async fn get_active_transformation_keys(
        &self,
        request_context: &RequestContext,
        timeout: Duration,
    ) -> Result<Vec<String>, tonic::Status> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("working set client to {} not ready: {}", self.endpoint, e);
            tonic::Status::unavailable("working set not ready")
        })?;

        let request = working_set::GetActiveTransformationKeysRequest {};

        let mut tonic_request = tonic::Request::new(request);
        tonic_request.set_timeout(timeout);
        request_context.annotate(tonic_request.metadata_mut());

        let response = client.get_active_transformation_keys(tonic_request).await?;
        Ok(response.into_inner().transformation_keys)
    }
}

/// A null implementation of the WorkingSetClient trait that does nothing, for testing.
pub struct NullWorkingSetClient;

impl NullWorkingSetClient {
    /// Create a new NullWorkingSetClient.
    pub fn new() -> Self {
        Self
    }
}

impl Default for NullWorkingSetClient {
    fn default() -> Self {
        Self::new()
    }
}

#[async_trait]
impl WorkingSetClient for NullWorkingSetClient {
    /// Register a working set.
    async fn register_working_set(
        &self,
        _request_context: &RequestContext,
        _blobs: base::blob_names::Blobs,
        _timeout: Duration,
    ) -> Result<(), tonic::Status> {
        // Do nothing
        Ok(())
    }

    /// Create an ANN index for a checkpoint.
    async fn create_ann_index_for_checkpoint(
        &self,
        _request_context: &RequestContext,
        _checkpoint_id: String,
        _transformation_key: String,
        _timeout: Duration,
    ) -> Result<(), tonic::Status> {
        // Do nothing
        Ok(())
    }

    async fn get_active_checkpoints(
        &self,
        _request_context: &RequestContext,
        _timeout: Duration,
    ) -> Result<Vec<String>, tonic::Status> {
        // Do nothing
        Ok(vec![])
    }

    async fn get_active_transformation_keys(
        &self,
        _request_context: &RequestContext,
        _timeout: Duration,
    ) -> Result<Vec<String>, tonic::Status> {
        // Do nothing
        Ok(vec![])
    }
}

/// A factory function to create a WorkingSetClient based on configuration.
pub fn create_working_set_client(
    endpoint: Option<String>,
    tls_config: Option<ClientTlsConfig>,
) -> Box<dyn WorkingSetClient + Send + Sync> {
    match endpoint {
        Some(endpoint) => Box::new(WorkingSetClientImpl::new(&endpoint, tls_config)),
        None => Box::new(NullWorkingSetClient::new()),
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use request_context::RequestId;
    use request_context::RequestSessionId;
    use request_context::RequestSource;

    #[tokio::test]
    async fn test_null_client() {
        let client = NullWorkingSetClient::new();
        let request_context = RequestContext::new(
            RequestId::create_random(),
            RequestSessionId::create_random(),
            RequestSource::Unknown,
            None,
        );

        let blobs = base::blob_names::Blobs {
            baseline_checkpoint_id: Some("test_checkpoint".to_string()),
            added: vec![],
            deleted: vec![],
        };

        // Test register_working_set
        let result = client
            .register_working_set(&request_context, blobs, Duration::from_secs(30))
            .await;
        assert!(result.is_ok());

        // Test create_ann_index_for_checkpoint
        let result = client
            .create_ann_index_for_checkpoint(
                &request_context,
                "test_checkpoint".to_string(),
                "test_transformation".to_string(),
                Duration::from_secs(30),
            )
            .await;
        assert!(result.is_ok());
    }
}
