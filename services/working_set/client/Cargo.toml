[package]
name = "working_set_client"
version = "0.1.0"
edition = "2021"

[lib]
name = "working_set_client"
path = "client.rs"

[dependencies]
async-lock = { workspace = true }
async-trait = { workspace = true }
grpc_client = { path = "../../lib/grpc/client" }
prost = { workspace = true }
prost-wkt = { workspace = true }
prost-wkt-types = { workspace = true }
tokio = { workspace = true }
tonic = { workspace = true }
tonic-build = { workspace = true }
tracing = { workspace = true }
tracing-tonic = { path = "../../../base/rust/tracing-tonic" }
request_context = { path = "../../lib/request_context" }

[build-dependencies]
tonic-build = { workspace = true }
prost-wkt-build = { workspace = true }
