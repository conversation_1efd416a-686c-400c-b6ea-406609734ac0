package main

import (
	"sort"
)

// LSHIndex implements a Locality Sensitive Hashing index
type LSHIndex struct {
	// Array of maps, one for each hash function in the minhash signature
	// Each map maps from hash value to a set of ann index IDs
	hashTable []map[int]map[string]struct{}
}

// LSHIndexSearchResult represents a search result from the LSH index
type LSHIndexSearchResult struct {
	Id         string
	MatchCount int
}

func NewLSHIndex() *LSHIndex {
	// Initialize an array of maps, one for each hash function
	hashTable := make([]map[int]map[string]struct{}, numMinHashFunctions)
	for i := range hashTable {
		hashTable[i] = make(map[int]map[string]struct{})
	}

	return &LSHIndex{
		hashTable: hashTable,
	}
}

// Adds each hash value from the given MinHash signature into the LSH index
func (idx *LSHIndex) AddAnnIndex(annIndexID string, signature []int) {
	if len(signature) > len(idx.hashTable) {
		panic("signature length exceeds hash table size")
	}

	// For each hash value in the signature, add annIndexID to the corresponding hash table
	for i := 0; i < len(signature); i++ {
		hashValue := signature[i]

		bucket, exists := idx.hashTable[i][hashValue]
		if !exists {
			bucket = make(map[string]struct{})
			idx.hashTable[i][hashValue] = bucket
		}

		bucket[annIndexID] = struct{}{}
	}
}

// Removes each hash value for the given annIndexID/signature from the LSH index
func (idx *LSHIndex) DeleteAnnIndex(annIndexID string, signature []int) {
	if len(signature) > len(idx.hashTable) {
		panic("signature length exceeds hash table size")
	}

	// For each hash value in the signature, remove the annIndexID from the corresponding hash table
	for i := 0; i < len(signature); i++ {
		hashValue := signature[i]

		if annIndexes, exists := idx.hashTable[i][hashValue]; exists {
			delete(annIndexes, annIndexID)

			// If the bucket is now empty, remove it from the hash table
			if len(annIndexes) == 0 {
				delete(idx.hashTable[i], hashValue)
			}
		}
	}
}

// Returns a list of LSHIndexSearchResult for indexes matching the given signature
// Match count is the number of hash values that are common between the given signature and
// the corresponding candidate.  The returned list is sorted by match count in descending order
func (idx *LSHIndex) FindCandidates(signature []int) []LSHIndexSearchResult {
	if len(signature) > len(idx.hashTable) {
		panic("signature length exceeds hash table size")
	}
	matchCounts := make(map[string]int)

	// For each hash value in the signature, find matching annIndexIDs
	for i := 0; i < len(signature); i++ {
		hashValue := signature[i]

		// If there are annIndexIDs for this hash value, increment their match counts
		if annIndexes, exists := idx.hashTable[i][hashValue]; exists {
			for id := range annIndexes {
				matchCounts[id]++
			}
		}
	}

	// Sort the candidates by match count in descending order
	candidates := make([]LSHIndexSearchResult, 0, len(matchCounts))
	for idxId, count := range matchCounts {
		candidates = append(candidates, LSHIndexSearchResult{
			Id:         idxId,
			MatchCount: count,
		})
	}
	sort.Slice(candidates, func(i, j int) bool {
		return candidates[i].MatchCount > candidates[j].MatchCount
	})

	return candidates
}

// Returns the number of unique ANN indexes that are indexed in this LSHIndex
func (idx *LSHIndex) NumAnnIndexes() int {
	// Create a set to track unique ANN index IDs
	uniqueIndexIDs := make(map[string]struct{})

	// Iterate through all hash tables and buckets to collect unique index IDs
	for _, hashMap := range idx.hashTable {
		for _, bucket := range hashMap {
			for indexID := range bucket {
				uniqueIndexIDs[indexID] = struct{}{}
			}
		}
	}

	return len(uniqueIndexIDs)
}
