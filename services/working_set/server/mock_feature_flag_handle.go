package main

import (
	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/stretchr/testify/mock"
)

// MockFeatureFlagHandle is a mock implementation of featureflags.FeatureFlagHandle
type MockFeatureFlagHandle struct {
	mock.Mock
}

// GetString implements featureflags.FeatureFlagHandle
func (m *MockFeatureFlagHandle) GetString(name string, defaultValue string) (string, error) {
	args := m.Called(name, defaultValue)
	return args.String(0), args.Error(1)
}

// GetBool implements featureflags.FeatureFlagHandle
func (m *MockFeatureFlagHandle) GetBool(name string, defaultValue bool) (bool, error) {
	args := m.Called(name, defaultValue)
	return args.Bool(0), args.Error(1)
}

// GetInt implements featureflags.FeatureFlagHandle
func (m *MockFeatureFlagHandle) GetInt(name string, defaultValue int) (int, error) {
	args := m.Called(name, defaultValue)
	return args.Int(0), args.Error(1)
}

// GetFloat implements featureflags.FeatureFlagHandle
func (m *MockFeatureFlagHandle) GetFloat(name string, defaultValue float64) (float64, error) {
	args := m.Called(name, defaultValue)
	return args.Get(0).(float64), args.Error(1)
}

// BindContext implements featureflags.FeatureFlagHandle
func (m *MockFeatureFlagHandle) BindContext(name string, value string) (featureflags.FeatureFlagHandle, error) {
	args := m.Called(name, value)
	return args.Get(0).(featureflags.FeatureFlagHandle), args.Error(1)
}
