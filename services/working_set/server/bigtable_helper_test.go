package main

import (
	"context"
	"testing"
	"time"

	fakebigtableproxy "github.com/augmentcode/augment/services/bigtable_proxy/client/fake_client"
	pb "github.com/augmentcode/augment/services/bigtable_proxy/proto"
	"github.com/stretchr/testify/assert"
)

func TestBigtableHelper(t *testing.T) {
	fakeBigtable := fakebigtableproxy.NewInMemoryBigtable([]string{pb.TableName_WORKING_SET.String()})

	ctx := context.Background()

	fakeRequestContextHelper := &FakeRequestContextHelper{}

	persistToBigtableFn := func() bool { return true } // persist to Bigtable
	bigtableHelper := NewBigtableHelper(ctx, fakeRequestContextHelper, fakeBigtable, persistToBigtableFn)

	tenantID := "test-tenant"
	checkpointID := "test-checkpoint"
	transformationKey := "test-transformation"
	timestamp := time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC)

	t.Run("WriteActiveCheckpoint", func(t *testing.T) {
		err := bigtableHelper.WriteActiveCheckpoint(tenantID, checkpointID, timestamp)
		assert.NoError(t, err)
	})

	t.Run("GetAllActiveCheckpoints", func(t *testing.T) {
		checkpoints, err := bigtableHelper.GetAllActiveCheckpoints(tenantID)
		assert.NoError(t, err)
		assert.Len(t, checkpoints, 1)
		assert.Equal(t, checkpointID, checkpoints[0].CheckpointID)
		assert.Equal(t, timestamp.UnixNano(), checkpoints[0].Timestamp.UnixNano())
	})

	t.Run("DeleteActiveCheckpoint", func(t *testing.T) {
		err := bigtableHelper.DeleteActiveCheckpoint(tenantID, checkpointID)
		assert.NoError(t, err)

		checkpointsAfter, err := bigtableHelper.GetAllActiveCheckpoints(tenantID)
		assert.NoError(t, err)
		assert.Len(t, checkpointsAfter, 0, "The active checkpoint should be deleted")
	})

	t.Run("WritePendingIndex", func(t *testing.T) {
		err := bigtableHelper.WritePendingIndex(tenantID, checkpointID, transformationKey, timestamp)
		assert.NoError(t, err)
	})

	t.Run("GetAllPendingIndexes", func(t *testing.T) {
		indexes, err := bigtableHelper.GetAllPendingIndexes(tenantID)
		assert.NoError(t, err)
		assert.Len(t, indexes, 1)
		assert.Equal(t, checkpointID, indexes[0].CheckpointID)
		assert.Equal(t, transformationKey, indexes[0].TransformationKey)
		assert.Equal(t, timestamp.UnixNano(), indexes[0].Timestamp.UnixNano())
	})

	t.Run("DeletePendingIndex", func(t *testing.T) {
		err := bigtableHelper.DeletePendingIndex(tenantID, checkpointID, transformationKey)
		assert.NoError(t, err)

		indexesAfter, err := bigtableHelper.GetAllPendingIndexes(tenantID)
		assert.NoError(t, err)
		assert.Len(t, indexesAfter, 0, "The pending index should be deleted")
	})

	t.Run("WriteActiveTransformationKey", func(t *testing.T) {
		err := bigtableHelper.WriteActiveTransformationKey(tenantID, transformationKey, timestamp)
		assert.NoError(t, err)
	})

	t.Run("GetAllActiveTransformationKeys", func(t *testing.T) {
		transformationKeys, err := bigtableHelper.GetAllActiveTransformationKeys(tenantID)
		assert.NoError(t, err)
		assert.Len(t, transformationKeys, 1)
		assert.Equal(t, transformationKey, transformationKeys[0].TransformationKey)
		assert.Equal(t, timestamp.UnixNano(), transformationKeys[0].Timestamp.UnixNano())
	})

	t.Run("DeleteActiveTransformationKey", func(t *testing.T) {
		err := bigtableHelper.DeleteActiveTransformationKey(tenantID, transformationKey)
		assert.NoError(t, err)

		transformationKeysAfter, err := bigtableHelper.GetAllActiveTransformationKeys(tenantID)
		assert.NoError(t, err)
		assert.Len(t, transformationKeysAfter, 0, "The active transformation key should be deleted")
	})
}

func TestBigtableHelperWithPersistDisabled(t *testing.T) {
	fakeBigtable := fakebigtableproxy.NewInMemoryBigtable([]string{pb.TableName_WORKING_SET.String()})

	ctx := context.Background()

	fakeRequestContextHelper := &FakeRequestContextHelper{}

	persistToBigtableFn := func() bool { return false } // don't persist to Bigtable
	bigtableHelper := NewBigtableHelper(ctx, fakeRequestContextHelper, fakeBigtable, persistToBigtableFn)

	tenantID := "test-tenant"
	checkpointID := "test-checkpoint"
	transformationKey := "test-transformation"
	timestamp := time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC)

	// Operations should be no-ops when persistToBigtable is false
	t.Run("WriteActiveCheckpoint with persist disabled", func(t *testing.T) {
		err := bigtableHelper.WriteActiveCheckpoint(tenantID, checkpointID, timestamp)
		assert.NoError(t, err)

		// Verify no data was written
		checkpoints, err := bigtableHelper.GetAllActiveCheckpoints(tenantID)
		assert.NoError(t, err)
		assert.Len(t, checkpoints, 0, "No checkpoints should be written when persistToBigtable is false")
	})

	t.Run("WritePendingIndex with persist disabled", func(t *testing.T) {
		err := bigtableHelper.WritePendingIndex(tenantID, checkpointID, transformationKey, timestamp)
		assert.NoError(t, err)

		// Verify no data was written
		indexes, err := bigtableHelper.GetAllPendingIndexes(tenantID)
		assert.NoError(t, err)
		assert.Len(t, indexes, 0, "No pending indexes should be written when persistToBigtable is false")
	})

	t.Run("WriteActiveTransformationKey with persist disabled", func(t *testing.T) {
		err := bigtableHelper.WriteActiveTransformationKey(tenantID, transformationKey, timestamp)
		assert.NoError(t, err)

		// Verify no data was written
		transformationKeys, err := bigtableHelper.GetAllActiveTransformationKeys(tenantID)
		assert.NoError(t, err)
		assert.Len(t, transformationKeys, 0, "No transformation keys should be written when persistToBigtable is false")
	})
}

func TestBigtableHelperMultipleEntries(t *testing.T) {
	fakeBigtable := fakebigtableproxy.NewInMemoryBigtable([]string{pb.TableName_WORKING_SET.String()})

	ctx := context.Background()

	fakeRequestContextHelper := &FakeRequestContextHelper{}

	persistToBigtableFn := func() bool { return true } // persist to Bigtable
	bigtableHelper := NewBigtableHelper(ctx, fakeRequestContextHelper, fakeBigtable, persistToBigtableFn)

	tenantID := "test-tenant"
	checkpointIDs := []string{"checkpoint1", "checkpoint2", "checkpoint3"}
	transformationKeys := []string{"transform1", "transform2", "transform3"}
	timestamps := []time.Time{
		time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC),
		time.Date(2023, 1, 2, 12, 0, 0, 0, time.UTC),
		time.Date(2023, 1, 3, 12, 0, 0, 0, time.UTC),
	}

	// Write multiple active checkpoints
	for i, checkpointID := range checkpointIDs {
		err := bigtableHelper.WriteActiveCheckpoint(tenantID, checkpointID, timestamps[i])
		assert.NoError(t, err)
	}

	// Write multiple pending indexes
	for i, checkpointID := range checkpointIDs {
		err := bigtableHelper.WritePendingIndex(tenantID, checkpointID, transformationKeys[i], timestamps[i])
		assert.NoError(t, err)
	}

	// Write multiple active transformation keys
	for i, transformationKey := range transformationKeys {
		err := bigtableHelper.WriteActiveTransformationKey(tenantID, transformationKey, timestamps[i])
		assert.NoError(t, err)
	}

	t.Run("GetAllActiveCheckpoints with multiple entries", func(t *testing.T) {
		checkpoints, err := bigtableHelper.GetAllActiveCheckpoints(tenantID)
		assert.NoError(t, err)
		assert.Len(t, checkpoints, len(checkpointIDs))

		checkpointMap := make(map[string]time.Time)
		for _, cp := range checkpoints {
			checkpointMap[cp.CheckpointID] = cp.Timestamp
		}

		for i, checkpointID := range checkpointIDs {
			timestamp, exists := checkpointMap[checkpointID]
			assert.True(t, exists)
			assert.Equal(t, timestamps[i].UnixNano(), timestamp.UnixNano())
		}
	})

	t.Run("GetAllPendingIndexes with multiple entries", func(t *testing.T) {
		indexes, err := bigtableHelper.GetAllPendingIndexes(tenantID)
		assert.NoError(t, err)
		assert.Len(t, indexes, len(checkpointIDs))

		type indexInfo struct {
			transformationKey string
			timestamp         time.Time
		}
		indexMap := make(map[string]indexInfo)
		for _, idx := range indexes {
			indexMap[idx.CheckpointID] = indexInfo{
				transformationKey: idx.TransformationKey,
				timestamp:         idx.Timestamp,
			}
		}

		for i, checkpointID := range checkpointIDs {
			info, exists := indexMap[checkpointID]
			assert.True(t, exists)
			assert.Equal(t, transformationKeys[i], info.transformationKey)
			assert.Equal(t, timestamps[i].UnixNano(), info.timestamp.UnixNano())
		}
	})

	t.Run("Delete one checkpoint", func(t *testing.T) {
		// Verify all checkpoints exist
		checkpointsBefore, err := bigtableHelper.GetAllActiveCheckpoints(tenantID)
		assert.NoError(t, err)
		assert.Len(t, checkpointsBefore, len(checkpointIDs))

		// Delete the first checkpoint
		err = bigtableHelper.DeleteActiveCheckpoint(tenantID, checkpointIDs[0])
		assert.NoError(t, err)

		// Verify the first checkpoint is deleted
		checkpointsAfter, err := bigtableHelper.GetAllActiveCheckpoints(tenantID)
		assert.NoError(t, err)
		assert.Len(t, checkpointsAfter, len(checkpointIDs)-1, "One checkpoint should be deleted")

		// Verify the deleted checkpoint is not in the results
		for _, cp := range checkpointsAfter {
			assert.NotEqual(t, checkpointIDs[0], cp.CheckpointID, "The deleted checkpoint should not be present")
		}

		// Verify the remaining checkpoints are still there
		checkpointMap := make(map[string]bool)
		for _, cp := range checkpointsAfter {
			checkpointMap[cp.CheckpointID] = true
		}
		for i := 1; i < len(checkpointIDs); i++ {
			_, exists := checkpointMap[checkpointIDs[i]]
			assert.True(t, exists, "Checkpoint %s should still exist", checkpointIDs[i])
		}
	})

	t.Run("Delete one pending index", func(t *testing.T) {
		// Verify all pending indexes exist
		indexesBefore, err := bigtableHelper.GetAllPendingIndexes(tenantID)
		assert.NoError(t, err)
		assert.Len(t, indexesBefore, len(checkpointIDs))

		// Delete the second pending index
		err = bigtableHelper.DeletePendingIndex(tenantID, checkpointIDs[1], transformationKeys[1])
		assert.NoError(t, err)

		// Verify the second pending index is deleted
		indexesAfter, err := bigtableHelper.GetAllPendingIndexes(tenantID)
		assert.NoError(t, err)
		assert.Len(t, indexesAfter, len(checkpointIDs)-1, "One pending index should be deleted")

		type indexInfo struct {
			transformationKey string
			timestamp         time.Time
		}
		indexMap := make(map[string]indexInfo)
		for _, idx := range indexesAfter {
			indexMap[idx.CheckpointID] = indexInfo{
				transformationKey: idx.TransformationKey,
				timestamp:         idx.Timestamp,
			}
		}

		// Verify the deleted index is not in the results
		_, exists := indexMap[checkpointIDs[1]]
		assert.False(t, exists, "The deleted pending index should not be present")

		// Verify the remaining indexes are still there
		for i, checkpointID := range checkpointIDs {
			if i != 1 { // Skip the deleted index
				info, exists := indexMap[checkpointID]
				assert.True(t, exists, "Pending index for checkpoint %s should still exist", checkpointID)
				assert.Equal(t, transformationKeys[i], info.transformationKey)
				assert.Equal(t, timestamps[i].UnixNano(), info.timestamp.UnixNano())
			}
		}
	})

	t.Run("GetAllActiveTransformationKeys with multiple entries", func(t *testing.T) {
		transformationKeyEntries, err := bigtableHelper.GetAllActiveTransformationKeys(tenantID)
		assert.NoError(t, err)
		assert.Len(t, transformationKeyEntries, len(transformationKeys))

		transformationKeyMap := make(map[string]time.Time)
		for _, entry := range transformationKeyEntries {
			transformationKeyMap[entry.TransformationKey] = entry.Timestamp
		}

		for i, transformationKey := range transformationKeys {
			timestamp, exists := transformationKeyMap[transformationKey]
			assert.True(t, exists)
			assert.Equal(t, timestamps[i].UnixNano(), timestamp.UnixNano())
		}
	})

	t.Run("Delete one active transformation key", func(t *testing.T) {
		// Verify all transformation keys exist
		transformationKeysBefore, err := bigtableHelper.GetAllActiveTransformationKeys(tenantID)
		assert.NoError(t, err)
		assert.Len(t, transformationKeysBefore, len(transformationKeys))

		// Delete the second transformation key
		err = bigtableHelper.DeleteActiveTransformationKey(tenantID, transformationKeys[1])
		assert.NoError(t, err)

		// Verify the second transformation key is deleted
		transformationKeysAfter, err := bigtableHelper.GetAllActiveTransformationKeys(tenantID)
		assert.NoError(t, err)
		assert.Len(t, transformationKeysAfter, len(transformationKeys)-1, "One transformation key should be deleted")

		transformationKeyMap := make(map[string]bool)
		for _, entry := range transformationKeysAfter {
			transformationKeyMap[entry.TransformationKey] = true
		}

		// Verify the deleted transformation key is not in the results
		_, exists := transformationKeyMap[transformationKeys[1]]
		assert.False(t, exists, "The deleted transformation key should not be present")

		// Verify the remaining transformation keys are still there
		for i, transformationKey := range transformationKeys {
			if i != 1 { // Skip the deleted transformation key
				_, exists := transformationKeyMap[transformationKey]
				assert.True(t, exists, "Transformation key %s should still exist", transformationKey)
			}
		}
	})
}
