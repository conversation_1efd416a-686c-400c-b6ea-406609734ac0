package main

import (
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"testing"

	blob_names "github.com/augmentcode/augment/base/blob_names"
)

func TestIsPrime(t *testing.T) {
	testCases := []struct {
		num      int
		expected bool
	}{
		{1, false},
		{2, true},
		{3, true},
		{4, false},
		{5, true},
		{6, false},
		{7, true},
		{8, false},
		{9, false},
		{10, false},
		{11, true},
		{97, true},
		{100, false},
		{101, true},
		{169, false}, // 13^2
	}

	for _, tc := range testCases {
		result := isPrime(tc.num)
		if result != tc.expected {
			t.Errorf("isPrime(%d) = %v; expected %v", tc.num, result, tc.expected)
		}
	}
}

func TestGeneratePrimes(t *testing.T) {
	primes0 := generatePrimes(0)
	if len(primes0) != 0 {
		t.<PERSON><PERSON><PERSON>("Expected 0 primes, got %d", len(primes0))
	}

	primes10 := generatePrimes(10)
	expected10 := []int{2, 3, 5, 7, 11, 13, 17, 19, 23, 29}
	if len(primes10) != 10 {
		t.Errorf("Expected 10 primes, got %d", len(primes10))
	}
	for i, p := range expected10 {
		if primes10[i] != p {
			t.Errorf("Expected prime at index %d to be %d, got %d", i, p, primes10[i])
		}
	}

	primes20 := generatePrimes(20)
	if len(primes20) != 20 {
		t.Errorf("Expected 20 primes, got %d", len(primes20))
	}
	// Check that the first 10 match our expected list
	for i, p := range expected10 {
		if primes20[i] != p {
			t.Errorf("Expected prime at index %d to be %d, got %d", i, p, primes20[i])
		}
	}
	// Check that all numbers in the list are prime
	for i, p := range primes20 {
		if !isPrime(p) {
			t.Errorf("Number at index %d (%d) is not prime", i, p)
		}
	}
}

// generateBlobName creates a 32-byte blob name (SHA-256 hash)
func generateBlobName(seed int) blob_names.BlobName {
	input := fmt.Sprintf("blob-seed-%d", seed)
	hash := sha256.Sum256([]byte(input))
	return blob_names.BlobName(hex.EncodeToString(hash[:]))
}

// calculateSimilarity computes the similarity between two signatures
func calculateSimilarity(sig1, sig2 []int) float64 {
	if len(sig1) != len(sig2) {
		return 0.0
	}

	sameValues := 0
	for i := range sig1 {
		if sig1[i] == sig2[i] {
			sameValues++
		}
	}

	return float64(sameValues) / float64(len(sig1))
}

func TestComputeMinHashSignature(t *testing.T) {
	// Generate 15,000 unique blob names
	allBlobNames := make([]blob_names.BlobName, 15000)
	for i := range allBlobNames {
		allBlobNames[i] = generateBlobName(i)
	}

	// Create checkpoint 1 with 10,000 blob names
	blobNames1 := allBlobNames[:10000]

	// Create checkpoint 2 with 9,500 same blob names as checkpoint 1 and 500 different ones
	blobNames2 := make([]blob_names.BlobName, 10000)
	copy(blobNames2, blobNames1[:9500])
	// Add 500 different blob names (from the remaining 5,000)
	copy(blobNames2[9500:], allBlobNames[10000:10500])

	// Create checkpoint 3 with 5,000 same blob names as checkpoint 1 and 5,000 different ones
	blobNames3 := make([]blob_names.BlobName, 10000)
	copy(blobNames3, blobNames1[:5000])
	// Add 5,000 different blob names (from the remaining 10,000)
	copy(blobNames3[5000:], allBlobNames[10000:])

	// Test case 1: Checkpoints with highly similar blob sets should have similar signatures
	t.Run("Similar blob sets produce similar signatures", func(t *testing.T) {
		signature1 := computeMinHashSignature("checkpoint1", blobNames1)
		signature2 := computeMinHashSignature("checkpoint2", blobNames2)

		// Calculate similarity between signatures
		similarity := calculateSimilarity(signature1, signature2)
		t.Logf("Similarity between checkpoint 1 and 2: %.4f", similarity)

		// With 95% of blob names being the same, we expect high similarity
		// The similarity should be close to 0.95 but not exactly due to the probabilistic nature of MinHash
		if similarity < 0.8 {
			t.Errorf("Expected high similarity (>0.8) for 95%% similar blob sets, but got %.4f", similarity)
		}
	})

	// Test case 2: Checkpoints with significantly different blob sets should have different signatures
	t.Run("Different blob sets produce different signatures", func(t *testing.T) {
		signature1 := computeMinHashSignature("checkpoint1", blobNames1)
		signature3 := computeMinHashSignature("checkpoint3", blobNames3)

		// Calculate similarity between signatures
		similarity := calculateSimilarity(signature1, signature3)
		t.Logf("Similarity between checkpoint 1 and 3: %.4f", similarity)

		// With 50% of blob names being the same, we expect lower similarity
		// The similarity should be close to 0.5 but not exactly due to the probabilistic nature of MinHash
		if similarity > 0.7 {
			t.Errorf("Expected lower similarity (<0.7) for 50%% similar blob sets, but got %.4f", similarity)
		}

		// But the similarity should still be significant since 50% of blob names are the same
		if similarity < 0.3 {
			t.Errorf("Expected some similarity (>0.3) for 50%% similar blob sets, but got %.4f", similarity)
		}
	})
}
