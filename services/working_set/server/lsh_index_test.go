package main

import (
	"testing"
)

func TestLSHIndex(t *testing.T) {
	index := NewLSHIndex()

	// Create three indexs with different signatures
	// Two of them will be similar, one will be completely different
	signature1 := make([]int, 100)
	signature2 := make([]int, 100)
	for i := 0; i < 100; i++ {
		signature1[i] = i % 50
		signature2[i] = i % 50
	}

	// Make a few differences to ensure they're not identical
	// but still similar enough to be detected as similar
	signature2[10] = 999
	signature2[20] = 998
	signature2[30] = 997

	// Create a completely different signature for index3
	signature3 := make([]int, 100)
	for i := 0; i < 100; i++ {
		signature3[i] = 500 + (i % 50)
	}

	// Test 0: NumAnnIndexes should return 0 for an empty index
	if count := index.NumAnnIndexes(); count != 0 {
		t.Errorf("Expected NumAnnIndexes to return 0 for an empty index, but got %d", count)
	}

	index.AddAnnIndex("index1", signature1)

	// Test 0.1: NumAnnIndexes should return 1 after adding one index
	if count := index.NumAnnIndexes(); count != 1 {
		t.<PERSON><PERSON><PERSON>("Expected NumAnnIndexes to return 1 after adding one index, but got %d", count)
	}

	index.AddAnnIndex("index2", signature2)
	index.AddAnnIndex("index3", signature3)

	// Test 0.2: NumAnnIndexes should return 3 after adding three indexes
	if count := index.NumAnnIndexes(); count != 3 {
		t.Errorf("Expected NumAnnIndexes to return 3 after adding three indexes, but got %d", count)
	}

	// Test 1: FindCandidates for checkpoint1 should return index1
	candidates1 := index.FindCandidates(signature1)

	// Verify that index2 is in the candidates and index3 is not
	found2 := false
	found3 := false
	for _, candidate := range candidates1 {
		if candidate.Id == "index2" {
			found2 = true
		}
		if candidate.Id == "index3" {
			found3 = true
		}
	}

	if !found2 {
		t.Errorf("Expected index2 to be a candidate for index1, but it was not found")
	}

	if found3 {
		t.Errorf("Did not expect index3 to be a candidate for index1, but it was found")
	}

	// Test 2: DeleteAnnIndex should remove the index from the LSH index
	index.DeleteAnnIndex("index2", signature2)

	// After deletion, index2 should no longer be a candidate for index1
	candidatesAfterDelete := index.FindCandidates(signature1)

	found2AfterDelete := false
	found3AfterDelete := false
	for _, candidate := range candidatesAfterDelete {
		if candidate.Id == "index2" {
			found2AfterDelete = true
		}
		if candidate.Id == "index3" {
			found3AfterDelete = true
		}
	}

	if found2AfterDelete {
		t.Errorf("index2 should have been deleted, but it was still found as a candidate")
	}

	// Verify index3 is still not a candidate
	if found3AfterDelete {
		t.Errorf("Did not expect index3 to be a candidate for index1, but it was found")
	}

	// Test 3: NumAnnIndexes should return 2 after deleting one index
	if count := index.NumAnnIndexes(); count != 2 {
		t.Errorf("Expected NumAnnIndexes to return 2 after deleting one index, but got %d", count)
	}

	// Delete the remaining indexes
	index.DeleteAnnIndex("index1", signature1)
	index.DeleteAnnIndex("index3", signature3)

	// Test 4: NumAnnIndexes should return 0 after deleting all indexes
	if count := index.NumAnnIndexes(); count != 0 {
		t.Errorf("Expected NumAnnIndexes to return 0 after deleting all indexes, but got %d", count)
	}
}
