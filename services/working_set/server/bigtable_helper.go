package main

import (
	"context"
	"encoding/binary"
	"fmt"
	"strings"
	"time"

	googlepb "cloud.google.com/go/bigtable/apiv2/bigtablepb"
	bigtableproxy "github.com/augmentcode/augment/services/bigtable_proxy/client"
	pb "github.com/augmentcode/augment/services/bigtable_proxy/proto"
	"github.com/rs/zerolog/log"
)

type BigtableHelper interface {
	DeleteActiveCheckpoint(tenantID string, checkpointID string) error
	GetAllActiveCheckpoints(tenantID string) ([]struct {
		CheckpointID string
		Timestamp    time.Time
	}, error)
	WriteActiveCheckpoint(tenantID string, checkpointID string, timestamp time.Time) error

	DeletePendingIndex(tenantID string, checkpointID string, transformationKey string) error
	GetAllPendingIndexes(tenantID string) ([]struct {
		CheckpointID      string
		TransformationKey string
		Timestamp         time.Time
	}, error)
	WritePendingIndex(tenantID string, checkpointID string, transformationKey string, timestamp time.Time) error

	DeleteActiveTransformationKey(tenantID string, transformationKey string) error
	GetAllActiveTransformationKeys(tenantID string) ([]struct {
		TransformationKey string
		Timestamp         time.Time
	}, error)
	WriteActiveTransformationKey(tenantID string, transformationKey string, timestamp time.Time) error
}

// PersistToBigtableFn is a function wrapper that returns whether to persist to Bigtable
type PersistToBigtableFn func() bool

type bigtableHelper struct {
	ctx                  context.Context
	client               bigtableproxy.BigtableProxyClient
	requestContextHelper RequestContextHelper
	persistToBigtable    PersistToBigtableFn
}

func NewBigtableHelper(ctx context.Context, requestContextHelper RequestContextHelper, client bigtableproxy.BigtableProxyClient, persistToBigtable PersistToBigtableFn) BigtableHelper {
	log.Info().Msgf("Configured with PersistToBigtable: %v", persistToBigtable())

	return &bigtableHelper{
		ctx:                  ctx,
		client:               client,
		requestContextHelper: requestContextHelper,
		persistToBigtable:    persistToBigtable,
	}
}

// makeTimestampSetCellMutation creates a SetCell mutation for a timestamp value
func makeTimestampSetCellMutation(timestamp time.Time) *googlepb.Mutation {
	timestampBytes := make([]byte, 8)
	binary.BigEndian.PutUint64(timestampBytes, uint64(timestamp.UnixNano()))

	return &googlepb.Mutation{
		Mutation: &googlepb.Mutation_SetCell_{
			SetCell: &googlepb.Mutation_SetCell{
				FamilyName:      "State",
				ColumnQualifier: []byte("Timestamp"),
				TimestampMicros: -1, // Use server timestamp
				Value:           timestampBytes,
			},
		},
	}
}

// makeDeleteRowMutation creates a DeleteFromRow mutation
func makeDeleteRowMutation() *googlepb.Mutation {
	return &googlepb.Mutation{
		Mutation: &googlepb.Mutation_DeleteFromRow_{
			DeleteFromRow: &googlepb.Mutation_DeleteFromRow{},
		},
	}
}

// makeMutateRowsEntry creates a MutateRowsRequest_Entry with the given row key and mutation
func makeMutateRowsEntry(rowKey string, mutation *googlepb.Mutation) *googlepb.MutateRowsRequest_Entry {
	return &googlepb.MutateRowsRequest_Entry{
		RowKey:    []byte(rowKey),
		Mutations: []*googlepb.Mutation{mutation},
	}
}

// performMutation performs a mutation operation on the bigtable
func (h *bigtableHelper) performMutation(tenantID string, entry *googlepb.MutateRowsRequest_Entry, operationDesc string) error {
	requestContext, err := h.requestContextHelper.GetBackgroundRequestContext(tenantID)
	if err != nil {
		log.Error().Msgf("Failed to get request context: %v", err)
		return err
	}

	_, err = h.client.MutateRows(
		h.ctx,
		tenantID,
		pb.TableName_WORKING_SET,
		[]*googlepb.MutateRowsRequest_Entry{entry},
		requestContext,
	)
	if err != nil {
		return fmt.Errorf("failed to %s: %w", operationDesc, err)
	}

	return nil
}

// readRowsWithPrefix reads all rows with the given prefix from the bigtable
func (h *bigtableHelper) readRowsWithPrefix(tenantID string, prefix string, operationDesc string) ([]*bigtableproxy.Row, error) {
	requestContext, err := h.requestContextHelper.GetBackgroundRequestContext(tenantID)
	if err != nil {
		log.Error().Msgf("Failed to get request context: %v", err)
		return nil, err
	}

	// Create a RowSet that matches all rows with the given prefix
	prefixBytes := []byte(prefix)
	endBytes := append([]byte(prefix), 0xFF)
	rowRange := &googlepb.RowRange{
		StartKey: &googlepb.RowRange_StartKeyClosed{StartKeyClosed: prefixBytes},
		EndKey:   &googlepb.RowRange_EndKeyClosed{EndKeyClosed: endBytes},
	}
	rowSet := &googlepb.RowSet{
		RowRanges: []*googlepb.RowRange{rowRange},
	}

	rows, err := h.client.ReadRows(
		h.ctx,
		tenantID,
		pb.TableName_WORKING_SET,
		rowSet,
		&googlepb.RowFilter{
			Filter: &googlepb.RowFilter_CellsPerColumnLimitFilter{
				CellsPerColumnLimitFilter: 1,
			},
		},
		0, // No row limit
		requestContext,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to %s: %w", operationDesc, err)
	}

	return rows, nil
}

// getActiveCheckpointRowKey returns the row key for an active checkpoint.
func getActiveCheckpointRowKey(checkpointID string) string {
	return fmt.Sprintf("activeCheckpoint#%s", checkpointID)
}

// WriteActiveCheckpoint writes an active checkpoint row (either creates it or updates it)
func (h *bigtableHelper) WriteActiveCheckpoint(tenantID string, checkpointID string, timestamp time.Time) error {
	if !h.persistToBigtable() {
		return nil
	}
	rowKey := getActiveCheckpointRowKey(checkpointID)
	mutation := makeTimestampSetCellMutation(timestamp)
	entry := makeMutateRowsEntry(rowKey, mutation)
	return h.performMutation(tenantID, entry, "write active checkpoint")
}

// DeleteActiveCheckpoint deletes an active checkpoint row
func (h *bigtableHelper) DeleteActiveCheckpoint(tenantID string, checkpointID string) error {
	if !h.persistToBigtable() {
		return nil
	}
	rowKey := getActiveCheckpointRowKey(checkpointID)
	mutation := makeDeleteRowMutation()
	entry := makeMutateRowsEntry(rowKey, mutation)
	return h.performMutation(tenantID, entry, "delete active checkpoint")
}

// getPendingIndexRowKey returns the row key for a pending index.
func getPendingIndexRowKey(checkpointID string, transformationKey string) string {
	return fmt.Sprintf("pendingIndex#%s#%s", checkpointID, transformationKey)
}

// WritePendingIndex writes a pending index record for a tenant.
func (h *bigtableHelper) WritePendingIndex(tenantID string, checkpointID string, transformationKey string, timestamp time.Time) error {
	if !h.persistToBigtable() {
		return nil
	}
	rowKey := getPendingIndexRowKey(checkpointID, transformationKey)
	mutation := makeTimestampSetCellMutation(timestamp)
	entry := makeMutateRowsEntry(rowKey, mutation)
	return h.performMutation(tenantID, entry, "write pending index")
}

// DeletePendingIndex deletes a pending index record for a tenant.
func (h *bigtableHelper) DeletePendingIndex(tenantID string, checkpointID string, transformationKey string) error {
	if !h.persistToBigtable() {
		return nil
	}
	rowKey := getPendingIndexRowKey(checkpointID, transformationKey)
	mutation := makeDeleteRowMutation()
	entry := makeMutateRowsEntry(rowKey, mutation)
	return h.performMutation(tenantID, entry, "delete pending index")
}

// getActiveTransformationKeyRowKey returns the row key for an active transformation key.
func getActiveTransformationKeyRowKey(transformationKey string) string {
	return fmt.Sprintf("activeTransformationKey#%s", transformationKey)
}

// WriteActiveTransformationKey writes an active transformation key row (either creates it or updates it)
func (h *bigtableHelper) WriteActiveTransformationKey(tenantID string, transformationKey string, timestamp time.Time) error {
	if !h.persistToBigtable() {
		return nil
	}
	rowKey := getActiveTransformationKeyRowKey(transformationKey)
	mutation := makeTimestampSetCellMutation(timestamp)
	entry := makeMutateRowsEntry(rowKey, mutation)
	return h.performMutation(tenantID, entry, "write active transformation key")
}

// DeleteActiveTransformationKey deletes an active transformation key row
func (h *bigtableHelper) DeleteActiveTransformationKey(tenantID string, transformationKey string) error {
	if !h.persistToBigtable() {
		return nil
	}
	rowKey := getActiveTransformationKeyRowKey(transformationKey)
	mutation := makeDeleteRowMutation()
	entry := makeMutateRowsEntry(rowKey, mutation)
	return h.performMutation(tenantID, entry, "delete active transformation key")
}

// getTimestampFromCell extracts a timestamp from a cell
func getTimestampFromCell(row *bigtableproxy.Row, entityDesc string) (time.Time, error) {
	if len(row.Cells) < 1 {
		return time.Time{}, fmt.Errorf("expected at least one cell for %s, got %d", entityDesc, len(row.Cells))
	}
	// The result will have multiple cells but we only care about the latest one. Log a warning if we end up
	// having too many cells in the result - we can implement a filter to only get the top result, but this
	// will require fixing the fake bigtable proxy client to support that filter.
	if len(row.Cells) > 1000 {
		log.Warn().Msgf("getTimestampFromCell: got more than most 1000 cells for %s, got %d", entityDesc, len(row.Cells))
	}
	cell := row.Cells[0]
	if cell.FamilyName != "State" || string(cell.Qualifier) != "Timestamp" {
		return time.Time{}, fmt.Errorf("unexpected cell family/qualifier for %s: %s/%s",
			entityDesc, cell.FamilyName, string(cell.Qualifier))
	}

	timestampNano := binary.BigEndian.Uint64(cell.Value)
	return time.Unix(0, int64(timestampNano)), nil
}

// GetAllActiveCheckpoints retrieves all active checkpoints for a tenant.
func (h *bigtableHelper) GetAllActiveCheckpoints(tenantID string) ([]struct {
	CheckpointID string
	Timestamp    time.Time
}, error,
) {
	prefix := "activeCheckpoint#"
	rows, err := h.readRowsWithPrefix(tenantID, prefix, "read active checkpoints")
	if err != nil {
		return nil, err
	}

	var results []struct {
		CheckpointID string
		Timestamp    time.Time
	}

	for _, row := range rows {
		rowKey := string(row.RowKey)
		checkpointID := rowKey[len(prefix):]

		timestamp, err := getTimestampFromCell(row, fmt.Sprintf("active checkpoint %s", checkpointID))
		if err != nil {
			return nil, err
		}

		results = append(results, struct {
			CheckpointID string
			Timestamp    time.Time
		}{
			CheckpointID: checkpointID,
			Timestamp:    timestamp,
		})
	}

	return results, nil
}

// GetAllPendingIndexes retrieves all pending indexes for a tenant.
func (h *bigtableHelper) GetAllPendingIndexes(tenantID string) ([]struct {
	CheckpointID      string
	TransformationKey string
	Timestamp         time.Time
}, error,
) {
	prefix := "pendingIndex#"
	rows, err := h.readRowsWithPrefix(tenantID, prefix, "read pending indexes")
	if err != nil {
		return nil, err
	}

	var results []struct {
		CheckpointID      string
		TransformationKey string
		Timestamp         time.Time
	}

	for _, row := range rows {
		rowKey := string(row.RowKey)
		// Extract checkpointID and transformationKey from the row key
		// Format: pendingIndex#<checkpointID>#<transformationKey>
		parts := strings.Split(rowKey[len(prefix):], "#")
		if len(parts) != 2 {
			return nil, fmt.Errorf("invalid row key format for pending index: %s", rowKey)
		}

		timestamp, err := getTimestampFromCell(row, fmt.Sprintf("pending index %s#%s", parts[0], parts[1]))
		if err != nil {
			return nil, err
		}

		results = append(results, struct {
			CheckpointID      string
			TransformationKey string
			Timestamp         time.Time
		}{
			CheckpointID:      parts[0],
			TransformationKey: parts[1],
			Timestamp:         timestamp,
		})
	}

	return results, nil
}

// GetAllActiveTransformationKeys retrieves all active transformation keys for a tenant.
func (h *bigtableHelper) GetAllActiveTransformationKeys(tenantID string) ([]struct {
	TransformationKey string
	Timestamp         time.Time
}, error,
) {
	prefix := "activeTransformationKey#"
	rows, err := h.readRowsWithPrefix(tenantID, prefix, "read active transformation keys")
	if err != nil {
		return nil, err
	}

	var results []struct {
		TransformationKey string
		Timestamp         time.Time
	}

	for _, row := range rows {
		rowKey := string(row.RowKey)
		transformationKey := rowKey[len(prefix):]

		timestamp, err := getTimestampFromCell(row, fmt.Sprintf("active transformation key %s", transformationKey))
		if err != nil {
			return nil, err
		}

		results = append(results, struct {
			TransformationKey string
			Timestamp         time.Time
		}{
			TransformationKey: transformationKey,
			Timestamp:         timestamp,
		})
	}

	return results, nil
}
