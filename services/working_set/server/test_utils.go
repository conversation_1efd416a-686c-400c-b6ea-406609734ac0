package main

import (
	"context"
	"os"
	"testing"

	"cloud.google.com/go/pubsub"
	blob_names "github.com/augmentcode/augment/base/blob_names"
	blobs_pb "github.com/augmentcode/augment/base/blob_names/proto"
	"github.com/augmentcode/augment/base/go/secretstring"
	pubsub_utils "github.com/augmentcode/augment/base/test_utils/pubsub"
	contentmanagerclient "github.com/augmentcode/augment/services/content_manager/client"
	content_manager_pb "github.com/augmentcode/augment/services/content_manager/proto"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"google.golang.org/api/option"
)

const (
	ProjectID = "test-project"
)

// FakeRequestContextHelper is a fake implementation of RequestContextHelper for testing
type FakeRequestContextHelper struct{}

// GetBackgroundRequestContext returns a fake request context for testing
func (m *FakeRequestContextHelper) GetBackgroundRequestContext(tenantID string) (*requestcontext.RequestContext, error) {
	requestId := requestcontext.NewRandomRequestId()
	sessionId := requestcontext.NewRandomRequestSessionId()
	return requestcontext.New(requestId, sessionId, "test-request-source", secretstring.New("test-auth-token")), nil
}

// MockContentManagerClient is a mock implementation of contentmanagerclient.ContentManagerClient
type MockContentManagerClient struct {
	mock.Mock
}

func (m *MockContentManagerClient) Close() {
	m.Called()
}

func (m *MockContentManagerClient) UploadBlobContent(ctx context.Context, content []byte, path string, requestContext *requestcontext.RequestContext) (string, bool, error) {
	args := m.Called(ctx, content, path, requestContext)
	return args.String(0), args.Bool(1), args.Error(2)
}

func (m *MockContentManagerClient) BatchUploadBlobContent(ctx context.Context, blobs []*content_manager_pb.UploadBlobContent, priority content_manager_pb.IndexingPriority, requestContext *requestcontext.RequestContext) ([]*content_manager_pb.UploadBlobResult, error) {
	args := m.Called(ctx, blobs, priority, requestContext)
	return args.Get(0).([]*content_manager_pb.UploadBlobResult), args.Error(1)
}

func (m *MockContentManagerClient) FindMissingBlobs(ctx context.Context, blobNames []blob_names.BlobName, transformationKey string, subKey string, requestContext *requestcontext.RequestContext) ([]string, error) {
	args := m.Called(ctx, blobNames, transformationKey, subKey, requestContext)
	return args.Get(0).([]string), args.Error(1)
}

func (m *MockContentManagerClient) CheckpointBlobs(ctx context.Context, blobs *blobs_pb.Blobs, requestContext *requestcontext.RequestContext) (string, error) {
	args := m.Called(ctx, blobs, requestContext)
	return args.String(0), args.Error(1)
}

func (m *MockContentManagerClient) GetAllBlobsFromCheckpoint(ctx context.Context, checkpointID string, requestContext *requestcontext.RequestContext) ([]blob_names.BlobName, error) {
	args := m.Called(ctx, checkpointID, requestContext)
	return args.Get(0).([]blob_names.BlobName), args.Error(1)
}

func (m *MockContentManagerClient) GetBlobInfo(ctx context.Context, blobName blob_names.BlobName, requestContext *requestcontext.RequestContext) (*content_manager_pb.GetBlobInfoResponse, error) {
	args := m.Called(ctx, blobName, requestContext)
	return args.Get(0).(*content_manager_pb.GetBlobInfoResponse), args.Error(1)
}

func (m *MockContentManagerClient) BatchGetBlobInfo(ctx context.Context, blobNames []blob_names.BlobName, tenantID string, requestContext *requestcontext.RequestContext) (*content_manager_pb.BatchGetBlobInfoResponse, error) {
	args := m.Called(ctx, blobNames, tenantID, requestContext)
	return args.Get(0).(*content_manager_pb.BatchGetBlobInfoResponse), args.Error(1)
}

func (m *MockContentManagerClient) BatchDownloadContent(ctx context.Context, keys []*content_manager_pb.BlobContentKey, requestContext *requestcontext.RequestContext, tenantID string) (<-chan contentmanagerclient.BatchDownloadContentResult, error) {
	args := m.Called(ctx, keys, requestContext, tenantID)
	return args.Get(0).(<-chan contentmanagerclient.BatchDownloadContentResult), args.Error(1)
}

func (m *MockContentManagerClient) GetBestAnnIndex(ctx context.Context, tenantId *string, transformationKey string, checkpointId string, requestContext *requestcontext.RequestContext) (*contentmanagerclient.GetBestAnnIndexResult, error) {
	args := m.Called(ctx, tenantId, transformationKey, checkpointId, requestContext)
	return args.Get(0).(*contentmanagerclient.GetBestAnnIndexResult), args.Error(1)
}

func (m *MockContentManagerClient) GetAnnIndexBlobInfos(ctx context.Context, keys contentmanagerclient.AnnIndexKey, requestContext requestcontext.RequestContext) (*contentmanagerclient.GetAnnIndexBlobInfosResult, error) {
	args := m.Called(ctx, keys, requestContext)
	return args.Get(0).(*contentmanagerclient.GetAnnIndexBlobInfosResult), args.Error(1)
}

func (m *MockContentManagerClient) GetAnnIndexAsset(ctx context.Context, tenantId *string, transformationKey string, indexId string, subKey string, requestContext requestcontext.RequestContext) ([]byte, error) {
	args := m.Called(ctx, tenantId, transformationKey, indexId, subKey, requestContext)
	return args.Get(0).([]byte), args.Error(1)
}

func (m *MockContentManagerClient) AddAnnIndexMapping(ctx context.Context, tenantId *string, transformationKey string, checkpointId string, indexId string, added []blob_names.BlobName, removed []blob_names.BlobName, requestContext requestcontext.RequestContext) (*content_manager_pb.AddAnnIndexMappingResponse, error) {
	args := m.Called(ctx, tenantId, transformationKey, checkpointId, indexId, added, removed, requestContext)
	return &content_manager_pb.AddAnnIndexMappingResponse{}, args.Error(1)
}

func (m *MockContentManagerClient) UploadAnnIndexBlobInfos(ctx context.Context, tenantId *string, transformationKey string, indexId string, infos []contentmanagerclient.AnnIndexBlobInfo, requestContext requestcontext.RequestContext) (*content_manager_pb.UploadAnnIndexBlobInfosResponse, error) {
	args := m.Called(ctx, tenantId, transformationKey, indexId, infos, requestContext)
	return &content_manager_pb.UploadAnnIndexBlobInfosResponse{}, args.Error(1)
}

func (m *MockContentManagerClient) UploadAnnIndexAssets(ctx context.Context, tenantId *string, transformationKey string, indexId string, assets []contentmanagerclient.AnnIndexAssetInput, requestContext requestcontext.RequestContext) (*content_manager_pb.UploadAnnIndexAssetsResponse, error) {
	args := m.Called(ctx, tenantId, transformationKey, indexId, assets, requestContext)
	return &content_manager_pb.UploadAnnIndexAssetsResponse{}, args.Error(1)
}

// PubSubFixture provides a PubSub emulator and client
type PubSubFixture struct {
	Ctx       context.Context
	Client    *pubsub.Client
	ProjectID string
	Endpoint  string
	Cleanup   func()
	TopicName string
	Topic     *pubsub.Topic
	SubName   string
	Sub       *pubsub.Subscription
}

// NewPubSubFixture creates a new test environment with PubSub emulator
func NewPubSubFixture(t *testing.T, topicName string, subName string) *PubSubFixture {
	t.Helper()
	ctx := context.Background()

	endpoint, cleanup, err := pubsub_utils.StartEmulator(0)
	require.NoError(t, err)

	os.Setenv("PUBSUB_EMULATOR_HOST", endpoint)

	client, err := pubsub.NewClient(
		ctx,
		ProjectID,
		option.WithEndpoint(endpoint),
		option.WithoutAuthentication(),
	)
	require.NoError(t, err)

	topic, err := client.CreateTopic(ctx, topicName)
	require.NoError(t, err)

	sub, err := client.CreateSubscription(ctx, subName, pubsub.SubscriptionConfig{
		Topic: topic,
	})
	require.NoError(t, err)

	finalCleanup := func() {
		sub.Delete(ctx)
		topic.Delete(ctx)
		client.Close()
		cleanup()
		os.Unsetenv("PUBSUB_EMULATOR_HOST")
	}

	return &PubSubFixture{
		Ctx:       ctx,
		Client:    client,
		ProjectID: ProjectID,
		Endpoint:  endpoint,
		Cleanup:   finalCleanup,
		TopicName: topicName,
		Topic:     topic,
		SubName:   subName,
		Sub:       sub,
	}
}
