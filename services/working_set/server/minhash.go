package main

import (
	"hash/fnv"
	"math"

	blob_names "github.com/augmentcode/augment/base/blob_names"
	"github.com/rs/zerolog/log"
)

const (
	numMinHashFunctions = 128
	maxMinHashValue     = math.MaxInt32
	largePrime          = 4294967291 // Largest prime less than 2^32
)

var minHashFunctions []func(blob_names.BlobName) int

// isPrime checks if a number is prime using a simple trial division method
func isPrime(n int) bool {
	if n <= 1 {
		return false
	}
	if n <= 3 {
		return true
	}
	if n%2 == 0 || n%3 == 0 {
		return false
	}

	// Check divisibility by numbers of form 6k +/- 1 up to sqrt(n)
	for i := 5; i*i <= n; i += 6 {
		if n%i == 0 || n%(i+2) == 0 {
			return false
		}
	}
	return true
}

// generatePrimes returns the first n prime numbers
func generatePrimes(n int) []int {
	if n <= 0 {
		return []int{}
	}

	primes := make([]int, 0, n)
	num := 2 // Start with the first prime

	for len(primes) < n {
		if isPrime(num) {
			primes = append(primes, num)
		}
		num++
	}

	return primes
}

// generateHashFunctions creates a set of independent hash functions
// Each function uses a combination of FNV-1a hash and random coefficients
// to create independent hash functions with good distribution
func generateHashFunctions(numFunctions int, maxValue int) []func(blob_names.BlobName) int {
	hashFunctions := make([]func(blob_names.BlobName) int, numFunctions)

	// Generate the first numFunctions*2 prime numbers to ensure we have enough
	// for both a and b coefficients
	primes := generatePrimes(numFunctions * 2)

	for i := 0; i < numFunctions; i++ {
		// Use different coefficients for each hash function
		a := primes[i%len(primes)]
		b := primes[(i+7)%len(primes)]

		hashFunctions[i] = func(blobName blob_names.BlobName) int {
			// Use FNV-1a hash as a base for better distribution
			h := fnv.New32a()
			h.Write([]byte(blobName))
			hash := int(h.Sum32())

			// Apply universal hashing formula (ax + b) mod p
			return ((a*hash + b) % largePrime) % maxValue
		}
	}
	return hashFunctions
}

func init() {
	minHashFunctions = generateHashFunctions(numMinHashFunctions, maxMinHashValue)
}

// computeMinHashSignature generates a MinHash signature for a set of blob names.
// The signature is a vector of minimum hash values for each hash function.
func computeMinHashSignature(setId string, blobNames []blob_names.BlobName) []int {
	// Initialize signature with maximum values
	signature := make([]int, numMinHashFunctions)
	for i := range signature {
		signature[i] = maxMinHashValue
	}

	// For each blob, compute all hash functions and keep the minimum
	for _, blobName := range blobNames {
		for i, hashFunc := range minHashFunctions {
			hashValue := hashFunc(blobName)
			// Keep the minimum hash value for each hash function
			if hashValue < signature[i] {
				signature[i] = hashValue
			}
		}
	}

	log.Info().Msgf("Computed MinHash signature for blob set %s (%d blobs) with %d hash functions",
		setId, len(blobNames), numMinHashFunctions)
	log.Debug().Msgf("MinHash signature for %s: %v", setId, signature)

	return signature
}
