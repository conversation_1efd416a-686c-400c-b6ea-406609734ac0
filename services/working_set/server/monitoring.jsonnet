local monitoringLib = import 'deploy/gcp/monitoring-lib.jsonnet';
function(cloud)
  local requestBacklogSpec = {
    displayName: 'Checkpoint indexing request pub/sub backlog',
    conditionPrometheusQueryLanguage: {
      duration: '900s',
      evaluationInterval: '300s',
      labels: { severity: 'warning' },
      query: 'sum by (subscription_id)(pubsub_googleapis_com:subscription_num_undelivered_messages{monitored_resource="pubsub_subscription",subscription_id=~".*checkpoint-indexer-sub"}) > 200',
    },
  };
  local responseBacklogSpec = {
    displayName: 'Checkpoint indexing response pub/sub backlog',
    conditionPrometheusQueryLanguage: {
      duration: '900s',
      evaluationInterval: '300s',
      labels: { severity: 'warning' },
      query: 'sum by (subscription_id)(pubsub_googleapis_com:subscription_num_undelivered_messages{monitored_resource="pubsub_subscription",subscription_id=~".*working-set-sub"}) > 50',
    },
  };

  local requestUnackedSpec = {
    displayName: 'Checkpoint indexing request pub/sub old unacked message',
    conditionPrometheusQueryLanguage: {
      duration: '900s',
      evaluationInterval: '300s',
      labels: { severity: 'warning' },
      // 21600 seconds is 6 hours
      query: 'max by (subscription_id)(pubsub_googleapis_com:subscription_oldest_unacked_message_age{monitored_resource="pubsub_subscription",subscription_id=~".*checkpoint-indexer-sub"}) > 21600',
    },
  };
  local responseUnackedSpec = {
    displayName: 'Checkpoint indexing response pub/sub old unacked message',
    conditionPrometheusQueryLanguage: {
      duration: '900s',
      evaluationInterval: '300s',
      labels: { severity: 'warning' },
      // 7200 seconds is 2 hours
      query: 'max by (subscription_id)(pubsub_googleapis_com:subscription_oldest_unacked_message_age{monitored_resource="pubsub_subscription",subscription_id=~".*working-set-sub"}) > 7200',
    },
  };


  [
    monitoringLib.alertPolicy(cloud, requestBacklogSpec, 'checkpoint-indexing-request-pubsub-backlog', 'Checkpoint indexing request pub/sub %s has more than 200 undelivered messages' % monitoringLib.label('subscription_id')),
    monitoringLib.alertPolicy(cloud, responseBacklogSpec, 'checkpoint-indexing-response-pubsub-backlog', 'Checkpoint indexing response pub/sub %s has more than 50 undelivered messages' % monitoringLib.label('subscription_id')),
    monitoringLib.alertPolicy(cloud, requestUnackedSpec, 'checkpoint-indexing-request-pubsub-unacked', 'Checkpoint indexing request pub/sub %s has messages unacked for more than 6 hours' % monitoringLib.label('subscription_id')),
    monitoringLib.alertPolicy(cloud, responseUnackedSpec, 'checkpoint-indexing-response-pubsub-unacked', 'Checkpoint indexing response pub/sub %s has messages unacked for more than 2 hours' % monitoringLib.label('subscription_id')),
  ]
