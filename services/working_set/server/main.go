package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"net"
	"net/http"
	_ "net/http/pprof"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	"cloud.google.com/go/pubsub"
	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/go/durationutil"
	"github.com/augmentcode/augment/base/logging"
	tracing "github.com/augmentcode/augment/base/tracing/go"
	bigtableproxy "github.com/augmentcode/augment/services/bigtable_proxy/client"
	contentmanagerclient "github.com/augmentcode/augment/services/content_manager/client"
	"github.com/augmentcode/augment/services/lib/grpc/auth"
	grpc_metrics "github.com/augmentcode/augment/services/lib/grpc/metrics"
	"github.com/augmentcode/augment/services/lib/grpc/recovery"
	tlsconfig "github.com/augmentcode/augment/services/lib/grpc/tls_config"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	tenantwatcherclient "github.com/augmentcode/augment/services/tenant_watcher/client"
	tokenexchange "github.com/augmentcode/augment/services/token_exchange/client"
	tokenexchangepb "github.com/augmentcode/augment/services/token_exchange/proto"
	proto "github.com/augmentcode/augment/services/working_set/proto"
	grpcprom "github.com/grpc-ecosystem/go-grpc-middleware/providers/prometheus"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/rs/zerolog/log"
	"go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc"
	_ "go.uber.org/automaxprocs"
	"google.golang.org/grpc"
	"google.golang.org/grpc/health"
	healthgrpc "google.golang.org/grpc/health/grpc_health_v1"
	"google.golang.org/grpc/reflection"
)

var configFile = flag.String("config", "", "Path to config file")

type Config struct {
	// the port the grpc server will listen on
	Port int `json:"port"`

	// TLS configuration
	ServerMtls        *tlsconfig.ServerConfig `json:"server_mtls"`
	ClientMtls        *tlsconfig.ClientConfig `json:"client_mtls"`
	CentralClientMtls *tlsconfig.ClientConfig `json:"central_client_mtls"`

	Namespace string `json:"namespace"`

	TokenExchangeEndpoint  string `json:"token_exchange_endpoint"`
	ContentManagerEndpoint string `json:"content_manager_endpoint"`
	BigtableProxyEndpoint  string `json:"bigtable_proxy_endpoint"`
	TenantWatcherEndpoint  string `json:"tenant_watcher_endpoint"`

	// Prometheus metrics port
	PromPort int `json:"prom_port"`

	// GCP project id
	ProjectId string `json:"project_id"`

	// the path to the feature flag sdk key
	FeatureFlagsSdkKeyPath string `json:"feature_flags_sdk_key_path"`

	// the endpoint for the dynamic feature flags service or None if not used
	DynamicFeatureFlagsEndpoint string `json:"dynamic_feature_flags_endpoint"`

	// whether we should emit the blob index metrics that require querying content manager
	PublishBlobIndexMetrics bool `json:"publish_blob_index_metrics"`

	// the timeout for working set sessions
	SessionTimeout durationutil.JSONDuration `json:"session_timeout"`

	// the interval for running stats loop
	StatsLoop durationutil.JSONDuration `json:"stats_loop"`

	// the interval for running cleanup loop
	CleanupLoop durationutil.JSONDuration `json:"cleanup_loop"`

	// how long to keep checkpoint blobs in the cache before evicting them
	CheckpointBlobCacheTTL durationutil.JSONDuration `json:"checkpoint_blob_cache_ttl"`

	// the max size of the checkpoint blob cache in MB
	CheckpointBlobCacheSizeInMB int `json:"checkpoint_blob_cache_size_mb"`

	// how long to keep checkpoints before cleaning them up
	CheckpointExpirationTime durationutil.JSONDuration `json:"checkpoint_expiration_time"`

	// the name of the pubsub topic to publish checkpoint index creation messages to
	AnnIndexCreationTopicName string `json:"ann_index_creation_topic_name"`

	// the name of the pubsub subscription to listen to for checkpoint index creation messages
	AnnIndexCreationSubName string `json:"ann_index_creation_sub_name"`

	// how long to keep ANN index blobs in the cache before evicting them
	AnnIndexBlobCacheTTL durationutil.JSONDuration `json:"ann_index_blob_cache_ttl"`

	// the max size of the ANN index blob cache in MB
	AnnIndexBlobCacheSizeInMB int `json:"ann_index_blob_cache_size_mb"`

	// how long to keep transformation keys before cleaning them up
	TransformationKeyExpirationTime durationutil.JSONDuration `json:"transformation_key_expiration_time"`

	// how long to keep pending ANN index creation entries before cleaning them up
	PendingAnnIndexCreationTimeout durationutil.JSONDuration `json:"pending_ann_index_creation_timeout"`

	// minimum number of blobs required to be present in a checkpoint to create an ANN
	// index
	AnnIndexMinimumBlobs int `json:"ann_index_minimum_blobs"`

	// maximum number of goroutines in the CPU task pool used for parallel processing
	CpuTaskPoolMaxSize int `json:"cpu_task_pool_max_size"`

	// maximum number of goroutines in the IO task pool used for parallel processing
	IoTaskPoolSize int `json:"io_task_pool_size"`
}

type RequestContextHelper interface {
	GetBackgroundRequestContext(tenantID string) (*requestcontext.RequestContext, error)
}

type requestContextHelper struct {
	tokenExchangeClient tokenexchange.TokenExchangeClient
	sessionId           requestcontext.RequestSessionId
}

// GetBackgroundRequestContext gets the request context for the given tenant
// This is intended to be used from background operations and not for requests.
func (h *requestContextHelper) GetBackgroundRequestContext(tenantID string) (*requestcontext.RequestContext, error) {
	authToken, err := h.tokenExchangeClient.GetSignedTokenForService(context.Background(), tenantID, []tokenexchangepb.Scope{
		tokenexchangepb.Scope_CONTENT_RW,
	})
	if err != nil {
		return nil, err
	}
	requestContext := &requestcontext.RequestContext{
		RequestId:        requestcontext.NewRandomRequestId(),
		RequestSessionId: h.sessionId,
		RequestSource:    "background",
		AuthToken:        authToken,
	}
	return requestContext, nil
}

func main() {
	startTime := time.Now()
	logging.SetupServerLogging()

	flag.Parse()
	log.Info().Msgf("Config file: %s", *configFile)

	var config Config
	if *configFile == "" {
		log.Fatal().Msg("Missing config file")
	}

	f, err := os.Open(*configFile)
	if err != nil {
		log.Fatal().Err(err).Msg("Error opening config file")
	}
	defer f.Close()

	decoder := json.NewDecoder(f)
	decoder.DisallowUnknownFields()
	if err := decoder.Decode(&config); err != nil {
		log.Fatal().Err(err).Msg("Error decoding config file")
	}
	log.Info().Msgf("Config: %v", config)

	tracingShutdown := tracing.Init()
	defer tracingShutdown()

	// Create a channel to catch OS signals
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGTERM)
	wg := sync.WaitGroup{}

	go func() {
		http.Handle("/metrics", promhttp.Handler())
		err := http.ListenAndServe(fmt.Sprintf(":%d", config.PromPort), nil)
		if err != nil && err != http.ErrServerClosed {
			log.Fatal().Err(err).Msg("Error starting Prometheus metrics server")
		}
	}()

	// Setup metrics.
	srvMetrics := grpcprom.NewServerMetrics(
		grpcprom.WithServerHandlingTimeHistogram(),
	)
	prometheus.MustRegister(srvMetrics)

	// Create client credentials for the client.
	clientCreds, err := tlsconfig.GetClientTls(config.ClientMtls)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating client credentials")
	}
	_ = clientCreds // this is just to suppress unused variable warning. Remove the code is not used

	// Create client credentials for the central client.
	centralClientCreds, err := tlsconfig.GetClientTls(config.CentralClientMtls)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating client credentials")
	}

	featureFlagHandle, err := featureflags.NewFeatureFlagHandleFromFile(config.FeatureFlagsSdkKeyPath,
		config.DynamicFeatureFlagsEndpoint)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating feature flag handle")
	}

	serverTls, err := tlsconfig.GetServerTls([]*tlsconfig.ServerConfig{config.ServerMtls})
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating TLS config")
	}
	var opts []grpc.ServerOption
	opts = append(opts, grpc.Creds(serverTls))
	opts = append(opts, grpc.StatsHandler(otelgrpc.NewServerHandler()))
	opts = append(opts, grpc.ChainUnaryInterceptor(
		recovery.UnaryServerInterceptor(),
		srvMetrics.UnaryServerInterceptor(),
	))
	opts = append(opts, grpc.ChainStreamInterceptor(
		recovery.StreamingServerInterceptor(),
		srvMetrics.StreamServerInterceptor(),
	))

	// Set up service token auth.
	tokenExchangeClient, err := tokenexchange.New(
		config.TokenExchangeEndpoint, config.Namespace, grpc.WithTransportCredentials(centralClientCreds),
	)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating token exchange client")
		os.Exit(1)
	}
	defer tokenExchangeClient.Close()

	contentManagerClient, err := contentmanagerclient.NewContentManagerClient(
		config.ContentManagerEndpoint, clientCreds,
	)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating content manager client")
	}
	defer contentManagerClient.Close()

	bigtableProxyClient, err := bigtableproxy.NewBigtableProxyClient(config.BigtableProxyEndpoint, clientCreds)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating bigtable proxy client")
	}

	tenantWatcherClient := tenantwatcherclient.New(config.TenantWatcherEndpoint, grpc.WithTransportCredentials(centralClientCreds))
	defer tenantWatcherClient.Close()

	serviceTokenAuth := auth.NewServiceTokenAuth(tokenExchangeClient)
	authInterceptor := auth.NewAuthServerInterceptor(serviceTokenAuth.ValidateAccess)
	grpcMetricsInterceptor := grpc_metrics.NewMetricsInterceptor()

	opts = append(opts, grpc.ChainUnaryInterceptor(authInterceptor.Intercept, grpcMetricsInterceptor.UnaryInterceptor))
	opts = append(opts, grpc.ChainStreamInterceptor(authInterceptor.StreamIntercept, grpcMetricsInterceptor.StreamInterceptor))

	grpcServer := grpc.NewServer(opts...)
	// setup prometheus metrics for GRPC calls
	srvMetrics.InitializeMetrics(grpcServer)

	// setup reflection for debugging
	reflection.Register(grpcServer)
	// setup health service
	healthgrpc.RegisterHealthServer(grpcServer, health.NewServer())

	// add cancel
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// set up pubsub
	pubsubClient, err := pubsub.NewClient(ctx, config.ProjectId)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating pubsub client")
	}

	annIndexCreationTopic := pubsubClient.Topic(config.AnnIndexCreationTopicName)

	annIndexCreationSub := pubsubClient.Subscription(config.AnnIndexCreationSubName)

	requestContextHelper := &requestContextHelper{
		tokenExchangeClient: tokenExchangeClient,
		sessionId:           requestcontext.NewRandomRequestSessionId(),
	}

	// Function wrapper to check the PersistToBigtable feature flag in BigtableHelper
	persistToBigtableFn := func() bool {
		persistToBigtable, err := PersistToBigtableFlag.Get(featureFlagHandle)
		if err != nil {
			log.Warn().Err(err).Msg("Error getting PersistToBigtable feature flag value")
			return false
		}
		return persistToBigtable
	}
	bigtableHelper := NewBigtableHelper(ctx, requestContextHelper, bigtableProxyClient, persistToBigtableFn)

	workingSetStore := NewWorkingSetStore(
		requestContextHelper,
		contentManagerClient,
		bigtableHelper,
		tenantWatcherClient,
		config.SessionTimeout.ToDuration(),
		config.StatsLoop.ToDuration(),
		config.CleanupLoop.ToDuration(),
		config.CheckpointBlobCacheTTL.ToDuration(),
		config.CheckpointBlobCacheSizeInMB,
		config.PublishBlobIndexMetrics,
		config.CheckpointExpirationTime.ToDuration(),
		config.AnnIndexBlobCacheTTL.ToDuration(),
		config.AnnIndexBlobCacheSizeInMB,
		annIndexCreationTopic,
		annIndexCreationSub,
		config.TransformationKeyExpirationTime.ToDuration(),
		config.PendingAnnIndexCreationTimeout.ToDuration(),
		config.AnnIndexMinimumBlobs,
		config.CpuTaskPoolMaxSize,
		config.IoTaskPoolSize,
	)

	err = workingSetStore.Init(ctx)
	if err != nil {
		log.Fatal().Err(err).Msg("Error initializing working set store")
	}
	go workingSetStore.Run(ctx, false) // Not running under test

	server := NewWorkingSetManagerServer(featureFlagHandle, workingSetStore)
	proto.RegisterWorkingSetManagerServer(grpcServer, server)
	lis, err := net.Listen("tcp", fmt.Sprintf(":%d", config.Port))
	if err != nil {
		log.Fatal().Err(err).Msgf("Failed to listen")
	}
	log.Info().Msgf("Listening on %v", lis.Addr())

	startupTimeMetric.Observe(time.Since(startTime).Seconds())

	go func() {
		wg.Add(1)
		defer wg.Done()
		err = grpcServer.Serve(lis)
		if err != nil && err != grpc.ErrServerStopped {
			log.Fatal().Err(err).Msg("Error serving")
		}
		log.Info().Msg("gRPC server closed")
	}()

	// Wait for either a shutdown signal or an OS signal
	sig := <-sigChan
	log.Info().Msgf("Received signal: %v", sig)
	cancel()
	grpcServer.GracefulStop()
	wg.Wait()
	log.Info().Msg("Server stopped")
}
