# Working Set Service

The working set service is a service that manages working sets on behalf of sessions. A working set
is a set of blobs (checkpoint + delta) that are relevant to a user at a point in time. The working
set service is responsible for keeping track of the working sets for all sessions, managing SCANN
indexes for checkpoints and publishing metrics related to working sets and SCANN index management.

## APIs

The service exposes the following APIs:

### RegisterWorkingSet

Registers a working set for a session. The working set is defined by a base checkpoint and a
delta (added/removed) on top of that base checkpoint. The service keeps track of the working sets for
all sessions and prunes old working sets that are no longer relevant.

### CreateAnnIndexForCheckpoint

This is the entry point to SCANN index management. Given a checkpoint ID and a transformation key,
the service decides whether to a) create a new SCANN index for the checkpoint, b) use an existing
SCANN index that is "close enough" to the checkpoint, or c) decide that no action is needed. New
SCANN index creation is triggered via publishing a pub/sub message to the checkpoint_indexer service.
The working set service persists checkpoint to ANN index mappings in Bigtable as a result of this
RPC, and these mappings can be used elsewhere in the system (eg. embeddings search) to determine
which ANN index to use for a given checkpoint.

### GetActiveCheckpoints

Returns a list of "active" checkpoints that are currently being used by users.

### GetActiveTransformationKeys

Returns a list of "active" transformation keys that are currently being used by users.
