load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:go.bzl", "go_grpc_library")
load("//tools/bzl:python.bzl", "py_grpc_library")

proto_library(
    name = "working_set_proto",
    srcs = ["working_set.proto"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//base/blob_names:blob_names_proto",
    ],
)

py_grpc_library(
    name = "working_set_py_proto",
    protos = [":working_set_proto"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//base/blob_names:blob_names_py_proto",
    ],
)

go_grpc_library(
    name = "working_set_go_proto",
    importpath = "github.com/augmentcode/augment/services/working_set/proto",
    proto = ":working_set_proto",
    visibility = [
        "//services:__subpackages__",
    ],
    deps = [
        "//base/blob_names:blob_names_go_proto",
    ],
)
