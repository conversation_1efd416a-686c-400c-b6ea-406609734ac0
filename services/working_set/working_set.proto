syntax = "proto3";

package working_set;

import "base/blob_names/blob_names.proto";

service WorkingSetManager {
  rpc RegisterWorkingSet(RegisterWorkingSetRequest) returns (RegisterWorkingSetResponse) {}
  rpc CreateAnnIndexForCheckpoint(CreateAnnIndexForCheckpointRequest) returns (CreateAnnIndexForCheckpointResponse) {}
  rpc GetActiveCheckpoints(GetActiveCheckpointsRequest) returns (GetActiveCheckpointsResponse) {}
  rpc GetActiveTransformationKeys(GetActiveTransformationKeysRequest) returns (GetActiveTransformationKeysResponse) {}
}

message RegisterWorkingSetRequest {
  repeated base.blob_names.Blobs blobs = 1;
}

message RegisterWorkingSetResponse {}

message CreateAnnIndexForCheckpointRequest {
  string checkpoint_id = 1;
  string transformation_key = 2;
}

message CreateAnnIndexForCheckpointResponse {}

message GetActiveCheckpointsRequest {}

message GetActiveCheckpointsResponse {
  // hex encoded ckpt id = 64 bytes so we can support ~32k active checkpoints
  // in a unary response without having to worry about grpc decoding limits
  repeated string checkpoint_ids = 1;
}

message GetActiveTransformationKeysRequest {}

message GetActiveTransformationKeysResponse {
  repeated string transformation_keys = 1;
}
