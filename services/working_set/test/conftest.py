"""conftest.py is a special file that pytest will automatically load.

pytest will automatically load and execute before any other test files. This is a
good place to put fixtures that are used by multiple test files.
"""

import itertools
import os
import random
import struct
import tempfile
import time
import typing
from pathlib import Path
from typing import Callable, Generator, Optional

import grpc
import kubernetes
import pytest

import base.python.k8s_test_helper.k8s_test_helper as k8s_test_helper
import services.content_manager.client.content_manager_client
import services.content_manager.content_manager_pb2_grpc as content_manager_pb2_grpc
from base.python.cloud import cloud as cloud_lib
from base.python.grpc.health_check import HealthChecker
from services.auth.central.server import auth_entities_pb2
from services.content_manager.client.content_manager_client import ContentManagerClient
from services.lib.request_context.request_context import (
    RequestContext,
    create_request_session_id,
)
from services.tenant_watcher.client.client import TenantsClient
from services.test.fake_feature_flags.client import FakeFeatureFlagsClient
from services.token_exchange import token_exchange_pb2
from services.token_exchange.client.client import (
    GrpcTokenExchangeClient,
    TokenExchangeClient,
)
from services.working_set.client.client import (
    GrpcWorkingSetClient,
    WorkingSetClient,
)

# Container details


def pytest_addoption(parser):
    """Add command line options to pytest."""
    parser.addoption(
        "--skip-deployment",
        action="store_true",
        help="skip deploy and delete of the models",
        default=False,
    )
    parser.addoption(
        "--skip-deployment-teardown",
        action="store_true",
        help="skip deploy and delete of the models",
        default=not k8s_test_helper.is_running_in_test_infra(),
    )
    parser.addoption(
        "--teardown-bigtable",
        action="store_true",
        help="tear down Bigtable resources during cleanup",
        default=False,
    )
    if not k8s_test_helper.is_running_in_test_infra():
        parser.addoption(
            "--teardown-deployment",
            action="store_true",
            help="tear down the complete deployment after the run",
            default=False,
        )
    parser.addoption(
        "--skip-deployment-check",
        action="store_true",
        help="skip checking if the deployments are settled",
        default=False,
    )
    parser.addoption(
        "--cloud",
        help="Cloud to use",
        default=cloud_lib.get_default_cloud(),
        choices=cloud_lib.get_cloud_list(gcp_only=True),
    )
    parser.addoption(
        "--skip-transformation-key-deletion",
        action="store_true",
        help="skip the deletion of the transformation key. This allows a quicker iteration cycle during development.",
        default=False,
    )
    parser.addoption(
        "--user-id-prefix",
        default="cm-integ-user",
        help="User ID prefix to use for testing",
    )
    parser.addoption(
        "--namespace",
        default=None,
        help="Namespace to use for testing. If not provided, kubecfg resolves namespace following `get_dev_namespace()`.",
    )


NEXT_FORWARDED_PORT = 50052


def get_next_forwarded_port() -> int:
    """Return a fresh local port."""
    global NEXT_FORWARDED_PORT  # pylint: disable=global-statement
    port = NEXT_FORWARDED_PORT
    NEXT_FORWARDED_PORT += 1
    return port


def _test_response(
    url: str,
    credentials: Optional[grpc.ChannelCredentials],
    service_name: str,
    options: list[tuple[str, str]] | None = None,
) -> bool:
    try:
        checker = HealthChecker(url, credentials, options=options)
        status = checker.is_serving(service_name)
        return status
    except grpc.RpcError as ex:
        print(ex, flush=True)
        return False


def generate_random_bytes(n: int) -> bytes:
    """Generate a random bytes array of a given size."""
    return (
        b"".join(
            map(
                struct.Struct("!Q").pack,
                map(random.getrandbits, itertools.repeat(64, (n + 7) // 8)),
            )
        )
    )[:n]


@pytest.fixture()
def small_file() -> Generator[Path, None, None]:
    """Return the name to a small file (64K)."""
    with tempfile.NamedTemporaryFile() as tmp_file:
        tmp_file.write(generate_random_bytes(64 * 1024))
        yield Path(tmp_file.name)


@pytest.fixture(scope="session")
def request_session_id() -> Generator[str, None, None]:
    """Return a request context."""
    yield create_request_session_id()


@pytest.fixture()
def request_context(
    request_session_id: str, token_exchange_client: TokenExchangeClient, tenant_id: str
) -> Generator[RequestContext, None, None]:
    """Return a request context."""

    token = token_exchange_client.get_signed_token_for_service(
        tenant_id=tenant_id, scopes=[token_exchange_pb2.CONTENT_ADMIN]
    )
    yield RequestContext.create_for_session(request_session_id, auth_token=token)


@pytest.fixture()
def namespaced_request_context(
    request_session_id: str,
    token_exchange_client: TokenExchangeClient,
) -> Generator[RequestContext, None, None]:
    """Return a request context."""

    token = token_exchange_client.get_signed_token_for_service(
        tenant_id="", scopes=[token_exchange_pb2.CONTENT_ADMIN]
    )
    yield RequestContext.create_for_session(request_session_id, auth_token=token)


@pytest.fixture()
def request_context_user_lambda(
    request_session_id: str,
    token_exchange_client: TokenExchangeClient,
    tenant_id: str,
) -> Callable[[str, str], RequestContext]:
    """Return a request context for a user.

    Since a user request context depends on a user id and we use different user
    ids throughout our testing, provide this as a lambda rather than a single
    fixed RequestContext.
    """

    def create_user_request_context(
        augment_user_id: str, user_email: str
    ) -> RequestContext:
        token = token_exchange_client.get_signed_token_for_user(
            user_id=augment_user_id,
            opaque_user_id=auth_entities_pb2.UserId(
                user_id=augment_user_id,
                user_id_type=auth_entities_pb2.UserId.UserIdType.AUGMENT,
            ),
            user_email=user_email,
            tenant_id=tenant_id,
        )
        return RequestContext.create_for_session(request_session_id, auth_token=token)

    return create_user_request_context


@pytest.fixture(scope="session")
def services_deploy(
    request,
) -> Generator[k8s_test_helper.DeployInfo, None, None]:
    """Deploys the services as pytest fixture."""
    k8s_test_helper.print_link_to_logs(request.config.getoption("--namespace"))
    skip_deploy = request.config.getoption("--skip-deployment")
    skip_deploy_check = request.config.getoption("--skip-deployment-check")
    skip_deploy_check_teardown = request.config.getoption(
        "--skip-deployment-teardown"
    ) and not request.config.getoption("--teardown-deployment")
    cloud = request.config.getoption("--cloud")
    namespace = request.config.getoption("--namespace")

    with k8s_test_helper.deploy(
        skip_deploy=skip_deploy,
        skip_deploy_check=skip_deploy_check,
        skip_deploy_check_teardown=skip_deploy_check_teardown,
        cloud=cloud,
        namespace=namespace,
        kubecfg_binaries=[
            Path("services/working_set/test/test_kubecfg.sh"),
        ],
    ) as deploy_info:
        yield deploy_info


def wait_for_transformation_key(
    client: ContentManagerClient,
    request_context: RequestContext,
    transformation_key: str,
    timeout: float = 120.0,
):
    start_time = time.monotonic()
    end_time = start_time + timeout
    while True:

        def window_provider() -> typing.Iterable[int]:
            yield 0

        exception = None
        try:
            for _ in client.subscribe_flow_controlled(
                transformation_key,
                request_context=request_context,
                window_provider=window_provider(),
            ):
                break
            return
        except Exception as ex:  # pylint: disable=broad-except
            exception = ex

        if time.monotonic() > end_time:
            print("Exception: ", exception, flush=True)
            raise RuntimeError("Timed out waiting for transformation key")
        time.sleep(0.5)


@pytest.fixture(scope="session")
def content_manager_stub(
    services_deploy: k8s_test_helper.DeployInfo,
    bigtable_proxy_ready,
) -> Generator[content_manager_pb2_grpc.ContentManagerStub, None, None]:
    """Return an GRPC stub to access the content manager.

    If applicable, it will update or create the application in Kubernetes.
    """
    credentials = services_deploy.kubectl.load_grpc_credentials()
    # change the endpoint name when verifying the TLS certificate as "localhost" is not
    # on the certificate.
    target_name_override_endpoint = "content-manager-svc"
    options: list[tuple[str, str]] = [
        ("grpc.ssl_target_name_override", target_name_override_endpoint)
    ]

    url = ""
    for _ in range(30):
        with services_deploy.kubectl.port_forward(
            "deployment/content-manager", 50051, get_next_forwarded_port()
        ) as port:
            url = f"localhost:{port}"
            if not _test_response(
                url,
                credentials=credentials,
                options=options,
                service_name="",
            ):
                time.sleep(10)
                continue
            else:
                yield services.content_manager.client.content_manager_client.setup_stub(
                    url, credentials=credentials, options=options
                )
                break
    else:
        print(f"TIMEOUT testing response from {url}")
        # the test will likely fail
        yield services.content_manager.client.content_manager_client.setup_stub(
            url, credentials=credentials, options=options
        )


@pytest.fixture(scope="session")
def content_manager_client(
    services_deploy: k8s_test_helper.DeployInfo,
    content_manager_stub,
    token_exchange_client,
    request,
) -> Generator[
    ContentManagerClient,
    None,
    None,
]:
    """Return a ContentManagerClient to access the content manager."""
    data = {
        "apiVersion": "eng.augmentcode.com/v1",
        "kind": "TransformationKey",
        "metadata": {
            "name": "test",
            "namespace": services_deploy.namespace,
        },
        "spec": {
            "transformationKeyName": "test",
        },
    }
    print("Applying transformation key", flush=True)
    services_deploy.kubectl.apply(data)

    client = ContentManagerClient(content_manager_stub)

    token = token_exchange_client.get_signed_token_for_service(
        tenant_id="", scopes=[token_exchange_pb2.CONTENT_ADMIN]
    )
    namespaced_request_context = RequestContext.create_for_session(
        create_request_session_id(), auth_token=token
    )

    print("Waiting for transformation key", flush=True)
    wait_for_transformation_key(client, namespaced_request_context, "test")
    print("Transformation key ready", flush=True)

    yield client

    skip_delete = request.config.getoption("--skip-transformation-key-deletion")
    if not skip_delete:
        services_deploy.kubectl.delete(data)


@pytest.fixture(scope="session")
def token_exchange_client(
    services_deploy: k8s_test_helper.DeployInfo,
) -> Generator[TokenExchangeClient, None, None]:
    """Return an GRPC stub to access the content manager.

    If applicable, it will update or create the application in Kubernetes.
    """
    credentials = services_deploy.kubectl.load_grpc_credentials()
    # change the endpoint name when verifying the TLS certificate as "localhost" is not
    # on the certificate.
    target_name_override_endpoint = "token-exchange-central-svc"
    options: list[tuple[str, str]] = [
        ("grpc.ssl_target_name_override", target_name_override_endpoint)
    ]

    url = ""
    for _ in range(30):
        with services_deploy.kubectl.port_forward(
            "deployment/token-exchange-central", 50051, get_next_forwarded_port()
        ) as port:
            url = f"localhost:{port}"
            if not _test_response(
                url, credentials=credentials, options=options, service_name=""
            ):
                time.sleep(10)
                continue
            else:
                yield GrpcTokenExchangeClient(
                    url,
                    credentials=credentials,
                    options=options,
                    namespace=services_deploy.namespace,
                )
                break
    else:
        print(f"TIMEOUT testing response from {url}")
        # the test will likely fail
        yield GrpcTokenExchangeClient(
            url,
            credentials=credentials,
            options=options,
            namespace=services_deploy.namespace,
        )


@pytest.fixture(scope="session")
def tenant_watcher_client(
    services_deploy: k8s_test_helper.DeployInfo,
) -> Generator[TenantsClient, None, None]:
    """Return an GRPC stub to access the content manager.

    If applicable, it will update or create the application in Kubernetes.
    """
    credentials = services_deploy.kubectl.load_grpc_credentials()
    # change the endpoint name when verifying the TLS certificate as "localhost" is not
    # on the certificate.
    target_name_override_endpoint = "tenant-central-svc"
    options: list[tuple[str, str]] = [
        ("grpc.ssl_target_name_override", target_name_override_endpoint)
    ]

    url = ""
    for _ in range(30):
        with services_deploy.kubectl.port_forward(
            "deployment/tenant-central", 50051, get_next_forwarded_port()
        ) as port:
            url = f"localhost:{port}"
            if not _test_response(
                url, credentials=credentials, options=options, service_name=""
            ):
                time.sleep(10)
                continue
            else:
                yield TenantsClient(
                    url,
                    credentials=credentials,
                    options=options,
                )
                break
    else:
        print(f"TIMEOUT testing response from {url}")
        # the test will likely fail
        yield TenantsClient(
            url,
            credentials=credentials,
            options=options,
        )


@pytest.fixture(scope="session")
def working_set_client(
    services_deploy: k8s_test_helper.DeployInfo,
) -> Generator[WorkingSetClient, None, None]:
    """Return an GRPC stub to access the working set.

    If applicable, it will update or create the application in Kubernetes.
    """
    credentials = services_deploy.kubectl.load_grpc_credentials()
    # change the endpoint name when verifying the TLS certificate as "localhost" is not
    # on the certificate.
    target_name_override_endpoint = "working-set-svc"
    options: list[tuple[str, str]] = [
        ("grpc.ssl_target_name_override", target_name_override_endpoint)
    ]

    url = ""
    for _ in range(30):
        with services_deploy.kubectl.port_forward(
            "deployment/working-set", 50051, get_next_forwarded_port()
        ) as port:
            url = f"localhost:{port}"
            if not _test_response(
                url, credentials=credentials, options=options, service_name=""
            ):
                time.sleep(10)
                continue
            else:
                yield GrpcWorkingSetClient.create_for_endpoint(
                    url,
                    credentials=credentials,
                    options=options,
                )
                break
    else:
        print(f"TIMEOUT testing response from {url}")
        # the test will likely fail
        yield GrpcWorkingSetClient.create_for_endpoint(
            url,
            credentials=credentials,
            options=options,
        )


# We don't directly use bigtable_proxy_client for anything. This is just to ensure that the pod
# is properly up and port-forwarded to an open port.
@pytest.fixture(scope="session")
def bigtable_proxy_ready(
    services_deploy: k8s_test_helper.DeployInfo,
) -> Generator[bool, None, None]:
    """Ensure the bigtable proxy pod is up and accessible.
    Returns True if the pod is ready, False if it times out.
    If applicable, it will update or create the application in Kubernetes.
    """
    credentials = services_deploy.kubectl.load_grpc_credentials()
    # change the endpoint name when verifying the TLS certificate as "localhost" is not
    # on the certificate.
    target_name_override_endpoint = "bigtable-proxy-svc"
    options: list[tuple[str, str]] = [
        ("grpc.ssl_target_name_override", target_name_override_endpoint)
    ]
    url = ""
    for _ in range(30):
        with services_deploy.kubectl.port_forward(
            "deployment/bigtable-proxy", 50051, get_next_forwarded_port()
        ) as port:
            url = f"localhost:{port}"
            if not _test_response(
                url, credentials=credentials, options=options, service_name=""
            ):
                time.sleep(10)
                continue
            else:
                yield True
                break
    else:
        print(f"TIMEOUT testing response from {url}")
        # the test will likely fail
        yield False


@pytest.fixture(scope="session")
def tenant_id(
    tenant_watcher_client: TenantsClient,
) -> str:
    tenants = tenant_watcher_client.get_tenants()
    assert tenants
    tid = tenants[0].id
    assert tid, "Failed to find tenant ID for tenant 'augment'"
    return tid


@pytest.fixture()
def user_id_lambda() -> Callable[[str], tuple[str, str]]:
    """Return a lambda that can be used for generating unique user ids and emails.

    This functionality is desired so that tests involving the user index can
    use separate user ids and avoid using to the same index.
    """

    def create_user_id(prefix: str) -> tuple[str, str]:
        user_id = f"{prefix}-{generate_random_bytes(4).hex()}"
        user_email = f"{user_id}@augmentcode.com"
        return user_id, user_email

    return create_user_id


def _get_pod(
    api_client: kubernetes.client.ApiClient | None, namespace: str, deployment_name: str
) -> Optional[str]:
    v1 = kubernetes.client.CoreV1Api(api_client)
    ret = v1.list_namespaced_pod(namespace)
    for service in ret.items:
        name = service.metadata.name
        if name.startswith(deployment_name):
            return name


@pytest.fixture(scope="session")
def feature_flags_client(
    services_deploy: k8s_test_helper.DeployInfo,
) -> Generator[FakeFeatureFlagsClient, None, None]:
    """Return a client for the fake feature flags service."""
    with services_deploy.kubectl.port_forward(
        "deployment/fake-feature-flags", 8080, get_next_forwarded_port()
    ) as port:
        url = f"http://localhost:{port}"
        yield FakeFeatureFlagsClient(
            url,
        )


@pytest.fixture(autouse=True)
def feature_flag_clear(feature_flags_client: FakeFeatureFlagsClient):
    """Clear all feature flags before each test."""
    feature_flags_client.clear()
