load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:kubecfg.bzl", "kubecfg_multi")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

kubecfg_multi(
    name = "test_kubecfg",
    deps = [
        "//services/bigtable_proxy/server:kubecfg",
        "//services/checkpoint_indexer/server:kubecfg",
        "//services/content_manager/server:kubecfg",
        "//services/deploy:base_kubecfg",
        "//services/deploy:shard_namespace_base_kubecfg",
        "//services/tenant_watcher/server:kubecfg",
        "//services/test/fake_feature_flags:kubecfg",
        "//services/working_set/server:kubecfg",
    ],
)

pytest_test(
    name = "working_set_test",
    size = "enormous",
    timeout = "eternal",
    srcs = [
        "conftest.py",
        "working_set_test.py",
    ],
    args = [
        "--cloud",
        "GCP_US_CENTRAL1_DEV",
        # per-test case timeout
        "--timeout",
        "600",
    ],
    data = [
        ":test_kubecfg",
        "@k8s_binary//file:kubectl",
    ],
    tags = [
        "exclusive",
        "gcp",
        "postmerge-test",
        "system-test",
    ],
    deps = [
        "//base/blob_names/python:blob_names",
        "//base/python/grpc:health_check",
        "//base/python/k8s_test_helper",
        "//base/python/k8s_test_helper:k8s_resource",
        "//services/auth/central/server:auth_entities_py_proto",
        "//services/content_manager/client",
        "//services/tenant_watcher/client",
        "//services/test/fake_feature_flags:client_py",
        "//services/token_exchange/client:client_py",
        "//services/working_set/client:client_py",
        requirement("kubernetes"),
    ],
)
