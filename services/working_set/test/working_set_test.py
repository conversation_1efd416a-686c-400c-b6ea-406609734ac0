"""Integration test for the working set service."""

import hashlib
import logging
import queue
import threading
import time
import typing
from datetime import datetime, timedelta
from pathlib import Path
from random import randbytes

from google.protobuf.timestamp_pb2 import Timestamp
import grpc
import pytest

from base.blob_names.python.blob_names import Blo<PERSON>, get_blob_name
from base.blob_names import blob_names_pb2
from services.content_manager import content_manager_pb2
from services.content_manager.client.content_manager_client import (
    AnnIndexAsset,
    AnnIndexBlobInfo,
    AnnIndexKey,
    ContentKey,
    ContentManagerClient,
    ContentManagerException,
)
from services.lib.request_context.request_context import RequestContext
from services.working_set.client.client import WorkingSetClient


def content_from_blob_name_subkey(blob_name: str, sub_key: str) -> bytes:
    """Returns the content for a given blob name and sub key."""
    return hashlib.sha256(f"{blob_name}_{sub_key}".encode()).digest()


def test_upload_and_download(
    content_manager_client: ContentManagerClient,
    small_file: Path,
    request_context: RequestContext,
):
    """Tests the basic upload and download functionality of the content manager."""
    content = small_file.read_bytes()
    (blob_name, existed) = content_manager_client.upload(
        content, path=small_file.name, request_context=request_context
    )
    assert not existed

    # download
    download_content, metadata = content_manager_client.download_all(
        blob_name=blob_name, request_context=request_context
    )
    assert content == download_content
    assert metadata["path"] == small_file.name


def test_working_set_minimal(
    working_set_client: WorkingSetClient,
    request_context: RequestContext,
):
    """Tests the basic working set functionality of the working set service."""
    blobs = blob_names_pb2.Blobs(
        baseline_checkpoint_id="",
        added=[],
        deleted=[],
    )
    working_set_client.register_working_set(blobs, request_context)


def test_working_set_active_checkpoint_and_tkey(
    working_set_client: WorkingSetClient,
    content_manager_client: ContentManagerClient,
    small_file: Path,
    request_context: RequestContext,
):
    """Tests the working set functionality of the working set service when registering an active checkpoint."""
    content = small_file.read_bytes()
    (blob_name, existed) = content_manager_client.upload(
        content, path=small_file.name, request_context=request_context
    )
    assert not existed

    blobs = Blobs(
        added=[blob_name.encode()],
        deleted=[],
    )
    checkpoint_id = content_manager_client.checkpoint_blobs(
        blobs=blobs, request_context=request_context
    )

    working_set_client.create_ann_index_for_checkpoint(
        checkpoint_id, "test_transformation", request_context
    )

    active_checkpoints = working_set_client.get_active_checkpoints(request_context)
    assert len(active_checkpoints) == 1
    assert checkpoint_id in active_checkpoints

    active_transformation_keys = working_set_client.get_active_transformation_keys(
        request_context
    )
    assert len(active_transformation_keys) == 1
    assert "test_transformation" in active_transformation_keys
