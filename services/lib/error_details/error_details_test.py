"""Integration test for error details propagation."""

import json
import pytest
from unittest.mock import Mock
import grpc

from services.lib.error_details.error_details_pb2 import <PERSON>rrorC<PERSON>, ErrorDetails
from google.protobuf import json_format


class MockRpcError(grpc.RpcError):
    """Mock RPC error for testing."""

    def __init__(self, message: str, error_details: ErrorDetails = None):
        self.message = message
        self.error_details = error_details

    def code(self):
        return grpc.StatusCode.INVALID_ARGUMENT

    def details(self):
        return f"invalid argument: {self.message}"


def test_error_details_propagation_through_grpc():
    """Test that error details can be propagated through gRPC metadata."""
    # Create error details using protobuf API
    original_details = ErrorDetails()
    original_details.code = ErrorCode.INVALID_TOOL_DEFINITION
    original_details.message = "Invalid tool definition"
    original_details.detail = "Tool name contains invalid characters"
    original_details.help_uri = (
        "https://docs.augmentcode.com/errors/invalid-tool-definition"
    )

    # Create RPC error with details
    rpc_error = MockRpcError("Test error", original_details)

    # Verify the error has the details
    assert rpc_error.error_details is not None
    assert rpc_error.error_details.code == ErrorCode.INVALID_TOOL_DEFINITION
    assert rpc_error.error_details.message == "Invalid tool definition"
    assert rpc_error.error_details.detail == "Tool name contains invalid characters"
    assert (
        rpc_error.error_details.help_uri
        == "https://docs.augmentcode.com/errors/invalid-tool-definition"
    )

    # Simulate serialization for gRPC metadata using protobuf json_format
    serialized = json_format.MessageToJson(
        rpc_error.error_details, preserving_proto_field_name=True
    )

    # Simulate deserialization from gRPC metadata using protobuf json_format
    deserialized = ErrorDetails()
    json_format.Parse(serialized, deserialized)

    # Verify the details survived the round trip
    assert deserialized.code == original_details.code
    assert deserialized.message == original_details.message
    assert deserialized.detail == original_details.detail
    assert deserialized.help_uri == original_details.help_uri
