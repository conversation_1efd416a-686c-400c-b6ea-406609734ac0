load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:python.bzl", "py_proto_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

package(default_visibility = ["//visibility:public"])

proto_library(
    name = "error_details_proto",
    srcs = ["error_details.proto"],
    visibility = ["//services:__subpackages__"],
)

py_proto_library(
    name = "error_details_py_proto",
    protos = [":error_details_proto"],
    visibility = [
        "//base:__subpackages__",
        "//services:__subpackages__",
    ],
)

pytest_test(
    name = "error_details_test",
    srcs = ["error_details_test.py"],
    deps = [
        ":error_details_py_proto",
    ],
)
