syntax = "proto3";

package error_details;

// ErrorDetails provides more information on the underlying reason for an
// API error. Returned with HTTP response as application/json.
message ErrorDetails {
  // Note: code will be serialized as an int in JSON.
  ErrorCode code = 1;

  // Description of the error. Not specific to this instance of the error.
  string message = 2;

  // A more detailed error message which may include information specific to
  // this instance of the error.
  string detail = 3;

  // A URI to documentation about this error, if available
  optional string help_uri = 4;
}

enum ErrorCode {
  // An unusual choice...
  ERROR_UNSPECIFIED = 0;

  // Tool name or input schema do not match the required format
  // schema: jsonformat 2020-12
  // name: ^[a-zA-Z0-9_-]{1,64}$
  INVALID_TOOL_DEFINITION = 1;
  DUPLICATE_TOOL_NAMES = 2;

  // Image processing error
  IMAGE_PROCESSING_ERROR = 3;
  IMAGE_EMPTY = 4;
  IMAGE_INVALID_BASE64 = 5;

  // Tool use without tool result.
  // Tool result without tool use.
  // Duplicate or missing tool use ids.
  // These are all extension bugs, not user error.
  INVALID_TOOL_USE_HISTORY = 6;

  // Exceeded prompt length for a model call. This may not be due to any single
  // component being too long. Prompt formatter issue likely.
  PROMPT_LENGTH_EXCEEDED = 7;
  // Specific input component is too long; request is failed so that we do not
  // silently discard anything. Specific component listed in `detail`.
  INPUT_COMPONENT_LENGTH_EXCEEDED = 8;
}
