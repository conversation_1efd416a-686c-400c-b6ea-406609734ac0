"""Tests for the AnthropicBalancedClient."""

import unittest
from unittest.mock import MagicMock, patch
import time

from base import feature_flags
from base.third_party_clients.common import (
    InvalidArgumentRpcError,
    ResourceExhaustedRpcError,
    UnavailableRpcError,
)
from base.third_party_clients.third_party_model_client import ThirdPartyModelResponse
from services.lib.balanced_third_party_clients.anthropic_balanced import (
    AnthropicBalancedClient,
    CHAT_DEFAULT_RETRY_AFTER_SECONDS,
    CHAT_FALLBACK_MAX_SECONDS,
)
from services.lib.request_context.request_context import RequestContext
from services.third_party_arbiter import third_party_arbiter_pb2 as pb2
from services.third_party_arbiter.client.client import ThirdPartyArbiterClient


class AnthropicBalancedClientTest(unittest.TestCase):
    """Tests for the AnthropicBalancedClient."""

    def setUp(self):
        """Set up the test."""
        # Create mock arbiter client
        self.mock_arbiter_client = MagicMock(spec=ThirdPartyArbiterClient)

        # Create the balanced client with mocked constructor
        with patch(
            "services.lib.balanced_third_party_clients.anthropic_balanced.AnthropicDirectClient"
        ) as mock_direct_client_class, patch(
            "services.lib.balanced_third_party_clients.anthropic_balanced.AnthropicVertexAiClient"
        ) as mock_vertex_client_class:
            # Set up the mocks for the client instances
            self.mock_direct_client = MagicMock()
            self.mock_vertex_us_client = MagicMock()
            self.mock_vertex_eu_client = MagicMock()
            self.mock_vertex_asia_client = MagicMock()

            # Configure the mock classes to return our mock instances
            mock_direct_client_class.return_value = self.mock_direct_client
            mock_vertex_client_class.side_effect = [
                self.mock_vertex_us_client,
                self.mock_vertex_eu_client,
                self.mock_vertex_asia_client,
            ]

            # Create the balanced client with a valid model name from the mapping
            self.client = AnthropicBalancedClient(
                arbiter_client=self.mock_arbiter_client,
                anthropic_api_key="test-api-key",  # pragma: allowlist secret
                gcp_project_id="test-project-id",
                model_name="claude-3-7-sonnet@20250219",  # This will map to claude-3-7-sonnet-20250219 for direct client
                temperature=0.7,
                max_output_tokens=4096,
            )

        # Create a request context for testing
        self.request_context = RequestContext.create(request_source="test")

    def test_model_name_mapping(self):
        """Test that model names are correctly mapped."""
        # Test with a valid model name
        with patch(
            "services.lib.balanced_third_party_clients.anthropic_balanced.AnthropicDirectClient"
        ) as mock_direct_client_class, patch(
            "services.lib.balanced_third_party_clients.anthropic_balanced.AnthropicVertexAiClient"
        ) as mock_vertex_client_class:
            # Create mock instances
            mock_direct = MagicMock()
            mock_vertex = MagicMock()

            # Configure mocks
            mock_direct_client_class.return_value = mock_direct
            mock_vertex_client_class.return_value = mock_vertex

            # Create client with valid model name
            _ = AnthropicBalancedClient(
                arbiter_client=self.mock_arbiter_client,
                anthropic_api_key="test-api-key",  # pragma: allowlist secret
                gcp_project_id="test-project-id",
                model_name="claude-3-7-sonnet@20250219",
                temperature=0.7,
                max_output_tokens=4096,
            )

            # Verify direct client was created with mapped model name
            mock_direct_client_class.assert_called_once_with(
                api_key="test-api-key",  # pragma: allowlist secret
                model_name="claude-3-7-sonnet-20250219",  # Mapped name
                temperature=0.7,
                max_output_tokens=4096,
            )

            # Verify vertex client was created with original model name
            mock_vertex_client_class.assert_any_call(
                project_id="test-project-id",
                region="us-east5",
                model_name="claude-3-7-sonnet@20250219",  # Original name
                temperature=0.7,
                max_output_tokens=4096,
            )

        # Test with an invalid model name
        with patch(
            "services.lib.balanced_third_party_clients.anthropic_balanced.AnthropicDirectClient"
        ), patch(
            "services.lib.balanced_third_party_clients.anthropic_balanced.AnthropicVertexAiClient"
        ):
            # Create client with invalid model name
            with self.assertRaises(ValueError) as context:
                AnthropicBalancedClient(
                    arbiter_client=self.mock_arbiter_client,
                    anthropic_api_key="test-api-key",  # pragma: allowlist secret
                    gcp_project_id="test-project-id",
                    model_name="unsupported-model",
                    temperature=0.7,
                    max_output_tokens=4096,
                )

            # Verify error message
            self.assertIn(
                "Unsupported model name for Model enum: unsupported-model",
                str(context.exception),
            )
            self.assertIn("claude-3-7-sonnet@20250219", str(context.exception))
            self.assertIn("claude-3-5-sonnet-v2@20241022", str(context.exception))
            self.assertIn("claude-sonnet-4@20250514", str(context.exception))
            self.assertIn("claude-opus-4@20250514", str(context.exception))

    def test_claude_4_0_model_mapping(self):
        """Test that Claude 4.0 model names are correctly mapped."""
        with patch(
            "services.lib.balanced_third_party_clients.anthropic_balanced.AnthropicDirectClient"
        ) as mock_direct_client_class, patch(
            "services.lib.balanced_third_party_clients.anthropic_balanced.AnthropicVertexAiClient"
        ) as mock_vertex_client_class:
            # Create mock instances
            mock_direct = MagicMock()
            mock_vertex = MagicMock()

            # Configure mocks
            mock_direct_client_class.return_value = mock_direct
            mock_vertex_client_class.return_value = mock_vertex

            # Create client with Claude 4.0 model name
            client = AnthropicBalancedClient(
                arbiter_client=self.mock_arbiter_client,
                anthropic_api_key="test-api-key",  # pragma: allowlist secret
                gcp_project_id="test-project-id",
                model_name="claude-sonnet-4@20250514",
                temperature=0.7,
                max_output_tokens=4096,
            )

            # Verify direct client was created with mapped model name
            mock_direct_client_class.assert_called_once_with(
                api_key="test-api-key",  # pragma: allowlist secret
                model_name="claude-sonnet-4-20250514",  # Mapped name
                temperature=0.7,
                max_output_tokens=4096,
            )

            # Verify the model enum is correctly set
            self.assertEqual(client.model_enum, pb2.Model.CLAUDE_4_0_SONNET)

    def test_claude_4_0_opus_model_mapping(self):
        """Test that Claude 4.0 Opus model names are correctly mapped."""
        with patch(
            "services.lib.balanced_third_party_clients.anthropic_balanced.AnthropicDirectClient"
        ) as mock_direct_client_class, patch(
            "services.lib.balanced_third_party_clients.anthropic_balanced.AnthropicVertexAiClient"
        ) as mock_vertex_client_class:
            # Create mock instances
            mock_direct = MagicMock()
            mock_vertex = MagicMock()

            # Configure mocks
            mock_direct_client_class.return_value = mock_direct
            mock_vertex_client_class.return_value = mock_vertex

            # Create client with Claude 4.0 Opus model name
            client = AnthropicBalancedClient(
                arbiter_client=self.mock_arbiter_client,
                anthropic_api_key="test-api-key",  # pragma: allowlist secret
                gcp_project_id="test-project-id",
                model_name="claude-opus-4@20250514",
                temperature=0.7,
                max_output_tokens=4096,
            )

            # Verify direct client was created with mapped model name
            mock_direct_client_class.assert_called_once_with(
                api_key="test-api-key",  # pragma: allowlist secret
                model_name="claude-opus-4-20250514",  # Mapped name
                temperature=0.7,
                max_output_tokens=4096,
            )

            # Verify the model enum is correctly set
            self.assertEqual(client.model_enum, pb2.Model.CLAUDE_4_0_OPUS)

    def test_count_tokens(self):
        """Test the count_tokens method."""
        # Set up the mock
        self.mock_direct_client.count_tokens.return_value = 10

        # Call the method
        result = self.client.count_tokens("test message")

        # Verify the result
        self.assertEqual(result, 10)
        self.mock_direct_client.count_tokens.assert_called_once_with("test message")

    def test_generate_response_stream_direct_client(self):
        """Test the generate_response_stream method with direct client."""
        # Set up the mock arbiter response
        mock_target_response = pb2.GetTargetResponse(
            targets=[pb2.ThirdPartyClient.ANTHROPIC_DIRECT],
            delay_ms=0,
        )
        self.mock_arbiter_client.get_target.return_value = mock_target_response

        # Set up the mock direct client response
        mock_response = ThirdPartyModelResponse(text="test response")

        # Make sure the stream works for a generator
        self.mock_direct_client.generate_response_stream.return_value = (
            r for r in [mock_response]
        )

        # Call the method
        responses = list(
            self.client.generate_response_stream(
                model_caller="test",
                cur_message="test message",
                request_context=self.request_context,
            )
        )

        # Verify the result
        self.assertEqual(len(responses), 1)
        self.assertEqual(responses[0].text, "test response")

        # Verify the arbiter client was called with model and supported_targets
        self.mock_arbiter_client.get_target.assert_called_once_with(
            self.request_context,
            model=pb2.Model.CLAUDE_3_7_SONNET,
            supported_targets=[
                pb2.ThirdPartyClient.ANTHROPIC_DIRECT,
                pb2.ThirdPartyClient.ANTHROPIC_VERTEXAI_US_E5,
                pb2.ThirdPartyClient.ANTHROPIC_VERTEXAI_EU_W1,
                pb2.ThirdPartyClient.ANTHROPIC_VERTEXAI_AS_SE1,
            ],
        )

        # Verify the direct client was called
        self.mock_direct_client.generate_response_stream.assert_called_once()

        # Verify the report_response was called
        self.mock_arbiter_client.report_response.assert_called_once_with(
            request_context=self.request_context,
            model=pb2.Model.CLAUDE_3_7_SONNET,
            target=pb2.ThirdPartyClient.ANTHROPIC_DIRECT,
            is_successful=True,
            retry_after_seconds=None,
        )

    def test_generate_response_stream_claude_4_0(self):
        """Test the generate_response_stream method with Claude 4.0 model."""
        # Create a client with Claude 4.0 model
        with patch(
            "services.lib.balanced_third_party_clients.anthropic_balanced.AnthropicDirectClient"
        ) as mock_direct_client_class, patch(
            "services.lib.balanced_third_party_clients.anthropic_balanced.AnthropicVertexAiClient"
        ) as mock_vertex_client_class:
            # Create mock instances
            mock_direct = MagicMock()
            mock_vertex = MagicMock()

            # Configure mocks
            mock_direct_client_class.return_value = mock_direct
            mock_vertex_client_class.return_value = mock_vertex

            # Create client with Claude 4.0 model name
            claude_4_client = AnthropicBalancedClient(
                arbiter_client=self.mock_arbiter_client,
                anthropic_api_key="test-api-key",  # pragma: allowlist secret
                gcp_project_id="test-project-id",
                model_name="claude-sonnet-4@20250514",
                temperature=0.7,
                max_output_tokens=4096,
            )

            # Set up the mock arbiter response
            mock_target_response = pb2.GetTargetResponse(
                targets=[pb2.ThirdPartyClient.ANTHROPIC_DIRECT],
                delay_ms=0,
            )
            self.mock_arbiter_client.get_target.return_value = mock_target_response

            # Set up the mock direct client response
            mock_response = ThirdPartyModelResponse(text="Claude 4.0 response")
            mock_direct.generate_response_stream.return_value = (
                r for r in [mock_response]
            )

            # Call the method
            responses = list(
                claude_4_client.generate_response_stream(
                    model_caller="test",
                    cur_message="test message",
                    request_context=self.request_context,
                )
            )

            # Verify the result
            self.assertEqual(len(responses), 1)
            self.assertEqual(responses[0].text, "Claude 4.0 response")

            # Verify the arbiter client was called with Claude 4.0 model
            self.mock_arbiter_client.get_target.assert_called_with(
                self.request_context,
                model=pb2.Model.CLAUDE_4_0_SONNET,
                supported_targets=[
                    pb2.ThirdPartyClient.ANTHROPIC_DIRECT,
                    pb2.ThirdPartyClient.ANTHROPIC_VERTEXAI_US_E5,
                    pb2.ThirdPartyClient.ANTHROPIC_VERTEXAI_EU_W1,
                    pb2.ThirdPartyClient.ANTHROPIC_VERTEXAI_AS_SE1,
                ],
            )

            # Verify the report_response was called with Claude 4.0 model
            self.mock_arbiter_client.report_response.assert_called_with(
                request_context=self.request_context,
                model=pb2.Model.CLAUDE_4_0_SONNET,
                target=pb2.ThirdPartyClient.ANTHROPIC_DIRECT,
                is_successful=True,
                retry_after_seconds=None,
            )

    def test_generate_response_stream_claude_4_0_opus(self):
        """Test the generate_response_stream method with Claude 4.0 Opus model."""
        # Create a client with Claude 4.0 Opus model
        with patch(
            "services.lib.balanced_third_party_clients.anthropic_balanced.AnthropicDirectClient"
        ) as mock_direct_client_class, patch(
            "services.lib.balanced_third_party_clients.anthropic_balanced.AnthropicVertexAiClient"
        ) as mock_vertex_client_class:
            # Create mock instances
            mock_direct = MagicMock()
            mock_vertex = MagicMock()

            # Configure mocks
            mock_direct_client_class.return_value = mock_direct
            mock_vertex_client_class.return_value = mock_vertex

            # Create client with Claude 4.0 Opus model name
            claude_4_opus_client = AnthropicBalancedClient(
                arbiter_client=self.mock_arbiter_client,
                anthropic_api_key="test-api-key",  # pragma: allowlist secret
                gcp_project_id="test-project-id",
                model_name="claude-opus-4@20250514",
                temperature=0.7,
                max_output_tokens=4096,
            )

            # Set up the mock arbiter response
            mock_target_response = pb2.GetTargetResponse(
                targets=[pb2.ThirdPartyClient.ANTHROPIC_DIRECT],
                delay_ms=0,
            )
            self.mock_arbiter_client.get_target.return_value = mock_target_response

            # Set up the mock direct client response
            mock_response = ThirdPartyModelResponse(text="Claude 4.0 Opus response")
            mock_direct.generate_response_stream.return_value = (
                r for r in [mock_response]
            )

            # Call the method
            responses = list(
                claude_4_opus_client.generate_response_stream(
                    model_caller="test",
                    cur_message="test message",
                    request_context=self.request_context,
                )
            )

            # Verify the result
            self.assertEqual(len(responses), 1)
            self.assertEqual(responses[0].text, "Claude 4.0 Opus response")

            # Verify the arbiter client was called with Claude 4.0 Opus model
            self.mock_arbiter_client.get_target.assert_called_with(
                self.request_context,
                model=pb2.Model.CLAUDE_4_0_OPUS,
                supported_targets=[
                    pb2.ThirdPartyClient.ANTHROPIC_DIRECT,
                ],
            )

            # Verify the report_response was called with Claude 4.0 Opus model
            self.mock_arbiter_client.report_response.assert_called_with(
                request_context=self.request_context,
                model=pb2.Model.CLAUDE_4_0_OPUS,
                target=pb2.ThirdPartyClient.ANTHROPIC_DIRECT,
                is_successful=True,
                retry_after_seconds=None,
            )

    def test_generate_response_stream_vertex_us_client(self):
        """Test the generate_response_stream method with VertexAI US client."""
        # Set up the mock arbiter response
        mock_target_response = pb2.GetTargetResponse(
            targets=[pb2.ThirdPartyClient.ANTHROPIC_VERTEXAI_US_E5],
            delay_ms=0,
        )
        self.mock_arbiter_client.get_target.return_value = mock_target_response

        # Set up the mock vertex client response
        mock_response = ThirdPartyModelResponse(text="test response from vertex")

        # Make sure the stream works for something that isn't a generator
        self.mock_vertex_us_client.generate_response_stream.return_value = [
            mock_response
        ]

        # Call the method
        responses = list(
            self.client.generate_response_stream(
                model_caller="test",
                cur_message="test message",
                request_context=self.request_context,
            )
        )

        # Verify the result
        self.assertEqual(len(responses), 1)
        self.assertEqual(responses[0].text, "test response from vertex")

        # Verify the arbiter client was called with model and supported_targets
        self.mock_arbiter_client.get_target.assert_called_once_with(
            self.request_context,
            model=pb2.Model.CLAUDE_3_7_SONNET,
            supported_targets=[
                pb2.ThirdPartyClient.ANTHROPIC_DIRECT,
                pb2.ThirdPartyClient.ANTHROPIC_VERTEXAI_US_E5,
                pb2.ThirdPartyClient.ANTHROPIC_VERTEXAI_EU_W1,
                pb2.ThirdPartyClient.ANTHROPIC_VERTEXAI_AS_SE1,
            ],
        )

        # Verify the vertex client was called
        self.mock_vertex_us_client.generate_response_stream.assert_called_once()

        # Verify the report_response was called
        self.mock_arbiter_client.report_response.assert_called_once_with(
            request_context=self.request_context,
            model=pb2.Model.CLAUDE_3_7_SONNET,
            target=pb2.ThirdPartyClient.ANTHROPIC_VERTEXAI_US_E5,
            is_successful=True,
            retry_after_seconds=None,
        )

    def test_generate_response_stream_invalid_argument_error(self):
        """Test InvalidArgumentRpcError handling in the generate_response_stream method."""
        # Set up the mock arbiter response
        mock_target_response = pb2.GetTargetResponse(
            targets=[pb2.ThirdPartyClient.ANTHROPIC_DIRECT],
            delay_ms=0,
        )
        self.mock_arbiter_client.get_target.return_value = mock_target_response

        # Create an InvalidArgumentRpcError
        invalid_argument_error = InvalidArgumentRpcError("Invalid argument")

        # Set up the mock direct client to raise the exception
        self.mock_direct_client.generate_response_stream.side_effect = (
            invalid_argument_error
        )

        # Call the method and expect an exception
        with self.assertRaises(InvalidArgumentRpcError):
            list(
                self.client.generate_response_stream(
                    model_caller="test",
                    cur_message="test message",
                    request_context=self.request_context,
                )
            )

        # Verify the report_response was NOT called (no reporting for InvalidArgumentRpcError)
        self.mock_arbiter_client.report_response.assert_not_called()

    def test_no_fallback_for_invalid_argument_error(self):
        """Test that there is NO fallback for InvalidArgumentRpcError failures."""
        # Set up the mock arbiter response with multiple targets
        mock_target_response = pb2.GetTargetResponse(
            targets=[
                pb2.ThirdPartyClient.ANTHROPIC_DIRECT,
                pb2.ThirdPartyClient.ANTHROPIC_VERTEXAI_US_E5,
            ],
            delay_ms=0,
        )
        self.mock_arbiter_client.get_target.return_value = mock_target_response

        # Create an InvalidArgumentRpcError
        invalid_argument_error = InvalidArgumentRpcError("Invalid argument")

        # Set up the mock direct client to raise the exception
        self.mock_direct_client.generate_response_stream.side_effect = (
            invalid_argument_error
        )

        # Set up the mock vertex client to return a successful response
        # (this should never be called)
        mock_response = ThirdPartyModelResponse(text="fallback response from vertex")
        self.mock_vertex_us_client.generate_response_stream.return_value = [
            mock_response
        ]

        # Call the method and expect an InvalidArgumentRpcError
        with self.assertRaises(InvalidArgumentRpcError):
            list(
                self.client.generate_response_stream(
                    model_caller="test",
                    cur_message="test message",
                    request_context=self.request_context,
                )
            )

        # Verify the direct client was called
        self.mock_direct_client.generate_response_stream.assert_called_once()

        # Verify the vertex client was NOT called (no fallback)
        self.mock_vertex_us_client.generate_response_stream.assert_not_called()

        # Verify the report_response was NOT called (no reporting for InvalidArgumentRpcError)
        self.mock_arbiter_client.report_response.assert_not_called()

    def test_generate_response_stream_resource_exhausted_error(self):
        """Test ResourceExhaustedRpcError handling in the generate_response_stream method."""
        # Set up the mock arbiter response
        mock_target_response = pb2.GetTargetResponse(
            targets=[pb2.ThirdPartyClient.ANTHROPIC_DIRECT],
            delay_ms=0,
        )
        self.mock_arbiter_client.get_target.return_value = mock_target_response

        # Create a ResourceExhaustedRpcError with retry_after_seconds
        resource_exhausted_error = ResourceExhaustedRpcError(
            "Rate limit exceeded",
            True,
            5,  # 5 seconds
        )

        # Set up the mock direct client to raise the exception
        self.mock_direct_client.generate_response_stream.side_effect = (
            resource_exhausted_error
        )

        # Call the method and expect an exception
        with self.assertRaises(ResourceExhaustedRpcError):
            list(
                self.client.generate_response_stream(
                    model_caller="test",
                    cur_message="test message",
                    request_context=self.request_context,
                )
            )

        # Verify the report_response was called with is_successful=False and retry_after_seconds=5
        self.mock_arbiter_client.report_response.assert_called_once_with(
            request_context=self.request_context,
            model=pb2.Model.CLAUDE_3_7_SONNET,
            target=pb2.ThirdPartyClient.ANTHROPIC_DIRECT,
            is_successful=False,
            retry_after_seconds=5,  # 5 seconds
        )

    def test_generate_response_stream_resource_exhausted_mid_stream(self):
        """Test ResourceExhaustedRpcError handling in the generate_response_stream method when it occurs mid-stream.
        Expect no fallback and no reporting to arbiter."""
        # Set up the mock arbiter response
        mock_target_response = pb2.GetTargetResponse(
            targets=[
                pb2.ThirdPartyClient.ANTHROPIC_DIRECT,
                pb2.ThirdPartyClient.ANTHROPIC_VERTEXAI_US_E5,
            ],
            delay_ms=0,
        )
        self.mock_arbiter_client.get_target.return_value = mock_target_response

        # Create a ResourceExhaustedRpcError with retry_after_seconds
        resource_exhausted_error = ResourceExhaustedRpcError(
            "Rate limit exceeded mid-stream",
            True,
            5,  # 5 seconds
        )

        # Set up the mock direct client to raise the exception mid-stream
        ignored_response = ThirdPartyModelResponse(
            text="", final_parameters={"key": "value"}
        )
        mock_response = ThirdPartyModelResponse(text="test response")

        def mock_generator():
            yield ignored_response
            yield mock_response
            raise resource_exhausted_error

        self.mock_direct_client.generate_response_stream.return_value = mock_generator()

        # Call the method and expect an exception
        with patch.object(
            self.client, "logger", wraps=self.client.logger
        ) as mock_logger:
            with self.assertRaises(ResourceExhaustedRpcError):
                list(
                    self.client.generate_response_stream(
                        model_caller="test",
                        cur_message="test message",
                        request_context=self.request_context,
                    )
                )

            # Verify log was called
            mock_logger.error.assert_called_once_with(
                "Mid-stream error from client ANTHROPIC_DIRECT: ('Rate limit exceeded mid-stream', True, 5)"
            )

        # Called primary target
        self.mock_direct_client.generate_response_stream.assert_called_once()
        # Should NOT have called fallback after primary failed mid-stream
        self.mock_vertex_us_client.generate_response_stream.assert_not_called()  # No fallback mid-stream
        # Should report success before mid-stream error
        self.mock_arbiter_client.report_response.assert_called_once_with(
            request_context=self.request_context,
            model=pb2.Model.CLAUDE_3_7_SONNET,
            target=pb2.ThirdPartyClient.ANTHROPIC_DIRECT,
            is_successful=True,
            retry_after_seconds=None,
        )

    def test_generate_response_stream_resource_exhausted_error_no_retry(self):
        """Test ResourceExhaustedRpcError without retry_after_seconds."""
        # Set up the mock arbiter response
        mock_target_response = pb2.GetTargetResponse(
            targets=[pb2.ThirdPartyClient.ANTHROPIC_DIRECT],
            delay_ms=0,
        )
        self.mock_arbiter_client.get_target.return_value = mock_target_response

        # Create a ResourceExhaustedRpcError without retry_after_seconds
        resource_exhausted_error = ResourceExhaustedRpcError(
            "Credit balance too low", False, None
        )

        # Set up the mock direct client to raise the exception
        self.mock_direct_client.generate_response_stream.side_effect = (
            resource_exhausted_error
        )

        # Mock the feature flag context and set the default value
        mock_context = MagicMock()
        mock_context.lookup.return_value = 3  # Default value from feature flag

        with patch("base.feature_flags.get_global_context", return_value=mock_context):
            # Call the method and expect an exception
            with self.assertRaises(ResourceExhaustedRpcError):
                list(
                    self.client.generate_response_stream(
                        model_caller="test",
                        cur_message="test message",
                        request_context=self.request_context,
                    )
                )

            # Verify the report_response was called with is_successful=False and default retry_after_seconds
            self.mock_arbiter_client.report_response.assert_called_once_with(
                request_context=self.request_context,
                model=pb2.Model.CLAUDE_3_7_SONNET,
                target=pb2.ThirdPartyClient.ANTHROPIC_DIRECT,
                is_successful=False,
                retry_after_seconds=3,  # Default 3 seconds
            )

    def test_generate_response_stream_unavailable_error(self):
        """Test UnavailableRpcError handling in the generate_response_stream method."""
        # Set up the mock arbiter response
        mock_target_response = pb2.GetTargetResponse(
            targets=[pb2.ThirdPartyClient.ANTHROPIC_DIRECT],
            delay_ms=0,
        )
        self.mock_arbiter_client.get_target.return_value = mock_target_response

        # Create an UnavailableRpcError
        unavailable_error = UnavailableRpcError("Service unavailable")

        # Set up the mock direct client to raise the exception
        self.mock_direct_client.generate_response_stream.side_effect = unavailable_error

        # Call the method and expect an exception
        with self.assertRaises(UnavailableRpcError):
            list(
                self.client.generate_response_stream(
                    model_caller="test",
                    cur_message="test message",
                    request_context=self.request_context,
                )
            )

        # Verify the report_response was called with is_successful=False
        self.mock_arbiter_client.report_response.assert_called_once_with(
            request_context=self.request_context,
            model=pb2.Model.CLAUDE_3_7_SONNET,
            target=pb2.ThirdPartyClient.ANTHROPIC_DIRECT,
            is_successful=False,
            retry_after_seconds=None,
        )

    def test_fallback_on_resource_exhausted(self):
        """Test fallback to next target when ResourceExhaustedRpcError is raised."""
        # Set up the mock arbiter response with multiple targets
        mock_target_response = pb2.GetTargetResponse(
            targets=[
                pb2.ThirdPartyClient.ANTHROPIC_DIRECT,
                pb2.ThirdPartyClient.ANTHROPIC_VERTEXAI_US_E5,
            ],
            delay_ms=0,
        )
        self.mock_arbiter_client.get_target.return_value = mock_target_response

        # Create a ResourceExhaustedRpcError
        resource_exhausted_error = ResourceExhaustedRpcError(
            "Rate limit exceeded",
            True,
            5,  # 5 seconds
        )

        # Set up the mock direct client to raise the exception
        self.mock_direct_client.generate_response_stream.side_effect = (
            resource_exhausted_error
        )

        # Set up the mock vertex client to return a successful response
        mock_response = ThirdPartyModelResponse(text="fallback response from vertex")
        self.mock_vertex_us_client.generate_response_stream.return_value = [
            mock_response
        ]

        # Call the method
        responses = list(
            self.client.generate_response_stream(
                model_caller="test",
                cur_message="test message",
                request_context=self.request_context,
            )
        )

        # Verify the result
        self.assertEqual(len(responses), 1)
        self.assertEqual(responses[0].text, "fallback response from vertex")

        # Verify both clients were called
        self.mock_direct_client.generate_response_stream.assert_called_once()
        self.mock_vertex_us_client.generate_response_stream.assert_called_once()

        # Verify the report_response was called twice - once for the failure and once for the success
        self.assertEqual(self.mock_arbiter_client.report_response.call_count, 2)

        # First call should report failure for direct client
        first_call_args = self.mock_arbiter_client.report_response.call_args_list[0][1]
        self.assertEqual(
            first_call_args["model"],
            pb2.Model.CLAUDE_3_7_SONNET,
        )
        self.assertEqual(
            first_call_args["target"],
            pb2.ThirdPartyClient.ANTHROPIC_DIRECT,
        )
        self.assertEqual(first_call_args["is_successful"], False)
        self.assertEqual(first_call_args["retry_after_seconds"], 5)

        # Second call should report success for vertex client
        second_call_args = self.mock_arbiter_client.report_response.call_args_list[1][1]
        self.assertEqual(
            second_call_args["model"],
            pb2.Model.CLAUDE_3_7_SONNET,
        )
        self.assertEqual(
            second_call_args["target"],
            pb2.ThirdPartyClient.ANTHROPIC_VERTEXAI_US_E5,
        )
        self.assertEqual(second_call_args["is_successful"], True)
        self.assertEqual(second_call_args["retry_after_seconds"], None)

    def test_fallback_on_unavailable(self):
        """Test fallback to next target when UnavailableRpcError is raised."""
        # Set up the mock arbiter response with multiple targets
        mock_target_response = pb2.GetTargetResponse(
            targets=[
                pb2.ThirdPartyClient.ANTHROPIC_DIRECT,
                pb2.ThirdPartyClient.ANTHROPIC_VERTEXAI_US_E5,
            ],
            delay_ms=0,
        )
        self.mock_arbiter_client.get_target.return_value = mock_target_response

        # Create an UnavailableRpcError
        unavailable_error = UnavailableRpcError("Service unavailable")

        # Set up the mock direct client to raise the exception
        self.mock_direct_client.generate_response_stream.side_effect = unavailable_error

        # Set up the mock vertex client to return a successful response
        mock_response = ThirdPartyModelResponse(text="fallback response from vertex")
        self.mock_vertex_us_client.generate_response_stream.return_value = [
            mock_response
        ]

        # Call the method
        responses = list(
            self.client.generate_response_stream(
                model_caller="test",
                cur_message="test message",
                request_context=self.request_context,
            )
        )

        # Verify the result
        self.assertEqual(len(responses), 1)
        self.assertEqual(responses[0].text, "fallback response from vertex")

        # Verify both clients were called
        self.mock_direct_client.generate_response_stream.assert_called_once()
        self.mock_vertex_us_client.generate_response_stream.assert_called_once()

        # Verify the report_response was called twice - once for the failure and once for the success
        self.assertEqual(self.mock_arbiter_client.report_response.call_count, 2)

        # First call should report failure for direct client
        first_call_args = self.mock_arbiter_client.report_response.call_args_list[0][1]
        self.assertEqual(
            first_call_args["model"],
            pb2.Model.CLAUDE_3_7_SONNET,
        )
        self.assertEqual(
            first_call_args["target"],
            pb2.ThirdPartyClient.ANTHROPIC_DIRECT,
        )
        self.assertEqual(first_call_args["is_successful"], False)
        self.assertEqual(first_call_args["retry_after_seconds"], None)

        # Second call should report success for vertex client
        second_call_args = self.mock_arbiter_client.report_response.call_args_list[1][1]
        self.assertEqual(
            second_call_args["model"],
            pb2.Model.CLAUDE_3_7_SONNET,
        )
        self.assertEqual(
            second_call_args["target"],
            pb2.ThirdPartyClient.ANTHROPIC_VERTEXAI_US_E5,
        )
        self.assertEqual(second_call_args["is_successful"], True)
        self.assertEqual(second_call_args["retry_after_seconds"], None)

    def test_fallback_all_targets_fail(self):
        """Test behavior when all targets fail with ResourceExhaustedRpcError or UnavailableRpcError."""
        # Set up the mock arbiter response with multiple targets
        mock_target_response = pb2.GetTargetResponse(
            targets=[
                pb2.ThirdPartyClient.ANTHROPIC_DIRECT,
                pb2.ThirdPartyClient.ANTHROPIC_VERTEXAI_US_E5,
                pb2.ThirdPartyClient.ANTHROPIC_VERTEXAI_EU_W1,
            ],
            delay_ms=0,
        )
        self.mock_arbiter_client.get_target.return_value = mock_target_response

        # Create errors for each client
        direct_error = ResourceExhaustedRpcError(
            "Rate limit exceeded", True, 5
        )  # 5 seconds
        vertex_us_error = UnavailableRpcError("Service unavailable")
        vertex_eu_error = ResourceExhaustedRpcError(
            "Rate limit exceeded", True, 10
        )  # 10 seconds

        # Set up the mock clients to raise exceptions
        self.mock_direct_client.generate_response_stream.side_effect = direct_error
        self.mock_vertex_us_client.generate_response_stream.side_effect = (
            vertex_us_error
        )
        self.mock_vertex_eu_client.generate_response_stream.side_effect = (
            vertex_eu_error
        )

        # Call the method and expect the last error to be raised
        with self.assertRaises(ResourceExhaustedRpcError) as context:
            list(
                self.client.generate_response_stream(
                    model_caller="test",
                    cur_message="test message",
                    request_context=self.request_context,
                )
            )

        # Verify the exception is the last one (from vertex_eu_client)
        self.assertEqual(context.exception, vertex_eu_error)

        # Verify all clients were called
        self.mock_direct_client.generate_response_stream.assert_called_once()
        self.mock_vertex_us_client.generate_response_stream.assert_called_once()
        self.mock_vertex_eu_client.generate_response_stream.assert_called_once()

        # Verify report_response was called for each failure
        self.assertEqual(self.mock_arbiter_client.report_response.call_count, 3)

        # Check each call reports the correct failure
        first_call_args = self.mock_arbiter_client.report_response.call_args_list[0][1]
        self.assertEqual(
            first_call_args["model"],
            pb2.Model.CLAUDE_3_7_SONNET,
        )
        self.assertEqual(
            first_call_args["target"],
            pb2.ThirdPartyClient.ANTHROPIC_DIRECT,
        )
        self.assertEqual(first_call_args["is_successful"], False)
        self.assertEqual(first_call_args["retry_after_seconds"], 5)

        second_call_args = self.mock_arbiter_client.report_response.call_args_list[1][1]
        self.assertEqual(
            second_call_args["model"],
            pb2.Model.CLAUDE_3_7_SONNET,
        )
        self.assertEqual(
            second_call_args["target"],
            pb2.ThirdPartyClient.ANTHROPIC_VERTEXAI_US_E5,
        )
        self.assertEqual(second_call_args["is_successful"], False)
        self.assertEqual(second_call_args["retry_after_seconds"], None)

        third_call_args = self.mock_arbiter_client.report_response.call_args_list[2][1]
        self.assertEqual(
            third_call_args["model"],
            pb2.Model.CLAUDE_3_7_SONNET,
        )
        self.assertEqual(
            third_call_args["target"],
            pb2.ThirdPartyClient.ANTHROPIC_VERTEXAI_EU_W1,
        )
        self.assertEqual(third_call_args["is_successful"], False)
        self.assertEqual(third_call_args["retry_after_seconds"], 10)

    def test_no_targets_from_arbiter(self):
        """Test behavior when the arbiter returns no targets."""
        # Set up the mock arbiter response with no targets
        mock_target_response = pb2.GetTargetResponse(
            targets=[],
            delay_ms=0,
        )
        self.mock_arbiter_client.get_target.return_value = mock_target_response

        # Call the method and expect a RuntimeError
        with self.assertRaises(RuntimeError) as context:
            list(
                self.client.generate_response_stream(
                    model_caller="test",
                    cur_message="test message",
                    request_context=self.request_context,
                )
            )

        # Verify the error message
        self.assertEqual(str(context.exception), "No targets returned from arbiter")

        # Verify no clients were called
        self.mock_direct_client.generate_response_stream.assert_not_called()
        self.mock_vertex_us_client.generate_response_stream.assert_not_called()
        self.mock_vertex_eu_client.generate_response_stream.assert_not_called()
        self.mock_vertex_asia_client.generate_response_stream.assert_not_called()

        # Verify report_response was not called
        self.mock_arbiter_client.report_response.assert_not_called()

    def test_arbiter_error(self):
        """Test behavior when there's an error getting targets from the arbiter."""
        # Set up the mock arbiter client to raise an exception
        self.mock_arbiter_client.get_target.side_effect = Exception("Arbiter error")

        # Call the method and expect the exception to be propagated
        with self.assertRaises(Exception) as context:
            list(
                self.client.generate_response_stream(
                    model_caller="test",
                    cur_message="test message",
                    request_context=self.request_context,
                )
            )

        # Verify the error message
        self.assertEqual(str(context.exception), "Arbiter error")

        # Verify no clients were called
        self.mock_direct_client.generate_response_stream.assert_not_called()
        self.mock_vertex_us_client.generate_response_stream.assert_not_called()
        self.mock_vertex_eu_client.generate_response_stream.assert_not_called()
        self.mock_vertex_asia_client.generate_response_stream.assert_not_called()

        # Verify report_response was not called
        assert self.mock_arbiter_client.report_response.call_count == 0

    @patch(
        "services.lib.balanced_third_party_clients.anthropic_balanced.CHAT_FALLBACK_MAX_SECONDS"
    )
    @patch("base.feature_flags.get_global_context")
    @patch("time.time")
    def test_generate_response_stream_fallback_timeout_exceeded(
        self, mock_time, mock_get_global_context, mock_chat_fallback_max_seconds_flag
    ):
        """Test that fallback is skipped and TimeoutError is raised if max fallback time is exceeded."""
        # Configure mock for CHAT_FALLBACK_MAX_SECONDS feature flag
        FALLBACK_TIMEOUT_SECONDS = 10
        mock_chat_fallback_max_seconds_flag.get.return_value = FALLBACK_TIMEOUT_SECONDS

        # Configure mock for feature_flags.get_global_context
        mock_ff_context = MagicMock()
        mock_get_global_context.return_value = mock_ff_context

        # Configure lookup for CHAT_DEFAULT_RETRY_AFTER_SECONDS
        def mock_lookup_config(flag_name, default_value):
            if flag_name == CHAT_DEFAULT_RETRY_AFTER_SECONDS.name:
                return (
                    CHAT_DEFAULT_RETRY_AFTER_SECONDS.default
                )  # Return the flag's default integer
            return default_value  # Fallback for other flags if any

        mock_ff_context.lookup = MagicMock(side_effect=mock_lookup_config)

        # Configure time.time mock to simulate time passing
        # First call for start_time_seconds, second for elapsed_seconds check
        mock_time.side_effect = [1000.0, 1000.0 + FALLBACK_TIMEOUT_SECONDS + 0.1]

        # Set up the mock arbiter response with multiple targets
        mock_target_response = pb2.GetTargetResponse(
            targets=[
                pb2.ThirdPartyClient.ANTHROPIC_DIRECT,
                pb2.ThirdPartyClient.ANTHROPIC_VERTEXAI_US_E5,
            ],
            delay_ms=0,
        )
        self.mock_arbiter_client.get_target.return_value = mock_target_response

        # Set up the mock direct client to raise an exception
        # Using a generic ResourceExhaustedRpcError without retry_after_seconds to test default handling
        resource_exhausted_error = ResourceExhaustedRpcError("Rate limit exceeded")
        self.mock_direct_client.generate_response_stream.side_effect = (
            resource_exhausted_error
        )

        # Patch the logger for the client instance
        with patch.object(
            self.client, "logger", wraps=self.client.logger
        ) as mock_logger:
            # Call the method and expect a TimeoutError
            with self.assertRaises(ResourceExhaustedRpcError) as context:
                list(
                    self.client.generate_response_stream(
                        model_caller="test_timeout",
                        cur_message="test message",
                        request_context=self.request_context,
                    )
                )

        self.assertEqual(str(context.exception), "Rate limit exceeded")

        # Verify the logger warning
        # The elapsed time in the log message will be exactly FALLBACK_TIMEOUT_SECONDS + 0.1
        _start_time_sim = 1000.0
        _end_time_sim = _start_time_sim + FALLBACK_TIMEOUT_SECONDS + 0.1
        expected_elapsed_time = _end_time_sim - _start_time_sim
        expected_log_message = f"Failed to get response after {expected_elapsed_time} seconds, skipping fallback"
        mock_logger.warning.assert_any_call(expected_log_message)

        # Verify the direct client was called
        self.mock_direct_client.generate_response_stream.assert_called_once()

        # Verify the vertex client was NOT called (fallback skipped)
        self.mock_vertex_us_client.generate_response_stream.assert_not_called()

        # Verify the report_response was called for the first client
        # CHAT_DEFAULT_RETRY_AFTER_SECONDS is imported and its .get() will be called
        # on mock_ff_context

        self.mock_arbiter_client.report_response.assert_called_once_with(
            request_context=self.request_context,
            model=pb2.Model.CLAUDE_3_7_SONNET,
            target=pb2.ThirdPartyClient.ANTHROPIC_DIRECT,
            is_successful=False,
            retry_after_seconds=CHAT_DEFAULT_RETRY_AFTER_SECONDS.get(mock_ff_context),
        )
        # Ensure CHAT_FALLBACK_MAX_SECONDS.get was called with the correct context
        mock_chat_fallback_max_seconds_flag.get.assert_called_once_with(mock_ff_context)

    @patch(
        "services.lib.balanced_third_party_clients.anthropic_balanced.CHAT_FALLBACK_MAX_SECONDS"
    )
    @patch("base.feature_flags.get_global_context")
    @patch("time.time")
    def test_generate_response_stream_fallback_within_timeout(
        self, mock_time, mock_get_global_context, mock_chat_fallback_max_seconds_flag
    ):
        """Test that fallback occurs if time is within the max fallback time."""
        # Configure mock for CHAT_FALLBACK_MAX_SECONDS feature flag
        FALLBACK_TIMEOUT_SECONDS = 10
        mock_chat_fallback_max_seconds_flag.get.return_value = FALLBACK_TIMEOUT_SECONDS

        # Configure mock for feature_flags.get_global_context
        mock_ff_context = MagicMock()

        # Configure lookup for CHAT_DEFAULT_RETRY_AFTER_SECONDS
        def mock_lookup_config(flag_name, default_value):
            if flag_name == CHAT_DEFAULT_RETRY_AFTER_SECONDS.name:
                return (
                    CHAT_DEFAULT_RETRY_AFTER_SECONDS.default
                )  # Return the flag's default integer
            return default_value  # Fallback for other flags if any

        mock_ff_context.lookup = MagicMock(side_effect=mock_lookup_config)
        mock_get_global_context.return_value = mock_ff_context

        # Configure time.time mock to simulate time passing
        # First call for start_time_seconds, second for elapsed_seconds check
        mock_time.side_effect = [2000.0, 2000.0 + FALLBACK_TIMEOUT_SECONDS - 0.1]

        # Set up the mock arbiter response with multiple targets
        mock_target_response = pb2.GetTargetResponse(
            targets=[
                pb2.ThirdPartyClient.ANTHROPIC_DIRECT,
                pb2.ThirdPartyClient.ANTHROPIC_VERTEXAI_US_E5,
            ],
            delay_ms=0,
        )
        self.mock_arbiter_client.get_target.return_value = mock_target_response

        # Set up the mock direct client to raise an exception
        resource_exhausted_error = ResourceExhaustedRpcError("Rate limit exceeded")
        self.mock_direct_client.generate_response_stream.side_effect = (
            resource_exhausted_error
        )

        # Set up the mock vertex client to return a successful response
        mock_fallback_response = ThirdPartyModelResponse(text="fallback response")
        self.mock_vertex_us_client.generate_response_stream.return_value = [
            mock_fallback_response
        ]

        # Patch the logger for the client instance
        with patch.object(
            self.client, "logger", wraps=self.client.logger
        ) as mock_logger:
            # Call the method
            responses = list(
                self.client.generate_response_stream(
                    model_caller="test_fallback_ok",
                    cur_message="test message",
                    request_context=self.request_context,
                )
            )

        # Verify the result from the fallback client
        self.assertEqual(len(responses), 1)
        self.assertEqual(responses[0].text, "fallback response")

        # Verify the "giving up" warning was NOT called
        # The elapsed time in the potential log message would be FALLBACK_TIMEOUT_SECONDS - 0.1
        expected_elapsed_time = FALLBACK_TIMEOUT_SECONDS - 0.1
        giving_up_log_message = (
            f"Failed to get response after {expected_elapsed_time} seconds, giving up"
        )
        for call_args in mock_logger.warning.call_args_list:
            self.assertNotEqual(call_args[0][0], giving_up_log_message)

        # Verify the "Falling back to next target" info log was called
        fallback_log_message = f"Falling back to next target after failure with {pb2.ThirdPartyClient.Name(pb2.ThirdPartyClient.ANTHROPIC_DIRECT)}"
        mock_logger.info.assert_any_call(fallback_log_message)

        # Verify the direct client was called
        self.mock_direct_client.generate_response_stream.assert_called_once()

        # Verify the vertex client WAS called (fallback occurred)
        self.mock_vertex_us_client.generate_response_stream.assert_called_once()

        # Verify the report_response was called for both clients
        self.assertEqual(self.mock_arbiter_client.report_response.call_count, 2)

        # First call (failure)
        self.mock_arbiter_client.report_response.assert_any_call(
            request_context=self.request_context,
            model=pb2.Model.CLAUDE_3_7_SONNET,
            target=pb2.ThirdPartyClient.ANTHROPIC_DIRECT,
            is_successful=False,
            retry_after_seconds=CHAT_DEFAULT_RETRY_AFTER_SECONDS.get(mock_ff_context),
        )
        # Second call (success)
        self.mock_arbiter_client.report_response.assert_any_call(
            request_context=self.request_context,
            model=pb2.Model.CLAUDE_3_7_SONNET,
            target=pb2.ThirdPartyClient.ANTHROPIC_VERTEXAI_US_E5,
            is_successful=True,
            retry_after_seconds=None,
        )
        # Ensure CHAT_FALLBACK_MAX_SECONDS.get was called with the correct context
        mock_chat_fallback_max_seconds_flag.get.assert_called_once_with(mock_ff_context)


if __name__ == "__main__":
    unittest.main()
