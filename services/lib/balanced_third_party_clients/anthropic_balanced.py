"""A client that uses the Third Party Arbiter to balance traffic between Anthropic clients."""

import time
from typing import Any, Generator, List, Optional, Tuple

import structlog
from typing_extensions import override

from base import feature_flags
from base.third_party_clients.anthropic_direct_client import AnthropicDirectClient
from base.third_party_clients.anthropic_vertexai_client import AnthropicVertexAiClient
from base.third_party_clients.common import (
    InvalidArgumentRpcError,
    ResourceExhaustedRpcError,
    UnavailableRpcError,
)
from base.third_party_clients.third_party_model_client import (
    Exchange,
    RequestMessage,
    ThirdPartyModelClient,
    ThirdPartyModelResponse,
    ToolChoice,
    ToolDefinition,
)
from services.lib.request_context.request_context import RequestContext
from services.third_party_arbiter import third_party_arbiter_pb2
from services.third_party_arbiter.client.client import ThirdPartyArbiterClient

logger = structlog.get_logger()


# Feature flag for default retry time in seconds for 429 errors when no retry-after header is provided
CHAT_DEFAULT_RETRY_AFTER_SECONDS = feature_flags.IntFlag(
    "chat_default_retry_after_seconds", 3
)

# Feature flag for maximum fallback time in seconds - do not attempt fallback after this time passed
CHAT_FALLBACK_MAX_SECONDS = feature_flags.IntFlag("chat_fallback_max_seconds", 30)

# Region constants for VertexAI clients
REGION_US_EAST5 = "us-east5"
REGION_EUROPE_WEST1 = "europe-west1"
REGION_ASIA_SOUTHEAST1 = "asia-southeast1"

# Constants for supported clients per model
CLAUDE_3_5_SUPPORTED_CLIENTS = [
    third_party_arbiter_pb2.ThirdPartyClient.ANTHROPIC_DIRECT,
    third_party_arbiter_pb2.ThirdPartyClient.ANTHROPIC_VERTEXAI_US_E5,
    third_party_arbiter_pb2.ThirdPartyClient.ANTHROPIC_VERTEXAI_EU_W1,
    third_party_arbiter_pb2.ThirdPartyClient.ANTHROPIC_VERTEXAI_AS_SE1,
]

CLAUDE_3_7_SUPPORTED_CLIENTS = [
    third_party_arbiter_pb2.ThirdPartyClient.ANTHROPIC_DIRECT,
    third_party_arbiter_pb2.ThirdPartyClient.ANTHROPIC_VERTEXAI_US_E5,
    third_party_arbiter_pb2.ThirdPartyClient.ANTHROPIC_VERTEXAI_EU_W1,
    third_party_arbiter_pb2.ThirdPartyClient.ANTHROPIC_VERTEXAI_AS_SE1,
]

CLAUDE_4_0_SUPPORTED_CLIENTS = [
    third_party_arbiter_pb2.ThirdPartyClient.ANTHROPIC_DIRECT,
    third_party_arbiter_pb2.ThirdPartyClient.ANTHROPIC_VERTEXAI_US_E5,
    third_party_arbiter_pb2.ThirdPartyClient.ANTHROPIC_VERTEXAI_EU_W1,
    third_party_arbiter_pb2.ThirdPartyClient.ANTHROPIC_VERTEXAI_AS_SE1,
]

CLAUDE_4_0_OPUS_SUPPORTED_CLIENTS = [
    third_party_arbiter_pb2.ThirdPartyClient.ANTHROPIC_DIRECT,
]


class AnthropicBalancedClient(ThirdPartyModelClient):
    """A client that uses the Third Party Arbiter to balance traffic between Anthropic clients.

    This client implements the ThirdPartyModelClient protocol and uses the Third Party Arbiter
    to determine which Anthropic client to use for each request. It also reports the result
    of each request back to the arbiter for load balancing purposes.
    """

    # Map of VertexAI model names to Direct Anthropic model names
    _MODEL_NAME_MAP = {
        "claude-3-7-sonnet@20250219": "claude-3-7-sonnet-20250219",
        "claude-3-5-sonnet-v2@20241022": "claude-3-5-sonnet-20241022",
        "claude-sonnet-4@20250514": "claude-sonnet-4-20250514",
        "claude-opus-4@20250514": "claude-opus-4-20250514",
    }

    # Map of VertexAI model names to Model enum values
    _MODEL_ENUM_MAP = {
        "claude-3-7-sonnet@20250219": third_party_arbiter_pb2.Model.CLAUDE_3_7_SONNET,
        "claude-3-5-sonnet-v2@20241022": third_party_arbiter_pb2.Model.CLAUDE_3_5_SONNET_V2,
        "claude-sonnet-4@20250514": third_party_arbiter_pb2.Model.CLAUDE_4_0_SONNET,
        "claude-opus-4@20250514": third_party_arbiter_pb2.Model.CLAUDE_4_0_OPUS,
    }

    def __init__(
        self,
        arbiter_client: ThirdPartyArbiterClient,
        anthropic_api_key: str,
        gcp_project_id: str,
        model_name: str,
        temperature: float,
        max_output_tokens: int,
    ):
        """Initialize the AnthropicBalancedClient.

        Args:
            arbiter_client: The Third Party Arbiter client to use for load balancing.
            anthropic_api_key: The API key for the direct Anthropic client.
            gcp_project_id: The GCP project ID for the VertexAI clients.
            model_name: The name of the model to use (in VertexAI format).
            temperature: The temperature parameter for controlling randomness.
            max_output_tokens: The maximum number of tokens to generate.

        Raises:
            ValueError: If the model name is not supported.
        """
        self.arbiter_client = arbiter_client
        self.vertex_model_name = model_name
        self.temperature = temperature
        self.max_output_tokens = max_output_tokens
        self.logger = logger.bind(client_type="anthropic_balanced")

        # Get the model enum value for this model
        self.model_enum = self._MODEL_ENUM_MAP.get(model_name)
        if self.model_enum is None:
            raise ValueError(
                f"Unsupported model name for Model enum: {model_name}. Supported models: {list(self._MODEL_ENUM_MAP.keys())}"
            )

        self.logger.info(
            "Creating anthropic_balanced client for model_name %s",
            model_name,
        )

        # Map the VertexAI model name to the Direct Anthropic model name
        direct_model_name = self._MODEL_NAME_MAP.get(self.vertex_model_name)
        if direct_model_name is None:
            raise ValueError(
                f"Unsupported model name: {model_name}. Supported models: {list(self._MODEL_NAME_MAP.keys())}"
            )

        self.logger.info(
            f"Using model mapping: {self.vertex_model_name} -> {direct_model_name}"
        )

        # Get the supported targets for this model
        supported_targets = []
        if self.model_enum == third_party_arbiter_pb2.Model.CLAUDE_4_0_SONNET:
            supported_targets = CLAUDE_4_0_SUPPORTED_CLIENTS
        elif self.model_enum == third_party_arbiter_pb2.Model.CLAUDE_3_7_SONNET:
            supported_targets = CLAUDE_3_7_SUPPORTED_CLIENTS
        elif self.model_enum == third_party_arbiter_pb2.Model.CLAUDE_3_5_SONNET_V2:
            supported_targets = CLAUDE_3_5_SUPPORTED_CLIENTS
        elif self.model_enum == third_party_arbiter_pb2.Model.CLAUDE_4_0_OPUS:
            supported_targets = CLAUDE_4_0_OPUS_SUPPORTED_CLIENTS
        else:
            raise ValueError(f"Unsupported model enum: {self.model_enum}")

        self.clients = {}
        if (
            third_party_arbiter_pb2.ThirdPartyClient.ANTHROPIC_VERTEXAI_US_E5
            in supported_targets
        ):
            self.clients[
                third_party_arbiter_pb2.ThirdPartyClient.ANTHROPIC_VERTEXAI_US_E5
            ] = AnthropicVertexAiClient(
                project_id=gcp_project_id,
                region=REGION_US_EAST5,
                model_name=model_name,
                temperature=temperature,
                max_output_tokens=max_output_tokens,
            )

        if (
            third_party_arbiter_pb2.ThirdPartyClient.ANTHROPIC_VERTEXAI_EU_W1
            in supported_targets
        ):
            self.clients[
                third_party_arbiter_pb2.ThirdPartyClient.ANTHROPIC_VERTEXAI_EU_W1
            ] = AnthropicVertexAiClient(
                project_id=gcp_project_id,
                region=REGION_EUROPE_WEST1,
                model_name=model_name,
                temperature=temperature,
                max_output_tokens=max_output_tokens,
            )

        if (
            third_party_arbiter_pb2.ThirdPartyClient.ANTHROPIC_VERTEXAI_AS_SE1
            in supported_targets
        ):
            self.clients[
                third_party_arbiter_pb2.ThirdPartyClient.ANTHROPIC_VERTEXAI_AS_SE1
            ] = AnthropicVertexAiClient(
                project_id=gcp_project_id,
                region=REGION_ASIA_SOUTHEAST1,
                model_name=model_name,
                temperature=temperature,
                max_output_tokens=max_output_tokens,
            )

        if (
            third_party_arbiter_pb2.ThirdPartyClient.ANTHROPIC_DIRECT
            in supported_targets
        ):
            self.clients[third_party_arbiter_pb2.ThirdPartyClient.ANTHROPIC_DIRECT] = (
                AnthropicDirectClient(
                    api_key=anthropic_api_key,
                    model_name=direct_model_name,
                    temperature=temperature,
                    max_output_tokens=max_output_tokens,
                )
            )

    @override
    def generate_response_stream(
        self,
        model_caller: str,
        messages: List[Tuple[str, str]] = [],  # Deprecated: use chat_history instead
        system_prompt: Optional[str] = None,
        cur_message: RequestMessage = "",
        chat_history: Optional[List[Exchange]] = None,
        tools: List[str] = [],
        tool_definitions: List[ToolDefinition] = [],
        tool_choice: Optional[ToolChoice] = None,
        temperature: Optional[float] = None,
        max_output_tokens: Optional[int] = None,
        prefill: Optional[str] = None,
        use_caching: bool = False,
        request_context: Optional[Any] = None,
        yield_final_parameters: bool = False,
    ) -> Generator[ThirdPartyModelResponse, None, None]:
        """Generate a response based on the given message.

        This method calls the Third Party Arbiter to determine which client to use,
        forwards the request to that client, and reports the result back to the arbiter.
        If a client fails with ResourceExhaustedRpcError or UnavailableRpcError, it will
        try the next client in the list of targets.

        Args:
            model_caller: A field passed in by the caller to track the caller.
            messages: Deprecated, use chat_history instead.
            system_prompt: The system prompt to use for the model.
            cur_message: The current message to generate a response for.
            chat_history: The messages to generate a response for.
            tools: List of tool names to use for the model.
            tool_definitions: List of tool definitions to use for the model.
            tool_choice: Control how the model uses tools.
            temperature: The temperature parameter for controlling randomness.
            max_output_tokens: The maximum number of tokens to generate.
            prefill: If provided, the model will start generating from this string.
            use_caching: If True, we insert cache breakpoints on the last two assistant messages.
            request_context: Optional request context containing information about the request.
            yield_final_parameters: If True, yield a ThirdPartyModelResponse with final_parameters.

        Returns:
            Generator[ThirdPartyModelResponse]: A sequence of responses from the model.
        """
        # Create a request context if not provided
        if not request_context:
            request_context = RequestContext.create(request_source="anthropic_balanced")

        feature_flag_context = feature_flags.get_global_context()

        # Get the target clients from the arbiter
        try:
            # Get the list of supported targets based on the model
            model_enum = self.model_enum
            assert model_enum is not None, "Model enum must be set"

            if model_enum == third_party_arbiter_pb2.Model.CLAUDE_4_0_SONNET:
                supported_targets = CLAUDE_4_0_SUPPORTED_CLIENTS
            elif model_enum == third_party_arbiter_pb2.Model.CLAUDE_3_7_SONNET:
                supported_targets = CLAUDE_3_7_SUPPORTED_CLIENTS
            elif model_enum == third_party_arbiter_pb2.Model.CLAUDE_3_5_SONNET_V2:
                supported_targets = CLAUDE_3_5_SUPPORTED_CLIENTS
            elif model_enum == third_party_arbiter_pb2.Model.CLAUDE_4_0_OPUS:
                supported_targets = CLAUDE_4_0_OPUS_SUPPORTED_CLIENTS
            else:
                # Fallback to all available clients
                supported_targets = list(self.clients.keys())

            self.logger.info(
                f"Requesting targets from arbiter with supported_targets: {supported_targets}"
            )

            target_response = self.arbiter_client.get_target(
                request_context,
                model=model_enum,
                supported_targets=supported_targets,
            )
            targets = target_response.targets

            if not targets or len(targets) == 0:
                self.logger.error("No targets returned from arbiter")
                raise RuntimeError("No targets returned from arbiter")

            # Apply any delay from the arbiter
            if target_response.delay_ms > 0:
                self.logger.info(f"Delaying request by {target_response.delay_ms}ms")
                time.sleep(target_response.delay_ms / 1000.0)

        except Exception as e:
            self.logger.error(f"Error getting target from arbiter: {e}")
            # Raise the exception instead of falling back to direct client
            raise

        start_time_seconds = time.time()

        # Try each target in sequence, falling back to the next one if the current one fails
        # due to any type of unavailability
        for target_index, target in enumerate(targets):
            client = self.clients.get(target)
            if not client:
                self.logger.error(
                    f"Unknown target: {third_party_arbiter_pb2.ThirdPartyClient.Name(target)}"
                )
                continue

            self.logger.info(
                f"Using client: {third_party_arbiter_pb2.ThirdPartyClient.Name(target)} (attempt {target_index + 1}/{len(targets)})"
            )

            # Get the stream and peek into first element to make sure it works - this block should end either
            # with a good response_generator, or raising an exception
            response_generator = None
            try:
                # Create a generator to capture the response
                response_generator = iter(
                    client.generate_response_stream(
                        model_caller=model_caller,
                        messages=messages,
                        system_prompt=system_prompt,
                        cur_message=cur_message,
                        chat_history=chat_history,
                        tools=tools,
                        tool_definitions=tool_definitions,
                        tool_choice=tool_choice,
                        temperature=temperature or self.temperature,
                        max_output_tokens=max_output_tokens or self.max_output_tokens,
                        prefill=prefill,
                        use_caching=use_caching,
                        request_context=request_context,
                        yield_final_parameters=yield_final_parameters,
                    )
                )

                # For reporting to arbiter, peek into first message
                # report success if first message succeeds
                # report failure if first message fails due to any type of unavailability
                first_messages = []
                for response in response_generator:
                    first_messages.append(response)
                    # `final_parameters` doesn't prove we got a successful third party response
                    if (
                        response.final_parameters is None
                        or len(response.final_parameters) == 0
                    ):
                        # If we got here, we were able to read the first third party API response successfully,
                        # report success to arbiter and yield everything we got so far
                        self.logger.info(
                            f"Reporting success to arbiter for target {third_party_arbiter_pb2.ThirdPartyClient.Name(target)} messages={len(first_messages)}"
                        )
                        try:
                            # Pass the model enum to the arbiter client
                            model_enum = self.model_enum
                            assert model_enum is not None, "Model enum must be set"

                            self.arbiter_client.report_response(
                                request_context=request_context,
                                model=model_enum,
                                target=target,
                                is_successful=True,
                                retry_after_seconds=None,
                            )
                        except Exception as report_error:
                            self.logger.error(
                                f"Error reporting response to arbiter: {report_error}"
                            )

                        break  # Just process the first item to check for errors

            except (ResourceExhaustedRpcError, UnavailableRpcError) as e:
                retry_after_seconds = None
                if isinstance(e, ResourceExhaustedRpcError):
                    self.logger.warning(
                        f"ResourceExhaustedRpcError from client {third_party_arbiter_pb2.ThirdPartyClient.Name(target)}: {e}"
                    )

                    # Extract retry_after_seconds if available
                    if (
                        hasattr(e, "retry_after_seconds")
                        and e.retry_after_seconds is not None
                    ):
                        retry_after_seconds = int(e.retry_after_seconds)
                    else:
                        # Default retry after for resource exhausted errors
                        retry_after_seconds = CHAT_DEFAULT_RETRY_AFTER_SECONDS.get(
                            feature_flag_context
                        )
                else:  # UnavailableRpcError
                    self.logger.warning(
                        f"UnavailableRpcError from client {third_party_arbiter_pb2.ThirdPartyClient.Name(target)}: {e}"
                    )

                try:
                    # Pass the model enum to the arbiter client
                    model_enum = self.model_enum
                    assert model_enum is not None, "Model enum must be set"

                    self.arbiter_client.report_response(
                        request_context=request_context,
                        model=model_enum,
                        target=target,
                        is_successful=False,
                        retry_after_seconds=retry_after_seconds,
                    )
                except Exception as report_error:
                    self.logger.error(
                        f"Error reporting response to arbiter: {report_error}"
                    )

                # If we've tried all targets and they all failed, raise the last error
                if target_index == len(targets) - 1:
                    self.logger.error("All targets failed, last error: {e}")
                    raise e

                # If we've exceeded the fallback timeout, do not fallback anymore
                elapsed_seconds = time.time() - start_time_seconds
                if elapsed_seconds > CHAT_FALLBACK_MAX_SECONDS.get(
                    feature_flag_context
                ):
                    self.logger.warning(
                        f"Failed to get response after {elapsed_seconds} seconds, skipping fallback"
                    )
                    raise e

                # The are more targets to try, continue to the next one
                self.logger.info(
                    f"Falling back to next target after failure with {third_party_arbiter_pb2.ThirdPartyClient.Name(target)}"
                )
                continue
            except InvalidArgumentRpcError as e:
                self.logger.error(
                    f"InvalidArgumentRpcError from client {third_party_arbiter_pb2.ThirdPartyClient.Name(target)}: {e}"
                )
                # Re-raise the exception immediately without trying other targets
                raise

            # We have a good response_generator, yield the responses
            try:
                yield from first_messages
                yield from response_generator
                self.logger.info("Stream completed successfully")
                return
            except Exception as e:
                self.logger.error(
                    f"Mid-stream error from client {third_party_arbiter_pb2.ThirdPartyClient.Name(target)}: {e}"
                )
                raise e

        # No success and no raise means no targets were attempted
        raise RuntimeError("All targets failed")

    @override
    def count_tokens(self, message: str) -> int:
        """Count the number of tokens in the given message.

        Args:
            message: The message to count the tokens for.

        Returns:
            int: The number of tokens in the given message.
        """
        # Use the direct client for token counting
        return self.clients[
            third_party_arbiter_pb2.ThirdPartyClient.ANTHROPIC_DIRECT
        ].count_tokens(message)
