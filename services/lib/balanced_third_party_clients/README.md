# Balanced Third Party Clients

These are third-party "virtual" clients that use the third-party-arbiter to load balance between a few third-party clients (generally defined in `base/third_party_clients`).

## AnthropicBalancedClient

The `AnthropicBalancedClient` is a client that uses the Third Party Arbiter to balance traffic between different Anthropic clients. It implements the `ThirdPartyModelClient` protocol and has the same API as `AnthropicMultiClient`.

### How it works

1. For each request, the client calls the Third Party Arbiter's `GetTarget` gRPC method to determine which client to use.
2. The client forwards the request to the selected client.
3. The client reports the result back to the arbiter using the `ReportResponse` gRPC method.
