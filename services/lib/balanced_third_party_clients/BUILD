load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:python.bzl", "py_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

py_library(
    name = "balanced_third_party_clients",
    srcs = [
        "__init__.py",
        "anthropic_balanced.py",
    ],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//base/third_party_clients:clients",
        "//base/third_party_clients:third_party_model_client",
        "//services/lib/request_context:request_context_py",
        "//services/third_party_arbiter:third_party_arbiter_py_proto",
        "//services/third_party_arbiter/client:client_py",
        requirement("structlog"),
    ],
)

pytest_test(
    name = "anthropic_balanced_test",
    srcs = ["anthropic_balanced_test.py"],
    deps = [
        ":balanced_third_party_clients",
        "//base/third_party_clients:third_party_model_client",
        "//services/lib/request_context:request_context_py",
        "//services/third_party_arbiter:third_party_arbiter_py_proto",
        "//services/third_party_arbiter/client:client_py",
    ],
)
