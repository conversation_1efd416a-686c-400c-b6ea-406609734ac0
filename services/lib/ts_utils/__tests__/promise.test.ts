import { describe, it, expect } from "vitest";
import {
  thenableCallback,
  DeferredPromise,
  dynamicRace,
  dynamicAny,
  CancellablePromise,
} from "../promise";

describe("promise.ts", () => {
  describe("thenableCallback", () => {
    it("should resolve with function return value when called", async () => {
      const fn = () => "hello";
      const thenable = thenableCallback(fn);

      thenable();
      const result = await thenable;

      expect(result).toBe("hello");
    });

    it("should pass arguments to the function", async () => {
      const fn = (a: number, b: number) => a + b;
      const thenable = thenableCallback(fn);

      thenable(2, 3);
      const result = await thenable;

      expect(result).toBe(5);
    });

    it("should reject when function throws", async () => {
      const error = new Error("test error");
      const fn = () => {
        throw error;
      };
      const thenable = thenableCallback(fn);

      expect(() => thenable()).toThrow(error);
      await expect(Promise.resolve(thenable)).rejects.toBe(error);
    });
  });

  describe("DeferredPromise", () => {
    it("should resolve with provided value", async () => {
      const deferred = new DeferredPromise<string>();

      deferred.resolve("success");

      expect(deferred.status).toBe("resolved");
      expect(deferred.value).toBe("success");
      await expect(deferred).resolves.toBe("success");
    });

    it("should reject with provided reason", async () => {
      const deferred = new DeferredPromise<string>();
      const error = new Error("test error");

      deferred.reject(error);

      expect(deferred.status).toBe("rejected");
      expect(deferred.reason).toBe(error);
      await expect(deferred).rejects.toBe(error);
    });

    it("should ignore subsequent resolve/reject calls", async () => {
      const deferred = new DeferredPromise<string>();

      deferred.resolve("first");
      deferred.resolve("second");
      deferred.reject(new Error("error"));

      expect(deferred.status).toBe("resolved");
      expect(deferred.value).toBe("first");
      await expect(deferred).resolves.toBe("first");
    });
  });

  describe("dynamicRace", () => {
    it("should resolve with the first settled promise", async () => {
      const promise1 = new DeferredPromise<string>();
      const promise2 = new DeferredPromise<string>();

      const racer = dynamicRace(promise1, promise2);
      promise1.resolve("second");
      const result = await racer;

      expect(result).toBe("second");
    });

    it("should allow adding promises dynamically", async () => {
      const promise1 = new DeferredPromise<string>();
      const racer = dynamicRace(promise1);
      const promise2 = new DeferredPromise<string>();
      racer.add(promise2);
      promise2.resolve("second");
      const result = await racer;
      expect(result).toBe("second");
    });

    it("should reject if the first settled promise rejects", async () => {
      const error = new Error("test error");
      const promise1 = new DeferredPromise<string>();
      const promise2 = new DeferredPromise<string>();

      const racer = dynamicRace(promise1, promise2);
      promise1.reject(error);

      await expect(racer).rejects.toBe(error);
    });
  });

  describe("dynamicAny", () => {
    it("should resolve with the first resolved promise", async () => {
      const promise1 = new DeferredPromise<string>();
      const promise2 = new DeferredPromise<string>();

      const any = dynamicAny(promise1, promise2);
      promise2.resolve("second");
      const result = await any;

      expect(result).toBe("second");
    });

    it("should ignore rejections", async () => {
      const error = new Error("test error");
      const promise1 = new DeferredPromise<string>();
      const promise2 = new DeferredPromise<string>();

      const any = dynamicAny(promise1, promise2);
      any.catch(() => {});
      // ignore unhandled rejections for the duration of this test
      process.once("unhandledRejection", () => {});
      promise1.reject(error);
      promise2.resolve("success");

      const result = await any;
      expect(result).toBe("success");
    });

    it("should allow adding promises dynamically", async () => {
      const any = dynamicAny();
      const promise1 = new DeferredPromise<string>();
      any.add(promise1);
      const promise2 = new DeferredPromise<string>();
      any.add(promise2);
      promise2.resolve("second");

      const result = await any;
      expect(result).toBe("second");
    });
  });

  describe("CancellablePromise", () => {
    it("should cancel a pending promise", () => {
      const promise = new CancellablePromise<string>();

      promise.cancel("test reason");

      expect(promise.status).toBe("cancelled");
      expect(promise.reason).toBe("test reason");
    });

    it("should not cancel a resolved promise", async () => {
      const promise = new CancellablePromise<string>();

      promise.resolve("success");
      promise.cancel("test reason");

      expect(promise.status).toBe("resolved");
      expect(promise.value).toBe("success");
      await expect(promise).resolves.toBe("success");
    });

    it("should not cancel a rejected promise", async () => {
      const promise = new CancellablePromise<string>();
      const error = new Error("test error");

      promise.reject(error);
      promise.cancel("test reason");

      expect(promise.status).toBe("rejected");
      expect(promise.reason).toBe(error);
      await expect(promise).rejects.toBe(error);
    });

    it("should not resolve or reject a cancelled promise", async () => {
      const promise = new CancellablePromise<string>();

      promise.cancel("test reason");
      promise.resolve("success");
      promise.reject(new Error("test error"));

      expect(promise.status).toBe("cancelled");
      expect(promise.reason).toBe("test reason");
    });
  });
});
