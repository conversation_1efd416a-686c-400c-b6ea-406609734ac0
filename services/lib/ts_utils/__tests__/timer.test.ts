import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import {
  createExponentialBackoff,
  delay,
  withTimeout,
  waitUntil,
  microtask,
  debounce,
} from "../timer";

describe("timer.ts", () => {
  beforeEach(() => {
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  describe("microtask", () => {
    it("should queue a microtask", async () => {
      const fn = vi.fn();

      microtask(fn);

      expect(fn).not.toHaveBeenCalled();

      // Wait for microtasks to execute
      await Promise.resolve();

      expect(fn).toHaveBeenCalledTimes(1);
    });

    it("should do nothing if no function is provided", async () => {
      // This just verifies it doesn't throw
      await expect(microtask()).resolves.toBeUndefined();
    });
  });

  describe("delay", () => {
    it("should resolve after the specified time", async () => {
      const promise = delay(100);

      // Initially not resolved
      let resolved = false;
      promise.then(() => {
        resolved = true;
      });

      expect(resolved).toBe(false);

      // Advance time and check resolution
      vi.advanceTimersByTime(99);
      await Promise.resolve();
      expect(resolved).toBe(false);

      vi.advanceTimersByTime(1);
      await Promise.resolve();
      expect(resolved).toBe(true);
    });
  });

  describe("withTimeout", () => {
    it("should resolve with the original promise if it completes before timeout", async () => {
      const originalPromise = new Promise<string>((resolve) => {
        setTimeout(() => resolve("success"), 50);
      });

      const timeoutPromise = withTimeout(
        originalPromise,
        100,
        () => new Error("Timeout"),
      );

      vi.advanceTimersByTime(50);
      await Promise.resolve();

      await expect(timeoutPromise).resolves.toBe("success");
    });

    it("should reject with the provided error if timeout occurs", async () => {
      const originalPromise = new Promise<string>((resolve) => {
        setTimeout(() => resolve("too late"), 200);
      });

      const timeoutPromise = withTimeout(
        originalPromise,
        100,
        () => new Error("Timeout"),
      );

      vi.advanceTimersByTime(100);
      await Promise.resolve();

      await expect(timeoutPromise).rejects.toThrow("Timeout");
    });

    it("should clear the timeout when the original promise resolves", async () => {
      const timeoutFn = vi.fn(() => new Error("Timeout"));

      const originalPromise = new Promise<string>((resolve) => {
        setTimeout(() => resolve("success"), 50);
      });

      const timeoutPromise = withTimeout(originalPromise, 100, timeoutFn);

      vi.advanceTimersByTime(50);
      await Promise.resolve();
      await timeoutPromise;

      vi.advanceTimersByTime(50);
      await Promise.resolve();

      expect(timeoutFn).not.toHaveBeenCalled();
    });
  });

  describe("waitUntil", () => {
    it("should resolve immediately if condition is already met", async () => {
      const condition = (value: string) => value.length > 0;
      const waitForString = waitUntil(condition, 1000, 100);

      const result = await waitForString(() => Promise.resolve("hello"));

      expect(result).toBe("hello");
    });

    it("should poll until condition is met", async () => {
      let counter = 0;
      const task = async () => ++counter;
      const condition = (value: number): value is number => value >= 3;

      const waitForCondition = waitUntil(condition, 1000, 100);
      waitForCondition(task);

      // First call happens immediately
      await Promise.resolve();
      expect(counter).toBe(1);

      // Second call after 100ms
      vi.advanceTimersByTime(100);
      await Promise.resolve();
      expect(counter).toBe(2);
    });

    it("should throw an error if timeout is reached", async () => {
      const condition = (value: number): value is number => value > 10;
      const task = async () => 5; // Will never meet condition

      const waitForValue = waitUntil(condition, 300, 100);
      const promise = waitForValue(task);

      vi.advanceTimersByTime(300);
      await Promise.resolve();

      await expect(promise).rejects.toThrow("Timeout reached");
    });
  });
});

describe("createExponentialBackoff", () => {
  beforeEach(() => {
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it("should succeed on first attempt", async () => {
    const backoff = createExponentialBackoff();
    const task = vi.fn().mockResolvedValue("success");

    const promise = backoff(task);
    await vi.runAllTimersAsync();

    const result = await promise;
    expect(result).toBe("success");
    expect(task).toHaveBeenCalledTimes(1);
  });

  it("should retry on failure", async () => {
    const backoff = createExponentialBackoff({
      initialDelayMs: 100,
      factor: 2,
    });

    const task = vi
      .fn()
      .mockRejectedValueOnce(new Error("fail"))
      .mockResolvedValueOnce("success");

    const promise = backoff(task);

    // First attempt fails
    await vi.runAllTimersAsync();

    // Wait for backoff delay
    await vi.advanceTimersByTimeAsync(100);

    // Second attempt succeeds
    await vi.runAllTimersAsync();

    const result = await promise;
    expect(result).toBe("success");
    expect(task).toHaveBeenCalledTimes(2);
  });

  it("should respect maxRetries", async () => {
    const backoff = createExponentialBackoff({
      initialDelayMs: 100,
      maxRetries: 2,
    });

    const error = new Error("fail");
    const task = vi.fn().mockRejectedValue(error);

    const promise = backoff(task);

    const onReject = vi.fn();
    promise.then(() => {}, onReject);

    // First attempt
    await vi.runAllTimersAsync();

    // First retry
    await vi.advanceTimersByTimeAsync(100);

    // Second retry
    await vi.advanceTimersByTimeAsync(200);

    expect(onReject).toHaveBeenCalledOnce();
    expect(task).toHaveBeenCalledTimes(3); // Initial + 2 retries
  });

  it("should apply exponential backoff with factor", async () => {
    const backoff = createExponentialBackoff({
      initialDelayMs: 100,
      factor: 2,
      jitter: false,
    });

    const task = vi
      .fn()
      .mockRejectedValueOnce(new Error("fail 1"))
      .mockRejectedValueOnce(new Error("fail 2"))
      .mockResolvedValueOnce("success");

    const promise = backoff(task);

    // First attempt fails
    expect(task).toHaveBeenCalledTimes(1);

    // First retry with 100ms delay
    await vi.advanceTimersByTimeAsync(100);
    expect(task).toHaveBeenCalledTimes(2);

    // Second retry with 200ms delay
    await vi.advanceTimersByTimeAsync(200);
    expect(task).toHaveBeenCalledTimes(3);

    const result = await promise;
    expect(result).toBe("success");
  });

  it("should respect maxDelayMs", async () => {
    const backoff = createExponentialBackoff({
      initialDelayMs: 100,
      maxDelayMs: 150,
      factor: 2,
      jitter: false,
    });

    const task = vi
      .fn()
      .mockRejectedValueOnce(new Error("fail 1"))
      .mockRejectedValueOnce(new Error("fail 2"))
      .mockResolvedValueOnce("success");

    const promise = backoff(task);

    // First attempt fails
    expect(task).toHaveBeenCalledTimes(1);

    // First retry with 100ms delay
    await vi.advanceTimersByTimeAsync(100);
    expect(task).toHaveBeenCalledTimes(2);

    // Second retry with 150ms delay (capped at maxDelayMs)
    await vi.advanceTimersByTimeAsync(150);
    expect(task).toHaveBeenCalledTimes(3);

    const result = await promise;
    expect(result).toBe("success");
  });

  it("should call onCoolDown after error", async () => {
    const onCoolDown = vi.fn();
    const backoff = createExponentialBackoff({
      initialDelayMs: 100,
      coolDownMs: 500,
      onCoolDown,
    });

    const task = vi.fn().mockRejectedValue(new Error("fail"));

    const promise = backoff(task);
    promise.catch(() => {});

    // First attempt fails
    expect(task).toHaveBeenCalledTimes(1);

    // First retry with 100ms delay
    await vi.advanceTimersByTimeAsync(100);

    // Second retry with 200ms delay
    await vi.advanceTimersByTimeAsync(200);

    // Third retry with 400ms delay
    await vi.advanceTimersByTimeAsync(400);

    // Should not have called onCoolDown yet
    expect(onCoolDown).not.toHaveBeenCalled();

    // Advance past cooldown period
    await vi.advanceTimersByTimeAsync(500);
    expect(onCoolDown).toHaveBeenCalledTimes(1);
  });
});

describe("debounce", () => {
  it("should debounce function calls", async () => {
    const fn = vi.fn();
    const debouncedFn = debounce(fn, 100);

    debouncedFn();
    expect(fn).not.toHaveBeenCalled();

    debouncedFn();
    expect(fn).not.toHaveBeenCalled();

    vi.advanceTimersByTime(50);
    expect(fn).not.toHaveBeenCalled();

    vi.advanceTimersByTime(50);
    await Promise.resolve();
    expect(fn).toHaveBeenCalledTimes(1);
  });

  it("should use latest arguments", async () => {
    const fn = vi.fn();
    const debouncedFn = debounce(fn, 100);

    debouncedFn(1);
    debouncedFn(2);

    vi.advanceTimersByTime(100);
    await Promise.resolve();
    expect(fn).toHaveBeenCalledWith(2);
  });

  it("should cancel previous timeout on new calls", async () => {
    const fn = vi.fn();
    const debouncedFn = debounce(fn, 100);

    debouncedFn();
    vi.advanceTimersByTime(50);
    debouncedFn();
    vi.advanceTimersByTime(50);
    expect(fn).not.toHaveBeenCalled();

    vi.advanceTimersByTime(50);
    await Promise.resolve();
    expect(fn).toHaveBeenCalledTimes(1);
  });
});
