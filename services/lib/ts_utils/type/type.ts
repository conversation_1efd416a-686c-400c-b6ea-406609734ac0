import type { KeyOf } from "./object";

// gets the union of K's types from each union member of T.
type UnionPropType<T, K extends PropertyKey> = T extends any
  ? K extends keyof T
    ? T[K]
    : never
  : never;

// gets whether K is a common key of union T
type IsCommonKey<T, K extends PropertyKey> =
  // Distribute over the union and check if K exists in each branch.
  Exclude<
    T extends any ? (K extends keyof T ? true : false) : never,
    true
  > extends never
    ? true
    : false;

/**
 * Intersects a union of objects.
 * @example
 * type Obj1 = { a: number; b: string; d?: boolean };
 * type Obj2 = { a: number; c: symbol; d: boolean };
 * type Intersected = IntersectedUnion<Obj1 | Obj2>;
 * //   ^? { a: number; b?: string; c?: symbol; d: boolean | undefined }
 */
export type IntersectedUnion<T extends object> = {
  [K in KeyOf<T> as IsCommonKey<T, K> extends true
    ? K
    : never]-?: UnionPropType<T, K>;
} & {
  [K in KeyOf<T> as <PERSON><PERSON>om<PERSON><PERSON><PERSON><T, K> extends true ? never : K]?: UnionPropType<
    T,
    K
  >;
};

type _ExampleIntersectedUnion = IntersectedUnion<
  { a: number; b: string; d?: boolean } | { a: number; c: symbol; d: boolean }
>;

/**
 * Converts a union to an intersection.
 * @example
 * type Union = { a: number } | { b: string } | { c: symbol };
 * type Intersection = UnionToIntersection<Union>;
 * //   ^? { a: number } & { b: string } & { c: symbol }
 */
export type UnionToIntersection<T> = (
  T extends any ? (x: T) => any : never
) extends (x: infer R) => any
  ? R
  : never;

type _ExampleUnionToIntersection = UnionToIntersection<
  { a: number } | { b: string } | { c: symbol }
>;

export type AnyFunction = (...args: any[]) => any;

export type IsAny<T> = 0 extends 1 & T ? true : false;

export type OmitAny<T> = {
  [K in keyof T as IsAny<T[K]> extends true ? never : K]: T[K];
};

export type Nullish = null | undefined;
