type _IncDigit = [1, 2, 3, 4, 5, 6, 7, 8, 9, 0];
type Digit = _IncDigit[number];

type _Inc<T extends string> = T extends `${infer F}${Digit}`
  ? T extends `${F}${infer L extends Digit}`
    ? `${L extends 9 ? _Inc<F> : F}${_IncDigit[L]}`
    : never
  : 1;

/** Works for numbers 0-1E16 */
export type Increment<T extends number> = number extends T
  ? number
  : `${T}` extends `${string}${"." | "+" | "-" | "e"}${string}`
    ? number
    : _Inc<`${T}`> extends `${infer N extends number}`
      ? N
      : never;

type _DecDigit = [9, 0, 1, 2, 3, 4, 5, 6, 7, 8];

type _Dec<T extends string> = T extends `${infer F}${Digit}`
  ? T extends `${F}${infer L extends Digit}`
    ? `${L extends 0 ? (F extends "1" ? "" : _Dec<F>) : F}${_DecDigit[L]}`
    : never
  : 0;

/** Works for numbers 1-1E50  */
export type Decrement<T extends number> = number extends T
  ? number
  : `${T}` extends `${string}${"." | "+" | "-" | "e"}${string}`
    ? number
    : T extends 0
      ? number
      : _Dec<`${T}`> extends `${infer N extends number}`
        ? N
        : never;

export type Range<Start extends number, End extends number> = Start extends End
  ? Start
  : Start | (End extends Start ? never : Range<Increment<Start>, End>);
