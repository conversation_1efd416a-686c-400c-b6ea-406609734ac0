import { expect, test } from "vitest";
import type { Satisfies } from "./satisfies";

test("should pass static type analysis", () => {
  // Objects --------------------------------------------------
  {
    type ObjectA = {
      name: string;
      age: number;
    };

    // PASS:
    let _a: Satisfies<{ name: string }, ObjectA>;
    let _b: Satisfies<{ name: string; age: number }, ObjectA>;

    // FAIL:
    type ObjWithExtraProp = { name: string; age: number; extra: boolean };
    // @ts-expect-error Test
    type _ObjectTest3 = Satisfies<ObjWithExtraProp, ObjectA>;
    // @ts-expect-error Test
    type _ObjectTest4 = Satisfies<{ name: "test" }, ObjWithExtraProp>;
  }

  // Arrays ---------------------------------------------------
  {
    // PASS:
    type _ArrayTest1 = Satisfies<string[], string[]>;

    // FAIL:
    // @ts-expect-error Test
    type _ArrayTest2 = Satisfies<string[], number[]>;
  }

  // Deep Objects ----------------------------------------------
  {
    type DeepObjectA = { user: { id: number; name: string } };

    // PASS:
    type _DeepObjectTest = Satisfies<{ user: { id: number } }, DeepObjectA>;

    // FAIL:
    // @ts-expect-error Test
    type _DeepObjectTest2 = Satisfies<{ user: { id: string } }, DeepObjectA>;
  }

  // Never ---------------------------------------------------
  {
    // PASS:
    type _NeverTest = Satisfies<never, never>;

    // FAIL:
    // @ts-expect-error Test
    type _NeverTest2 = Satisfies<never, string>;
  }

  /* Unions and Tuples should increase specificity */

  // Unions ---------------------------------------------------
  {
    // PASS:
    type _UnionTest = Satisfies<string | number, string>;

    // FAIL:
    // @ts-expect-error Test
    type _UnionTest2 = Satisfies<boolean, UnionA>;
  }

  // Tuples ---------------------------------------------------
  {
    // PASS:
    type _TupleTest1 = Satisfies<string[], [string, string]>;

    // FAIL:
    // @ts-expect-error Test
    type _TupleTest2 = Satisfies<[number], [string]>;
    // @ts-expect-error Test
    type _TupleTest3 = Satisfies<[string, number, string], [string, number]>;
  }

  // HACK: Add dummy test to satisfy Vitest minimum one test per file requirement
  expect(true).toBe(true);
});
