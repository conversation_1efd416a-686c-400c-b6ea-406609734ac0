export type DiscriminatedExtract<T, K extends keyof T, V extends T[K]> =
  T extends Record<K, V> ? T : never;

// Example usage:
type ExampleVehicle =
  | { type: "car"; wheels: 4; engine: "combustion" }
  | { type: "motorcycle"; wheels: 2; engine: "combustion" }
  | { type: "electric-scooter"; wheels: 2; engine: "electric" }
  | { type: "electric-car"; wheels: 4; engine: "electric" };

// Extract type where 'type' is 'car'
type _ExampleVehicleCar = DiscriminatedExtract<ExampleVehicle, "type", "car">;

// Extract type where 'engine' is 'electric'
type _ExampleVehicleElectric = DiscriminatedExtract<
  ExampleVehicle,
  "engine",
  "electric"
>;
