export type ExtractType<T, K extends PropertyKey, V> = T extends any
  ? K extends keyof T
    ? V extends T[K]
      ? T & Record<K, V>
      : never
    : never
  : never;

// Example usage:
type ExampleVehicle =
  | { type: "car"; wheels: 4; engine: "combustion" }
  | { type: "motorcycle"; wheels: 2; engine: "combustion" }
  | { type: "electric-scooter"; wheels: 2; engine: "electric" }
  | { type: "electric-car"; wheels: 4; engine: "electric" }
  | {
      type: "bicycle";
      wheels: 2;
      engine: "human";
      color: "red" | "blue" | "green";
    };

// Extract type where 'type' is 'car'
type _ExampleVehicleCar = ExtractType<ExampleVehicle, "type", "car">;

// Extract type where 'engine' is 'electric'
type _ExampleVehicleElectric = ExtractType<
  ExampleVehicle,
  "engine",
  "electric"
>;

// Extract type where 'color' is 'red'
type _ExampleRedBicycle = ExtractType<ExampleVehicle, "color", "red">;
