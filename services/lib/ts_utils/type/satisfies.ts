/**
 * Validates that a Prototype conforms to the minimal type requirements of Schema.
 *
 * This utility type encourages the definition of function parameters and interfaces
 * based on the minimal required properties and shapes.
 *
 * Minimizing over-specification promotes a more maintainable and adaptable
 * codebase. By focusing on what a function or method truly needs to operate, rather than
 * the complete structure of an input object, developers can reduce the effort required for
 * future modifications and enhancements.
 *
 * @example
 * type User = {
 *   id: number;
 *   name: string;
 *   age: number;
 *   enabled: boolean;
 * };
 *
 * function someFunc(
 *   user: Satisfies<{
 *     enabled: boolean;
 *     name: string;
 *   }, User>
 * ) {
 *   if (user.enabled) {
 *     doSomething(user.name);
 *   }
 * }
 * @template Prototype - Specifies the properties and types the consuming function or method actually needs.
 * @template Schema - A reference type that `Prototype` is validated against to ensure minimum necessary conformity.
 * @returns The `Prototype` type, signifying an object that satisfies the required properties and types of `Schema`.
 */
export type Satisfies<Prototype, _Schema extends Prototype> = Prototype;
