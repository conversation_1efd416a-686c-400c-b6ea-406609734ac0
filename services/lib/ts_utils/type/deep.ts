/**
 * Make all properties in T optional
 */
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends Array<infer U>
    ? Array<DeepPartial<U>>
    : T[P] extends object
      ? DeepPartial<T[P]>
      : T[P];
};

/** Hover over `_ExampleDeepPartial` to see the type */
type _ExampleDeepPartial = DeepPartial<{
  a: string;
  b: number;
  c: { d: string; e: number };
}>;

/**
 * Make all properties in T required
 */
export type DeepRequired<T> = {
  [P in keyof T]-?: T[P] extends Array<infer U>
    ? Array<DeepRequired<U>>
    : T[P] extends object
      ? DeepRequired<T[P]>
      : T[P];
};

/** Hover over `_ExampleDeepRequired` to see the type */
type _ExampleDeepRequired = DeepRequired<{
  a?: string;
  b?: number;
  c?: { d?: string; e?: number };
}>;

/**
 * Make all properties in T readonly
 */
export type DeepReadonly<T> = {
  readonly [P in keyof T]: T[P] extends ReadonlyArray<infer U>
    ? ReadonlyArray<DeepReadonly<U>>
    : T[P] extends object
      ? DeepReadonly<T[P]>
      : T[P];
};

/** Hover over `_ExampleDeepReadonly` to see the type */
type _ExampleDeepReadonly = DeepReadonly<{
  a: string;
  b: number;
  c: { d: string; e: number };
}>;

/** Make all properties in T mutable */
export type DeepMutable<T> =
  T extends ReadonlyArray<infer U>
    ? Array<DeepMutable<U>>
    : T extends ReadonlyMap<infer K, infer V>
      ? Map<DeepMutable<K>, DeepMutable<V>>
      : T extends ReadonlySet<infer U>
        ? Set<DeepMutable<U>>
        : {
            -readonly [P in keyof T]: DeepMutable<T[P]>;
          };

/** Hover over `_ExampleDeepMutable` to see the type */
type _ExampleDeepMutable = DeepMutable<{
  readonly a: string;
  readonly b: number;
  readonly c: { readonly d: string; readonly e: number };
}>;

/** Make all properties in T nullable*/
export type DeepNullable<T> = {
  [P in keyof T]: T[P] extends Array<infer U>
    ? Array<DeepNullable<U>>
    : T[P] extends object
      ? DeepNullable<T[P]>
      : T[P] | null;
};

/** Hover over `_ExampleDeepNullable` to see the type */
type _ExampleDeepNullable = DeepNullable<{
  a: string;
  b: number;
  c: { d: string; e: number };
}>;

/**
 * Make all properties in T non-nullable
 */
export type DeepNonNullable<T> = {
  [P in keyof T]: T[P] extends Array<infer U>
    ? Array<DeepNonNullable<U>>
    : T[P] extends object
      ? DeepNonNullable<T[P]>
      : NonNullable<T[P]>;
};

/** Hover over `_ExampleDeepNonNullable` to see the type */
type _ExampleDeepNonNullable = DeepNonNullable<{
  a: string | null;
  b: number | null;
  c: { d: string | null; e: number | null };
}>;
