/* eslint-disable @typescript-eslint/ban-types */

/**
 * Makes the type more readible by expanding all nested type definitions. This makes it easier to understand the structure of a type.
 * @note For development purposes only
 *
 * @example
 * type Foo = { a: string, b: { c: number } };
 * type Bar = Omit<Foo, 'a'>;
 * type BarPrettified = Prettify<Bar>; // { b: { c: number } }
 */
export type Prettify<T> = T extends Function
  ? T
  : T extends object
    ? T extends infer O
      ? { [K in keyof O]: Prettify<O[K]> }
      : never
    : T;
