import type { Prettify } from "./prettify";

export type EmptyObject = Record<never, never>;

/** gets the union of all keys from T */
export type KeyOf<T> = T extends any ? keyof T : never;

type _ExampleKeyOf = KeyOf<{ a: string; b: number; c: symbol }>;

/** Get the union of values of an object */
export type ValueOf<T> = T extends any ? T[keyof T] : never;

type _ExampleValueOf = ValueOf<{ a: string; b: number; c: symbol }>;

/** Get the union of all leaf values of an object */
export type DeepValueOf<T> = T extends object
  ? ValueOf<{ [P in keyof T]: DeepValueOf<T[P]> }>
  : T;

// Hover over `_ExampleDeepValueOf` to see the type
type _ExampleDeepValueOf = Prettify<
  DeepValueOf<{
    a: "a";
    b: 1;
    c: {
      d: "d";
      e: 2;
    };
  }>
>;
