import { thenableCallback } from "./promise.js";

/**
 * queues a microtask
 * @param work
 */
export async function microtask(work?: VoidFunction) {
  if (work) {
    queueMicrotask(work);
  }
}

/**
 * Delay for a given number of milliseconds.
 *
 * @param ms The number of milliseconds to delay.
 * @returns A promise that resolves after the delay.
 */
export function delay(ms: number) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

/**
 * Creates a timeout that can be cancelled or awaited.
 *
 * @param fn The function to call when the timeout is reached.
 * @param ms The number of milliseconds to wait before calling the function.
 * @returns A function that cancels the timeout but can also be awaited.
 * @example
 * const cancel = timeout(() => console.log("Hello, the socket is open"), 1000);
 * socket.onClose(cancel); // Will not log "Hello"
 * await cancel; // Will log "Hello"
 */
export function timeout(fn: () => void, ms?: number) {
  const timerId = setTimeout(fn, ms);
  return thenableCallback(() => {
    clearTimeout(timerId);
  });
}

/**
 * Creates an interval that can be cancelled or awaited.
 *
 * @param fn The function to call when the interval is reached.
 * @param ms The number of milliseconds to wait before calling the function.
 * @returns A function that cancels the interval but can also be awaited.
 */
export function interval(fn: () => void, ms: number) {
  const timerId = setInterval(fn, ms);
  return thenableCallback(() => {
    clearInterval(timerId);
  });
}

/**
 * Add a timeout to a promise.
 *
 * @example
 * await withTimeout(slowPromise(), 1000, () => {
 *   throw new Error("Timeout");
 * });
 */
export const withTimeout = <T, U>(
  promise: Promise<T>,
  timeoutMs: number,
  onTimeout: () => U,
): Promise<T> =>
  new Promise((resolve, reject) => {
    const timer = setTimeout(() => reject(onTimeout()), timeoutMs);
    promise.finally(() => clearTimeout(timer)).then(resolve, reject);
  });

/**
 * Creates a function that repeatedly executes an asynchronous task until
 * the result meets the specified condition or the timeout is reached.
 *
 * @param condition - A predicate function that checks the result.
 * @param timeoutMs - The maximum time in milliseconds to wait for the condition.
 * @param checkIntervalMs - The interval in milliseconds between task executions.
 * @returns A function that accepts an async task and returns a promise that resolves with the valid result.
 *
 * @example
 * const waitOneMinuteForTenantId = waitUntil(
 *   (tenantId: string | undefined) => tenantId !== undefined,
 *   minutesToMilliseconds(1), // timeout after 1 minute
 *   secondsToMilliseconds(5) // retry every 5 seconds
 * );
 * const tenantId = await waitOneMinuteForTenantId(async () => {
 *   const statusResponse =
 *     await authCentralClient.getCreateTenantForTeamStatus(
 *       user,
 *       createResponse.tenantCreationId,
 *     );
 *   return statusResponse.tenantCreation?.tenantId;
 * });
 */
export function waitUntil<T, U extends T>(
  condition: (result: T) => result is U,
  timeoutMs: number,
  checkIntervalMs?: number,
): (task: () => Promise<T>) => Promise<U>;

export function waitUntil<T>(
  condition: (result: T) => boolean,
  timeoutMs: number,
  checkIntervalMs?: number,
): (task: () => Promise<T>) => Promise<T>;

export function waitUntil<T, U extends T = T>(
  condition: ((result: T) => result is U) | ((result: T) => boolean),
  timeoutMs: number,
  checkIntervalMs = 100,
): (task: () => Promise<T>) => Promise<U> {
  return async function callTask(task) {
    const startTime = Date.now();
    let result: T = await task();

    while (!condition(result)) {
      if (Date.now() - startTime >= timeoutMs) {
        throw new TimeoutError();
      }
      // Wait before checking again.
      await delay(checkIntervalMs);
      result = await task();
    }
    return result as U;
  };
}

export class TimeoutError extends Error {
  name = "TimeoutError";
  constructor() {
    super("Timeout reached before condition was met.");
  }
}

type ExponentialBackoffOptions = {
  /** delay before the first retry (@default 100 ms) */
  initialDelayMs?: number;
  /** upper‑bound for the delay (@default 30 seconds) */
  maxDelayMs?: number;
  /** cool‑down time after a successful run (@default 30 seconds) */
  coolDownMs?: number;
  /** callback when cool‑down is triggered */
  onCoolDown?: () => void;
  /** multiplier applied after each failure (@default 2) */
  factor?: number;
  /** clamp the number of retries; `Infinity` means “keep trying” (@default `Infinity`) */
  maxRetries?: number;
  /**
   * Add ±50 % random jitter to each delay so that many callers don’t hammer
   * the service in lock‑step (@default `true`)
   */
  jitter?: boolean;
};

/**
 * Exponential‑back‑off
 * The delay grows by `factor` (default 2) after every *failing* attempt and
 * is **reset to the initial value** as soon as the task resolves without
 * throwing – i.e. when “everything is healthy again”.
 *
 * `createExponentialBackoff()` returns a closure that you call with the async
 * task you want to retry:
 *
 * ```ts
 * const backoff = createExponentialBackoff({ initialDelayMs: 200 });
 *
 * // …later – will keep retrying until `fetch("/health")` succeeds
 * await backoff(async () => {
 *   const res = await fetch("/health");
 *   if (!res.ok) throw new Error("unhealthy");
 *   return res;
 * });
 * ```
 */
export function createExponentialBackoff({
  initialDelayMs = 100,
  maxDelayMs = 30_000,
  coolDownMs = 30_000,
  onCoolDown,
  factor = 2,
  maxRetries = Infinity,
  jitter = true,
}: ExponentialBackoffOptions = {}) {
  let currentDelay = initialDelayMs;
  function resetToHealthy() {
    currentDelay = initialDelayMs;
    onCoolDown?.();
  }
  const coolDown = debounce(resetToHealthy, coolDownMs);

  /**
   * Runs `task` with automatic retries.
   * On success the internal delay counter is reset,
   * so the *next* failure starts back at `initialDelayMs`.
   */
  return async function runWithBackoff<T>(task: () => Promise<T>): Promise<T> {
    let attempt = 0;

    while (true) {
      try {
        return await task();
      } catch (err) {
        if (++attempt > maxRetries) throw err;
        const jitterAmount = jitter ? jitter50(currentDelay) : 0;
        await delay(currentDelay + jitterAmount);
        coolDown?.();

        // Exponentially increase delay for the *next* round.
        currentDelay = Math.min(currentDelay * factor, maxDelayMs);
      }
    }
  };
}

function jitter50(ms: number) {
  // random between 50 % and 150 % of `ms`
  return ms * (0.5 + Math.random());
}

/**
 * Debounces a function call.
 * @param func The function to debounce.
 * @param delay The delay in milliseconds.
 * @returns A debounced function.
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number,
) {
  let timeoutId: NodeJS.Timeout | null = null;
  return function (...args: Parameters<T>) {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    timeoutId = setTimeout(() => {
      func(...args);
    }, delay);
  };
}
