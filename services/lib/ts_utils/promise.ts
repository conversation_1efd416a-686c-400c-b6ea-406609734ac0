/**
 * transforms a function into a thenable function
 * a thenable function is a function that returns a promise that resolves with the return value of the function
 * when the function is called
 * @example
 * const thenable = thenableCallback(() => "hello");
 * setTimeout(thenable);
 * await thenable; // "hello"
 */
export function thenableCallback<T extends (...args: any[]) => any>(
  fn: T,
): T & Promise<ReturnType<T>> {
  const deferred: DeferredPromise<ReturnType<T>> = new DeferredPromise();
  const boundFunction = ((...args) => {
    try {
      if (deferred.status === "pending") {
        deferred.resolve(fn(...args));
      }
      return deferred.value;
    } catch (err) {
      deferred.reject(err);
      throw deferred.reason;
    }
  }) as T;

  return Object.setPrototypeOf(boundFunction, deferred);
}

type PromiseStatus = PromiseState<unknown>["status"];

export type PromiseState<T> =
  | {
      status: "pending";
    }
  | {
      status: "resolved";
      value: T;
    }
  | {
      status: "rejected";
      reason: Error;
    };

export class DeferredPromise<T, E = any, S extends string = never>
  implements Promise<T>
{
  protected promise: Promise<T>;
  /** resolves the promise */
  public resolve!: (value: T | PromiseLike<T>) => void;
  /** rejects the promise */
  public reject!: (reason?: any) => void;
  /** current state of the promise */
  public status: S | PromiseStatus = "pending";
  /** resolved value of the promise */
  public value?: T;
  /** rejection reason of the promise */
  public reason?: E;

  constructor(promise?: Promise<T>) {
    this.promise = new Promise<T>((resolve, reject) => {
      this.resolve = (value) => {
        if (this.status !== "pending") return;
        this.status = "resolved";
        this.value = value as T;
        resolve(value);
      };
      this.reject = (reason) => {
        if (this.status !== "pending") return;
        this.status = "rejected";
        this.reason = reason;
        reject(reason);
      };
    });
    promise?.then(this.resolve, this.reject);
  }

  then<TResult1 = T, TResult2 = never>(
    onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | null,
    onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | null,
  ): Promise<TResult1 | TResult2> {
    return this.promise.then(onfulfilled, onrejected);
  }

  catch<TResult = never>(
    onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | null,
  ): Promise<T | TResult> {
    return this.promise.catch(onrejected);
  }

  finally(onfinally?: (() => void) | null): Promise<T> {
    return this.promise.finally(onfinally);
  }

  get [Symbol.toStringTag](): string {
    return "Promise";
  }
}

type DynamicPromise<T> = DeferredPromise<T> & {
  add: (p: Promise<T>) => void;
};

/**
 * returns a promise that resolves when any of the promises settle
 * @example
 * const promise1 = new Promise(res => setTimeout(res, 1000, 'first!'));
 * const promise2 = new Promise(res => setTimeout(res, 500, 'second!'));
 * const racer = dynamicRace(promise1);
 * racer.then(result => {
 *   console.info('Race won by:', result);
 * });
 * // later, before promise1 settles, add promise2 into the race:
 * setTimeout(() => {
 *   racer.add(promise2);
 * }, 200);
 * // → after ~700ms you’ll see “Race won by: second!”
 */
export function dynamicRace<T>(...promises: Promise<T>[]): DynamicPromise<T> {
  const deferred = new DeferredPromise<T>() as DynamicPromise<T>;
  deferred.add = (p: Promise<T>) => {
    if (deferred.status !== "pending") return;
    p.then(deferred.resolve, deferred.reject);
  };
  promises.forEach(deferred.add);
  return deferred;
}

/**
 * returns a promise that resolves when any of the promises resolve and ignores rejections
 * @example
 * const any = dynamicAny();
 * const promise1 = new Promise(res => setTimeout(res, 1000, 'first!'));
 * const promise2 = new Promise(res => setTimeout(res, 500, 'second!'));
 * any.add(promise1);
 * any.add(promise2);
 * any.then(result => {
 *   console.info('Any resolved:', result);
 * });
 */
export function dynamicAny<T>(...promises: Promise<T>[]): DynamicPromise<T> {
  const deferred = new DeferredPromise<T>() as DynamicPromise<T>;
  deferred.add = (p: Promise<T>) => {
    if (deferred.status !== "pending") return;
    p.then(deferred.resolve);
  };
  promises.forEach(deferred.add);
  return deferred;
}

type CancellablePromiseState<T> =
  | PromiseState<T>
  | {
      status: "cancelled";
      reason?: any;
    };

export class CancellablePromise<T, E = any> extends DeferredPromise<
  T,
  E,
  CancellablePromiseState<T>["status"]
> {
  public status: CancellablePromiseState<T>["status"] = "pending";

  public cancel(reason?: any) {
    if (this.status !== "pending") return;
    this.status = "cancelled";
    this.reason = reason;
  }
}

export function isPromise<T>(v: any): v is Promise<T> {
  return v instanceof Promise;
}
