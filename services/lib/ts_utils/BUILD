load("@aspect_rules_js//js:defs.bzl", "js_library")
load("@aspect_rules_ts//ts:defs.bzl", "ts_config", "ts_project")
load("@npm//:defs.bzl", "npm_link_all_packages")
load("@npm//services/customer/frontend:vitest/package_json.bzl", vitest_bin = "bin")

npm_link_all_packages()

ts_config(
    name = "tsconfig",
    src = "tsconfig.json",
)

ts_project(
    name = "ts_utils",
    srcs = [
        "promise.ts",
        "timer.ts",
        "type/assert-unreachable.ts",
        "type/deep.ts",
        "type/extract.ts",
        "type/index.ts",
        "type/number.ts",
        "type/object.ts",
        "type/prettify.ts",
        "type/satisfies.ts",
        "type/type.ts",
        "type/union.ts",
    ],
    composite = True,
    declaration = True,
    incremental = True,
    out_dir = "ts_dist",
    resolve_json_module = True,
    ts_build_info_file = "ts_dist/.tsbuildinfo",
    tsconfig = ":tsconfig",
    visibility = ["//visibility:public"],
    deps = [
        ":node_modules/@types/node",
    ],
)

js_library(
    name = "pkg",
    srcs = [
        "package.json",
        ":ts_utils",
    ],
    visibility = ["//visibility:public"],
)

vitest_bin.vitest_test(
    name = "vitest_test",
    args = [
        "run",
        "--config=vite.config.ts",
    ],
    chdir = package_name(),
    data = glob(["**/*.ts"]) + [
        ":node_modules",
        ":ts_utils",
    ],
)
