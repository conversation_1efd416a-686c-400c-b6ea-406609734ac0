{"name": "@augment-internal/ts-utils", "version": "0.0.0", "private": true, "type": "module", "scripts": {"build": "tsc --build", "postinstall": "pnpm run build"}, "exports": {"./promise": {"types": "./ts_dist/promise.d.ts", "import": "./ts_dist/promise.js"}, "./timer": {"types": "./ts_dist/timer.d.ts", "import": "./ts_dist/timer.js"}, "./type": {"types": "./ts_dist/type/index.d.ts", "import": "./ts_dist/type/index.js"}}, "dependencies": {}, "devDependencies": {"@types/node": "^22.9.1", "typescript": "^5.6.3", "vite": "5.4.19", "vitest": "^2.1.5"}}