{"compilerOptions": {"lib": ["ES2022", "DOM"], "module": "ESNext", "target": "ES2022", "outDir": "ts_dist", "strict": true, "allowJs": false, "declaration": true, "incremental": true, "composite": true, "tsBuildInfoFile": "ts_dist/.tsbuildinfo", "esModuleInterop": true, "resolveJsonModule": true, "moduleResolution": "<PERSON><PERSON><PERSON>", "skipLibCheck": true, "types": ["node"]}, "include": ["*.ts", "type/**/*.ts"], "exclude": ["node_modules", "ts_dist", "__tests__", "**/*.test.ts"]}