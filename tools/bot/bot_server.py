"""Devtools slack bot server to send messages to engineers."""

import argparse
import logging
import os
import pathlib
import ssl
from concurrent.futures import Thread<PERSON>oolExecutor
from typing import List

import certifi
import grpc
from grpc_reflection.v1alpha import reflection
from prometheus_client import Counter, start_http_server
from slack_sdk import WebClient
from slack_sdk.errors import SlackApiError

import base.proto.services.access_pb2 as access_pb2
import tools.bot.bot_pb2 as bot_pb2
import tools.bot.bot_pb2_grpc as bot_pb2_grpc
from base.logging.struct_logging import setup_struct_logging
from tools.bot.config import BotConfig

_message_counter = Counter(
    "au_slack_messages_count",
    "Number of slack bot message events",
    labelnames=["type"],
)

_failure_counter = Counter(
    "au_slack_bot_failures",
    "Number of slack bot failures",
    labelnames=["type"],
)

SLACK_BLOCK_LIMIT = 3000
FEEDBACK_NOTE_LIMIT = 1000
USER_NOTE_MAX_LINES = 5
USER_AGENT_LIMIT = 80


def _render_access_type(access: access_pb2.AccessType) -> str:
    if access.WhichOneof("type") == "support_ui_access":
        # Check if this is a token scope request
        has_token_scopes = len(access.support_ui_access.token_scopes) > 0

        if has_token_scopes:
            scopes_str = ", ".join(
                f"`{scope}`" for scope in access.support_ui_access.token_scopes
            )
            return f"Token scope access for tenant `{access.support_ui_access.tenant_name}` with scopes: {scopes_str}"
        elif access.support_ui_access.scope == access_pb2.SupportUiAccessScope.REQUESTS:
            return f"Access to Requests and Content of tenant `{access.support_ui_access.tenant_name}`"
        elif access.support_ui_access.scope == access_pb2.SupportUiAccessScope.USERS:
            return f"Access for User Management of tenant `{access.support_ui_access.tenant_name}`"
        elif access.support_ui_access.scope == access_pb2.SupportUiAccessScope.FULL:
            return f"Full access to tenant `{access.support_ui_access.tenant_name}`"
        elif (
            access.support_ui_access.scope
            == access_pb2.SupportUiAccessScope.KUBERNETES_LIMITED
        ):
            return f"Limited Kubernetes access to tenant `{access.support_ui_access.tenant_name}`"
        else:
            return f"Access to tenant `{access.support_ui_access.tenant_name}` with scope `{access.support_ui_access.scope}`"
    elif access.WhichOneof("type") == "kubernetes_namespace_access":
        return f"Access Kubernetes resources for namespace `{access.kubernetes_namespace_access.namespace}`"
    else:
        raise NotImplementedError()


class SlackSender:
    """Class to send slack messages."""

    def __init__(self, token: str):
        self.token = token

    def get_link(self, channel: str, ts: str) -> str:
        """Get the link to a message in slack."""
        ssl_context = ssl.create_default_context(cafile=certifi.where())
        client = WebClient(token=self.token, ssl=ssl_context)
        try:
            response = client.chat_getPermalink(channel=channel, message_ts=ts)
            logging.info(response)
            return response["permalink"]
        except SlackApiError as e:
            logging.exception(e.response.get("error"))
            raise e

    def send_message(
        self, blocks: List, text: str, channel: str, thread_ts: str | None = None
    ) -> str:
        """Send a message to slack.

        Args:
            blocks: The blocks to send.
            text: The text to send.
            channel: The channel to send to.
            thread_ts: The thread to send to. If not set, send to the channel.

        Returns:
            The timestamp of the message.
        """
        ssl_context = ssl.create_default_context(cafile=certifi.where())
        client = WebClient(token=self.token, ssl=ssl_context)

        try:
            logging.info("Send message %s", blocks)
            response = client.chat_postMessage(
                channel=channel,
                blocks=blocks,
                text=text,
                thread_ts=thread_ts,
            )
            logging.info(response)
            return response["ts"]
        except SlackApiError as e:
            logging.exception(e.response.get("error"))
            raise e

    def lookup_user_by_email(self, email: str) -> str | None:
        ssl_context = ssl.create_default_context(cafile=certifi.where())
        client = WebClient(token=self.token, ssl=ssl_context)
        try:
            response = client.users_lookupByEmail(email=email)
            logging.info(response)
            return response["user"]["id"]
        except SlackApiError as e:
            logging.exception(e.response.get("error"))


class DevtoolsBotServices(bot_pb2_grpc.DevtoolsBotServicer):
    """DevtoolsBot RPC server."""

    def __init__(self, sender: SlackSender, config: BotConfig):
        self.sender = sender
        self.config = config

    def _limit_message(
        self,
        message: str,
        limit: int = SLACK_BLOCK_LIMIT,
        max_lines: int | None = None,
    ) -> str:
        # limit characters
        if len(message) > limit:
            message = message[: limit - 3] + "..."

        # limit lines
        if max_lines is not None:
            lines = message.splitlines()
            if len(lines) > max_lines:
                return "\n".join(lines[: max_lines - 1]) + "\n..."

        return message

    def NotifyAdhocDeployment(
        self, request: bot_pb2.NotifyAdhocDeploymentRequest, context
    ):
        try:
            logging.info("NotifyAdhocDeployment %s", request)
            message = f""":warning: Adhoc deployment requested by {request.requestor}@augmentcode.com."""
            message += (
                f"\nDeploy ID: <https://{request.deploy_url}|`{request.deploy_id}`>"
            )
            message += f"\nCommit: `{request.branch}/{request.commit_ref}`\n"
            message += f"\nReason: `{request.reason}`\n"
            for cloud in request.clouds:
                message += f"\nCloud: `{cloud}`"
            for namespace in request.namespaces:
                message += f"\nNamespace: `{namespace}`"
            for target in request.target_names:
                message += f"\nTarget: `{target}`"
            plain_message = (
                f"Adhoc deployment requested by {request.requestor}@augmentcode.com."
            )
            block = {
                "type": "section",
                "text": {"type": "mrkdwn", "text": self._limit_message(message)},
            }
            self.sender.send_message(
                [block], plain_message, channel=self.config.oncall_channel_id
            )
            _message_counter.labels(type="adhoc_deployment").inc()
            return bot_pb2.NotifyAdhocDeploymentResponse()
        except Exception as ex:  # pylint: disable=broad-exception-caught
            _failure_counter.labels(type="adhoc_deployment").inc()
            logging.error("NotifyAdhocDeployment failed: %s", ex)
            logging.exception(ex)
            raise

    def NotifyAdhocTestFinished(
        self, request: bot_pb2.NotifyAdhocTestFinishedRequest, context
    ):  # pylint: disable=unused-argument
        try:
            logging.info("NotifyAdhocTestFinished %s", request)
            if request.status == "Passed":
                message = f""":white_check_mark: Test <{request.details_url}|`{request.run_id}`> finished with status `{request.status}`.
"""
            else:
                message = f""":x: Test <{request.details_url}|`{request.run_id}`> finished with status `{request.status}`.
"""
                if request.non_success_test_targets:
                    message += "\n"
                    message += "Failed Test Targets:"
                    for t in request.non_success_test_targets:
                        message += f"\n - `{t}`"

                if request.flaky_test_targets:
                    message += "\n"
                    message += "Flaky Test Targets:"
                    for t in request.flaky_test_targets:
                        message += f"\n - `{t}`"

            plain_message = (
                f"Test {request.run_id} finished with status {request.status}."
            )
            block = {
                "type": "section",
                "text": {"type": "mrkdwn", "text": self._limit_message(message)},
            }
            user_id = self.sender.lookup_user_by_email(email=request.user_email)
            if not user_id:
                logging.error("User not found: %s", request.user_email)
            else:
                self.sender.send_message([block], plain_message, channel=user_id)
            response = bot_pb2.NotifyAdhocTestFinishedResponse()
            _message_counter.labels(type="adhoc_test_finished").inc()
            return response
        except Exception as ex:  # pylint: disable=broad-exception-caught
            _failure_counter.labels(type="adhoc_test_finished").inc()
            logging.error("NotifyAdhocTestFinished failed: %s", ex)
            logging.exception(ex)
            raise

    def NotifyDeploymentFailed(self, request: bot_pb2.NotifyDeploymentRequest, context):  # pylint: disable=unused-argument
        try:
            logging.info("NotifyDeploymentFailed %s", request)
            if request.namespace and request.cloud:
                message = f""":warning: Deployment `{request.name}` to namespace `{request.namespace}` in {request.cloud} failed."""
            else:
                message = f""":warning: Deployment `{request.name}` failed."""
            if request.commit:
                message = (
                    f"{message}\nCommit: <{request.commit_url}|`{request.commit}`>"
                )
            if request.details_url:
                message = f"{message}\nDetails <{request.details_url}>"
            if request.namespace:
                plain_message = (
                    f"Deployment {request.name} to {request.namespace} failed."
                )
            else:
                plain_message = f"Deployment {request.name} failed."
            block = {
                "type": "section",
                "text": {"type": "mrkdwn", "text": self._limit_message(message)},
            }
            self.sender.send_message(
                [block], plain_message, channel=self.config.cicd_channel_id
            )
            self.sender.send_message(
                [block], plain_message, channel=self.config.oncall_channel_id
            )
            _message_counter.labels(type="deployment_failed").inc()
            return bot_pb2.NotifyDeploymentResponse()
        except Exception as ex:  # pylint: disable=broad-exception-caught
            _failure_counter.labels(type="deployment_failed").inc()
            logging.error("NotifyDeploymentFailed failed: %s", ex)
            logging.exception(ex)
            raise

    def NotifyDeploymentFinished(
        self, request: bot_pb2.NotifyDeploymentFinishedRequest, context
    ):
        del context
        try:
            logging.info("NotifyDeploymentFinished %s", request)

            message = (
                f""":information_source: Deployment `{request.deploy_id}` finished."""
            )
            message += f"\nCommit: <{request.commit_url}|`{request.commit}`>"
            if request.deployment_schedule_name:
                message += (
                    f"\nDeployment Schedule: `{request.deployment_schedule_name}`"
                )
            if request.details_url:
                message += f"\nDetails <{request.details_url}>"

            deployment_blocks = []
            if request.deployments:
                message += "\nDeployment:\n"

                deployment_message = ""
                for deployment in sorted(
                    request.deployments,
                    key=lambda x: (-x.status, x.cloud, x.namespace, x.name),
                ):
                    if (
                        deployment.status != bot_pb2.DeploymentStatus.UNKNOWN
                        and deployment.status != bot_pb2.DeploymentStatus.SKIPPED
                    ):
                        # we do not report unknown and skipped deployments
                        status = bot_pb2.DeploymentStatus.Name(deployment.status)
                        if deployment.status == bot_pb2.DeploymentStatus.FAILED:
                            status = ":x:"
                        elif deployment.status == bot_pb2.DeploymentStatus.SUCCESS:
                            status = ":white_check_mark:"
                        deployment_name = f"{deployment.namespace}/{deployment.name}@{deployment.cloud}"
                        new_line = f"- `{deployment_name}`: {status}\n"
                        if len(deployment_message) + len(new_line) > SLACK_BLOCK_LIMIT:
                            deployment_blocks.append(
                                {
                                    "type": "section",
                                    "text": {
                                        "type": "mrkdwn",
                                        "text": self._limit_message(deployment_message),
                                    },
                                }
                            )
                            deployment_message = ""
                        deployment_message += new_line

                if deployment_message:
                    deployment_blocks.append(
                        {
                            "type": "section",
                            "text": {
                                "type": "mrkdwn",
                                "text": self._limit_message(deployment_message),
                            },
                        }
                    )

            plain_message = f"""Deployment `{request.deploy_id}` finished."""
            block = {
                "type": "section",
                "text": {"type": "mrkdwn", "text": self._limit_message(message)},
            }
            self.sender.send_message(
                [block] + deployment_blocks,
                plain_message,
                channel=self.config.cicd_channel_id,
            )

            return bot_pb2.NotifyDeploymentFinishedResponse()
        except grpc.RpcError as ex:
            logging.error("NotifyDeploymentFinished failed: %s", ex)
            logging.exception(ex)
            raise
        except Exception as ex:  # pylint: disable=broad-exception-caught
            logging.error("NotifyDeploymentFinished failed: %s", ex)
            logging.exception(ex)
            raise

    def NotifyPostmergeTestFailed(
        self,
        request: bot_pb2.NotifyPostMergeTestingFailedRequest,
        context,  # pylint: disable=unused-argument
    ):
        try:
            logging.info("NotifyPostmergeTestFailed %s", request)

            def commit_url(commit):
                return f"""<{commit.commit_url}|`{commit.sha[:8]}: {commit.commit_message}`> by {commit.author_name}"""

            if request.is_cancelled:
                message = [
                    f":bug: Post-Merge Testing of <{request.test_commit.commit_url}|`{request.test_commit.commit_message}`> cancelled\n"
                ]
            else:
                message = [
                    f":bug: Post-Merge Testing of <{request.test_commit.commit_url}|`{request.test_commit.commit_message}`> failed\n"
                ]
            thread_message = [f"Test Details: <{request.details_url}>"]

            if request.failed_test_targets:
                message.append("\n")
                message.append("Failed Test Tests:")
                for t in request.failed_test_targets:
                    if t.HasField("broken_since") and t.broken_since.run_id:
                        message.append(
                            f"\n - `{t.name}` - broken since {commit_url(t.broken_since.commit)} (<{t.broken_since.run_url}|`Test Run`>)"
                        )
                    else:
                        message.append(f"\n - `{t.name}` - *newly broken*")
            elif request.non_success_test_targets:
                message.append("\n")
                message.append("Failed Test Targets:")
                for t in request.non_success_test_targets:
                    message.append(f"\n - `{t}`")

            if request.flaky_test_targets:
                message.append("\n")
                message.append("Flaky Test Targets:")
                for t in request.flaky_test_targets:
                    message.append(f"\n - `{t}`")

            commits = reversed(list(request.included_commits))
            if commits:
                thread_message.append(
                    "\nChanges between last known good commit and test commit (oldest first):"
                )
                for commit in commits:
                    thread_message.append(f"\n - {commit_url(commit)}")
            else:
                thread_message.append(
                    "\nLast known good is probably already at or ahead of this commit due to a previous test run passing."
                )

            message_str = "\n".join(message)
            thread_message_str = "\n".join(thread_message)
            if request.is_cancelled:
                plain_message = (
                    f"Post-Merge Testing of {request.test_commit.sha} cancelled."
                )
            else:
                plain_message = (
                    f"Post-Merge Testing of {request.test_commit.sha} failed."
                )
            block = {
                "type": "section",
                "text": {"type": "mrkdwn", "text": self._limit_message(message_str)},
            }
            thread_block = {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": self._limit_message(thread_message_str),
                },
            }
            thread_plain_message = f"Test Details: {request.details_url}"

            thread_ts = self.sender.send_message(
                [block], plain_message, channel=self.config.cicd_channel_id
            )
            message_link = self.sender.get_link(
                channel=self.config.cicd_channel_id, ts=thread_ts
            )
            self.sender.send_message(
                [thread_block],
                thread_plain_message,
                channel=self.config.cicd_channel_id,
                thread_ts=thread_ts,
            )

            user_message = message[:]
            user_message.append(
                f"Check out this message in the thread: <{message_link}|Click here to view>"
            )
            user_message_str = "\n".join(user_message)
            user_block = {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": self._limit_message(user_message_str),
                },
            }
            if request.is_cancelled:
                user_plain_message = (
                    f"Post-Merge Testing of {request.test_commit.sha} cancelled."
                )
            else:
                user_plain_message = (
                    f"Post-Merge Testing of {request.test_commit.sha} failed."
                )

            commit_email = request.commit_email
            if not commit_email:
                if request.test_commit.author_email.endswith("@augmentcode.com"):
                    commit_email = request.test_commit.author_email

            if commit_email:
                user_id = self.sender.lookup_user_by_email(email=commit_email)
                if user_id:
                    self.sender.send_message(
                        [user_block],
                        user_plain_message,
                        user_id,
                    )
            _message_counter.labels("post_merge_test_failed").inc()
            return bot_pb2.NotifyPostMergeTestingFailedResponse()
        except Exception as ex:  # pylint: disable=broad-exception-caught
            _failure_counter.labels("post_merge_test_failed").inc()
            logging.error("NotifyPostmergeTestFailed failed: %s", ex)
            logging.exception(ex)
            raise

    def NotifyLastKnownGoodUpdated(
        self,
        request: bot_pb2.NotifyLastKnownGoodUpdatedRequest,
        context,  # pylint: disable=unused-argument
    ):
        try:
            logging.info("NotifyLastKnownGoodUpdated %s", request)

            def commit_url(commit):
                return f"""<{commit.commit_url}|`{commit.sha[:8]}: {commit.commit_message}`> by {commit.author_name}"""

            message = [
                f":rocket: Last Known Good Commit updated to <{request.last_known_good_commit.commit_url}|`{request.last_known_good_commit.commit_message}`>\n"
                "Changes:"
            ]
            commits = list(request.included_commits)
            for commit in commits:
                message.append(f"\n - {commit_url(commit)}")
            message = "\n".join(message)
            plain_message = f"Last Known Good Commit Updated to {request.last_known_good_commit.sha}"
            block = {
                "type": "section",
                "text": {"type": "mrkdwn", "text": self._limit_message(message)},
            }
            self.sender.send_message(
                [block], plain_message, channel=self.config.cicd_channel_id
            )
            _message_counter.labels("last_known_good_updated").inc()
            return bot_pb2.NotifyLastKnownGoodUpdatedResponse()
        except Exception as ex:  # pylint: disable=broad-exception-caught
            _failure_counter.labels("last_known_good_updated").inc()
            logging.error("NotifyLastKnownGoodUpdated failed: %s", ex)
            logging.exception(ex)
            raise

    def NotifyAccessProposed(
        self,
        request: bot_pb2.NotifyAccessProposedRequest,
        context: grpc.ServicerContext,  # pylint: disable=unused-argument
    ) -> bot_pb2.NotifyAccessProposedResponse:
        try:
            logging.info("NotifyAccessProposed %s", request)
            message = (
                f"""
            @channel :warning: Production cluster {request.cluster} access request from {request.proposer}@augmentcode.com
            \nAccess type: {_render_access_type(request.access)}
            \nReason: `{request.reason}`
            \n<{request.approval_url}|Approve>
            """
                if request.HasField("approval_url")
                else f"""
            :warning: Production cluster {request.cluster} access request from {request.proposer}@augmentcode.com
            \nAccess type: {_render_access_type(request.access)}
            \nReason: `{request.reason}`
            \nRequest auto-approved, no action required
            """
            )

            plain_message = f"Production cluster {request.cluster} access request from {request.proposer}@augmentcode.com"
            block = {
                "type": "section",
                "text": {"type": "mrkdwn", "text": self._limit_message(message)},
            }
            self.sender.send_message(
                [block], plain_message, channel=self.config.oncall_channel_id
            )
            _message_counter.labels(type="access_proposed").inc()
            return bot_pb2.NotifyAccessProposedResponse()
        except Exception as ex:  # pylint: disable=broad-exception-caught
            _failure_counter.labels(type="access_proposed").inc()
            logging.error("NotifyAccessProposed failed: %s", ex)
            logging.exception(ex)
            raise

    def NotifyAccessApproved(
        self,
        request: bot_pb2.NotifyAccessApprovedRequest,
        context: grpc.ServicerContext,  # pylint: disable=unused-argument
    ) -> bot_pb2.NotifyAccessApprovedResponse:
        try:
            logging.info("NotifyAccessApproved %s", request)
            message = f"""
            :warning: Production cluster {request.cluster} access for {request.proposer}@augmentcode.com approved by {request.approver}@augmentcode.com
            \nAccess type: {_render_access_type(request.access)}
            \nReason: `{request.reason}`
            """
            plain_message = f"Production cluster {request.cluster} access granted to {request.proposer}@augmentcode.com"
            block = {
                "type": "section",
                "text": {"type": "mrkdwn", "text": self._limit_message(message)},
            }
            self.sender.send_message(
                [block], plain_message, channel=self.config.oncall_channel_id
            )
            _message_counter.labels(type="access_approved").inc()
            return bot_pb2.NotifyAccessApprovedResponse()
        except Exception as ex:  # pylint: disable=broad-exception-caught
            _failure_counter.labels(type="access_approved").inc()
            logging.error("NotifyAccessApproved failed: %s", ex)
            logging.exception(ex)
            raise

    def NotifyUserFeedback(
        self,
        request: bot_pb2.NotifyUserFeedbackRequest,
        context: grpc.ServicerContext,  # pylint: disable=unused-argument
    ) -> bot_pb2.NotifyUserFeedbackResponse:
        try:
            logging.info(
                "Processing NotifyUserFeedback request for tenant %s request id %s",
                request.tenant_name,
                request.original_request_id,
            )
            genie_msg = (
                f" (<{request.genie_url}|Genie Access>)" if request.genie_url else ""
            )
            note = self._limit_message(
                request.note.strip(), FEEDBACK_NOTE_LIMIT, USER_NOTE_MAX_LINES
            )
            user_agent = self._limit_message(request.user_agent, USER_AGENT_LIMIT, 1)

            message = f""":memo: User Feedback For Request <{request.details_url}|{request.original_request_id}>{genie_msg}
Tenant: `{request.tenant_name}` | Rating: `{request.rating}`"""
            if user_agent:
                message += f"\nUser Agent: `{user_agent}`"
            plain_message = (
                f"Received User Feedback For Request {request.original_request_id}"
            )
            blocks = [
                {
                    "type": "section",
                    "text": {"type": "mrkdwn", "text": message},
                },
                {
                    "type": "section",
                    # Format the note in plain text so that we don't render links in user-provided text
                    "text": {"type": "plain_text", "text": f'"{note}"'},
                },
            ]
            self.sender.send_message(blocks, plain_message, channel=request.channel_id)
            _message_counter.labels(type="user_feedback").inc()
            return bot_pb2.NotifyUserFeedbackResponse()
        except Exception as ex:  # pylint: disable=broad-exception-caught
            _failure_counter.labels(type="user_feedback").inc()
            logging.error("NotifyUserFeedback failed: %s", ex)
            logging.exception(ex)
            raise

    def NotifyScheduledTestResult(
        self,
        request: bot_pb2.NotifyScheduledTestResultRequest,
        context: grpc.ServicerContext,  # pylint: disable=unused-argument
    ) -> bot_pb2.NotifyScheduledTestResultResponse:
        try:
            logging.info("NotifyScheduledTestResult %s", request)
            if request.status == "Passed":
                message = f":white_check_mark: Scheduled test `{request.schedule_name}` <{request.details_url}|`{request.run_id}`> finished with status `{request.status}`."
                if request.schedule_date:
                    message += f"\nScheduled Date: `{request.schedule_date}`"
            else:
                message = f":x: Scheduled test `{request.schedule_name}` <{request.details_url}|`{request.run_id}`> finished with status `{request.status}`."
                if request.schedule_date:
                    message += f"\nScheduled Date: `{request.schedule_date}`"

                if request.non_success_test_targets:
                    message += "\n\nFailed Test Targets:"
                    for t in request.non_success_test_targets:
                        message += f"\n - `{t}`"

                if request.flaky_test_targets:
                    message += "\n\nFlaky Test Targets:"
                    for t in request.flaky_test_targets:
                        message += f"\n - `{t}`"

            plain_message = f"Scheduled test {request.schedule_name} finished with status {request.status}."
            block = {
                "type": "section",
                "text": {"type": "mrkdwn", "text": self._limit_message(message)},
            }
            self.sender.send_message(
                [block], plain_message, channel=self.config.cicd_channel_id
            )
            _message_counter.labels(type="scheduled_test_result").inc()
            return bot_pb2.NotifyScheduledTestResultResponse()
        except Exception as ex:  # pylint: disable=broad-exception-caught
            _failure_counter.labels(type="scheduled_test_result").inc()
            logging.error("NotifyScheduledTestResult failed: %s", ex)
            logging.exception(ex)
            raise


def _serve(config: BotConfig):
    server = grpc.server(ThreadPoolExecutor(max_workers=1))
    sender = SlackSender(os.environ["SLACK_BOT_TOKEN"])
    bot_pb2_grpc.add_DevtoolsBotServicer_to_server(
        DevtoolsBotServices(sender, config), server
    )
    SERVICE_NAMES = (
        bot_pb2.DESCRIPTOR.services_by_name["DevtoolsBot"].full_name,
        reflection.SERVICE_NAME,
    )
    reflection.enable_server_reflection(SERVICE_NAMES, server)
    server.add_insecure_port("[::]:50051")
    server.start()
    logging.info("Listening on 50051")
    server.wait_for_termination()


def _load_config(config_file: pathlib.Path) -> BotConfig:
    return BotConfig.schema().loads(  # pylint: disable=no-member # type: ignore
        config_file.read_text()
    )


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--config-file", default=pathlib.Path("/config/config.json"), type=pathlib.Path
    )
    args = parser.parse_args()
    setup_struct_logging()

    logging.info("Args %s", args)

    config = _load_config(args.config_file)

    # begin listening for Prometheus requests
    start_http_server(9090)

    _serve(config)


if __name__ == "__main__":
    main()
