from launchdarkly_api.model.feature_flag import FeatureFlag
from launchdarkly_api.model.feature_flag_body import FeatureFlagBody

from tools.feature_flags.launchdarkly_types import (
    AugmentFeatureFlag,
    dict_to_feature_flag,
    apply_augment_flag,
    augment_flag_to_flag_body,
)
import pytest


def test_apply_augment_flag_delete_clause():
    """
    Reproduce and test a "list index out of range" bug in apply_augment_flag when removing some but not all clauses from a rule
    """
    flag_dict = {
        "name": "fake_boolean_flag",
        "key": "fake_boolean_flag",
        "description": "This is a boolean flag",
        "variations": [],
        "temporary": False,
        "environments": {
            "production": {
                "on": True,
                "archived": False,
                "fallthrough": {"variation": 0},
                "salt": "00000000000000000000000000000000",
                "sel": "00000000000000000000000000000000",
                "last_modified": 1709763631675,
                "version": 1,
                "site": {
                    "href": "/default/production/features/fake_boolean_flag",
                    "type": "text/html",
                },
                "environment_name": "Production",
                "track_events": False,
                "track_events_fallthrough": False,
                "rules": [
                    {
                        "id": "00000000-0000-0000-0000-000000000001",
                        "clauses": [
                            {
                                "id": "00000000-0000-0000-0000-000000000002",
                                "attribute": "key",
                                "op": "in",
                                "values": ["dogfood"],
                                "negate": False,
                                "contextKind": "namespace",
                            },
                            {
                                "id": "00000000-0000-0000-0000-000000000003",
                                "attribute": "pod_name",
                                "op": "startsWith",
                                "values": ["completion-blah-blah-foo"],
                                "negate": True,
                                "contextKind": "namespace",
                            },
                        ],
                        "ref": "fake_boolean_flag",
                        "track_events": False,
                    },
                ],
            }
        },
        "defaults": {"on_variation": 0, "off_variation": 1},
        "kind": "boolean",
        "version": 1,
        "creation_date": 1645555555555,
        "tags": [],
        "links": {},
        "archived": False,
        "deprecated": False,
        "experiments": {
            "baseline_idx": 0,
            "items": [],
        },
        "custom_properties": {},
        "client_side_availability": {
            "using_environment_id": False,
            "using_mobile_key": False,
        },
    }
    flag = dict_to_feature_flag(flag_dict)
    assert isinstance(flag, FeatureFlag)

    augment_flag_dict = {
        "key": "fake_boolean_flag",
        "sync": True,
        "description": "Removing a clause from the boolean flag",
        "envs": {
            "production": {
                "archived": False,
                "rules": [
                    {
                        "namespace": ["dogfood"],
                        "return_value": False,
                    }
                ],
            }
        },
    }
    augment_flag = AugmentFeatureFlag(**augment_flag_dict)
    assert isinstance(augment_flag, AugmentFeatureFlag)

    apply_augment_flag(flag, augment_flag)
    updated_rule = flag.environments["production"].rules[0]
    assert len(updated_rule.clauses) == 1
    assert updated_rule.clauses[0].attribute == "key"
    assert updated_rule.clauses[0].values == ["dogfood"]
    assert updated_rule.clauses[0].id == "00000000-0000-0000-0000-000000000002"


@pytest.mark.parametrize(
    "default_return_value, expected_variations, expected_names",
    [
        (True, [True, False], ["True", "False"]),
        (False, [False, True], ["False", "True"]),
        (42, [42, 43], ["42", "43"]),
        (3.14, [3.14, 4.14], ["3.14", "4.14"]),
        ("test", ["test", ""], ["test", "not-set"]),
        ("", ["", "unused"], ["not-set", "unused"]),
        (
            "a" * 256,
            ["a" * 256, ""],
            [
                "02d7160d77e18c6447be80c2e355c7ed4388545271702c50253b0914c65ce5fe",
                "not-set",
            ],
        ),
    ],
)
def test_augment_flag_to_flag_body(
    default_return_value, expected_variations: list, expected_names: list
):
    # Arrange
    augment_flag_dict = {
        "key": "test_flag",
        "sync": True,
        "description": "Test flag description",
        "envs": {
            "production": {
                "archived": False,
                "rules": [
                    {
                        "namespace": ["test"],
                        "return_value": expected_variations[1],
                    },
                    {
                        "return_value": expected_variations[0],
                    },
                ],
            }
        },
    }
    augment_flag = AugmentFeatureFlag(**augment_flag_dict)

    # Act
    result = augment_flag_to_flag_body(augment_flag)

    # Assert
    assert isinstance(result, FeatureFlagBody)
    assert result.key == "test_flag"
    assert result.name == "test_flag"
    assert result.description == "Test flag description"
    assert not result.temporary
    assert not result.client_side_availability.using_environment_id
    assert not result.client_side_availability.using_mobile_key
    assert result.defaults.on_variation == 0
    assert result.defaults.off_variation == 0

    assert len(result.variations) == 2
    assert result.variations[0].value == expected_variations[1]
    assert result.variations[1].value == expected_variations[0]

    assert result.variations[0].name == expected_names[1]
    assert result.variations[1].name == expected_names[0]
    assert result.variations[0].description == ""
    assert result.variations[1].description == ""


def test_augment_flag_to_flag_body_dict():
    variations = [{}, {"a": 1}]
    expected_variations = ["{}", '{"a": 1}']
    expected_names = ["{}", '{"a": 1}']
    # Arrange
    augment_flag_dict = {
        "key": "test_flag",
        "sync": True,
        "description": "Test flag description",
        "envs": {
            "production": {
                "archived": False,
                "rules": [
                    {
                        "namespace": ["test"],
                        "return_value": variations[1],
                    },
                    {
                        "return_value": variations[0],
                    },
                ],
            }
        },
    }
    augment_flag = AugmentFeatureFlag(**augment_flag_dict)

    # Act
    result = augment_flag_to_flag_body(augment_flag)

    # Assert
    assert isinstance(result, FeatureFlagBody)
    assert result.key == "test_flag"
    assert result.name == "test_flag"
    assert result.description == "Test flag description"
    assert not result.temporary
    assert not result.client_side_availability.using_environment_id
    assert not result.client_side_availability.using_mobile_key
    assert result.defaults.on_variation == 0
    assert result.defaults.off_variation == 0

    assert len(result.variations) == 2
    assert result.variations[0].value == expected_variations[1]
    assert result.variations[1].value == expected_variations[0]

    assert result.variations[0].name == expected_names[1]
    assert result.variations[1].name == expected_names[0]
    assert result.variations[0].description == ""
    assert result.variations[1].description == ""


def test_client_and_version_rules():
    """Test that client and client version rules are correctly parsed"""
    from tools.feature_flags.launchdarkly_types import (
        AugmentFeatureFlagRule,
        augment_flag_rule_to_flag_rule,
    )
    from launchdarkly_api.model.rule import Rule

    # Test client rule
    client_rule = AugmentFeatureFlagRule(return_value=True, client="intellij")

    flag_rule = augment_flag_rule_to_flag_rule(client_rule, [True, False])
    assert isinstance(flag_rule, Rule)
    assert len(flag_rule.clauses) == 1
    assert flag_rule.clauses[0].attribute == "client"
    assert flag_rule.clauses[0].op == "in"
    assert flag_rule.clauses[0].values == ["intellij"]
    assert flag_rule.clauses[0].context_kind == "namespace"

    # Test client list rule
    client_list_rule = AugmentFeatureFlagRule(
        return_value=True, client=["intellij", "vscode"]
    )

    flag_rule = augment_flag_rule_to_flag_rule(client_list_rule, [True, False])
    assert isinstance(flag_rule, Rule)
    assert len(flag_rule.clauses) == 1
    assert flag_rule.clauses[0].attribute == "client"
    assert flag_rule.clauses[0].op == "in"
    assert flag_rule.clauses[0].values == ["intellij", "vscode"]
    assert flag_rule.clauses[0].context_kind == "namespace"

    # Test min client version rule
    min_version_rule = AugmentFeatureFlagRule(
        return_value=True, min_client_version="0.70.0"
    )

    flag_rule = augment_flag_rule_to_flag_rule(min_version_rule, [True, False])
    assert isinstance(flag_rule, Rule)
    assert len(flag_rule.clauses) == 1
    assert flag_rule.clauses[0].attribute == "clientVersion"
    assert flag_rule.clauses[0].op == "semVerLessThan"
    assert flag_rule.clauses[0].negate
    assert flag_rule.clauses[0].values == ["0.70.0"]
    assert flag_rule.clauses[0].context_kind == "namespace"

    # Test max client version rule
    max_version_rule = AugmentFeatureFlagRule(
        return_value=True, max_client_version="0.80.0"
    )

    flag_rule = augment_flag_rule_to_flag_rule(max_version_rule, [True, False])
    assert isinstance(flag_rule, Rule)
    assert len(flag_rule.clauses) == 1
    assert flag_rule.clauses[0].attribute == "clientVersion"
    assert flag_rule.clauses[0].op == "semVerGreaterThan"
    assert flag_rule.clauses[0].negate
    assert flag_rule.clauses[0].values == ["0.80.0"]
    assert flag_rule.clauses[0].context_kind == "namespace"

    # Test combined client and version rule
    combined_rule = AugmentFeatureFlagRule(
        return_value=True,
        client="intellij",
        min_client_version="0.70.0",
        max_client_version="0.80.0",
    )

    flag_rule = augment_flag_rule_to_flag_rule(combined_rule, [True, False])
    assert isinstance(flag_rule, Rule)
    assert len(flag_rule.clauses) == 3

    # Find the client clause
    client_clauses = [c for c in flag_rule.clauses if c.attribute == "client"]
    assert len(client_clauses) == 1
    assert client_clauses[0].op == "in"
    assert client_clauses[0].values == ["intellij"]
    assert client_clauses[0].context_kind == "namespace"

    # Find the min version clause
    min_version_clauses = [
        c
        for c in flag_rule.clauses
        if c.attribute == "clientVersion" and c.op == "semVerLessThan" and c.negate
    ]
    assert len(min_version_clauses) == 1
    assert min_version_clauses[0].values == ["0.70.0"]
    assert min_version_clauses[0].context_kind == "namespace"

    # Find the max version clause
    max_version_clauses = [
        c
        for c in flag_rule.clauses
        if c.attribute == "clientVersion" and c.op == "semVerGreaterThan" and c.negate
    ]
    assert len(max_version_clauses) == 1
    assert max_version_clauses[0].values == ["0.80.0"]
    assert max_version_clauses[0].context_kind == "namespace"
