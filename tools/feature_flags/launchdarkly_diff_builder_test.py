import json
import pathlib
from launchdarkly_api.model.feature_flags import FeatureFlags
from launchdarkly_api.model.feature_flag import FeatureFlag

from unittest.mock import patch
from unittest import TestCase

from tools.feature_flags.config import LaunchDarklyConfig
from tools.feature_flags.launchdarkly_diff_builder import LaunchDarklyDiffBuilder
from tools.feature_flags.launchdarkly_types import (
    dict_to_feature_flag,
    dict_to_feature_flags,
)

MOCK_FLAGS_UPDATE_JSONNET = "tools/feature_flags/mock_flags.jsonnet"
MOCK_REMOTE_FLAGS_JSON = "tools/feature_flags/mock_remote_flags.json"


class MockFeatureFlagsApi:
    """
    Mock launchdarkly_api
    """

    def __init__(self):
        # Open the file in constructor so it happens before pathlib is mocked
        self.remote_flags_dict: dict = json.load(
            pathlib.Path(MOCK_REMOTE_FLAGS_JSON).open()
        )

    def get_feature_flags(
        self, project_key="", env="", limit=100, offset=0, summary=False
    ) -> FeatureFlags:
        """
        Mock get_feature_flags
        """
        result = dict_to_feature_flags(self.remote_flags_dict)
        assert isinstance(result, FeatureFlags)
        return result

    def get_feature_flag(self, project_key="", feature_flag_key=None) -> FeatureFlag:
        """
        Mock get_feature_flags
        """
        for flag in self.remote_flags_dict:
            assert isinstance(flag, dict)
            if flag["key"] == feature_flag_key:
                return dict_to_feature_flag(flag)
        raise Exception(f"Flag {feature_flag_key} not found in mock flags")


class MockConfiguration:
    def __init__(self):
        self.api_key = {"ApiKey": "fake-api-key"}  # pragma: allowlist secret


class TestLaunchDarklyDiffBuilder(TestCase):
    """
    Tests for LaunchDarklyDiffBuilder
    """

    def setUp(self):
        self.mocks = {
            "FeatureFlagsApi": patch(
                "launchdarkly_api.api.feature_flags_api.FeatureFlagsApi",
                return_value=MockFeatureFlagsApi(),
            ),
            "Configuration": patch(
                "launchdarkly_api.Configuration", return_value=MockConfiguration()
            ),
            "ApiClient": patch("launchdarkly_api.ApiClient"),
            "Path": patch(
                "tools.feature_flags.launchdarkly_diff_builder.pathlib.Path",
                return_value=pathlib.Path(MOCK_FLAGS_UPDATE_JSONNET),
            ),
            "now_string": patch(
                "tools.feature_flags.launchdarkly_diff_builder.now_string",
                return_value="2022-02-22 22:22:22",
            ),
        }
        for mock in self.mocks.values():
            mock.start()

    def tearDown(self):
        for mock in self.mocks.values():
            mock.stop()

    def test_get_flag_patch_operations(self):
        """
        Sanity test for patch building
        """
        launchDarklyDiffBuilder = LaunchDarklyDiffBuilder(
            LaunchDarklyConfig("fake-api-key"), "."
        )
        diff, keys_to_sync = launchDarklyDiffBuilder.get_diff()
        assert diff is not None
        assert set(keys_to_sync) == {"test_sync_flag", "fake_uni_boolean_flag"}
        result_dict = diff.to_dict()
        expected_dict = {
            "test_sync_flag": {
                "comment": "Flag Service update at 2022-02-22 22:22:22",
                "patch": [
                    {"op": "replace", "path": "/name", "value": "test_sync_flag"},
                    {
                        "op": "replace",
                        "path": "/description",
                        "value": "This is a flag update that replaces the variations",
                    },
                    {
                        "op": "replace",
                        "path": "/variations",
                        "value": [
                            {
                                "value": "binks-33B-FP8-v1-chat",
                                "name": "binks-33B-FP8-v1-chat",
                                "_id": "3ccf36df-52b0-4686-82ef-dc984fe440de",
                            },
                            {
                                "value": "",
                                "name": "not-set",
                                "_id": "bcdf4a0e-08e6-4e6e-9639-8ef0c92eba70",
                            },
                            {
                                "value": "new-fake-variation2",
                                "name": "new-fake-variation2",
                                "_id": "1234abcd-08e6-4e6e-9639-8ef0c92eba70",
                            },
                            {
                                "value": "foo",
                                "name": "foo",
                            },
                            {
                                "value": "bar",
                                "name": "bar",
                            },
                            {
                                "value": "uuid",
                                "name": "uuid",
                            },
                        ],
                    },
                    {
                        "op": "replace",
                        "path": "/defaults",
                        "value": {"onVariation": 1, "offVariation": 1},
                    },
                ],
            },
            "fake_boolean_flag": {
                "comment": "Flag Service update at 2022-02-22 22:22:22",
                "patch": [
                    {
                        "op": "replace",
                        "path": "/description",
                        "value": "Changing the boolean flag, but sync is false",
                    },
                    {
                        "op": "replace",
                        "path": "/defaults",
                        "value": {
                            "onVariation": 1,
                            "offVariation": 1,
                        },
                    },
                ],
            },
            "fake_uni_boolean_flag": {
                "comment": "Flag Service update at 2022-02-22 22:22:22",
                "patch": [
                    {
                        "op": "replace",
                        "path": "/description",
                        "value": "Boolean flag with only one variation should keep old variation",
                    },
                ],
            },
        }
        assert result_dict == expected_dict

        diff_env, keys_to_sync_env = launchDarklyDiffBuilder.get_diff(is_envs=True)
        assert diff_env is not None
        assert set(keys_to_sync_env) == {"test_sync_flag", "fake_uni_boolean_flag"}
        result_dict_env = diff_env.to_dict()
        expected_dict_env = {
            "test_sync_flag": {
                "comment": "Flag Service update at 2022-02-22 22:22:22",
                "patch": [
                    {
                        "op": "replace",
                        "path": "/environments/production/fallthrough",
                        "value": {"variation": 1},
                    },
                    {
                        "op": "replace",
                        "path": "/environments/production/rules",
                        "value": [
                            {
                                "clauses": [
                                    {
                                        "attribute": "key",
                                        "op": "in",
                                        "values": ["dogfood"],
                                        "negate": False,
                                        "contextKind": "namespace",
                                    }
                                ],
                                "trackEvents": False,
                                "variation": 3,
                            },
                            {
                                "clauses": [
                                    {
                                        "attribute": "pod_name",
                                        "op": "startsWith",
                                        "values": ["completion-blah-blah-foo"],
                                        "negate": True,
                                        "contextKind": "namespace",
                                    }
                                ],
                                "trackEvents": False,
                                "variation": 4,
                            },
                            {
                                "clauses": [
                                    {
                                        "attribute": "tenant_name",
                                        "op": "in",
                                        "values": ["aitutor-pareto"],
                                        "negate": False,
                                        "contextKind": "namespace",
                                    }
                                ],
                                "trackEvents": False,
                                "variation": 4,
                            },
                            {
                                "clauses": [
                                    {
                                        "attribute": "user_id_hmac",
                                        "op": "in",
                                        "values": ["fakesha"],
                                        "negate": False,
                                        "contextKind": "namespace",
                                    }
                                ],
                                "trackEvents": False,
                                "variation": 4,
                            },
                            {
                                "clauses": [
                                    {
                                        "attribute": "user_uuid",
                                        "op": "in",
                                        "values": ["fakeuuid"],
                                        "negate": False,
                                        "contextKind": "namespace",
                                    }
                                ],
                                "trackEvents": False,
                                "variation": 5,
                            },
                            {
                                "clauses": [
                                    {
                                        "attribute": "client",
                                        "op": "in",
                                        "values": ["intellij", "vscode"],
                                        "negate": False,
                                        "contextKind": "namespace",
                                    },
                                    {
                                        "attribute": "clientVersion",
                                        "op": "semVerGreaterThan",
                                        "values": ["0.90.0"],
                                        "negate": True,
                                        "contextKind": "namespace",
                                    },
                                ],
                                "trackEvents": False,
                                "variation": 4,
                            },
                            {
                                "clauses": [
                                    {
                                        "attribute": "client",
                                        "op": "in",
                                        "values": ["vscode"],
                                        "negate": False,
                                        "contextKind": "namespace",
                                    },
                                    {
                                        "attribute": "clientVersion",
                                        "op": "semVerLessThan",
                                        "values": ["0.75.0"],
                                        "negate": True,
                                        "contextKind": "namespace",
                                    },
                                ],
                                "trackEvents": False,
                                "variation": 3,
                            },
                        ],
                    },
                ],
            },
            "fake_boolean_flag": {
                "comment": "Flag Service update at 2022-02-22 22:22:22",
                "patch": [
                    {
                        "op": "replace",
                        "path": "/environments/production/on",
                        "value": True,
                    },
                    {
                        "op": "replace",
                        "path": "/environments/production/fallthrough",
                        "value": {"variation": 1},
                    },
                    {
                        "op": "replace",
                        "path": "/environments/production/rules",
                        "value": [
                            {
                                "clauses": [
                                    {
                                        "attribute": "key",
                                        "op": "in",
                                        "values": ["dogfood"],
                                        "negate": False,
                                        "contextKind": "namespace",
                                    }
                                ],
                                "trackEvents": False,
                                "variation": 0,
                            },
                            {
                                "clauses": [
                                    {
                                        "attribute": "pod_name",
                                        "op": "startsWith",
                                        "values": ["my_pod"],
                                        "negate": True,
                                        "contextKind": "namespace",
                                    }
                                ],
                                "trackEvents": False,
                                "variation": 0,
                            },
                        ],
                    },
                ],
            },
            "fake_uni_boolean_flag": {
                "comment": "Flag Service update at 2022-02-22 22:22:22",
                "patch": [
                    {
                        "op": "replace",
                        "path": "/environments/production/rules",
                        "value": [
                            {
                                "clauses": [
                                    {
                                        "attribute": "key",
                                        "contextKind": "namespace",
                                        "negate": False,
                                        "op": "in",
                                        "values": ["dogfood"],
                                    }
                                ],
                                "trackEvents": False,
                                "variation": 0,
                            },
                            {
                                "clauses": [
                                    {
                                        "attribute": "pod_name",
                                        "contextKind": "namespace",
                                        "negate": True,
                                        "op": "startsWith",
                                        "values": ["my_pod"],
                                    }
                                ],
                                "trackEvents": False,
                                "variation": 0,
                            },
                            {
                                "clauses": [
                                    {
                                        "attribute": "client",
                                        "contextKind": "namespace",
                                        "negate": False,
                                        "op": "in",
                                        "values": ["intellij"],
                                    },
                                    {
                                        "attribute": "clientVersion",
                                        "contextKind": "namespace",
                                        "negate": True,
                                        "op": "semVerLessThan",
                                        "values": ["1.0.0"],
                                    },
                                    {
                                        "attribute": "clientVersion",
                                        "contextKind": "namespace",
                                        "negate": True,
                                        "op": "semVerGreaterThan",
                                        "values": ["2.0.0"],
                                    },
                                ],
                                "trackEvents": False,
                                "variation": 0,
                            },
                        ],
                    },
                ],
            },
        }

        assert result_dict_env == expected_dict_env

    def test_new_flag_creation(self):
        """
        Sanity test for new flag creation
        """
        launchDarklyDiffBuilder = LaunchDarklyDiffBuilder(
            LaunchDarklyConfig("fake-api-key"), "."
        )
        new_flags, keys_to_sync = launchDarklyDiffBuilder.get_new_flags()
        assert new_flags is not None
        assert set(keys_to_sync) == {
            "test_sync_flag_new",
            "fake_one_variation_flag",
        }
        result_dict = [flag_body.to_dict() for flag_body in new_flags]

        # import pprint
        # pprint.pprint(result_dict)

        expected_dict = [
            {
                "client_side_availability": {
                    "using_environment_id": False,
                    "using_mobile_key": False,
                },
                "defaults": {"off_variation": 0, "on_variation": 0},
                "description": "Only one variation cannot be applied",
                "key": "fake_one_variation_flag",
                "name": "fake_one_variation_flag",
                "temporary": False,
                "variations": [
                    {"description": "", "name": "MyVal", "value": "MyVal"},
                    {"description": "", "name": "not-set", "value": ""},
                ],
            },
            {
                "client_side_availability": {
                    "using_environment_id": False,
                    "using_mobile_key": False,
                },
                "defaults": {"off_variation": 0, "on_variation": 0},
                "description": "I love flags",
                "key": "test_no_sync_flag_new",
                "name": "test_no_sync_flag_new",
                "temporary": False,
                "variations": [
                    {"description": "", "name": "foo", "value": "foo"},
                    {"description": "", "name": "bar", "value": "bar"},
                    {"description": "", "name": "not-set", "value": ""},
                ],
            },
            {
                "client_side_availability": {
                    "using_environment_id": False,
                    "using_mobile_key": False,
                },
                "defaults": {"off_variation": 0, "on_variation": 0},
                "description": "I love flags",
                "key": "test_sync_flag_new",
                "name": "test_sync_flag_new",
                "temporary": False,
                "variations": [
                    {"description": "", "name": "foo", "value": "foo"},
                    {"description": "", "name": "bar", "value": "bar"},
                    {"description": "", "name": "not-set", "value": ""},
                ],
            },
        ]
        for result in result_dict:
            assert result in expected_dict
