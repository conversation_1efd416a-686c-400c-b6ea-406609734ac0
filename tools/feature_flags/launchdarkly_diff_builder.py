#!/usr/bin/env python

from __future__ import annotations
from datetime import datetime
from typing import Optional
import pathlib
import yaml
import json
import os
import _gojsonnet as jsonnet

from launchdarkly_api.model.feature_flag import FeatureFlag
from launchdarkly_api.model.feature_flag_body import FeatureFlagBody
from launchdarkly_api.model.patch_with_comment import <PERSON><PERSON><PERSON>Comment
from launchdarkly_api.model.patch_operation import PatchOperation
from launchdarkly_api.model.json_patch import JSONPatch
from launchdarkly_api.model.variation import Variation
from launchdarkly_api.model_utils import model_to_dict
from tools.feature_flags.config import LaunchDarklyConfig
from tools.feature_flags.launchdarkly_client import LaunchDarkly<PERSON>lient
from tools.feature_flags.launchdarkly_types import (
    LaunchDarklyDiff,
    apply_augment_flag,
    augment_flag_to_flag_body,
    augment_flags_dict_to_augment_flags_dataclass,
    dict_to_feature_flag,
)

FLAGS_YAML = "tools/feature_flags/flags.yaml"
FLAGS_JSONNET = "tools/feature_flags/flags.jsonnet"

ENVIRONMENTS = ["production", "test"]


def load_flags(repo_dir: str) -> dict:
    def import_callback(dir, rel):
        full_path = os.path.join(repo_dir, rel)
        if not os.path.exists(full_path):
            raise RuntimeError(f"File not found: {full_path}")
        with open(full_path, "rb") as f:
            return full_path, f.read()

    jsonnet_path = pathlib.Path(repo_dir, FLAGS_JSONNET)
    if jsonnet_path.exists():
        return json.loads(
            jsonnet.evaluate_file(str(jsonnet_path), import_callback=import_callback)
        )
    else:
        yaml_path = pathlib.Path(repo_dir, FLAGS_YAML)
        return yaml.safe_load(yaml_path.open())


def now_string():
    return datetime.now().strftime("%Y-%m-%d %H:%M:%S")


def find_flag(flags: list[FeatureFlag], key: str) -> Optional[FeatureFlag]:
    for flag in flags:
        if flag.key == key:
            return flag
    return None


# Top-level (not nested) attributes that can be updated
top_level_attrs = [
    "name",
    "temporary",
    "archived",
    "deprecated",
    "description",
    "maintainer_id",
]

# TODO: Handle additional nested:
# "experiments",
# "custom_properties",
# "client_side_availability",
# "maintainer",  # Automatically create/update maintainer_id based on this? Or vice versa?

# None-updatable attribute keys:
# "key",  # Never changes
# "version",  # Used for checking, updates automatically
# "creation_date",
# "links",  # Readonly
# "archived_date",  # Probably automatically updated when archived
# "deprecated_date",  # Probably automatically updated when deprecated

# Unused root attribute keys:
# "maintainer_team_key",
# "maintainer_team",
# "purpose",
# "migration_settings",
# "tags",  # list
# "goal_ids",  # list


class LaunchDarklyDiffBuilder:
    def __init__(self, config: LaunchDarklyConfig, repo_dir: str):
        self.launchdarkly_client = LaunchDarklyClient(config)
        self.repo_dir = repo_dir

        augment_flags_dict = load_flags(repo_dir)

        # Build a dataclass - this is just for type safety
        self.augment_flags = augment_flags_dict_to_augment_flags_dataclass(
            augment_flags_dict
        )

    def get_new_flags(self) -> tuple[list[FeatureFlagBody], list[str]]:
        """Build a list of new flags to create in LaunchDarkly

        Note: Flag creation only includes a subset of information, so a patch
        must be done after the flag is created for a complete sync
        Returns:
            A list of FeatureFlagBody objects to create in LaunchDarkly,
            and a list of flag keys to sync
        """
        flag_bodies: list[FeatureFlagBody] = []  # Tracking new flags to create
        keys_to_sync: list[str] = []  # Tracking keys to sync to LD

        remote_flags: list[FeatureFlag] = self.launchdarkly_client.get_flags()
        for augment_flag in self.augment_flags:
            flag = find_flag(remote_flags, augment_flag.key)
            if flag is None:
                # New flag to create
                flag_bodies.append(augment_flag_to_flag_body(augment_flag))
                if augment_flag.sync:
                    keys_to_sync.append(augment_flag.key)
        return flag_bodies, keys_to_sync

    def get_diff(
        self, is_reverse: bool = False, is_envs: bool = False
    ) -> tuple[Optional[LaunchDarklyDiff], list[str]]:
        """Build a diff from local to remote that can be sync later
        Args:
            is_reverse: If true, the diff will be reversed, so that remote flags are updated to match local flags
            is_envs: If true, the diff will be for environments. Otherwise, it will be for everything else
        Returns:
            A LaunchDarklyDiff object if there are changes to apply, else None
            A list of flag keys to sync
        """
        current_time = now_string()

        remote_flags: list[FeatureFlag] = self.launchdarkly_client.get_flags()

        # The models in launchdarkly_api are not JSON serializable, so convert to dicts and back
        local_flags = [
            dict_to_feature_flag(remote_flag.to_dict()) for remote_flag in remote_flags
        ]

        # Apply the updates to local flags to get a diff from remote
        keys_to_sync: list[str] = []  # Tracking keys to sync to LD
        for augment_flag in self.augment_flags:
            flag = find_flag(local_flags, augment_flag.key)
            if flag is not None:
                # Merge
                apply_augment_flag(flag, augment_flag)
                if augment_flag.sync:
                    keys_to_sync.append(augment_flag.key)

        flags_to_patch_with_comment: dict[str, PatchWithComment] = {}

        # Build patch_flags
        if is_reverse:
            old_flags, new_flags = local_flags, remote_flags
        else:
            old_flags, new_flags = remote_flags, local_flags

        for new_flag in new_flags:
            key: str = new_flag.key
            old_flag = find_flag(old_flags, key)
            assert isinstance(old_flag, FeatureFlag)
            patch_operations: list[PatchOperation] = self.get_flag_patch_operations(
                old_flag, new_flag, is_envs
            )
            if len(patch_operations) > 0:
                patch_with_comment = PatchWithComment(
                    comment=f"Flag Service update at {current_time}",
                    patch=JSONPatch(patch_operations),
                )
                assert isinstance(patch_with_comment, PatchWithComment)
                flags_to_patch_with_comment[key] = patch_with_comment

        if len(flags_to_patch_with_comment) == 0:
            return None, []

        keys_to_sync = list(
            filter(lambda key: key in flags_to_patch_with_comment.keys(), keys_to_sync)
        )
        return LaunchDarklyDiff(flags_to_patch_with_comment), keys_to_sync

    @staticmethod
    def get_flag_patch_operations(
        old_flag: FeatureFlag, new_flag: FeatureFlag, is_envs: bool = False
    ) -> list[PatchOperation]:
        """Build a list of patches to update old_flag to match new_flag"""

        updates = {}
        if is_envs:
            # Rules
            new_environments = new_flag.environments
            assert isinstance(new_environments, dict)
            old_environments = old_flag.environments
            assert isinstance(old_environments, dict)
            if new_environments != old_environments:
                for env, new_env_config in new_environments.items():
                    assert env in ENVIRONMENTS, f"Unknown environment {env}"
                    old_env_config = old_environments[env]
                    if new_env_config != old_env_config:
                        if new_env_config.on != old_env_config.on:
                            updates[f"environments/{env}/on"] = new_env_config.on
                        if new_env_config.archived != old_env_config.archived:
                            updates[f"environments/{env}/archived"] = (
                                new_env_config.archived
                            )
                        if new_env_config.fallthrough != old_env_config.fallthrough:
                            updates[f"environments/{env}/fallthrough"] = model_to_dict(
                                new_env_config.fallthrough, True
                            )
                        if new_env_config.rules != old_env_config.rules:
                            updates[f"environments/{env}/rules"] = [
                                model_to_dict(rule, True)
                                for rule in new_env_config.rules
                            ]

        else:
            # Top-Level attributes (name, description, etc.)
            for attr_key in top_level_attrs:
                if old_flag.get(attr_key) != new_flag.get(attr_key):
                    updates[attr_key] = new_flag.get(attr_key)

            # Variations
            variation_updates = []
            new_variations = new_flag.variations
            assert isinstance(new_variations, list)
            old_variations = old_flag.variations
            assert isinstance(old_variations, list)
            if new_variations != old_variations:
                for variation in new_variations:
                    assert isinstance(variation, Variation)
                    variation_updates.append(model_to_dict(variation, True))
                updates["variations"] = variation_updates

            # Default variations
            if new_flag.defaults != old_flag.defaults:
                defaults = model_to_dict(new_flag.defaults, True)
                updates["defaults"] = defaults

        if len(updates) == 0:
            # No updates
            return []

        patchOperations: list[PatchOperation] = []

        for key, value in updates.items():
            if key in new_flag.attribute_map:
                json_key = new_flag.attribute_map[key]
            else:
                # Nested key (e.g. "environments/production/rules"), no need for
                # conversion to JSON key for now because all these keys are
                # single-word, so camel-case and snake-case are the same
                json_key = key
            patch_operation = PatchOperation(
                op="replace",
                path=f"/{json_key}",  # JSON key
                value=value,
            )
            assert isinstance(patch_operation, PatchOperation)
            patchOperations.append(patch_operation)

        return patchOperations
