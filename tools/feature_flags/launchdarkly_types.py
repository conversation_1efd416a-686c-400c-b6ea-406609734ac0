from __future__ import annotations
import json
import hashlib
from dataclasses import dataclass
from typing import Literal, Optional
import typing

from launchdarkly_api.model.clause import Clause
from launchdarkly_api.model.client_side_availability_post import (
    ClientSideAvailabilityPost,
)
from launchdarkly_api.model.defaults import Defaults
from launchdarkly_api.model.feature_flag import FeatureFlag
from launchdarkly_api.model.feature_flags import FeatureFlags
from launchdarkly_api.model.feature_flag_body import FeatureFlagBody
from launchdarkly_api.model.feature_flag_config import FeatureFlagConfig
from launchdarkly_api.model.json_patch import JSONPatch
from launchdarkly_api.model.patch_operation import PatchOperation
from launchdarkly_api.model.patch_with_comment import PatchWithComment
from launchdarkly_api.model.rule import Rule
from launchdarkly_api.model.variation import Variation
from launchdarkly_api.model.variation_or_rollout_rep import VariationOrRolloutRep

Env = Literal["production", "test"]
""" Environment configured in LaunchDarkly """

# Allowed clause operations in LaunchDarkly
CLAUSE_OPERATIONS = [
    "in",
    "endsWith",
    "startsWith",
    "matches",
    "contains",
    "lessThan",
    "lessThanOrEqual",
    "greaterThan",
    "greaterThanOrEqual",
    "before",
    "after",
    "segmentMatch",
    "semVerEqual",
    "semVerLessThan",
    "semVerGreaterThan",
]


def augment_flag_rule_to_flag_rule(
    rule: AugmentFeatureFlagRule, variation_values: list
) -> Rule:
    variation = rule.return_value
    variation_index = variation_values.index(variation)
    # Build clauses
    clauses = []

    def _build_clause(attribute, op, values, context_kind, negate=False) -> Clause:
        if op not in CLAUSE_OPERATIONS:
            raise Exception(f"Invalid clause operation {op}")
        # values can be a list or a string, string can be negated
        if isinstance(values, str):
            values = [values]
        if values[0].startswith("!"):
            negate = not negate
            # Validate all values are negative
            for value in values:
                if not value.startswith("!"):
                    raise Exception("Cannot mix negative and positive values")
            # Remove ! from prefixes
            values = [value[1:] for value in values]
        # Validate no mix of negatives:
        for value in values:
            if value.startswith("!"):
                raise Exception("Cannot mix negative and positive values")
        clause = Clause(
            attribute=attribute,
            op=op,
            values=values,
            context_kind=context_kind,
            negate=negate,
        )
        assert isinstance(clause, Clause)
        return clause

    values = None
    if rule.namespace:
        attribute = "key"
        context_kind = "namespace"
        op = "in"
        values = rule.namespace
        clauses.append(
            _build_clause(
                attribute=attribute, op=op, values=values, context_kind=context_kind
            )
        )
    if rule.pod_name:
        attribute = "pod_name"
        context_kind = "namespace"
        op = "startsWith"
        values = rule.pod_name
        clauses.append(
            _build_clause(
                attribute=attribute, op=op, values=values, context_kind=context_kind
            )
        )

    if rule.tenant_name:
        attribute = "tenant_name"
        context_kind = "namespace"
        op = "in"
        values = rule.tenant_name
        clauses.append(
            _build_clause(
                attribute=attribute, op=op, values=values, context_kind=context_kind
            )
        )

    if rule.user_id_hmac:
        attribute = "user_id_hmac"
        context_kind = "namespace"  # ??
        op = "in"
        values = rule.user_id_hmac
        clauses.append(
            _build_clause(
                attribute=attribute, op=op, values=values, context_kind=context_kind
            )
        )

    if rule.user_uuid:
        attribute = "user_uuid"
        context_kind = "namespace"  # ??
        op = "in"
        values = rule.user_uuid
        clauses.append(
            _build_clause(
                attribute=attribute, op=op, values=values, context_kind=context_kind
            )
        )

    if rule.api_key_user_id:
        attribute = "api_key_user_id"
        context_kind = "namespace"  # ??
        op = "in"
        values = rule.api_key_user_id
        clauses.append(
            _build_clause(
                attribute=attribute, op=op, values=values, context_kind=context_kind
            )
        )

    if rule.model_name:
        attribute = "model_name"
        context_kind = "namespace"
        op = "in"
        values = rule.model_name
        clauses.append(
            _build_clause(
                attribute=attribute, op=op, values=values, context_kind=context_kind
            )
        )

    if rule.email_domain:
        attribute = "email_domain"
        context_kind = "namespace"  # ??
        op = "in"
        values = rule.email_domain
        clauses.append(
            _build_clause(
                attribute=attribute, op=op, values=values, context_kind=context_kind
            )
        )

    if rule.cloud:
        attribute = "cloud"
        context_kind = "namespace"
        op = "in"
        values = rule.cloud
        clauses.append(
            _build_clause(
                attribute=attribute, op=op, values=values, context_kind=context_kind
            )
        )

    if rule.env:
        attribute = "env"
        context_kind = "namespace"
        op = "in"
        values = rule.env
        clauses.append(
            _build_clause(
                attribute=attribute, op=op, values=values, context_kind=context_kind
            )
        )

    if rule.client:
        attribute = "client"
        context_kind = "namespace"
        op = "in"
        values = rule.client
        clauses.append(
            _build_clause(
                attribute=attribute, op=op, values=values, context_kind=context_kind
            )
        )

    # we achieve `>=` by using `not <`
    if rule.min_client_version:
        attribute = "clientVersion"
        context_kind = "namespace"
        op = "semVerLessThan"
        negate = True
        values = rule.min_client_version
        clauses.append(
            _build_clause(
                attribute=attribute,
                op=op,
                values=values,
                context_kind=context_kind,
                negate=negate,
            )
        )

    # we achieve `<=` by using `not >`
    if rule.max_client_version:
        attribute = "clientVersion"
        context_kind = "namespace"
        op = "semVerGreaterThan"
        negate = True
        values = rule.max_client_version
        clauses.append(
            _build_clause(
                attribute=attribute,
                op=op,
                values=values,
                context_kind=context_kind,
                negate=negate,
            )
        )

    result = Rule(
        variation=variation_index,
        clauses=clauses,
        track_events=False,
    )
    assert isinstance(result, Rule)
    return result


def augment_flag_to_flag_body(augment_flag: AugmentFeatureFlag) -> FeatureFlagBody:
    """
    Creates a FeatureFlagBody (new feature flag) from a AugmentFeatureFlag

    Note that some fields are not part of the FeatureFlagBody, e.g. rules,
    so they have to be updated in a patch after the create

    See https://github.com/launchdarkly/api-client-python/blob/main/docs/FeatureFlagBody.md
    """
    # Find all possible variation values
    variation_values = []  # Default is index 0
    if getattr(augment_flag, "envs"):
        envs = augment_flag.envs
        assert isinstance(envs, dict)
        for env_config in envs.values():
            if env_config.rules:
                for rule in env_config.rules:
                    # Every rule clause must have a value
                    if rule.return_value not in variation_values:
                        variation_values.append(rule.return_value)

    # Must have at least 2 variations, so adding an unused one if only have one defined at the moment
    if len(variation_values) < 2:
        single_value = variation_values[0]
        # Add a value that is different than the value currently defined
        # This value has to be the same type
        if isinstance(single_value, bool):
            variation_values.append(not single_value)
        elif isinstance(single_value, (int, float, complex)):
            variation_values.append(single_value + 1)
        else:
            variation_values.append("unused" if single_value == "" else "")

    # Build body
    result = FeatureFlagBody(
        key=augment_flag.key,  # str
        name=augment_flag.key,  # str
        description=augment_flag.description,  # str
        client_side_availability=ClientSideAvailabilityPost(
            using_environment_id=False,  # bool
            using_mobile_key=False,  # bool
        ),
        variations=[
            Variation(
                name=value_to_variation_name(return_value),
                value=return_value,
                description="",
            )
            for return_value in variation_values
        ],
        temporary=False,
        defaults=Defaults(
            on_variation=0,
            off_variation=0,
        ),
    )
    assert isinstance(result, FeatureFlagBody)
    return result


def _get_default_rule_value(augment_flag: AugmentFeatureFlag) -> typing.Any:
    if not augment_flag.envs:
        return None
    for env in augment_flag.envs.values():
        if env.rules:
            return env.rules[-1].return_value
    return None


def apply_augment_flag(flag: FeatureFlag, augment_flag: AugmentFeatureFlag) -> None:
    """
    See https://github.com/launchdarkly/api-client-python/blob/main/docs/FeatureFlag.md
    """
    # Keep existing variations
    old_variation_values = [variation.value for variation in flag.variations]
    new_variation_values = []

    all_variation_values = old_variation_values + new_variation_values
    if getattr(augment_flag, "envs"):
        envs = augment_flag.envs
        assert isinstance(envs, dict)
        for env_config in envs.values():
            if env_config.rules:
                for rule in env_config.rules:
                    # Every rule clause must have a value
                    if (
                        rule.return_value not in old_variation_values
                        and rule.return_value not in new_variation_values
                    ):
                        new_variation_values.append(rule.return_value)

        # Update all_variation_values to include new variations found in envs
        all_variation_values = old_variation_values + new_variation_values
        # Update rules
        for env, env_config in envs.items():
            flag_envs = flag.environments
            assert isinstance(flag_envs, dict)
            flag_env = flag_envs[env]
            assert isinstance(flag_env, FeatureFlagConfig)
            flag_env.on = True  # We always enable all envs
            flag_env.archived = env_config.archived
            # Replace existing rules for this environment from flag
            if env_config.rules:
                old_rules = flag_env.rules
                assert isinstance(old_rules, list)
                flag_env.rules = []
                for i, rule in enumerate(env_config.rules):
                    flag_rule = augment_flag_rule_to_flag_rule(
                        rule, all_variation_values
                    )
                    if len(flag_rule.clauses):
                        if len(old_rules) > i:
                            old_rule = old_rules[i]
                            # Preserve ids for existing rules and clauses
                            flag_rule.id = old_rule.id
                            old_clauses = old_rule.clauses
                            assert isinstance(old_clauses, list)
                            # Rules have 0-1 clauses per (attribute, op) pair.
                            # Clause id should be preserved iff attributes and ops match.
                            for old_clause in old_clauses:
                                for new_clause in flag_rule.clauses:
                                    if (
                                        new_clause.attribute == old_clause.attribute
                                        and new_clause.op == old_clause.op
                                    ):
                                        new_clause.id = old_clause.id
                                        break
                            if hasattr(old_rule, "ref"):
                                flag_rule.ref = old_rule.ref

                        flag_env.rules.append(flag_rule)
                    else:
                        # If rule has no clauses, it's not a rule, just a fallthrough
                        flag_env.fallthrough = VariationOrRolloutRep(
                            variation=flag_rule.variation
                        )

    flag.key = augment_flag.key
    flag.name = augment_flag.key
    flag.description = augment_flag.description

    # Make sure we use all existing variation ids to prevent edge cases
    flag.variations.extend(
        [
            Variation(
                name=value_to_variation_name(v),
                value=v,
            )
            for v in new_variation_values
        ]
    )
    default_value = _get_default_rule_value(augment_flag)
    default_index = all_variation_values.index(default_value)
    flag.defaults = Defaults(
        on_variation=default_index,
        off_variation=default_index,
    )


def dict_to_feature_flag(flag_dict: dict) -> FeatureFlag:
    """
    See https://github.com/launchdarkly/api-client-python/blob/main/docs/FeatureFlag.md
    """
    result = FeatureFlag(**flag_dict)
    assert isinstance(result, FeatureFlag)
    return result


def dict_to_feature_flags(flags_dict: dict) -> FeatureFlags:
    """
    See https://github.com/launchdarkly/api-client-python/blob/main/docs/FeatureFlags.md
    """
    flags = [dict_to_feature_flag(flag_dict) for flag_dict in flags_dict]
    result = FeatureFlags(items=flags, links={}, total_count=len(flags))
    assert isinstance(result, FeatureFlags)
    return result


def dict_to_patch_with_comment(patch_with_comment_dict: dict) -> PatchWithComment:
    """
    See https://github.com/launchdarkly/api-client-python/blob/main/docs/PatchWithComment.md
    """
    comment = patch_with_comment_dict["comment"]
    assert isinstance(comment, str)
    list_of_patch_operations = patch_with_comment_dict["patch"]
    assert isinstance(list_of_patch_operations, list)
    patch_operations = [
        PatchOperation(
            op=patch_operation_dict["op"],
            path=patch_operation_dict["path"],
            value=patch_operation_dict["value"],
        )
        for patch_operation_dict in list_of_patch_operations
    ]
    result = PatchWithComment(comment=comment, patch=JSONPatch(patch_operations))
    assert isinstance(result, PatchWithComment)
    return result


def patch_with_comment_to_dict(patch_with_comment: PatchWithComment) -> dict:
    """
    See https://github.com/launchdarkly/api-client-python/blob/main/docs/PatchWithComment.md
    """
    json_patch = patch_with_comment.patch
    assert isinstance(json_patch, JSONPatch)
    list_of_patch_operations = json_patch.value
    assert isinstance(list_of_patch_operations, list)
    list_of_patch_operations_dict: list[dict] = []
    for patch_operation in list_of_patch_operations:
        assert isinstance(patch_operation, PatchOperation)
        patch_operation_dict = patch_operation.to_dict()
        assert isinstance(patch_operation_dict, dict)
        list_of_patch_operations_dict.append(patch_operation_dict)
    return {
        "comment": patch_with_comment.comment,
        "patch": list_of_patch_operations_dict,
    }


def normalize_value(value: str | int | float | bool | dict) -> str | int | float | bool:
    if isinstance(value, dict):
        return json.dumps(value)
    return value


@dataclass
class AugmentFeatureFlagRule:
    namespace: Optional[str | list[str]]
    """ Namespace to check for, e.g. "dogfood"

    This flag is always available.
    """

    tenant_name: Optional[str | list[str]]
    """ Tenant name to check for, e.g. "aitutor-pareto"


    This flag is bound in all feature flag checks that are done in the
    context of a tenant.
    """

    pod_name: Optional[str | list[str]]
    """ Pod name to check for, e.g. "completion-host"

    This flag is always bound.
    """

    user_id_hmac: Optional[str | list[str]]  # Deprecated: use user_uuid instead
    """ User ID HMAC digest to check for """

    user_uuid: Optional[str | list[str]]
    """ User UUID (auth entity ID) to check for.

    This flag is always bound for auth-db user entries, but not for API token users or IAP users and
    service tokens.
    The flasg is only bound in the api proxy.
    """

    api_key_user_id: Optional[str | list[str]]
    """ API key user id to check for.

    This flag is bound for API token users. It is not bound for IAP users and service tokens.
    This flag is only bound in the api proxy.
    """

    model_name: Optional[str | list[str]]
    """ Model name to check for, e.g. "droid-33B-FP8-r1-edit"

    The flag is rarely bound. Please check the intended use case.
    """

    email_domain: Optional[str | list[str]]
    """ Email domain to check for.

    This flag can be used for sign-up flows where user uuid can't be used. Please check the intended use case.
    """

    cloud: Optional[str | list[str]]
    """ Cloud to check for, e.g. "GCP_US_CENTRAL1_PROD"

    The flag is always bound.
    """

    env: Optional[str | list[str]]
    """ Environment to check for, e.g. "STAGING" or "PROD"

    The flag is always bound.
    """

    client: Optional[str | list[str]]
    """ Client type to check for, e.g. "intellij" or "vscode"

    This flag is bound when the client sends the user-agent header.
    """

    min_client_version: Optional[str]
    """ Minimum client version to check for, e.g. "0.70.0"

    This flag is bound when the client sends user-agent header.
    The comparison is done using semver rules (semVerLessThan with negation).
    """

    max_client_version: Optional[str]
    """ Maximum client version to check for, e.g. "0.80.0"

    This flag is bound when the client sends the user-agent header.
    The comparison is done using semver rules (semVerGreaterThan with negation).
    """

    return_value: str | int | float | bool
    """ Flag value to apply for the specific namespace and/or pod """

    def __init__(
        self,
        return_value: str | int | float | bool,
        namespace: Optional[str | list[str]] = None,
        pod_name: Optional[str | list[str]] = None,
        tenant_name: Optional[str | list[str]] = None,
        user_id_hmac: Optional[str | list[str]] = None,
        api_key_user_id: Optional[str | list[str]] = None,
        user_uuid: Optional[str | list[str]] = None,
        model_name: Optional[str | list[str]] = None,
        email_domain: Optional[str | list[str]] = None,
        env: Optional[str | list[str]] = None,
        cloud: Optional[str | list[str]] = None,
        client: Optional[str | list[str]] = None,
        min_client_version: Optional[str] = None,
        max_client_version: Optional[str] = None,
    ):
        self.return_value = normalize_value(return_value)
        self.namespace = namespace
        self.pod_name = pod_name
        self.tenant_name = tenant_name
        self.user_id_hmac = user_id_hmac
        self.api_key_user_id = api_key_user_id
        self.user_uuid = user_uuid
        self.model_name = model_name
        self.email_domain = email_domain
        self.env = env
        self.cloud = cloud
        self.client = client
        self.min_client_version = min_client_version
        self.max_client_version = max_client_version


@dataclass
class AugmentFeatureFlagEnv:
    """
    See https://github.com/launchdarkly/api-client-python/blob/main/docs/FeatureFlagConfig.md
    """

    archived: bool
    """ Flag is archived for env """
    rules: list[AugmentFeatureFlagRule]
    """ Flag rules for env """

    def __init__(self, archived: bool, rules: list[dict]):
        self.archived = archived or False
        self.rules = [AugmentFeatureFlagRule(**rule) for rule in rules]


def _is_type_compatible(a: typing.Any, b: typing.Type):
    if isinstance(a, b):
        return True
    if b == float and isinstance(a, int):
        return True
    if b == int and isinstance(a, float):
        return True
    return False


@dataclass
class AugmentFeatureFlag:
    """
    An Augment representation of a feature flag
    Dataclass exists mostly to validate the structure
    """

    key: str
    """ Flag key - the unique identifier of the key """
    sync: bool
    """ Flag indicates if the flag should be synced to LaunchDarkly """
    description: Optional[str]
    """ Flag default value returned if no rules match """
    envs: Optional[dict[str, AugmentFeatureFlagEnv]]
    """ Environments config"""

    def __init__(
        self,
        key: str,
        sync: Optional[bool],
        description: Optional[str],
        envs: Optional[dict[Env, dict]],
    ):
        self.key = key
        self.sync = sync or False
        self.description = description or ""
        self.envs = {}
        if envs is not None:
            for env, env_config in envs.items():
                archived = env_config.get("archived")
                if archived is None:
                    archived = False
                assert isinstance(archived, bool)
                rules = env_config.get("rules") or []
                assert isinstance(rules, list)
                assert archived or len(rules) > 0
                self.envs[env] = AugmentFeatureFlagEnv(archived=archived, rules=rules)
                first_rule_value = self.envs[env].rules[0].return_value
                # All return value values must be the same type
                for rule in self.envs[env].rules:
                    assert _is_type_compatible(
                        rule.return_value, type(first_rule_value)
                    ), (
                        f"Flag {key} return value type mismatch - Env {env} rule returns "
                        f"{type(rule.return_value).__name__} expected {type(first_rule_value).__name__}"
                    )


def augment_flags_dict_to_augment_flags_dataclass(
    augment_flags_dict: dict,
) -> list[AugmentFeatureFlag]:
    result: list[AugmentFeatureFlag] = []
    for key, augment_flag_dict in augment_flags_dict.items():
        augment_flag_dict["key"] = key
        augment_feature_flag = AugmentFeatureFlag(**augment_flag_dict)
        result.append(augment_feature_flag)
    return result


class LaunchDarklyDiff:
    patch_flags: dict[str, PatchWithComment]

    def __init__(
        self,
        patch_flags: dict[str, PatchWithComment] = {},
    ):
        self.patch_flags = patch_flags

    @staticmethod
    def from_dict(patch_flags_dict: dict):
        assert isinstance(patch_flags_dict, dict)
        return LaunchDarklyDiff(
            {
                flag_key: dict_to_patch_with_comment(patch_with_comment_dict)
                for flag_key, patch_with_comment_dict in patch_flags_dict.items()
            },
        )

    def to_dict(self):
        return {
            flag_key: patch_with_comment_to_dict(patch_with_comment)
            for flag_key, patch_with_comment in self.patch_flags.items()
        }

    def to_str(self) -> str:
        return json.dumps(self.to_dict(), indent=4)


def value_to_variation_name(return_value: str | int | float | bool) -> str:
    return_value_str = str(return_value)

    if return_value_str == "":
        name = "not-set"
    elif len(return_value_str) > 255:
        name = hashlib.sha256(return_value_str.encode("utf-8")).hexdigest()
    else:
        name = return_value_str

    return name
