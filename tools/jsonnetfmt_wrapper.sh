#!/bin/bash
set -euo pipefail

# Source the runfiles library
if [[ -f "${RUNFILES_DIR:-/dev/null}/bazel_tools/tools/bash/runfiles/runfiles.bash" ]]; then
	source "${RUNFILES_DIR}/bazel_tools/tools/bash/runfiles/runfiles.bash"
elif [[ -f "$0.runfiles/bazel_tools/tools/bash/runfiles/runfiles.bash" ]]; then
	source "$0.runfiles/bazel_tools/tools/bash/runfiles/runfiles.bash"
else
	echo "Error: Could not find runfiles library" >&2
	exit 1
fi

# Use rlocation to find the binary
JSONNETFMT="$(rlocation "jsonnet_go~/cmd/jsonnetfmt/jsonnetfmt_/jsonnetfmt")"
if [[ ! -x "$JSONNETFMT" ]]; then
	echo "Error: Could not find jsonnetfmt binary at: $JSONNETFMT" >&2
	exit 1
fi

# The actual business logic: apply extra options to jsonnetfmt
exec "$JSONNETFMT" --no-use-implicit-plus "$@"
