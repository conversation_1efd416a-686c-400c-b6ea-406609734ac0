"""CLI application for kubecfg targets.

Usually exposed via kubecfg bazel targets, but can in theory also be
used standalone.
"""

import argparse
import functools
import json
import logging
import os
import pathlib
import sys

from InquirerPy import prompt  # type: ignore

from base.logging.console_logging import setup_console_logging
from base.cloud.k8s import kubectl
from tools.kubecfg import kubecfg_diff
from tools.kubecfg.kubecfg import (
    KubeCfg,
    KubeCfgException,
    VisitConfig,
)
from tools.kubecfg.pusher import dependency_push_image

# kubecfg ran successfully
RETURN_CODE_OK = 0

# kubecfg failed due to an invalid argument
RETURN_CODE_ARGUMENT_ERROR = 1

# kubecfg failed due to a generic error
RETURN_CODE_ERROR = 2

# kubecfg apply was skipped due to no changes
RETURN_CODE_SKIPPED = 4


def confirm(message: str) -> bool:
    questions = [
        {
            "type": "confirm",
            "message": message,
            "name": "continue",
            "default": True,
        },
    ]

    answers = prompt(questions)
    return bool(answers["continue"])


class KubeCfgUtil:
    """CLI utility for kubecfg."""

    def __init__(
        self,
        kubecfg: KubeCfg,
        interactive: bool,
        stamp: bool = False,
        rewrite: bool = True,
        diff_check: bool = False,
    ):
        """Constructs a KubeCfgUtil instance.

        Args:
            kubecfg: KubeCfg instance
            interactive: Whether to use interactive mode or not.
            push_image: if set to true, the container image will be pushed if required. If set to False, the image will never be pushed.
            stamp: if the deployment should be stamped
            rewrite: if the configuration should be rewritten (e.g. target replacement and stamping)
            diff_check: if the diff should be checked. If disabled, the target is deployed even if there are no changes.
        """
        self.kubecfg = kubecfg
        self.interactive = interactive
        self.diff_check = diff_check
        self.visit_config = VisitConfig(stamp=stamp, rewrite=rewrite)

    def apply(
        self,
        output_file: pathlib.Path | None = None,
        exit_code_on_no_changes: int = RETURN_CODE_SKIPPED,
    ) -> int:
        """Applies the configuration to the cluster."""
        with self.kubecfg.get_config(self.visit_config) as config:
            if self.kubecfg.namespace:
                logging.info(
                    "Applying configuration to namespace '%s' in '%s'",
                    self.kubecfg.namespace,
                    self.kubecfg.env,
                )
            else:
                logging.info("Applying cluster-wide configuration")
            r = config.diff()

            if r.stdout:
                logging.info("%s", r.stdout)
            if r.stderr and kubectl.contains_error(r.stderr):
                logging.error("%s", r.stderr)
                return 1

            if output_file:
                output_file.write_text(config.dump())

            has_diff = True
            if (
                self.diff_check
                and not kubectl.contains_error(r.stderr)
                and not kubecfg_diff.is_meaningful_diff(r.stdout)
            ):
                has_diff = False
                logging.info("No changes to apply")

            if has_diff:
                if self.interactive and self.kubecfg.env != "DEV":
                    res = confirm(f"Apply changes to '{self.kubecfg.env}'?")
                else:
                    res = True
                if res:
                    r = config.apply()
                    if r.stdout:
                        logging.info("%s", r.stdout)
                    if r.stderr:
                        logging.error("%s", r.stderr)
                    if kubectl.contains_error(r.stderr):
                        return RETURN_CODE_ERROR
                return RETURN_CODE_OK
            else:
                return exit_code_on_no_changes

    def delete(self) -> int:
        """Deletes the configuration from the cluster."""
        with self.kubecfg.get_config(self.visit_config) as config:
            if self.kubecfg.namespace:
                logging.info(
                    "Deleting configuration from namespace %s", self.kubecfg.namespace
                )
            else:
                logging.info("Deleting cluster-wide configuration")

            if self.interactive:
                res = confirm("Delete config?")
            else:
                res = True
            if res:
                r = config.delete()
                if r.stdout:
                    logging.info("%s", r.stdout)
                if r.stderr:
                    logging.error("%s", r.stderr)
                    return RETURN_CODE_ERROR
            return RETURN_CODE_OK

    def diff(self) -> int:
        """Shows the diff between the expected state and th current state."""
        with self.kubecfg.get_config(self.visit_config) as config:
            r = config.diff()
            if r.stdout:
                logging.info("%s", r.stdout)
            if r.stderr:
                logging.error("%s", r.stderr)
                return RETURN_CODE_ERROR
            return RETURN_CODE_OK

    def dump(self):
        with self.kubecfg.get_config(self.visit_config) as config:
            # use print instead of logging to ensure printing to stdout without other processing
            print(config.dump())


def _add_default_args(parser):
    parser.add_argument("--env", default=None, choices=["DEV", "STAGING", "PROD"])
    parser.add_argument("--namespace")
    parser.add_argument("--batch", action="store_true")
    parser.add_argument("--skip-push-image", action="store_true")
    parser.add_argument("--stamp", action="store_true")
    parser.add_argument(
        "--kube-config-file",
        type=pathlib.Path,
        default=pathlib.Path.home().joinpath(".kube", "config"),
    )
    parser.add_argument(
        "--deployed-by",
        default=get_user(),
        help="User that deployed the configuration. Defaults to the augment user name.",
    )
    parser.add_argument("--extra-config-args", action="append", default=[])


def main():
    # Do not allow option abbreviation to prevent very confusing execution when trying
    # to pass an arg that only a subparser supports, but matches a unique prefix
    # of a top-level option. (Happened with `--namespace` already).
    parser = argparse.ArgumentParser(allow_abbrev=False)
    parser.add_argument("--file", action="append")
    parser.add_argument("--cluster-wide", action="store_true", default=False)
    parser.add_argument("--no-rewrite", action="store_true", default=False)
    parser.add_argument("--extra-kubectl-args", action="append", default=[])
    parser.add_argument("--allowed-cloud", action="append", default=None)
    parser.add_argument(
        "--cloud",
        default=None,
        type=str.upper,
        choices=[
            "GCP_US_CENTRAL1_PROD",
            "GCP_EU_WEST4_PROD",
            "GCP_US_CENTRAL1_DEV",
            "GCP_US_CENTRAL1_GSC_PROD",
            "GCP_AGENT_US_CENTRAL1_PROD",
            "GCP_AGENT_EU_WEST4_PROD",
        ],
    )
    parser.add_argument(
        "--namespace-config-path",
        type=pathlib.Path,
        default=None,
        help="Path to the namespace config file",
    )
    parser.set_defaults(
        action="apply",
        env=None,
        namespace=None,
        batch=None,
        skip_push_image=None,
        stamp=False,
        skip_diff_check=False,
        output_file=None,
        deployed_by=get_user(),
        extra_config_args=[],
        kube_config_file=pathlib.Path.home().joinpath(".kube", "config"),
        exit_zero_on_no_changes=False,
    )

    subparsers = parser.add_subparsers(help="sub-commands")
    apply_parser = subparsers.add_parser("apply")
    _add_default_args(apply_parser)
    apply_parser.add_argument(
        "--output-file",
        type=pathlib.Path,
        help="File that will contian the applied configuration as yaml.",
    )
    apply_parser.add_argument(
        "--skip-diff-check",
        action="store_true",
        help="Skip the check for meaningful diffs. Useful for debugging.",
    )
    apply_parser.add_argument(
        "--exit-zero-on-no-changes",
        action="store_true",
        help="Exit with zero return code even if there are no changes.",
    )
    apply_parser.set_defaults(action="apply")

    delete_parser = subparsers.add_parser("delete")
    _add_default_args(delete_parser)
    delete_parser.set_defaults(action="delete")

    dump_parser = subparsers.add_parser("dump")
    _add_default_args(dump_parser)

    dump_parser.set_defaults(action="dump")

    diff_parser = subparsers.add_parser("diff")
    _add_default_args(diff_parser)
    diff_parser.set_defaults(action="diff")

    args = parser.parse_args()
    if args.allowed_cloud is None:
        args.allowed_cloud = [
            "GCP_US_CENTRAL1_PROD",
            "GCP_EU_WEST4_PROD",
            "GCP_US_CENTRAL1_DEV",
            "GCP_US_CENTRAL1_GSC_PROD",
            "GCP_AGENT_US_CENTRAL1_PROD",
            "GCP_AGENT_EU_WEST4_PROD",
        ]
    if args.cloud and args.allowed_cloud and args.cloud not in args.allowed_cloud:
        parser.exit(RETURN_CODE_ARGUMENT_ERROR, f"Cloud '{args.cloud}' is not allowed")
    if not args.cluster_wide and not args.env:
        # use the default environment
        args.env = "DEV"
    if not args.cloud:
        args.cloud = "GCP_US_CENTRAL1_DEV"
    if args.cluster_wide or args.env != "DEV":
        # always stamp outside of "DEV"
        args.stamp = True
    if args.cluster_wide:
        if args.namespace:
            parser.exit(
                RETURN_CODE_ARGUMENT_ERROR,
                "namespace must not be set for cluster-wide configuration",
            )
        if args.env:
            parser.exit(
                RETURN_CODE_ARGUMENT_ERROR,
                "env must not be set for cluster-wide configuration",
            )
    if args.kube_config_file:
        if not pathlib.Path(args.kube_config_file).exists():
            parser.exit(
                RETURN_CODE_ARGUMENT_ERROR,
                f"kube-config-file '{args.kube_config_file}' does not exist",
            )

    setup_console_logging(add_timestamp=args.batch)

    logging.debug("Args: %s", args)

    def parse_args(arg):
        p = arg.strip().partition("=")
        if not p[1]:
            parser.exit(
                RETURN_CODE_ARGUMENT_ERROR,
                "extra-config must be in the form of key=value",
            )
        return (p[0], p[2])

    extra_config_args = [parse_args(arg) for arg in args.extra_config_args]

    kubecfg = KubeCfg(
        args.file,
        cloud=args.cloud,
        cluster_wide=args.cluster_wide,
        env=args.env,
        namespace=args.namespace,
        base_directory=pathlib.Path.cwd().absolute(),
        kube_config_file=args.kube_config_file,
        extra_kubectl_args=args.extra_kubectl_args,
        deployed_by=args.deployed_by,
        extra_config_args=extra_config_args,
        push_fn=dependency_push_image if not args.skip_push_image else None,
        namespace_config_path=args.namespace_config_path,
    )
    kubecfg_util = KubeCfgUtil(
        kubecfg,
        interactive=not args.batch,
        stamp=args.stamp,
        rewrite=not args.no_rewrite,
        diff_check=not args.skip_diff_check,
    )

    try:
        if args.action == "apply":
            sys.exit(
                kubecfg_util.apply(
                    output_file=args.output_file,
                    exit_code_on_no_changes=RETURN_CODE_OK
                    if args.exit_zero_on_no_changes
                    else RETURN_CODE_SKIPPED,
                )
            )
        if args.action == "delete":
            sys.exit(kubecfg_util.delete())
        elif args.action == "dump":
            sys.exit(kubecfg_util.dump())
        elif args.action == "diff":
            sys.exit(kubecfg_util.diff())
        else:
            logging.error("Invalid action '%s'", args.action)
            sys.exit(RETURN_CODE_ARGUMENT_ERROR)
    except KubeCfgException as e:
        logging.error("%s", e.msg)
        if e.stdout:
            logging.error("%s", e.stdout)
        if e.stderr:
            logging.error("%s", e.stderr)
        sys.exit(RETURN_CODE_ERROR)


@functools.cache
def get_user() -> str | None:
    """Returns the current user."""
    if "BAZEL_BUILD_USER" in os.environ:
        return os.environ["BAZEL_BUILD_USER"]
    if pathlib.Path.home().joinpath(".augment", "user.json").exists():
        with pathlib.Path.home().joinpath(".augment", "user.json").open(
            encoding="utf-8"
        ) as user_file:
            user_data = json.load(user_file)
            user_name = user_data["name"]
            return user_name


if __name__ == "__main__":
    main()
