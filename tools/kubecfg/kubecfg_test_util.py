"""Test Binary to test kubecfg configuration."""

import argparse
import logging
import pathlib
import sys

from base.logging.console_logging import setup_console_logging
from tools.kubecfg.kubecfg import KubeCfg, KubeCfgException
from tools.kubecfg.kubecfg_test_lib import KubeCfgTestUtil

FORBIDDEN_DEPS = [
    "research",
    "experimental",
    "clients/intellij",
    "clients/sidecar",
]


def _check_deps(deps_file: pathlib.Path):
    content = deps_file.read_text()
    for dep in content.splitlines():
        if any(dep.startswith(prefix) for prefix in FORBIDDEN_DEPS):
            logging.error("Dependency check failed: Depends on forbidden path: %s", dep)
            return False
    return True


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--file", action="append")
    parser.add_argument("--deps-file", type=pathlib.Path)
    parser.add_argument("--allowed-cloud", action="append")
    parser.add_argument("--cluster-wide", action="store_true", default=False)
    parser.add_argument("--allow-multi-apps", action="store_true", default=False)
    parser.add_argument(
        "--tla-str",
        action="append",
        default=[],
        help="top-level string arguments to pass to jsonnet. Should be formatted as 'key=str_value'.",
    )
    args = parser.parse_args()

    def parse_args(arg):
        p = arg.strip().partition("=")
        if not p[1]:
            parser.exit(
                1,
                "tla-str must be in the form of key=value",
            )
        return (p[0], p[2])

    extra_config_args = [parse_args(arg) for arg in args.tla_str]

    setup_console_logging(add_timestamp=False)

    logging.debug("%s", args)

    failed = False

    configs = [
        {
            "cloud": "GCP_US_CENTRAL1_DEV",
            "env": "DEV",
        },
        {
            "cloud": "GCP_US_CENTRAL1_PROD",
            "env": "PROD",
        },
    ]

    for config in configs:
        cloud = config["cloud"]
        env = config["env"]
        if args.allowed_cloud and cloud not in args.allowed_cloud:
            continue
        # some problems are related to the length of the object and the length of the
        # namespace, so we use a long namespace name
        long_namespace_name = "dev-ef0123456789"  # as long as "central-staging"
        logging.info("Kubecfg generation test for '%s'", cloud)
        kubecfg = KubeCfg(
            args.file,
            cloud=cloud,
            cluster_wide=args.cluster_wide,
            env=env if not args.cluster_wide else None,
            namespace=long_namespace_name if not args.cluster_wide else None,
            base_directory=pathlib.Path.cwd().absolute(),
            push_fn=lambda src, dst: f"{dst}@test",
            extra_config_args=extra_config_args,
        )
        kubecfg_test_util = KubeCfgTestUtil(
            kubecfg, allow_multi_apps=args.allow_multi_apps
        )

        try:
            kubecfg_test_util.test()
            logging.info("Kubecfg generation test for '%s' succeeded", cloud)
        except AssertionError as e:
            logging.error("Kubecfg generation test for '%s' failed", cloud)
            logging.error("%s", e)
            failed = True
        except KubeCfgException as e:
            logging.error("Kubecfg generation test for '%s' failed", cloud)
            if e.stdout:
                logging.error("%s", e.stdout)
            if e.stderr:
                logging.error("%s", e.stderr)
            failed = True

    if args.deps_file:
        if not _check_deps(args.deps_file):
            failed = True

    if failed:
        logging.error("Test Failed")
        sys.exit(2)
    else:
        logging.info("Test Passed")
        sys.exit(0)


if __name__ == "__main__":
    main()
