load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:metadata.bzl", "metadata_test")
load("//tools/bzl:python.bzl", "py_binary", "py_library", "py_oci_image")

py_library(
    name = "config",
    srcs = ["config.py"],
    deps = [
        requirement("dataclasses_json"),
    ],
)

py_library(
    name = "consumer",
    srcs = ["consumer.py"],
    deps = [
        requirement("GitPython"),
        ":config",
        "//base/cloud/k8s:docker",
        "//base/cloud/k8s:kubectl_factory",
        "//base/cloud/k8s:kubernetes_client",
        "//tools/bazel_runner/client",
        "//tools/bazel_runner/git:checkout",
        "//tools/bazel_runner/github_webhook:github_py_proto",
        "//tools/bot:bot_client",
        "//tools/deploy_runner:deployment_finder",
    ],
)

py_library(
    name = "gcp",
    srcs = ["gcp.py"],
    deps = [
        ":config",
        requirement("google-cloud-pubsub"),
        "//tools/bazel_runner/github_webhook:github_py_proto",
    ],
)

py_binary(
    name = "server",
    srcs = [
        "server.py",
    ],
    data = [
        "//tools/deploy:auth_kube_config_yaml",
        "@gke-gcloud-auth-plugin//:all",
    ],
    main = "server.py",
    deps = [
        ":config",
        ":consumer",
        ":gcp",
        "//base/cloud/k8s:docker",
        "//base/cloud/k8s:kubectl_factory",
        "//base/cloud/k8s:kubernetes_client",
        "//base/logging:struct_logging",
        "//base/python/cloud",
        "//base/python/cloud:gcp",
        "//tools/bazel_runner/git:app_token",
        "//tools/bazel_runner/git:checkout",
        "//tools/bazel_runner/github_webhook:github_py_proto",
        "//tools/bot:bot_client",
        "//tools/deploy:kube_config_copy",
        "//tools/deploy_runner:deployment_finder",
        requirement("google-cloud-pubsub"),
        requirement("grpcio"),
        requirement("grpcio-health-checking"),
        requirement("grpcio-reflection"),
        requirement("protobuf"),
        requirement("prometheus_client"),
    ],
)

py_oci_image(
    name = "image",
    package_name = package_name(),
    base = "//tools/docker:cbazel_base_image",
    binary = ":server",
    trivy_allow_list = [
        "CVE-2024-34156",  # AU-4115
    ],
    visibility = ["//tools/deploy_runner:__subpackages__"],
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        ":image",
    ],
    visibility = ["//tools/bazel_runner:__subpackages__"],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:eng-lib",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/common:telemetry-lib",
        "//deploy/gcp:gcp-lib",
        "//tools/deploy:github_readonly_token_lib",
    ],
)

kubecfg(
    name = "kubecfg_shared",
    src = "deploy_shared.jsonnet",
    cluster_wide = True,
)

metadata_test(
    name = "metadata_test",
    src = "METADATA.jsonnet",
    deps = [
        ":kubecfg",
        ":kubecfg_shared",
    ],
)
