load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:python.bzl", "py_binary", "py_library", "py_oci_image")

py_library(
    name = "config",
    srcs = ["config.py"],
    deps = [
        requirement("dataclasses_json"),
    ],
)

py_library(
    name = "notify",
    srcs = ["notify.py"],
    deps = [
        ":config",
        requirement("google-cloud-pubsub"),
        "//tools/deploy_runner:deployment_finder",
    ],
)

py_binary(
    name = "deploy_pod",
    srcs = ["deploy_pod.py"],
    data = [
        "//tools/deploy:auth_kube_config_yaml",
        "@gke-gcloud-auth-plugin//:all",
    ],
    deps = [
        ":config",
        ":notify",
        "//base/cloud/k8s:docker",
        "//base/cloud/k8s:kubectl_factory",
        "//base/cloud/k8s:kubernetes_client",
        "//base/logging:struct_logging",
        "//base/python/cloud",
        "//base/python/cloud:env_info",
        "//base/python/cloud:gcp",
        "//tools/bazel_runner/git:app_token",
        "//tools/bazel_runner/git:checkout",
        "//tools/bot:bot_client",
        "//tools/deploy_runner:deploy_gate",
        "//tools/deploy_runner:deployment_finder",
        requirement("kubernetes"),
        requirement("prometheus_client"),
    ],
)

py_oci_image(
    name = "image",
    package_name = package_name(),
    base = "//tools/docker:cbazel_base_image",
    binary = ":deploy_pod",
    trivy_allow_list = [
        "CVE-2024-34156",  # AU-4115
    ],
    visibility = ["//tools/deploy_runner:__subpackages__"],
)

py_library(
    name = "deploy_pod_factory",
    srcs = ["deploy_pod_factory.py"],
    data = [
        ":deploy_runner.jsonnet",
        "//deploy/common:cloud_info-files",
        "//deploy/common:node-lib-files",
        "@google_jsonnet_go//cmd/jsonnet",
    ],
    visibility = ["//tools/deploy_runner:__subpackages__"],
    deps = [
        requirement("python-ulid"),
        "//base/cloud/k8s:kubectl_factory",
        "//base/cloud/k8s:kubernetes_client",
        "//tools/deploy_runner:metadata_py_proto",
        "//tools/deploy_runner/server:deploy_py_proto",
    ],
)

py_binary(
    name = "util",
    srcs = ["util.py"],
    data = [
        "//tools/deploy:auth_kube_config_yaml",
        "@gke-gcloud-auth-plugin//:all",
    ],
    deps = [
        ":deploy_pod_factory",
        "//base/cloud/k8s:kubectl_factory",
        "//base/cloud/k8s:kubernetes_client",
        "//base/logging:console_logging",
        "//tools/deploy_runner/server:deploy_py_proto",
    ],
)

# opens a pod with a shell on the deploy PVC for debugging
kubecfg(
    name = "kubecfg_shell",
    src = "shell.jsonnet",
    cluster_wide = True,
    data = [
        ":image",
    ],
    deps = [
        "//deploy/common:node-lib",
    ],
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    visibility = ["//services/deploy:__subpackages__"],
    deps = [
        "//deploy/common:cloud_info",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/gcp:gcp-lib",
        "//tools/deploy:github_readonly_token_lib",
    ],
)
