// k8s configuration for the deploy runner controller that should be pre-applied to the cluster
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local githubAppSealedLib = import 'tools/deploy/github_readonly_app_sealed.jsonnet';
function(cloud, env, namespace, namespace_config)
  local projectId = cloudInfo[cloud].projectId;
  local buildImageRepoName = 'projects/%s/locations/%s/repositories/%s' % [cloudInfo[cloud].projectId, cloudInfo[cloud].region, if cloudInfo.isLeadCluster(cloud) then 'build-images' else 'build-images-%s' % cloudInfo[cloud].shortName];

  local devObjects = if cloud == 'GCP_US_CENTRAL1_DEV' then [
    // give dev PROD control access to IAP
    {
      apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
      kind: 'IAMPartialPolicy',
      metadata: {
        name: 'dev-deploy-iap-web-access',
        namespace: namespace,
        labels: {
          app: 'deploy',
        },
      },
      spec: {
        resourceRef: {
          kind: 'Project',
          external: cloudInfo[cloud].projectId,
        },
        bindings: [
          {
            role: 'roles/iap.httpsResourceAccessor',
            members: [
              {
                member: 'serviceAccount:<EMAIL>',
              },
            ],
          },
        ],
      },
    },
  ] else [];

  // give PROD deploy job account deploy-role permissions in every cluster.
  local crossClusterRoles = [
    {
      apiVersion: 'rbac.authorization.k8s.io/v1',
      kind: 'ClusterRoleBinding',
      metadata: {
        name: '%s-cloud-deploy-runner-role-binding' % namespace,
        labels: {
          app: 'deploy',
        },
      },
      subjects: [
        // always give the deploy service account of the prod project permission to deploy
        {
          kind: 'User',
          name: '<EMAIL>',
        },
      ] + if cloud == 'GCP_US_CENTRAL1_DEV' then [
        // always give the deploy service account of the dev project permission to deploy
        {
          kind: 'User',
          name: '<EMAIL>',
        },
      ] else [],
      roleRef: {
        kind: 'ClusterRole',
        name: 'deploy-role',
        apiGroup: 'rbac.authorization.k8s.io',
      },
    },
    // give prod access to the dev build artifact registry
    // the deploy job is building the images and pushing them to the artifact registry
    {
      apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
      kind: 'IAMPartialPolicy',
      metadata: {
        name: 'deploy-sa-dev-artifact-writer',
        namespace: namespace,
        labels: {
          app: 'deploy',
        },
      },
      spec: {
        resourceRef: {
          kind: 'ArtifactRegistryRepository',
          external: buildImageRepoName,
        },
        bindings: [
          {
            role: 'roles/artifactregistry.writer',
            members: [
              {
                member: 'serviceAccount:<EMAIL>',
              },
            ],
          },
        ],
      },
    },
    // Add this new IAM policy for agent-sandbox-prod project
    {
      apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
      kind: 'IAMPartialPolicy',
      metadata: {
        name: 'deploy-sa-agent-sandbox-container-admin',
        namespace: namespace,
        labels: {
          app: 'deploy',
        },
      },
      spec: {
        resourceRef: {
          kind: 'Project',
          external: 'agent-sandbox-prod',
        },
        bindings: [
          {
            role: 'roles/container.admin',  // This includes container.thirdPartyObjects.update
            members: [
              {
                member: 'serviceAccount:<EMAIL>',
              },
            ],
          },
          {
            role: 'roles/monitoring.admin',
            members: [
              {
                member: 'serviceAccount:<EMAIL>',
              },
            ],
          },
        ],
      },
    },
  ];

  // we want the deploy view in the prod lead cluster and in one DEV cluster
  if cloud == 'GCP_US_CENTRAL1_DEV' || cloud == 'GCP_US_CENTRAL1_PROD' then
    local githubSecret = githubAppSealedLib(cloud=cloud, namespace=namespace, appName='deploy');
    local crossProjectObjects = if cloud == 'GCP_US_CENTRAL1_DEV' && namespace == 'devtools' then [
      // give prod access to the base images
      {
        apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
        kind: 'IAMPartialPolicy',
        metadata: {
          name: 'prod-deploy-sa-base-image-reader',
          namespace: namespace,
          labels: {
            app: 'deploy',
          },
        },
        spec: {
          resourceRef: {
            kind: 'ArtifactRegistryRepository',
            external: 'projects/system-services-dev/locations/us-central1/repositories/base-images',
          },
          bindings: [
            {
              role: 'roles/artifactregistry.reader',
              members: [
                {
                  member: 'serviceAccount:<EMAIL>',
                },
                {
                  member: 'serviceAccount:<EMAIL>',
                },
              ],
            },
          ],
        },
      },
      // give prod access to the bazel data bucket
      {
        apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
        kind: 'IAMPartialPolicy',
        metadata: {
          name: 'prod-deploy-sa-bazel-data-bucket-policy',
          namespace: namespace,
          labels: {
            app: 'deploy',
          },
        },
        spec: {
          resourceRef: {
            kind: 'StorageBucket',
            external: 'augment-bazel-data',
          },
          bindings: [
            {
              role: 'roles/storage.objectUser',
              members: [
                {
                  member: 'serviceAccount:<EMAIL>',
                },
              ],
            },
          ],
        },
      },
    ] else [];
    local gcpObjects = [
      {
        apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
        kind: 'IAMServiceAccount',
        metadata: {
          name: '%s-deploy-iam' % namespace,
          namespace: namespace,
          labels: {
            app: 'deploy',
          },
        },
        spec: {
          displayName: '%s-deploy-iam' % namespace,
        },
      },
      {
        apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
        kind: 'IAMPolicy',
        metadata: {
          name: 'github-deploy-identity',
          namespace: namespace,
          labels: {
            app: 'deploy',
          },
        },
        spec: {
          resourceRef: {
            apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
            kind: 'IAMServiceAccount',
            name: '%s-deploy-iam' % namespace,
          },
          bindings: [
            {
              role: 'roles/iam.workloadIdentityUser',
              members: [
                'serviceAccount:%s.svc.id.goog[%s/deploy-sa]' % [projectId, namespace],
              ],
            },
            // required for IAP
            {
              role: 'roles/iam.serviceAccountTokenCreator',
              members: [
                'serviceAccount:%s.svc.id.goog[%s/deploy-sa]' % [projectId, namespace],
              ],
            },
          ],
        },
      },
      if cloudInfo.isProdCluster(cloud) then [
        {
          apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
          kind: 'IAMPartialPolicy',
          metadata: {
            name: 'deploy-sa-artifact-writer',
            namespace: namespace,
            labels: {
              app: 'deploy',
            },
          },
          spec: {
            resourceRef: {
              kind: 'ArtifactRegistryRepository',
              external: 'projects/%s/locations/us-central1/repositories/build-images' % projectId,
            },
            bindings: [
              {
                role: 'roles/artifactregistry.writer',
                members: [
                  {
                    memberFrom: {
                      serviceAccountRef: {
                        name: '%s-deploy-iam' % namespace,
                      },
                    },
                  },
                ],
              },
            ],
          },
        },
        {
          apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
          kind: 'IAMPartialPolicy',
          metadata: {
            name: 'deploy-sa-artifact-writer-eu-west-4',
            namespace: namespace,
            labels: {
              app: 'deploy',
            },
          },
          spec: {
            resourceRef: {
              kind: 'ArtifactRegistryRepository',
              external: 'projects/%s/locations/europe-west4/repositories/build-images-eu-w4' % projectId,
            },
            bindings: [
              {
                role: 'roles/artifactregistry.writer',
                members: [
                  {
                    memberFrom: {
                      serviceAccountRef: {
                        name: '%s-deploy-iam' % namespace,
                      },
                    },
                  },
                ],
              },
            ],
          },
        },
      ] else null,
      {
        apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
        kind: 'IAMPartialPolicy',
        metadata: {
          name: 'deploy-sa-base-image-reader',
          namespace: namespace,
          labels: {
            app: 'deploy',
          },
        },
        spec: {
          resourceRef: {
            kind: 'ArtifactRegistryRepository',
            external: 'projects/system-services-dev/locations/us-central1/repositories/base-images',
          },
          bindings: [
            {
              role: 'roles/artifactregistry.reader',
              members: [
                {
                  memberFrom: {
                    serviceAccountRef: {
                      name: '%s-deploy-iam' % namespace,
                    },
                  },
                },
              ],
            },
          ],
        },
      },
      {
        apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
        kind: 'IAMPartialPolicy',
        metadata: {
          name: 'deploy-sa-bazel-data-bucket-policy',
          namespace: namespace,
          labels: {
            app: 'deploy',
          },
        },
        spec: {
          resourceRef: {
            kind: 'StorageBucket',
            external: 'augment-bazel-data',
          },
          bindings: [
            {
              role: 'roles/storage.objectUser',
              members: [
                {
                  memberFrom: {
                    serviceAccountRef: {
                      name: '%s-deploy-iam' % namespace,
                    },
                  },
                },
              ],
            },
          ],
        },
      },
    ];
    local serviceAccount = {
      apiVersion: 'v1',
      kind: 'ServiceAccount',
      metadata: {
        name: 'deploy-sa',
        namespace: namespace,
        annotations: {
          'iam.gke.io/gcp-service-account': '%s-deploy-iam@%s.iam.gserviceaccount.com' % [namespace, projectId],
        },
        labels: {
          app: 'deploy',
        },
      },
    };
    local createTrackVolume(name) = {
      apiVersion: 'v1',
      kind: 'PersistentVolumeClaim',
      metadata: {
        name: name,
        namespace: namespace,
        labels: {
          app: 'deploy',
        },
      },
      spec: {
        accessModes: [
          'ReadWriteOnce',
        ],
        storageClassName: 'premium-rwo',
        resources: {
          requests: {
            storage: '512Gi',
          },
        },
      },
    };
    local volumes = [
      createTrackVolume('deploy-pvc'),  // for 'default' track
      createTrackVolume('deploy-bypass-pvc'),  // for 'bypass' track
      createTrackVolume('deploy-experimental-pvc'),  // for 'experimental' track
      createTrackVolume('deploy-infer-and-embedder-pvc'),  // for 'infer-and-embedder' track
      createTrackVolume('deploy-tombstone-pvc'),  // for 'tombstone' track
      createTrackVolume('deploy-cicd-pvc'),  // for 'cicd' track
    ];
    local pdb = nodeLib.podDisruption(appName='deploy', namespace=namespace, env=env);
    lib.flatten([
      githubSecret,
      serviceAccount,
      gcpObjects,
      crossProjectObjects,
      crossClusterRoles,
      pdb,
      volumes,
      devObjects,
    ])
  else
    crossClusterRoles
