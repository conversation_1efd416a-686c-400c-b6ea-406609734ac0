"""Notification for the deploy pod."""

from tools.deploy_runner import deploy_target
from tools.deploy_runner import deploy_events_pb2
from tools.deploy_runner.control.config import Config
import logging
from google.cloud import pubsub_v1  # type: ignore


class PubSubNotifier(deploy_target.DeployNotifier):
    """Notifier that sends notifications to pubsub."""

    def __init__(self, config: Config, publisher: pubsub_v1.PublisherClient):
        assert config.pubsub
        self.deploy_id = config.deploy_id
        self.publisher = publisher
        self.topic_path = self.publisher.topic_path(
            config.pubsub.project_id, config.pubsub.topic_name
        )
        logging.info("Events will be written to %s", self.topic_path)
        self.sequence_number = 1

    def _get_event(self) -> deploy_events_pb2.DeployEvent:
        e = deploy_events_pb2.DeployEvent(
            deploy_id=self.deploy_id,
            sequence_number=self.sequence_number,
        )
        e.time.GetCurrentTime()
        self.sequence_number += 1
        return e

    def _send(self, event: deploy_events_pb2.DeployEvent):
        data = event.SerializeToString()
        future = self.publisher.publish(self.topic_path, data)
        future.result()

    def deploy_instance_started(self, event: deploy_events_pb2.DeployInstanceStarted):
        """Send an event."""
        try:
            e = self._get_event()
            e.deploy_instance_started.CopyFrom(event)
            self._send(e)
        except Exception as e:
            logging.error("Failed to send event: %s", e)

    def deploy_instance_finished(self, event: deploy_events_pb2.DeployInstanceFinished):
        """Send an event."""
        try:
            e = self._get_event()
            e.deploy_instance_finished.CopyFrom(event)
            self._send(e)
        except Exception as e:
            logging.error("Failed to send event: %s", e)

    def planned(self, event: deploy_events_pb2.DeployPlanned):
        """Send an event."""
        try:
            e = self._get_event()
            e.deploy_planned.CopyFrom(event)
            self._send(e)
        except Exception as e:
            logging.error("Failed to send event: %s", e)

    def started(self, event: deploy_events_pb2.DeployStarted):
        """Send an event."""
        try:
            e = self._get_event()
            e.deploy_started.CopyFrom(event)
            self._send(e)
        except Exception as e:
            logging.error("Failed to send event: %s", e)

    def finished(self, event: deploy_events_pb2.DeployFinished):
        """Send an event."""
        try:
            e = self._get_event()
            e.deploy_finished.CopyFrom(event)
            self._send(e)
        except Exception as e:
            logging.error("Failed to send event: %s", e)

    def validation_finished(self, event: deploy_events_pb2.ValidationFinished):
        """Send an event."""
        try:
            e = self._get_event()
            e.validation_finished.CopyFrom(event)
            self._send(e)
        except Exception as e:
            logging.error("Failed to send event: %s", e)

    def output(self, event: deploy_events_pb2.DeployOutput):
        """Send an event."""
        try:
            e = self._get_event()
            e.deploy_output.CopyFrom(event)
            self._send(e)
        except Exception as e:
            logging.error("Failed to send event: %s", e)

    def progress(self, event: deploy_events_pb2.DeployProgress):
        """Send an event."""
        try:
            e = self._get_event()
            e.deploy_progress.CopyFrom(event)
            self._send(e)
        except Exception as e:
            logging.error("Failed to send event: %s", e)

    def configuration(self, event: deploy_events_pb2.DeployConfiguration):
        """Send an event."""
        try:
            e = self._get_event()
            e.deploy_configuration.CopyFrom(event)
            self._send(e)
        except Exception as e:
            logging.error("Failed to send event: %s", e)

    def deploy_gate_started(self, event: deploy_events_pb2.DeployGateStarted):
        """Send an event."""
        try:
            e = self._get_event()
            e.deploy_gate_started.CopyFrom(event)
            self._send(e)
        except Exception as e:
            logging.error("Failed to send event: %s", e)

    def deploy_gate_finished(self, event: deploy_events_pb2.DeployGateFinished):
        """Send an event."""
        try:
            e = self._get_event()
            e.deploy_gate_finished.CopyFrom(event)
            self._send(e)
        except Exception as e:
            logging.error("Failed to send event: %s", e)

    @classmethod
    def create_from_args(cls, config: Config):
        publisher = pubsub_v1.PublisherClient()
        return cls(config, publisher)
