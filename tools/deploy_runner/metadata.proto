// see
// https://www.notion.so/Bazel-based-testing-and-deployment-638c55d03c9a446c884fd0b0d0b25447?pvs=4#74fd85baf83f43fd97c47bb4485d218e
syntax = "proto3";

package metadata;

message Rollout {
  // name of the kubernetes deployment
  string name = 1;

  // kubernetes namespace
  string namespace = 2;
}

message KubeCfgTask {
  enum Env {
    ENV_NOT_SET = 0;
    STAGING = 1;
    PROD = 2;
  }

  enum Cloud {
    // ALL is an ALIAS to ALL_GCP
    ALL = 0 [deprecated = true];

    // GCP_US_CENTRAL1_DEV is the default dev cloud
    GCP_US_CENTRAL1_DEV = 1;

    reserved 7;

    // the default US prod cluster in us-central1
    // it is also the lead prod cluster
    GCP_US_CENTRAL1_PROD = 2;

    // the Google Super Computing (GPU optimized) cluster in us-central1
    GCP_US_CENTRAL1_GSC_PROD = 8;

    // the default EU prod cluster in europe-west4
    GCP_EU_WEST4_PROD = 5;

    // agent clusters in agent-sandbox-prod project
    GCP_AGENT_US_CENTRAL1_PROD = 9;
    GCP_AGENT_EU_WEST4_PROD = 10;

    COREWEAVE = 3 [deprecated = true];

    // ALL is expanded to [GCP_US_CENTRAL1_DEV, GCP_US_CENTRAL1_PROD, GCP_EU_WEST4_PROD, "GCP_US_CENTRAL1_GSC_PROD"]
    ALL_GCP = 4;

    // ALL_LEADS is expanded to GCP_US_CENTRAL1_DEV, GCP_US_CENTRAL1_GSC_PROD, and GCP_US_CENTRAL1_PROD
    ALL_LEADS = 6;

    // ALL_GCP_AGENTS is expanded to [GCP_AGENT_US_CENTRAL1_PROD, GCP_AGENT_EU_WEST4_PROD]
    ALL_GCP_AGENTS = 11;
  }

  reserved 2, 5;

  Cloud cloud = 1;

  // environment to deploy to.
  //
  // Environment type will help determine the sizing of machines, redundancy of deployments
  //
  // It also affects naming of services but that should probably change.
  Env env = 3;

  // namespace to deploy to
  string namespace = 4;
}

message ExtraConfigArgs {
  // name of the argument
  string name = 1;

  // value of the argument (passed as string)
  string value = 2;
}

message KubeCfgDeployment {
  // bazel target for the kubecfg rule
  string target = 1;

  repeated KubeCfgTask task = 2;

  // extra arguments passed to the configuration generation
  repeated ExtraConfigArgs extra_config_args = 3;
}

message BazelDeployment {
  // bazel target to run
  string target = 1;

  // Arguments to pass to the command being run
  repeated string arguments = 2;
}

message KubecfgTombstoneObject {
  // kind and name of the object to delete
  // app: delete any object with the label "app" or "app.kubernetes.io/name"
  string kind = 1;

  // name of the object to tombstone.
  // if the kind is app, this is the app name
  string name = 2;

  // api version of the kind. Can be omitted if kind is "app"
  string api_version = 3;
}

// tombstone for kubecfg objects
//
// A tombstone is a list of apps or objects that should be deleted.
// The deployment system will delete the configured kubernetes objects
message KubecfgTombstone {
  // delete any of the given objects
  repeated KubecfgTombstoneObject object = 1;
  repeated KubeCfgTask task = 2;
}

enum ServiceTier {
  // Not set.
  //
  // Service tier is not set.
  TIER_UNSPECIFIED = 0;

  // High impact Production service.
  //
  // Supported by oncall.
  //
  // Any outage is a immediate general customer impact.
  // The service fulfills high requirements on testing, monitoring, and alerting.
  //
  // Example: api-proxy.
  TIER_1_A = 1;

  // Production service
  //
  // Supported by oncall.
  //
  // Any outage can directly or over time lead to a customer impact unless
  // fixed soon or impacts operations.
  //
  // Example: feature flag syncer
  TIER_1_B = 2;

  // Non-production service.
  //
  // Supported by subject matter experts.
  //
  // Any outage is not leading to a customer impact. An outage
  // might impact customers that explicitly signed up for early access (alpha/beta) features.
  //
  // As the service is not supported by oncall, it is not guaranteed to be up.
  // As the service still runs in production, it has to fulfill basic security requirements like
  // reviews, testing, and secure coding practices.
  //
  // Example: review edit bot
  TIER_2 = 3;
}

message SubjectMatterExpert {
  // user names from eng.jsonnet, usually the canonical augmentcode.com email address
  repeated string users = 1;

  // name of a slack channel to contact
  string slack_channel = 2;
}

message ServiceHealth {
  // tier of the service
  // not not change the service with without explicit review
  ServiceTier tier = 1;

  // subject matter expert for the service
  //
  // how to contact in case of emergencies
  // these are not the owners of a service, but people that can help with the service
  SubjectMatterExpert experts = 2;
}

message Deployment {
  reserved 2, 7;

  // name of the deployment
  //
  // e.g. used in the deployment ui.
  // Has ot be unique across all deployments.
  // needs to be lower-case letters with '-".
  string name = 1;

  // description of the deployment.
  // Short introduction, e.g. for oncall, into the Deployment.
  // Not used beyond for displaying.
  string description = 9;

  oneof deployment_config {
    // for bazel run based deployment
    BazelDeployment bazel = 4;

    // for kubecfg based deployment
    KubeCfgDeployment kubecfg = 5;

    // tombstone for kubecfg objects
    // will delete the configured kubernetes objects
    KubecfgTombstone kubecfg_tombstone = 6;
  }

  // priority of the deployment

  // the priority changes the order of deployments during a deployment run
  // targets with a lower priority will be deployed before targets with a higher priority
  int32 priority = 3;

  // health information for the service
  // should be set for all deployments that can reasonably fail
  ServiceHealth health = 8;

  // a deployment train defines a particular approach and schedule
  // to run the deployments.
  // A deployment starts usually on a pre-defined schedule and deploys bazel-last-known-good
  // for all targets configured for that schedule.
  //
  // When in doubt: use DEFAULT
  enum DeploymentScheduleName {
    // default deployment schedule, e.g. staging and prod, usually twice per
    // working days.
    DEFAULT = 0;

    // disabled deployments are not deployed as part of any deployment schedule.
    DISABLED = 1;

    // quick deploy means that a deployment will run at time of the merge to main
    // it should only be used for extremly time sensitive changes.
    //
    // quick deploy targets are not deployed as part of a scheduled deployment run, but
    // instead are deployed as part of a merge to main. They do not recognize evening
    // and night deployment blackout windows. That means that quick deployment targets
    // should be limited in scope, should not cause any downtime, and should only be used
    // if the deployment is time sensitive.
    //
    // quick deployments can only contain a subset of the resource changes and
    // cannot contain container image pushs.
    QUICK_DEPLOY = 2;

    // experimental schedule, can only deploy TIER 2 services with reduced safeguards
    // they can deploy multiple times per day.
    //
    // Using the experimental schedule can improve developer iteration time at an
    // increased risk.
    EXPERIMENTAL = 3;

    // schedule for deploying infer and embedder services
    //
    // This schedule is used for deploying infer and embedder services.
    // it often takes a large amount of time to deloy these services, so we
    // have a separate schedule for them.
    INFER_AND_EMBEDDER = 4;

    // tombstone schedule, used for deleting resources
    // tombstones take a long time to run, so we have a separate schedule for them.
    // they are not important for the health of the system, so we can run them less
    // often.
    TOMBSTONE = 5;

    // CICD schedule that runs with the same schedule as DEFAULT
    // This schedule is used for CICD services that need to be deployed with the same
    // frequency as the default schedule but need to be tracked separately.
    CICD = 6;
  }

  // deployment schedule name
  DeploymentScheduleName deployment_schedule_name = 10;
}

enum DeploymentGateLocation {
  // default, do not set
  LOCATION_UNSPECIFIED = 0;

  // run the deployment gate after deploying to staging, but before deploying to prod
  LOCATION_BEFORE_PROD = 1;
}

// A deployment gate that runs a bazel test target using
// the bazel runner test infrastucture.
message BazelTestGate {
  // bazel command to execute (build|test|run)
  // defaults to test if not set
  string command = 1;

  // list of bazel targets
  // the targets can containg wild cards that are expanded, e.g. `//tools/...`
  repeated string targets = 2;

  // list of any extra arguments to bazel to the bazel command
  repeated string extra_args = 3;

  // the timeout for the test in seconds
  // note that we do not use Duration to make this easier to configure in JSON/JSONNET.
  int32 timeout_seconds = 4;
}

// a deployment gate is a check that runs at some point during the Continous Deployment
// process to check if the deployment should continue.
//
// If a deployment gate fails, the deployment is aborted.
//
// Deployment gates are defined in the METADATA.jsonnet files
//
// Examples:
// - run a test suite before deploying to production
message DeploymentGate {
  // name of the deployment gate
  string name = 1;

  // description of the deployment gate
  string description = 2;

  // when the gate is applied
  DeploymentGateLocation location = 3;

  // subject matter expert for the deployment gate
  //
  // this is not the owner of the gate, but people that can help with the gate
  // it is for documentation and contact in case of emergencies
  SubjectMatterExpert experts = 4;

  oneof gate {
    // gate that runs a bazel test target using the bazel runner test infrastructure
    //
    // the tests will be run in the test infrastructure (similar to CI).
    //
    // if the target returns with a non-zero exit code, the deployment is aborted
    BazelTestGate bazel_test_gate = 5;
  }
}

// top-level message for METADATA
message Metadata {
  // deployments defined in this METADATA file
  repeated Deployment deployment = 1;

  // definition of a deployment gate
  repeated DeploymentGate deployment_gates = 2;
}
