"""Finds deployment targets in a workspace."""

import typing

import tools.deploy_runner.metadata_pb2 as metadata_pb2  # pylint: disable=no-name-in-module
from tools.deploy_runner.deploy_target import (
    AGENT_CLOUDS,
    CLOUDS,
    BazelDeployTarget,
    DeploymentInfo,
    DeployTarget,
    KubecfgTombstoneDeployTarget,
    TargetFilter,
)
from tools.deploy_runner.kubecfg_deploy_target import (
    KubecfgDeployParent,
    KubecfgDeployTarget,
)


def per_cloud_tasks(
    task: metadata_pb2.KubeCfgTask,
) -> typing.Sequence[metadata_pb2.KubeCfgTask]:
    """Create a task for a specific cloud or cloud alias."""
    # ALL is expanded to GCP DEV and PROD

    tasks = []
    if (
        task.cloud == metadata_pb2.KubeCfgTask.Cloud.ALL
        or task.cloud == metadata_pb2.KubeCfgTask.Cloud.ALL_GCP
    ):
        for cloud in CLOUDS:
            t = metadata_pb2.KubeCfgTask()
            t.MergeFrom(task)
            t.cloud = metadata_pb2.KubeCfgTask.Cloud.Value(cloud)
            tasks.append(t)
    elif task.cloud == metadata_pb2.KubeCfgTask.Cloud.ALL_GCP_AGENTS:
        for cloud in AGENT_CLOUDS:
            t = metadata_pb2.KubeCfgTask()
            t.MergeFrom(task)
            t.cloud = metadata_pb2.KubeCfgTask.Cloud.Value(cloud)
            tasks.append(t)
    elif task.cloud == metadata_pb2.KubeCfgTask.Cloud.ALL_LEADS:
        for cloud in [
            "GCP_US_CENTRAL1_DEV",
            "GCP_US_CENTRAL1_PROD",
        ]:
            t = metadata_pb2.KubeCfgTask()
            t.MergeFrom(task)
            t.cloud = metadata_pb2.KubeCfgTask.Cloud.Value(cloud)
            tasks.append(t)
    else:
        tasks.append(task)
    return tasks


def create_deploy_targets(
    deployment_info: DeploymentInfo,
    target: metadata_pb2.Deployment,
    task_filter: TargetFilter,
) -> typing.Sequence["DeployTarget"]:
    """Create a deployment target.

    Args:
        deployment_info: The deployment information, e.g. the workspace of the kubecfg file.
        target: The deployment target to deploy.
        task_filter: The filter to use for the deployment target.
    """
    if target.bazel.target:
        bazel_tasks = [BazelDeployTarget(deployment_info, target)]
        bazel_tasks = [
            t
            for t in bazel_tasks
            if task_filter.filter(
                t.target.bazel, "bazel", target.deployment_schedule_name
            )
        ]
        return bazel_tasks
    if target.kubecfg.target:
        new_target = metadata_pb2.Deployment()
        new_target.MergeFrom(target)
        tasks: list[metadata_pb2.KubeCfgTask] = []
        for task in new_target.kubecfg.task:
            tasks.extend(per_cloud_tasks(task))
        tasks = [
            t
            for t in tasks
            if task_filter.filter(t, "kubecfg", target.deployment_schedule_name)
        ]
        while new_target.kubecfg.task:
            new_target.kubecfg.task.pop()
        deploy_targets = []
        parent = KubecfgDeployParent(deployment_info, new_target)
        for task in tasks:
            deploy_targets.append(
                KubecfgDeployTarget(
                    parent=parent,
                    task=task,
                )
            )
        return deploy_targets
    if target.kubecfg_tombstone:
        new_target = metadata_pb2.Deployment()
        new_target.MergeFrom(target)
        tasks = []
        for task in new_target.kubecfg_tombstone.task:
            tasks.extend(per_cloud_tasks(task))
        tasks = [
            t
            for t in tasks
            if task_filter.filter(
                t, "kubecfg_tombstone", target.deployment_schedule_name
            )
        ]
        while new_target.kubecfg.task:
            new_target.kubecfg.task.pop()
        deploy_targets = []
        for task in tasks:
            deploy_targets.append(
                KubecfgTombstoneDeployTarget(
                    deployment_info,
                    new_target,
                    task=task,
                    kubectl=deployment_info.kubectl_factory(
                        metadata_pb2.KubeCfgTask.Cloud.Name(task.cloud)
                    ),
                )
            )
        return deploy_targets
    raise ValueError(f"Unknown deployment target {target}")
