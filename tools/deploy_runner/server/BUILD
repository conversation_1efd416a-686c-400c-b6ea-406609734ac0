load("@python_pip//:requirements.bzl", "requirement")
load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:metadata.bzl", "metadata_test")
load("//tools/bzl:python.bzl", "py_binary", "py_grpc_library", "py_library", "py_oci_image", "pytest_test")

proto_library(
    name = "deploy_proto",
    srcs = ["deploy.proto"],
    visibility = ["//tools/deploy_runner:__subpackages__"],
    deps = [
        "//tools/deploy_runner:deploy_events_proto",
        "//tools/deploy_runner:metadata_proto",
        "@protobuf//:timestamp_proto",
    ],
)

py_grpc_library(
    name = "deploy_py_proto",
    output_mode = "NO_PREFIX_FLAT",
    protos = [":deploy_proto"],
    visibility = ["//tools/deploy_runner:__subpackages__"],
    deps = [
        "//tools/deploy_runner:deploy_events_py_proto",
        "//tools/deploy_runner:metadata_py_proto",
    ],
)

proto_library(
    name = "deploy_store_proto",
    srcs = ["deploy_store.proto"],
    visibility = ["//tools:__subpackages__"],
    deps = [
        ":deploy_proto",
        "@protobuf//:timestamp_proto",
    ],
)

py_grpc_library(
    name = "deploy_store_py_proto",
    output_mode = "NO_PREFIX_FLAT",
    protos = [":deploy_store_proto"],
    deps = [
        "//tools/deploy_runner:deploy_events_py_proto",
    ],
)

py_library(
    name = "config",
    srcs = [
        "config.py",
    ],
    deps = [
        requirement("dataclasses_json"),
        "//services/lib/grpc/tls_config:grpc_tls_config_py",
    ],
)

py_library(
    name = "persistence",
    srcs = [
        "persistence.py",
    ],
    deps = [
        ":config",
        ":deploy_py_proto",
        ":deploy_store_py_proto",
        requirement("google-cloud-bigtable"),
    ],
)

py_binary(
    name = "rpc_server",
    srcs = [
        "rpc_server.py",
    ],
    data = [
        "//tools/deploy:auth_kube_config_yaml",
        "@gke-gcloud-auth-plugin//:all",
    ],
    deps = [
        ":config",
        ":deploy_py_proto",
        ":deploy_store_py_proto",
        ":persistence",
        "//base/cloud/k8s:kubectl_factory",
        "//base/cloud/k8s:kubernetes_client",
        "//base/logging:struct_logging",
        "//base/python/cloud:gcp",
        "//base/python/signal_handler",
        "//tools/bazel_runner/git:app_token",
        "//tools/bot:bot_client",
        "//tools/deploy_runner/control:deploy_pod_factory",
        requirement("dataclasses_json"),
        requirement("grpcio"),
        requirement("grpcio-reflection"),
        requirement("protobuf"),
        requirement("prometheus_client"),
        requirement("pygithub"),
    ],
)

pytest_test(
    name = "rpc_server_test",
    srcs = ["rpc_server_test.py"],
    deps = [
        ":rpc_server",
    ],
)

py_oci_image(
    name = "rpc_image",
    package_name = package_name(),
    binary = ":rpc_server",
    tars = ["//tools/docker:grpc_health_probe_tar"],
    trivy_allow_list = [
        "CVE-2024-34156",  # AU-4115
    ],  # see AU-2747
)

py_binary(
    name = "processor",
    srcs = [
        "processor.py",
    ],
    data = [
        "//tools/deploy:auth_kube_config_yaml",
        "@gke-gcloud-auth-plugin//:all",
    ],
    deps = [
        ":config",
        ":deploy_py_proto",
        ":deploy_store_py_proto",
        ":persistence",
        "//base/cloud/k8s:kubectl_factory",
        "//base/cloud/k8s:kubernetes_client",
        "//base/logging:struct_logging",
        "//base/python/cloud:gcp",
        "//tools/deploy_runner/control:deploy_pod_factory",
        requirement("protobuf"),
        requirement("prometheus_client"),
    ],
)

py_oci_image(
    name = "processor_image",
    package_name = package_name(),
    binary = ":processor",
    trivy_allow_list = [
        "CVE-2024-34156",  # AU-4115
    ],
)

py_binary(
    name = "pubsub_processor",
    srcs = [
        "pubsub_processor.py",
    ],
    deps = [
        ":config",
        ":deploy_py_proto",
        ":deploy_store_py_proto",
        ":persistence",
        "//base/logging:struct_logging",
        "//base/python/cloud:gcp",
        "//tools/deploy_runner:deploy_events_py_proto",
        requirement("protobuf"),
        requirement("google-cloud-pubsub"),
        requirement("prometheus_client"),
    ],
)

py_oci_image(
    name = "pubsub_processor_image",
    package_name = package_name(),
    binary = ":pubsub_processor",
)

py_binary(
    name = "slack_pubsub_processor",
    srcs = [
        "slack_pubsub_processor.py",
    ],
    deps = [
        ":config",
        ":deploy_py_proto",
        ":deploy_store_py_proto",
        "//base/logging:struct_logging",
        "//base/python/cloud:gcp",
        "//base/python/grpc:client_options",
        "//tools/bot:bot_client",
        "//tools/deploy_runner:deploy_events_py_proto",
        requirement("protobuf"),
        requirement("google-cloud-pubsub"),
        requirement("prometheus_client"),
    ],
)

py_oci_image(
    name = "slack_pubsub_processor_image",
    package_name = package_name(),
    binary = ":slack_pubsub_processor",
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        ":processor_image",
        ":pubsub_processor_image",
        ":rpc_image",
        ":slack_pubsub_processor_image",
        "//tools/deploy_runner/control:image",
    ],
    visibility = ["//services/deploy:__subpackages__"],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:config-map-lib",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/gcp:gcp-lib",
        "//tools/deploy:github_readonly_token_lib",
    ],
)

kubecfg(
    name = "kubecfg_shared",
    src = "deploy_shared.jsonnet",
    cluster_wide = True,
)

metadata_test(
    name = "metadata_test",
    src = "METADATA.jsonnet",
    deps = [
        ":kubecfg",
        ":kubecfg_shared",
    ],
)
