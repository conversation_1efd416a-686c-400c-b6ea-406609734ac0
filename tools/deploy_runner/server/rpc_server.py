"""GRPC server for the deploy runner service."""

import argparse
import logging
import pathlib
import re
import threading
import typing
import uuid
from concurrent.futures import ThreadPoolExecutor

import grpc
from grpc_reflection.v1alpha import reflection
from prometheus_client import start_http_server
import github

from base.cloud.k8s.kubectl_factory import create_kubectl_factory
from base.cloud.k8s.kubernetes_client import KubernetesClient, create_kubernetes_client
from base.logging.struct_logging import setup_struct_logging
from base.python.cloud import gcp
from base.python.signal_handler.signal_handler import GracefulSignalHandler
from tools.bazel_runner.git import app_token
from tools.bot import bot_pb2, bot_pb2_grpc
from tools.bot.bot_client import setup_client
from tools.deploy_runner.control import deploy_pod_factory
from tools.deploy_runner.server import deploy_pb2, deploy_pb2_grpc, deploy_store_pb2
from tools.deploy_runner.server.config import Config
from tools.deploy_runner.server.persistence import Persistence


def validate_k8s_namespace(namespace: str) -> bool:
    """Validate that the namespace is valid."""
    if len(namespace) > 63:
        return False
    if len(namespace) < 2:
        return False
    return bool(re.match("^[a-z0-9]([-a-z0-9]*[a-z0-9])?$", namespace))


def validate_target_name(target_name: str) -> bool:
    """Validate that the deployment target name is valid."""
    if len(target_name) > 63:
        return False
    if len(target_name) < 2:
        return False
    return bool(re.match("^[a-zA-Z0-9_-]+$", target_name))


class ScheduledDeploymentControl:
    """Controls the scheduled deployment."""

    def __init__(self, kubernetes_client: KubernetesClient, config: Config):
        self.kubernetes_client = kubernetes_client
        self.config = config

    def get_cron_job_name(
        self, deployment_track: deploy_pb2.DeploymentTrack.ValueType
    ) -> str:
        """Returns the name of the cron job for the given deployment track."""
        if deployment_track == deploy_pb2.DeploymentTrack.DEFAULT:
            return self.config.deploy_cron_job_name
        track_name = deploy_pb2.DeploymentTrack.Name(deployment_track).lower()
        track_name = track_name.replace("_", "-")
        return f"{self.config.deploy_cron_job_name}-{track_name}"

    def enable(self, deployment_track: deploy_pb2.DeploymentTrack.ValueType):
        """Enables the scheduled deployment."""
        self.kubernetes_client.login(self.config.cloud)
        j = self.kubernetes_client.get_batch_api(
            self.config.cloud
        ).read_namespaced_cron_job(
            name=self.get_cron_job_name(deployment_track),
            namespace=self.config.namespace,
        )
        j.spec.suspend = False  # type: ignore
        logging.info("Enable cronjob: %s", j.spec)  # type: ignore
        self.kubernetes_client.get_batch_api(
            self.config.cloud
        ).replace_namespaced_cron_job(
            name=self.get_cron_job_name(deployment_track),
            namespace=self.config.namespace,
            body=j,
        )

    def disable(self, deployment_track: deploy_pb2.DeploymentTrack.ValueType):
        """Disables the scheduled deployment."""
        self.kubernetes_client.login(self.config.cloud)
        j = self.kubernetes_client.get_batch_api(
            self.config.cloud
        ).read_namespaced_cron_job(
            name=self.get_cron_job_name(deployment_track),
            namespace=self.config.namespace,
        )
        j.spec.suspend = True  # type: ignore
        logging.info("Disable cronjob: %s", j.spec)  # type: ignore
        self.kubernetes_client.get_batch_api(
            self.config.cloud
        ).replace_namespaced_cron_job(
            name=self.get_cron_job_name(deployment_track),
            namespace=self.config.namespace,
            body=j,
        )

    def is_enabled(
        self, deployment_track: deploy_pb2.DeploymentTrack.ValueType
    ) -> bool:
        """Returns the current scheduled deployment config."""
        cron_job_name = self.get_cron_job_name(deployment_track)
        self.kubernetes_client.login(self.config.cloud)
        v1 = self.kubernetes_client.get_batch_api(self.config.cloud)
        j = v1.read_namespaced_cron_job(
            name=cron_job_name, namespace=self.config.namespace
        )
        return not j.spec.suspend  # type: ignore


def is_commit_on_branch(
    repo: github.Repository,  # type: ignore
    commit_sha: str,
    branch_name: str,
) -> bool:
    """
    Checks if a given commit SHA exists on a specific branch using PyGithub.
    """
    branch = repo.get_branch(branch_name)
    branch_sha = branch.commit.sha
    logging.info(
        "is_commit_on_branch: commit=%s branch=%s branch_sha=%s",
        commit_sha,
        branch_name,
        branch_sha,
    )
    status = compare_commits(repo, branch_sha, commit_sha)
    return status in ["identical", "behind"]


def compare_commits(repo: github.Repository, base_commit: str, head_commit: str) -> str:  # type: ignore
    """
    Compares two commits and returns the comparison status.

    Args:
        repo: GitHub repository
        base_commit: Base commit SHA
        head_commit: Head commit SHA to compare against base

    Returns:
        Comparison status: "ahead", "behind", "identical", or "diverged"
    """
    # If commits are the same, return identical
    if base_commit == head_commit:
        return "identical"

    comparison = repo.compare(base_commit, head_commit)
    logging.info(
        "compare_commits: base=%s head=%s status=%s",
        base_commit,
        head_commit,
        comparison.status,
    )
    assert comparison.status in ["ahead", "behind", "identical", "diverged"]
    # GitHub comparison status meanings:
    # - "ahead": head is ahead of base
    # - "behind": head is behind base
    # - "identical": commits are the same
    # - "diverged": commits have diverged
    return comparison.status


class DeployerServices(deploy_pb2_grpc.DeployServicer):
    """DeployerServices RPC server."""

    def __init__(
        self,
        config: Config,
        control_client: deploy_pod_factory.DeployRunnerControlClient,
        scheduled_deployment_control: ScheduledDeploymentControl,
        persistence: Persistence,
        slack_bot_client: bot_pb2_grpc.DevtoolsBotStub | None,
        github_client_fn: typing.Callable[[], github.Github],
    ):
        self.config = config
        self.control_client = control_client
        self.scheduled_deployment_control = scheduled_deployment_control
        self.persistence = persistence
        self.slack_bot_client = slack_bot_client
        self.github_client_fn = github_client_fn

    def ScheduleDeployment(
        self, request: deploy_pb2.ScheduleDeploymentRequest, context
    ):
        try:
            logging.info(request)

            if not request.branch and not request.pull_request_number:
                context.abort(
                    grpc.StatusCode.INVALID_ARGUMENT,
                    "Missing branch or pull_request_number",
                )
            if request.branch and request.branch not in self.config.allowed_branches:
                context.abort(grpc.StatusCode.INVALID_ARGUMENT, "Invalid branch")
            for namespace in request.namespaces:
                if not validate_k8s_namespace(namespace):
                    logging.warning("Invalid namespace %s", namespace)
                    context.abort(grpc.StatusCode.INVALID_ARGUMENT, "Invalid namespace")
            for target_name in request.target_names:
                if not validate_target_name(target_name):
                    logging.warning("Invalid target_name %s", target_name)
                    context.abort(
                        grpc.StatusCode.INVALID_ARGUMENT, "Invalid target_name"
                    )
            if request.HasField("adhoc"):
                if not request.adhoc.requestor:
                    context.abort(
                        grpc.StatusCode.INVALID_ARGUMENT, "Missing adhoc.requestor"
                    )
                if not request.adhoc.reason:
                    context.abort(
                        grpc.StatusCode.INVALID_ARGUMENT, "Missing adhoc.reason"
                    )
                # check that commit_ref is a valid git ref (at least it's a hex string), if it's not empty
                if request.commit_ref and not re.match(
                    r"^[a-fA-F0-9]+$", request.commit_ref
                ):
                    logging.warning("Invalid commit_ref %s", request.commit_ref)
                    context.abort(
                        grpc.StatusCode.INVALID_ARGUMENT, "Invalid commit_ref"
                    )

            deployment_schedule_name = None
            if request.HasField("deployment_schedule_name"):
                deployment_schedule_name = request.deployment_schedule_name
            else:
                if len(request.target_names) == 0:
                    context.abort(
                        grpc.StatusCode.INVALID_ARGUMENT,
                        "Either deployment_schedule_name or target_names needs to be set",
                    )

            deployment_info = self.control_client.schedule(
                branch=request.branch,
                pull_request_number=request.pull_request_number,
                ref=request.commit_ref,
                clouds=[deploy_pb2.DeployCloud.Name(c) for c in request.clouds],
                envs=[deploy_pb2.Env.Name(e) for e in request.envs],
                namespaces=request.namespaces,
                target_names=request.target_names,
                pause=request.pause,
                allow_rollback=request.allow_rollback,
                deployment_track=request.deployment_track,
                deployment_schedule_name=deployment_schedule_name,
            )
            logging.info("Created deployment %s", deployment_info)

            job_info = deploy_store_pb2.JobInfo(
                job_name=deployment_info.job_name, namespace=deployment_info.namespace
            )
            self.persistence.create(
                deployment_info.deploy_id, request, job_info=job_info
            )

            if request.HasField("adhoc"):
                if self.slack_bot_client:
                    self.slack_bot_client.NotifyAdhocDeployment(
                        bot_pb2.NotifyAdhocDeploymentRequest(
                            branch=request.branch,
                            commit_ref=request.commit_ref,
                            clouds=[
                                deploy_pb2.DeployCloud.Name(c) for c in request.clouds
                            ],
                            target_names=request.target_names,
                            requestor=request.adhoc.requestor,
                            reason=request.adhoc.reason,
                            namespaces=request.namespaces,
                            deploy_id=str(deployment_info.deploy_id),
                            deploy_url=f"{self.config.deploy_viewer_url}/deployment/{deployment_info.deploy_id}",
                        )
                    )

            return deploy_pb2.ScheduleDeploymentResponse(
                deploy_id=str(deployment_info.deploy_id)
            )
        except Exception as ex:  # pylint: disable=broad-exception-caught
            logging.error("ScheduleDeployment failed: %s", ex)
            logging.exception(ex)
            raise

    def GetDeployment(self, request: deploy_pb2.GetDeploymentRequest, context):
        try:
            logging.info(request)

            if not request.deploy_id:
                context.abort(grpc.StatusCode.INVALID_ARGUMENT, "Missing deploy_id")

            try:
                deploy_id = uuid.UUID(request.deploy_id)
            except ValueError:
                context.abort(grpc.StatusCode.INVALID_ARGUMENT, "Invalid deploy_id")
                return

            deployment = self.persistence.get(deploy_id)
            if not deployment:
                context.abort(grpc.StatusCode.NOT_FOUND, "Deployment not found")
                return

            return deploy_pb2.GetDeploymentResponse(
                deployment=deploy_pb2.DeploymentInfo(
                    deploy_id=str(deployment.deploy_id),
                    request=deployment.request,
                    state=deployment.state,
                    created_time=deployment.create_time,
                    updated_time=deployment.update_time,
                    finished_time=deployment.finished_time,
                )
            )
        except Exception as ex:  # pylint: disable=broad-exception-caught
            logging.error("GetDeployment failed: %s", ex)
            logging.exception(ex)
            raise

    def GetDeploymentEvents(
        self, request: deploy_pb2.GetDeploymentEventsRequest, context
    ):
        try:
            logging.info(request)
            if not request.deploy_id:
                context.abort(grpc.StatusCode.INVALID_ARGUMENT, "Missing deploy_id")
                return

            try:
                deploy_id = uuid.UUID(request.deploy_id)
            except ValueError:
                context.abort(grpc.StatusCode.INVALID_ARGUMENT, "Invalid deploy_id")
                return
            deployment = self.persistence.get(deploy_id)
            if not deployment:
                context.abort(grpc.StatusCode.NOT_FOUND, "Deployment not found")
                return

            for event in self.persistence.get_events(
                deploy_id, request.min_sequence_number, limit=request.limit
            ):
                yield event
        except Exception as ex:  # pylint: disable=broad-exception-caught
            logging.error("GetDeploymentEvents failed: %s", ex)
            logging.exception(ex)
            raise

    def GetDeployments(self, request: deploy_pb2.GetDeploymentsRequest, context):
        try:
            logging.info("GetDeployments: %s", request)
            oldest_deploy_id = None
            if request.oldest_deploy_id:
                try:
                    oldest_deploy_id = uuid.UUID(request.oldest_deploy_id)
                except ValueError:
                    context.abort(
                        grpc.StatusCode.INVALID_ARGUMENT, "Invalid oldest_deploy_id"
                    )
                    return
            newest_deploy_id = None
            if request.newest_deploy_id:
                try:
                    newest_deploy_id = uuid.UUID(request.newest_deploy_id)
                except ValueError:
                    context.abort(
                        grpc.StatusCode.INVALID_ARGUMENT, "Invalid newest_deploy_id"
                    )
                    return
            max_count = request.max_count if request.max_count else 20
            if max_count > 100:
                context.abort(
                    grpc.StatusCode.INVALID_ARGUMENT, "max_count must be <= 100"
                )
                return
            if max_count < 1:
                context.abort(
                    grpc.StatusCode.INVALID_ARGUMENT, "max_count must be >= 1"
                )
                return
            response = deploy_pb2.GetDeploymentsResponse()
            for deployment in self.persistence.get_deployments(
                oldest_deploy_id=oldest_deploy_id,
                newest_deploy_id=newest_deploy_id,
                max_count=max_count,
                oldest_inclusive=request.oldest_inclusive,
                newest_inclusive=request.newest_inclusive,
            ):
                response.deployments.append(
                    deploy_pb2.DeploymentInfo(
                        deploy_id=str(deployment.deploy_id),
                        request=deployment.request,
                        state=deployment.state,
                        created_time=deployment.create_time,
                        updated_time=deployment.update_time,
                        finished_time=deployment.finished_time,
                    )
                )
            logging.info(response)
            yield response
        except Exception as ex:  # pylint: disable=broad-exception-caught
            logging.error("GetDeployments failed: %s", ex)
            logging.exception(ex)
            raise

    def CancelDeployment(self, request: deploy_pb2.CancelDeploymentRequest, context):
        try:
            logging.info("CancelDeployment: %s", request)
            if not request.deploy_id:
                context.abort(grpc.StatusCode.INVALID_ARGUMENT, "Missing deploy_id")

            try:
                deploy_id = uuid.UUID(request.deploy_id)
            except ValueError:
                context.abort(grpc.StatusCode.INVALID_ARGUMENT, "Invalid deploy_id")
                return

            deployment = self.persistence.get(deploy_id)
            if not deployment:
                context.abort(grpc.StatusCode.NOT_FOUND, "Deployment not found")
                return

            if (
                deployment.state
                == deploy_pb2.DeploymentState.DEPLOYMENT_STATE_SUCCEEDED
            ):
                context.abort(
                    grpc.StatusCode.FAILED_PRECONDITION, "Deployment already succeeded"
                )
            elif deployment.state == deploy_pb2.DeploymentState.DEPLOYMENT_STATE_FAILED:
                context.abort(
                    grpc.StatusCode.FAILED_PRECONDITION, "Deployment already failed"
                )

            self.persistence.cancel(deploy_id)
            self.control_client.cancel(deploy_id)
            return deploy_pb2.CancelDeploymentResponse()
        except Exception as ex:  # pylint: disable=broad-exception-caught
            logging.error("CancelDeployment failed: %s", ex)
            logging.exception(ex)
            raise

    def ConfigureScheduledDeployment(
        self, request: deploy_pb2.ConfigureScheduledDeploymentRequest, context
    ):
        try:
            logging.info("ConfigureScheduledDeployment: %s", request)
            if request.enabled:
                self.scheduled_deployment_control.enable(request.deployment_track)
            else:
                self.scheduled_deployment_control.disable(request.deployment_track)
            return deploy_pb2.ConfigureScheduledDeploymentResponse()
        except Exception as ex:  # pylint: disable=broad-exception-caught
            logging.error("ConfigureScheduledDeployment failed: %s", ex)
            logging.exception(ex)
            raise

    def GetScheduledDeploymentConfig(
        self, request: deploy_pb2.GetScheduledDeploymentConfigRequest, context
    ):
        try:
            logging.info("GetScheduledDeploymentConfig: %s", request)
            return deploy_pb2.GetScheduledDeploymentConfigResponse(
                enabled=self.scheduled_deployment_control.is_enabled(
                    request.deployment_track
                )
            )
        except Exception as ex:  # pylint: disable=broad-exception-caught
            logging.error("GetScheduledDeploymentConfig failed: %s", ex)
            logging.exception(ex)
            raise

    def GetCommitState(self, request: deploy_pb2.GetCommitStateRequest, context):
        try:
            github_client: github.Github = self.github_client_fn()
            repo = github_client.get_repo("augmentcode/augment")
            logging.info("GetCommitState: %s", request)
            response = deploy_pb2.GetCommitStateResponse()
            commit_ref = ""
            if request.HasField("commit_ref"):
                try:
                    commit = repo.get_commit(request.commit_ref)
                    logging.info("commit: %s", commit)
                except github.GithubException as e:
                    if e.status == 422:
                        context.abort(grpc.StatusCode.NOT_FOUND, "Commit not found")
                        return response
                    raise
                commit_ref = request.commit_ref
            elif request.HasField("pull_request_number"):
                pull = repo.get_pull(int(request.pull_request_number))
                if not pull.merged:
                    return response
                # get merge commit
                merge_commit = repo.get_commit(pull.merge_commit_sha)
                commit_ref = merge_commit.sha
            else:
                context.abort(
                    grpc.StatusCode.INVALID_ARGUMENT,
                    "Either commit_ref or pull_request_number needs to be set",
                )
                return response

            logging.info("commit_ref: %s", commit_ref)
            is_on_main = is_commit_on_branch(repo, commit_ref, "main")
            is_on_blkg = is_commit_on_branch(repo, commit_ref, "bazel-last-known-good")

            response.is_merged = is_on_main
            response.is_known_good = is_on_blkg

            if is_on_main:
                # Get last successful deployments for each schedule and compare commits
                last_successful_deployments = (
                    self.persistence.get_last_successful_deployments_by_schedule()
                )

                for schedule_name, deployment in sorted(
                    last_successful_deployments.items(), key=lambda x: x[0]
                ):
                    schedule_state = (
                        deploy_pb2.GetCommitStateResponse.ScheduleDeploymentState()
                    )
                    schedule_state.deploy_id = deployment.deploy_id
                    schedule_state.deployment_schedule_name = schedule_name
                    schedule_state.deployment_time.CopyFrom(deployment.create_time)

                    deployment_commit_ref = deployment.request.commit_ref
                    if not deployment_commit_ref:
                        continue

                    # Set deployment commit
                    schedule_state.deployment_commit = deployment_commit_ref

                    # Compare the request commit with the deployment commit
                    comparison_status = compare_commits(
                        repo, deployment_commit_ref, commit_ref
                    )
                    schedule_state.contains_ref = comparison_status in [
                        "identical",
                        "behind",
                    ]
                    response.schedule_deployment_states.append(schedule_state)

            return response
        except Exception as ex:  # pylint: disable=broad-exception-caught
            logging.error("GetCommitState failed: %s", ex)
            logging.exception(ex)
            raise


def _serve(args: argparse.Namespace, config: Config, shutdown_event: threading.Event):
    kubernetes_client = create_kubernetes_client(args.kube_config_file)
    kubectl = create_kubectl_factory(args.kube_config_file)(config.cloud)

    runner_image = pathlib.Path(config.image_tag_file).read_text(encoding="utf-8")
    logging.info("Runner image %s", runner_image)
    control_client = deploy_pod_factory.DeployRunnerControlClient(
        kubernetes_client=kubernetes_client,
        kubectl=kubectl,
        namespace=config.namespace,
        runner_image=runner_image,
        cloud=config.cloud,
        env=config.env,
        topic_name=config.topic_name,
    )
    scheduled_deployment_control = ScheduledDeploymentControl(
        kubernetes_client=kubernetes_client, config=config
    )

    persistence = Persistence.setup(config)

    if config.slack_bot_endpoint:
        bot_stub = setup_client(config.slack_bot_endpoint)
    else:
        bot_stub = None

    # Setup GitHub client function
    token_gen = app_token.GitHubAppTokenSource.from_directory(
        pathlib.Path(config.github_app_path)
    )

    def github_client_fn():
        logging.info("Creating github client")
        return github.Github(token_gen.get_token())

    # to test the github client
    github_client_fn()

    server = grpc.server(ThreadPoolExecutor(max_workers=4))
    deploy_pb2_grpc.add_DeployServicer_to_server(
        DeployerServices(
            config=config,
            control_client=control_client,
            scheduled_deployment_control=scheduled_deployment_control,
            persistence=persistence,
            slack_bot_client=bot_stub,
            github_client_fn=github_client_fn,
        ),
        server,
    )
    SERVICE_NAMES = (
        deploy_pb2.DESCRIPTOR.services_by_name["Deploy"].full_name,
        reflection.SERVICE_NAME,
    )
    reflection.enable_server_reflection(SERVICE_NAMES, server)

    if config.server_mtls:
        server_credentials = grpc.ssl_server_credentials(
            [
                (
                    pathlib.Path(config.server_key_path).read_bytes(),
                    pathlib.Path(config.server_cert_path).read_bytes(),
                )
            ],
            root_certificates=pathlib.Path(config.server_ca_path).read_bytes(),
            require_client_auth=True,
        )
        server.add_secure_port(f"[::]:{config.port}", server_credentials)
        actual_port = server.add_secure_port(f"[::]:{config.port}", server_credentials)
    else:
        actual_port = server.add_insecure_port(f"[::]:{config.port}")
    logging.info("grpc listening on port %s", actual_port)

    server.start()
    logging.info("Listening on %s", actual_port)
    shutdown_event.wait()
    logging.info("Shutting down server")
    server.stop(grace=config.shutdown_grace_period_s).wait()
    logging.info("Server shutdown complete")


def _load_config(config_file: pathlib.Path) -> Config:
    return Config.schema().loads(  # pylint: disable=no-member # type: ignore
        config_file.read_text()
    )


def main():
    handler = GracefulSignalHandler()

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--config-file", default=pathlib.Path("/config/config.json"), type=pathlib.Path
    )
    parser.add_argument(
        "--kube-config-file",
        default=pathlib.Path("tools/deploy/auth_kube_config.yaml"),
        type=pathlib.Path,
    )
    args = parser.parse_args()
    setup_struct_logging()

    logging.info("Args %s", args)

    config = _load_config(args.config_file)
    logging.info("Config %s", config)
    logging.info("Service Account %s", gcp.get_active_gcp_service_account())

    # begin listening for Prometheus requests
    start_http_server(9090)

    _serve(args, config, handler.get_shutdown_event())


if __name__ == "__main__":
    main()
