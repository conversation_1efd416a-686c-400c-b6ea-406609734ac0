syntax = "proto3";

import "google/protobuf/timestamp.proto";
import "tools/deploy_runner/deploy_events.proto";
import "tools/deploy_runner/metadata.proto";

// RPC service for scheduling and retrieving deployment information
service Deploy {
  // schedules a new deployment
  rpc ScheduleDeployment(ScheduleDeploymentRequest) returns (ScheduleDeploymentResponse) {}

  // returns information about a deployment
  rpc GetDeployment(GetDeploymentRequest) returns (GetDeploymentResponse) {}

  rpc GetDeploymentEvents(GetDeploymentEventsRequest) returns (stream DeployEvent) {}

  rpc GetDeployments(GetDeploymentsRequest) returns (stream GetDeploymentsResponse) {}

  // cancels a pending or running deployment
  rpc CancelDeployment(CancelDeploymentRequest) returns (CancelDeploymentResponse) {}

  // configure the scheduled deployment
  //
  // Scheduled deployment here refers to regularly scheduled deployments (via a cronjob)
  rpc ConfigureScheduledDeployment(ConfigureScheduledDeploymentRequest) returns (ConfigureScheduledDeploymentResponse) {}

  // returns the current scheduled deployment config
  rpc GetScheduledDeploymentConfig(GetScheduledDeploymentConfigRequest) returns (GetScheduledDeploymentConfigResponse) {}

  // returns the deployment state of a given commit or pull request
  rpc GetCommitState(GetCommitStateRequest) returns (GetCommitStateResponse) {}
}

enum DeployCloud {
  GCP_US_CENTRAL1_DEV = 0;
  GCP_US_CENTRAL1_PROD = 1;
  GCP_US_CENTRAL1_GSC_PROD = 3;
  GCP_EU_WEST4_PROD = 2;
}

enum Env {
  STAGING = 0;
  PROD = 1;
}

enum DeploymentTrack {
  // default deployment track, e.g. staging and prod, usually twice per
  // working days.
  DEFAULT = 0;

  // bypassing deployment used for adhoc-deployments (YOLO deployments) by augment engineers.
  BYPASS = 1;

  // deployment track used for experimental deployments
  //
  // see the EXPERIMENTAL deployment schedule in metadata.proto
  EXPERIMENTAL = 2;

  // deployment track used for deploying infer and embedder services
  //
  // see the INFER_AND_EMBEDDER deployment schedule in metadata.proto
  INFER_AND_EMBEDDER = 3;

  // tombstone track, used for deleting resources
  //
  // see the TOMBSTONE deployment schedule in metadata.proto
  TOMBSTONE = 4;

  // CICD track, used for CICD services that need to be deployed with the same
  // frequency as the default schedule but need to be tracked separately
  //
  // see the CICD deployment schedule in metadata.proto
  CICD = 5;
}

// adhoc deployment request, e.g. deployment for incidents
message AdhocDeployment {
  // the name of the user who requested the deployment as augmentcode username
  string requestor = 1;

  // the reason for the deployment
  string reason = 2;
}

message ScheduledDeployment {
  // name of a pod that scheduled the deployment
  string parent = 1;
}

// request for scheduling a new deployment
message ScheduleDeploymentRequest {
  reserved 11;

  oneof source {
    // the branch to deploy
    // usually this has to be from a list of allowed branches, e.g. main or bazel-last-known-good
    //
    // this is mutually exclusive with pr
    string branch = 1;

    // the pull request to deploy.
    //
    // this is mutually exclusive with branch
    // the pr requires approval
    string pull_request_number = 14;
  }

  // the commit reference to deploy.
  // if empty, the latest commit on the branch will be used
  // if not empty, it needs to be a valid git reference
  string commit_ref = 2;

  // filters for the deployment
  // if not set, the deployment will be applied to all clouds
  repeated DeployCloud clouds = 3;

  // filters for the deployment
  // if not set, the deployment will be applied to all environments
  repeated Env envs = 4;

  // filters for the deployment
  // if not set, the deployment will be applied to all namespaces
  repeated string namespaces = 5;

  // the name of the targets to deploy.
  // needs to be valid deployment target names from METADATA files
  //
  // if not set, all targets for the given deployment schedule will be deployed
  repeated string target_names = 6;

  oneof deployment_type {
    AdhocDeployment adhoc = 7;
    ScheduledDeployment scheduled = 8;
  }

  // pause after STAGING targets before moving to PROD env targets
  bool pause = 9;

  // if set to true, allow rollbacks
  // rollbacks here refer to deploying a git version that is older than the current one
  bool allow_rollback = 10;

  // deployment track
  DeploymentTrack deployment_track = 12;

  // deployment schedule name
  // if set, the targets will be filtered by the deployment schedule name
  optional metadata.Deployment.DeploymentScheduleName deployment_schedule_name = 13;
}

// the state of a deployment
enum DeploymentState {
  DEPLOYMENT_STATE_NOT_SET = 0;

  // the deployment has been scheduled, but not running
  DEPLOYMENT_STATE_SCHEDULED = 1;

  // the deployment is currently running
  DEPLOYMENT_STATE_RUNNING = 2;

  // the deployment has succeeded
  DEPLOYMENT_STATE_SUCCEEDED = 3;

  // the deployment has failed
  DEPLOYMENT_STATE_FAILED = 4;

  // the deployment has been cancelled
  DEPLOYMENT_STATE_CANCELLED = 5;
}

message ScheduleDeploymentResponse {
  // unique id of the deployment
  string deploy_id = 1;
}

message GetDeploymentRequest {
  // unique id of the deployment
  string deploy_id = 1;
}

message DeploymentInfo {
  // unique id of the deployment
  string deploy_id = 1;

  // the request that was used to schedule the deployment
  ScheduleDeploymentRequest request = 2;

  // the current state
  DeploymentState state = 3;

  // time the deployment was created
  google.protobuf.Timestamp created_time = 4;

  // time the deployment was updated
  google.protobuf.Timestamp updated_time = 5;

  // time the deployment was finished
  google.protobuf.Timestamp finished_time = 6;
}

message GetDeploymentResponse {
  DeploymentInfo deployment = 1;
}

message GetDeploymentEventsRequest {
  // unique id of the deployment
  string deploy_id = 1;

  // the minimum sequence number any reported response
  // should have.
  // this is used to not sent duplicate information again
  uint64 min_sequence_number = 2;

  // the maximum number of events to return
  // if not set all events will be returned
  uint32 limit = 3;
}

message GetDeploymentsRequest {
  // the oldest deployment id to return
  string oldest_deploy_id = 1;

  // the newest deployment id to return
  string newest_deploy_id = 2;

  // the maximum number of deployments to return
  // if not set a default value will be used
  uint32 max_count = 3;

  bool oldest_inclusive = 4;
  bool newest_inclusive = 5;
}

message GetDeploymentsResponse {
  // the deployments will be sorted in reverse order of creation with the newest first
  repeated DeploymentInfo deployments = 1;
}

message CancelDeploymentRequest {
  // unique id of the deployment
  string deploy_id = 1;
}

message CancelDeploymentResponse {}

message ConfigureScheduledDeploymentRequest {
  DeploymentTrack deployment_track = 2;

  // if true, enable the scheduled deployment, otherwise disable it
  bool enabled = 1;
}

message ConfigureScheduledDeploymentResponse {}

message GetScheduledDeploymentConfigRequest {
  DeploymentTrack deployment_track = 1;
}

message GetScheduledDeploymentConfigResponse {
  // if true, the scheduled deployment is enabled, otherwise disabled
  bool enabled = 1;
}

message GetCommitStateRequest {
  oneof source {
    string commit_ref = 1;
    string pull_request_number = 2;
  }
}

message GetCommitStateResponse {
  // the commit is merged (aka on the main branch)
  bool is_merged = 1;
  // the commit is known to be good (aka on the bazel-last-known-good branch)
  bool is_known_good = 2;

  // information about the most recent succeeded deployment.
  message ScheduleDeploymentState {
    // name of the deployment schedule.
    metadata.Deployment.DeploymentScheduleName deployment_schedule_name = 1;

    // deployment id
    string deploy_id = 2;

    // true if and only if the deployment contains the commit ref or pull request number
    bool contains_ref = 3;

    // time when the deployment was created
    google.protobuf.Timestamp deployment_time = 4;

    // commit reference that was deployed
    string deployment_commit = 5;
  }

  // contains information about the most recently succeeded deployment for each deployment schedule
  // effectively this is a map from the deployment schedule name to the most recent succeeded deployment
  repeated ScheduleDeploymentState schedule_deployment_states = 6;
}
