// K8S deployment file for the deploy RPC server
local certLib = import 'deploy/common/cert-lib.jsonnet';
local gcpInfo = import 'deploy/common/cloud_info.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local githubReadonlyAppSealedLib = import 'tools/deploy/github_readonly_app_sealed.jsonnet';
function(env, namespace, cloud, namespace_config)
  local cloudInfo = gcpInfo[cloud];
  local appName = 'deploy-rpc';
  local githubSecret = githubReadonlyAppSealedLib(cloud=cloud, namespace=namespace, appName=appName);
  local service = {
    apiVersion: 'v1',
    kind: 'Service',
    metadata: {
      name: 'deploy-rpc-svc',
      namespace: namespace,
      labels: {
        app: appName,
        'eng.augmentcode.com/service-type': 'grpc',
      },
    },
    spec: {
      selector: {
        app: appName,
      },
      ports: [
        {
          protocol: 'TCP',
          port: 50051,
          targetPort: 'grpc-svc',
        },
      ],
    },
  };
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);
  local serviceAccount = gcpLib.createServiceAccount(app=appName, cloud=cloud, env=env, namespace=namespace, iam=true);
  local serverCert = certLib.createServerCert(name='deploy-rpc-server-cert',
                                              namespace=namespace,
                                              appName=appName,
                                              dnsNames=grpcLib.grpcServiceNames(appName, namespace=namespace),
                                              volumeName='certs');

  local clientCert =
    certLib.createClientCert(
      name='deploy-rpc-client-cert',
      namespace=namespace,
      appName=appName,
      volumeName='client-certs',
      dnsNames=grpcLib.grpcServiceNames(appName, namespace=namespace),
    );

  local bigtable = gcpLib.createBigtableTable(
    cloud=cloud,
    env=env,
    app=appName,
    namespace=namespace,
    tableName='deploy-runner-runner-main-table',
    columnFamily=[
      {
        family: 'Main',
      },
      {
        family: 'Timestamp',
      },
      {
        family: 'Event',
      },
    ],
    iamServiceAccountName=serviceAccount.iamServiceAccountName
  );
  local runNamespace = 'devtools';

  local pubsubTopic = {
    apiVersion: 'pubsub.cnrm.cloud.google.com/v1beta1',
    kind: 'PubSubTopic',
    metadata: {
      name: '%s-deploy-topic' % namespace,
      namespace: namespace,
      // Don't delete in dev because this causes a lot of errors at the end of a test run, if the
      // topic is deleted before the pod.
      annotations: if env == 'DEV' then {
        'cnrm.cloud.google.com/deletion-policy': 'abandon',
      } else {},
      labels: {
        app: appName,
      },
    },
    spec: {
    },
  };

  local pubsubSubscription = {
    apiVersion: 'pubsub.cnrm.cloud.google.com/v1beta1',
    kind: 'PubSubSubscription',
    metadata: {
      name: '%s-deploy-sub' % namespace,
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    spec: {
      topicRef: {
        name: pubsubTopic.metadata.name,
      },
      enableMessageOrdering: true,
      ackDeadlineSeconds: 10,
      retryPolicy: {
        minimumBackoff: '5s',
        maximumBackoff: '300s',
      },
    },
  };

  local slackPubsubSubscription = {
    apiVersion: 'pubsub.cnrm.cloud.google.com/v1beta1',
    kind: 'PubSubSubscription',
    metadata: {
      name: '%s-deploy-slack-sub' % namespace,
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    spec: {
      topicRef: {
        name: pubsubTopic.metadata.name,
      },
      enableMessageOrdering: true,
      ackDeadlineSeconds: 10,
      retryPolicy: {
        minimumBackoff: '5s',
        maximumBackoff: '300s',
      },
    },
  };
  local github_app_path = '/github-app';
  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
  local domainSuffix = cloudInfo.internalDomainSuffix;
  local deployViewerHostname = if namespace == 'devtools' then 'deploy-viewer.%s' % domainSuffix else 'deploy-viewer.%s.%s' % [namespace, domainSuffix];
  local config = {
    allowed_branches: ['bazel-last-known-good', 'main'],
    namespace: runNamespace,
    image_tag_file: '/config/image_tag.txt',
    cloud: cloud,
    env: env,
    project_id: bigtable.projectId,
    instance_name: bigtable.instanceName,
    table_name: bigtable.tableName,
    server_mtls: mtls,
    server_ca_path: '/certs/ca.crt',
    server_key_path: '/certs/tls.key',
    server_cert_path: '/certs/tls.crt',
    client_mtls: if mtls then clientCert.config else null,
    port: 50051,
    slack_bot_endpoint: 'slack-bot-svc:80',
    deploy_viewer_url: deployViewerHostname,
    topic_name: pubsubTopic.metadata.name,
    subscription_batch_size: 20,
    deploy_cron_job_name: 'deploy',
    github_app_path: github_app_path,

  };
  local extraConfig = {
    'image_tag.txt': {
      kubecfg: {
        image_push: '//tools/deploy_runner/control:image',
        image_name: 'devtools_control',
      },
    },
  };
  local configMap = configMapLib.createConfigMap(appName=appName, namespace=namespace, config=config, extraConfigs=extraConfig);

  local publishIam = {
    apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
    kind: 'IAMPartialPolicy',
    metadata: {
      name: '%s-deploy-publish' % namespace,
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    spec: {
      resourceRef: {
        kind: 'PubSubTopic',
        name: pubsubTopic.metadata.name,
      },
      bindings: [
        {
          role: 'roles/pubsub.publisher',
          members: [{
            member: 'serviceAccount:%s-deploy-iam@%s.iam.gserviceaccount.com' % [runNamespace, cloudInfo.projectId],
          }],
        },
      ],
    },
  };

  local subscribeIam = [
    {
      apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
      kind: 'IAMPartialPolicy',
      metadata: {
        name: '%s-deploy-sub' % namespace,
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        resourceRef: {
          kind: 'PubSubSubscription',
          name: pubsubSubscription.metadata.name,
        },
        bindings: [
          {
            role: 'roles/pubsub.subscriber',
            members: [{
              memberFrom: {
                serviceAccountRef: {
                  name: serviceAccount.iamServiceAccountName,
                },
              },
            }],
          },
        ],
      },
    },
    {
      apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
      kind: 'IAMPartialPolicy',
      metadata: {
        name: '%s-deploy-slack-sub' % namespace,
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        resourceRef: {
          kind: 'PubSubSubscription',
          name: slackPubsubSubscription.metadata.name,
        },
        bindings: [
          {
            role: 'roles/pubsub.subscriber',
            members: [{
              memberFrom: {
                serviceAccountRef: {
                  name: serviceAccount.iamServiceAccountName,
                },
              },
            }],
          },
        ],
      },
    },
  ];

  local roleBinding = {
    apiVersion: 'rbac.authorization.k8s.io/v1',
    kind: 'RoleBinding',
    metadata: {
      name: '%s-deploy-server-role-binding' % namespace,
      namespace: runNamespace,
      labels: {
        app: appName,
      },
    },
    subjects: [
      {
        kind: 'User',
        name: serviceAccount.serviceAccountGcpEmailAddress,
      },
    ],
    roleRef: {
      kind: 'ClusterRole',
      name: 'deploy-server-role',
      apiGroup: 'rbac.authorization.k8s.io',
    },
  };

  local rpcContainer =
    {
      name: 'deploy-rpc',
      target: {
        name: '//tools/deploy_runner/server:rpc_image',
        dst: 'devtools_deploy_rpc',
      },
      ports: [
        {
          containerPort: 50051,
          name: 'grpc-svc',
        },
      ],
      volumeMounts: [
        configMap.volumeMountDef,
        clientCert.volumeMountDef,
        serverCert.volumeMountDef,
        {
          mountPath: github_app_path,
          name: 'github-app-secret',
        },
      ],
      readinessProbe: {
        tcpSocket: {
          port: 50051,
        },
        initialDelaySeconds: 5,
        periodSeconds: 10,
      },
      livenessProbe: {
        tcpSocket: {
          port: 50051,
        },
        initialDelaySeconds: 15,
        periodSeconds: 20,
      },
      resources: {
        limits: {
          cpu: 1,
          memory: '1Gi',
        },
      },
    };
  local rpcPod =
    {
      priorityClassName: gcpInfo.envToPriorityClass(env),
      affinity: affinity,
      tolerations: tolerations,
      serviceAccountName: serviceAccount.name,
      containers: [
        rpcContainer,
      ],
      volumes: [
        serverCert.podVolumeDef,
        clientCert.podVolumeDef,
        configMap.podVolumeDef,
        {
          name: 'github-app-secret',
          secret: {
            secretName: githubSecret.metadata.name,  // pragma: allowlist secret
            optional: false,
          },
        },
      ],
    };
  local rpcDeployment =
    {
      apiVersion: 'apps/v1',
      kind: 'Deployment',
      metadata: {
        name: 'deploy-rpc',
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        minReadySeconds: if env == 'DEV' then 0 else 60,
        replicas: 1,
        strategy: {
          type: 'RollingUpdate',
          rollingUpdate: {
            maxSurge: 1,
            maxUnavailable: 0,
          },
        },
        selector: {
          matchLabels: {
            app: appName,
          },
        },
        template: {
          metadata: {
            labels: {
              app: appName,
            },
          },
          spec: rpcPod,
        },
      },
    };
  local processorContainer =
    {
      name: 'deploy-processor',
      target: {
        name: '//tools/deploy_runner/server:processor_image',
        dst: 'devtools_deploy_processor',
      },
      volumeMounts: [
        configMap.volumeMountDef,
        serverCert.volumeMountDef,
      ],
      resources: {
        limits: {
          cpu: 0.1,
          memory: '100Mi',
        },
      },
    };
  local processorPod =
    {
      serviceAccountName: serviceAccount.name,
      affinity: affinity,
      tolerations: tolerations,
      priorityClassName: gcpInfo.envToPriorityClass(env),
      containers: [
        processorContainer,
      ],
      volumes: [
        clientCert.podVolumeDef,
        configMap.podVolumeDef,
        serverCert.podVolumeDef,
      ],
    };
  local processorDeployment =
    {
      apiVersion: 'apps/v1',
      kind: 'Deployment',
      metadata: {
        name: 'deploy-processor',
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        replicas: 1,
        strategy: {
          type: 'RollingUpdate',
          rollingUpdate: {
            maxSurge: 0,
            maxUnavailable: 1,
          },
        },
        selector: {
          matchLabels: {
            app: appName,
          },
        },
        template: {
          metadata: {
            labels: {
              app: appName,
            },
          },
          spec: processorPod,
        },
      },
    };
  local subpubProcessorContainer =
    {
      name: 'deploy-pubsub-processor',
      target: {
        name: '//tools/deploy_runner/server:pubsub_processor_image',
        dst: 'devtools_deploy_pubsub_processor',
      },
      args: [
        '--subscription-name',
        pubsubSubscription.metadata.name,
      ],
      volumeMounts: [
        configMap.volumeMountDef,
        serverCert.volumeMountDef,
        clientCert.volumeMountDef,
      ],
      resources: {
        limits: {
          cpu: 0.1,
          memory: '100Mi',
        },
      },
    };
  local pubsubProcessorPod =
    {
      serviceAccountName: serviceAccount.name,
      affinity: affinity,
      tolerations: tolerations,
      priorityClassName: gcpInfo.envToPriorityClass(env),
      containers: [
        subpubProcessorContainer,
      ],
      volumes: [
        clientCert.podVolumeDef,
        configMap.podVolumeDef,
        serverCert.podVolumeDef,
      ],
    };
  local pubsubProcessorDeployment =
    {
      apiVersion: 'apps/v1',
      kind: 'Deployment',
      metadata: {
        name: 'deploy-pubsub-processor',
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        replicas: 1,
        strategy: {
          type: 'RollingUpdate',
          rollingUpdate: {
            maxSurge: 0,
            maxUnavailable: 1,
          },
        },
        selector: {
          matchLabels: {
            app: appName,
          },
        },
        template: {
          metadata: {
            labels: {
              app: appName,
            },
          },
          spec: pubsubProcessorPod,
        },
      },
    };
  local slackPubsubProcessorContainer =
    {
      name: 'deploy-pubsub-processor',
      target: {
        name: '//tools/deploy_runner/server:slack_pubsub_processor_image',
        dst: 'devtools_deploy_slack_pubsub_processor',
      },
      args: [
        '--deploy-endpoint',
        'deploy-rpc-svc:50051',
        '--subscription-name',
        slackPubsubSubscription.metadata.name,
      ],
      volumeMounts: [
        configMap.volumeMountDef,
        serverCert.volumeMountDef,
        clientCert.volumeMountDef,
      ],
      resources: {
        limits: {
          cpu: 0.1,
          memory: '100Mi',
        },
      },
    };
  local slackPubSubProcessorPod =
    {
      serviceAccountName: serviceAccount.name,
      containers: [
        slackPubsubProcessorContainer,
      ],
      affinity: affinity,
      tolerations: tolerations,
      priorityClassName: gcpInfo.envToPriorityClass(env),
      volumes: [
        configMap.podVolumeDef,
        clientCert.podVolumeDef,
        serverCert.podVolumeDef,
      ],
    };
  local slackPubsubProcessorDeployment =
    {
      apiVersion: 'apps/v1',
      kind: 'Deployment',
      metadata: {
        name: 'deploy-slack-pubsub-processor',
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        replicas: 1,
        strategy: {
          type: 'RollingUpdate',
          rollingUpdate: {
            maxSurge: 0,
            maxUnavailable: 1,
          },
        },
        selector: {
          matchLabels: {
            app: appName,
          },
        },
        template: {
          metadata: {
            labels: {
              app: appName,
            },
          },
          spec: slackPubSubProcessorPod,
        },
      },
    };
  lib.flatten([
    githubSecret,
    serviceAccount.objects,
    bigtable.objects,
    serverCert.objects,
    clientCert.objects,
    configMap.objects,
    service,
    rpcDeployment,
    processorDeployment,
    pubsubProcessorDeployment,
    slackPubsubProcessorDeployment,
    roleBinding,
    pubsubTopic,
    publishIam,
    subscribeIam,
    pubsubSubscription,
    slackPubsubSubscription,
  ])
