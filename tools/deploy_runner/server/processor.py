"""Part of the deploy runner service that observes the kubernetes jobs and updates the state in BigTable."""

import argparse
import datetime
import logging
import pathlib
import signal
import uuid

import kubernetes
import structlog
from kubernetes.client.exceptions import ApiException
from prometheus_client import start_http_server

from base.cloud.k8s.kubectl_factory import create_kubectl_factory
from base.cloud.k8s.kubernetes_client import KubernetesClient, create_kubernetes_client
from base.logging.struct_logging import setup_struct_logging
from base.python.cloud import gcp
from tools.deploy_runner.control import deploy_pod_factory
from tools.deploy_runner.server import deploy_pb2
from tools.deploy_runner.server.config import Config
from tools.deploy_runner.server.persistence import Persistence

log = structlog.get_logger()


class Processor:
    """The main code of the deploy runner processor.

    It listens on the kubernetes jobs and updates the state in BigTable.
    """

    def __init__(
        self,
        kubernetes_client: KubernetesClient,
        persistence: Persistence,
        config: Config,
        control_client: deploy_pod_factory.DeployRunnerControlClient,
    ):
        self.kubernetes_client = kubernetes_client
        self.persistence = persistence
        self.config = config
        self.control_client = control_client

    def _check(self, deploy_id: str):
        structlog.contextvars.bind_contextvars(deploy_id=deploy_id)
        try:
            deploy_id_uuid = uuid.UUID(deploy_id)
        except ValueError:
            return
        job_result = self.control_client.check_job(deploy_id_uuid)
        logging.info("Job result %s for %s", job_result, deploy_id)

        deployment = self.persistence.get(deploy_id_uuid)
        if not deployment:
            logging.info("Deployment not found")
            return
        logging.info("Deployment %s", deployment)
        now = datetime.datetime.now(datetime.timezone.utc)

        if job_result == deploy_pod_factory.JobState.FAILED:
            if (
                deployment.state == deploy_pb2.DeploymentState.DEPLOYMENT_STATE_FAILED
                or (
                    deployment.state
                    == deploy_pb2.DeploymentState.DEPLOYMENT_STATE_CANCELLED
                )
            ):
                return
            else:
                deployment.finished_time.FromDatetime(now)
                deployment.state = deploy_pb2.DeploymentState.DEPLOYMENT_STATE_FAILED
        elif job_result == deploy_pod_factory.JobState.MISSING:
            if (
                deployment.state == deploy_pb2.DeploymentState.DEPLOYMENT_STATE_FAILED
                or (
                    deployment.state
                    == deploy_pb2.DeploymentState.DEPLOYMENT_STATE_CANCELLED
                )
            ):
                return
            else:
                deployment.finished_time.FromDatetime(now)
                deployment.state = deploy_pb2.DeploymentState.DEPLOYMENT_STATE_FAILED
        elif job_result == deploy_pod_factory.JobState.RUNNING:
            if (
                deployment.state == deploy_pb2.DeploymentState.DEPLOYMENT_STATE_RUNNING
                or (
                    deployment.state
                    == deploy_pb2.DeploymentState.DEPLOYMENT_STATE_CANCELLED
                )
            ):
                return
            else:
                deployment.state = deploy_pb2.DeploymentState.DEPLOYMENT_STATE_RUNNING
        elif job_result == deploy_pod_factory.JobState.SCHEDULED:
            if (
                deployment.state
                == deploy_pb2.DeploymentState.DEPLOYMENT_STATE_SCHEDULED
                or (
                    deployment.state
                    == deploy_pb2.DeploymentState.DEPLOYMENT_STATE_CANCELLED
                )
            ):
                return
            else:
                deployment.state = deploy_pb2.DeploymentState.DEPLOYMENT_STATE_SCHEDULED
        elif job_result == deploy_pod_factory.JobState.SUCCEEDED:
            if (
                deployment.state
                == deploy_pb2.DeploymentState.DEPLOYMENT_STATE_SUCCEEDED
                or (
                    deployment.state
                    == deploy_pb2.DeploymentState.DEPLOYMENT_STATE_CANCELLED
                )
            ):
                return
            else:
                deployment.finished_time.FromDatetime(now)
                deployment.state = deploy_pb2.DeploymentState.DEPLOYMENT_STATE_SUCCEEDED

        # only call persistence if we have a change
        deployment.update_time.FromDatetime(now)
        logging.info("Update deployment %s", deployment)
        self.persistence.update(deployment)

    def run(self):
        # The watcher stream appears to stall occasionally. Restart the pod every 2 hours
        # to help avoid missing events.
        signal.alarm(2 * 60 * 60)

        """Listens on job events in the test namespace, so that the processor can ack on them."""
        try:
            self.kubernetes_client.login(self.config.cloud)
            while True:
                try:
                    core_api = self.kubernetes_client.get_batch_api(self.config.cloud)
                    watcher = kubernetes.watch.Watch()  # type: ignore
                    stream = watcher.stream(
                        core_api.list_namespaced_job, namespace=self.config.namespace
                    )
                    for raw_event in stream:
                        if not raw_event["object"].metadata.labels:  # type: ignore
                            continue
                        deploy_id = raw_event["object"].metadata.labels.get(  # type: ignore
                            "deploy-id"
                        )
                        if not deploy_id:
                            continue

                        logging.info(
                            "Kubernetes Job Event: %s %s",
                            raw_event["type"],  # type: ignore
                            raw_event["object"].metadata.name,  # type: ignore
                        )
                        self._check(deploy_id)
                except ApiException as ex:
                    logging.error(
                        "Api error while listening on job k8s events: status code %s, ex %s",
                        ex.status,
                        ex,
                    )
                    if ex.status == 401 or ex.status == 410:
                        # re-authenticate
                        self.kubernetes_client.login(self.config.cloud)
                        continue
                    else:
                        raise
        except Exception as ex:  # pylint: disable=broad-exception-caught
            logging.error("Error while listen on k8s job events: %s", ex)
            # This will crash the pod
            raise


def run(args: argparse.Namespace, config: Config):
    """Entry function to run the processing loop."""

    persistence = Persistence.setup(config=config)

    kubernetes_client = create_kubernetes_client(args.kube_config_file)

    kubectl = create_kubectl_factory(args.kube_config_file)(config.cloud)

    control_client = deploy_pod_factory.DeployRunnerControlClient(
        kubernetes_client=kubernetes_client,
        kubectl=kubectl,
        namespace=config.namespace,
        runner_image="",
        cloud=config.cloud,
        env=config.env,
        topic_name=config.topic_name,
    )

    processor = Processor(
        persistence=persistence,
        config=config,
        kubernetes_client=kubernetes_client,
        control_client=control_client,
    )

    processor.run()


def main():
    """Main function."""

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--config-file", default=pathlib.Path("/config/config.json"), type=pathlib.Path
    )
    parser.add_argument(
        "--kube-config-file",
        default=pathlib.Path("tools/deploy/auth_kube_config.yaml"),
        type=pathlib.Path,
    )
    args = parser.parse_args()

    setup_struct_logging()

    config = Config.load_config(args.config_file)
    logging.info("Config %s", config)
    logging.info("Service Account %s", gcp.get_active_gcp_service_account())

    # begin listening for Prometheus requests
    start_http_server(9090)

    run(args, config)


if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        logging.fatal("fatal exception %s", e)
        raise
