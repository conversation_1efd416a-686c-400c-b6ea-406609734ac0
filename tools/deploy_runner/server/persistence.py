import datetime
import logging
import uuid
import typing

from google.cloud import bigtable  # type: ignore

from tools.deploy_runner.server import deploy_pb2, deploy_store_pb2
from tools.deploy_runner import deploy_events_pb2, metadata_pb2
from tools.deploy_runner.server.config import Config


def _mutate_and_check(table, rows):
    response = table.mutate_rows(rows)
    logging.info("Response %s", response)
    for status in response:
        if status.code != 0:
            logging.error("Error writing row: %s", status.message)
            raise ValueError("Error writing row")


def get_timestamp_key(deploy_id: uuid.UUID) -> bytes:
    """Returns the BigTable timestamp key for the given deploy id.
    The deploy id is actually a ulid (https://github.com/ulid/spec) and thus sortable as timestamp.
    This function uses that property and reverses the sort order

    The key is setup so that newer deployments have a lower timestamp key then older deployments.

    Args:
        deploy_id: the deploy id

    Returns:
        The BigTable key for the given deploy id
    """
    return b"timestamp#v2#" + bytes(~byte & 0xFF for byte in deploy_id.bytes)


class Persistence:
    """Persistence for Deploy Runner based on GCP BigTable.

    The information is stored in a single bigtable table, usually called "deploy-runner-main".

    Column Families
        Column Family: Main
        Key: "deploy#{deploy_id}"
    """

    def __init__(self, config: Config, client: bigtable.Client):
        self.config = config
        self.client = client

        self.instance = client.instance(self.config.instance_name)
        self.table = self.instance.table(self.config.table_name)

    @classmethod
    def setup(cls, config: Config):
        """Creates a new persistence instance."""
        return cls(config, bigtable.Client())

    def cancel(self, deploy_id: uuid.UUID):
        """Cancels a deployment.

        Args:
            deploy_id: the deployment id
        """
        deployment = self.get(deploy_id)
        if not deployment:
            return
        deployment.state = deploy_pb2.DeploymentState.DEPLOYMENT_STATE_CANCELLED
        self.update(deployment)

    def create(
        self,
        deploy_id: uuid.UUID,
        request: deploy_pb2.ScheduleDeploymentRequest,
        job_info: deploy_store_pb2.JobInfo,
    ):
        """Creates a new deployment.

        Args:
            deploy_id: the deployment id
            request: the deployment request
            job_info: the job info
        """
        item = deploy_store_pb2.Deployment(
            deploy_id=str(deploy_id),
            request=request,
            job_info=job_info,
            state=deploy_pb2.DeploymentState.DEPLOYMENT_STATE_SCHEDULED,
        )
        item.create_time.FromDatetime(datetime.datetime.now(datetime.timezone.utc))

        rows = []
        row_key: bytes = f"deploy#{deploy_id}".encode("utf-8")
        row = self.table.direct_row(row_key=row_key)
        rows.append(row)
        row.set_cell("Main", "main", item.SerializeToString())

        row_key: bytes = get_timestamp_key(deploy_id)
        row = self.table.direct_row(row_key=row_key)
        rows.append(row)
        row.set_cell("Timestamp", "timestamp", deploy_id.bytes)

        _mutate_and_check(self.table, rows)

    def update(
        self,
        deployment: deploy_store_pb2.Deployment,
    ):
        """Updates a deployment.

        Args:
            deployment: the deployment
        """
        rows = []
        row_key: str = f"deploy#{deployment.deploy_id}"
        row = self.table.direct_row(row_key=row_key)
        rows.append(row)
        row.set_cell("Main", "main", deployment.SerializeToString())
        _mutate_and_check(self.table, rows)

    def get(self, deploy_id: uuid.UUID) -> deploy_store_pb2.Deployment | None:
        """Returns the deployment with the given id.

        Args:
            deploy_id: the deployment id

        Returns:
            The deployment or None if not found
        """
        row_key: str = f"deploy#{deploy_id}"
        row = self.table.read_row(row_key)
        if not row:
            return None
        main_cells = row.cells["Main"]
        main_cell = main_cells["main".encode("utf-8")][0]
        main_item_proto = deploy_store_pb2.Deployment()
        main_item_proto.ParseFromString(main_cell.value)  # type: ignore
        return main_item_proto

    def batch_get(
        self, deploy_ids: list[uuid.UUID]
    ) -> typing.Iterable[deploy_store_pb2.Deployment]:
        """Returns the deployment with the given id.

        Args:
            deploy_ids: the deployment ids

        Returns:
            The deployments. If a deployment is not found, it is not returned in the list.
            The ordering of the list is not guaranteed.
        """
        rowset = bigtable.row_set.RowSet()  # type: ignore
        for deploy_id in deploy_ids:
            rowset.add_row_key(f"deploy#{deploy_id}".encode("utf-8"))
        rows = self.table.read_rows(row_set=rowset)
        for row in rows:
            main_cells = row.cells["Main"]
            main_cell = main_cells["main".encode("utf-8")][0]
            main_item_proto = deploy_store_pb2.Deployment()
            main_item_proto.ParseFromString(main_cell.value)  # type: ignore
            yield main_item_proto

    def get_deployments(
        self,
        oldest_deploy_id: uuid.UUID | None,
        newest_deploy_id: uuid.UUID | None,
        oldest_inclusive: bool,
        newest_inclusive: bool,
        max_count: int,
    ) -> typing.Iterator[deploy_store_pb2.Deployment]:
        """Returns the previous deployments.

        Args:
            start_deploy_id: the earliest deployment id to return
            end_deploy_id: the latest deployment id to return
            max_count: the maximum number of deployments to return

        Returns:
            The deployments. The deployments are returned in reverse order,
            i.e., the latest deployment is returned first.
        """
        logging.info(
            "Getting deployments from %s to %s: max count %s",
            oldest_deploy_id,
            newest_deploy_id,
            max_count,
        )

        row_set = bigtable.row_set.RowSet()  # type: ignore
        if newest_deploy_id:
            start_key = get_timestamp_key(newest_deploy_id)
        else:
            start_key = b"timestamp#v2#"
        if oldest_deploy_id:
            end_key = get_timestamp_key(oldest_deploy_id)
        else:
            end_key = b"timestamp#v2$"
        row_set.add_row_range_from_keys(
            start_key=start_key,
            end_key=end_key,
            start_inclusive=newest_inclusive,
            end_inclusive=oldest_inclusive,
        )
        rows = self.table.read_rows(
            row_set=row_set,
        )
        count = 0
        deploy_ids = []
        for row in rows:
            cells = row.cells["Timestamp"]
            main_cell = cells["timestamp".encode("utf-8")][0]
            deploy_id = uuid.UUID(bytes=main_cell.value)
            deploy_ids.append(deploy_id)
            count += 1
            if count >= max_count:
                break

        if not deploy_ids:
            return
        infos = list(self.batch_get(deploy_ids))
        infos.sort(key=lambda info: info.deploy_id, reverse=True)
        for info in infos:
            yield info

    def get_last_successful_deployments_by_schedule(
        self, max_deployments_to_check: int = 1000
    ) -> dict[
        metadata_pb2.Deployment.DeploymentScheduleName.ValueType,
        deploy_store_pb2.Deployment,
    ]:
        """Returns the last successful deployment for each deployment schedule.

        Args:
            max_deployments_to_check: Maximum number of recent deployments to check

        Returns:
            Dictionary mapping deployment schedule name enum values to the last successful deployment
        """
        # Get recent deployments in reverse chronological order
        deployments = self.get_deployments(
            oldest_deploy_id=None,
            newest_deploy_id=None,
            oldest_inclusive=True,
            newest_inclusive=True,
            max_count=max_deployments_to_check,
        )

        last_successful_by_schedule = {}
        # number of all scheduled names (including DISABLED). This is used to break out
        # of the loop early if we found a successful deployment for all schedules.
        target_count = len(metadata_pb2.Deployment.DeploymentScheduleName.values()) - 1

        for deployment in deployments:
            # Skip if deployment doesn't have a schedule name or is DISABLED
            if not deployment.request.HasField("deployment_schedule_name"):
                continue

            schedule_name = deployment.request.deployment_schedule_name

            # Skip DISABLED schedules
            if schedule_name == metadata_pb2.Deployment.DeploymentScheduleName.DISABLED:
                continue

            # Skip if we already found a successful deployment for this schedule
            if schedule_name in last_successful_by_schedule:
                continue

            # Check if this deployment succeeded
            if (
                deployment.state
                == deploy_pb2.DeploymentState.DEPLOYMENT_STATE_SUCCEEDED
            ):
                last_successful_by_schedule[schedule_name] = deployment

                if len(last_successful_by_schedule) == target_count:
                    break

        return last_successful_by_schedule

    def create_events(self, events: typing.Sequence[deploy_events_pb2.DeployEvent]):
        """Creates events for a deployment.

        Args:
            events: the events
        """
        rows = []
        for event in events:
            row_key: str = f"event#{event.deploy_id}#{event.sequence_number:08d}"
            row = self.table.direct_row(row_key=row_key)
            rows.append(row)
            row.set_cell("Event", "", event.SerializeToString())
        _mutate_and_check(self.table, rows)

    def get_events(
        self, deploy_id: uuid.UUID, min_sequence_number: int, limit: int
    ) -> list[deploy_events_pb2.DeployEvent]:
        """Returns the events for the given deployment.

        Args:
            deploy_id: the deployment id
            min_sequence_number: the minimum sequence number

        Returns:
            The events
        """
        events = []
        start_key: str = f"event#{deploy_id}#{min_sequence_number:08d}"
        end_key: str = f"event#{deploy_id}$"
        row_set = bigtable.row_set.RowSet()  # type: ignore
        row_set.add_row_range_from_keys(
            start_key.encode("utf-8"), end_key.encode("utf-8")
        )
        if limit > 0:
            rows = self.table.read_rows(row_set=row_set, limit=limit)
        else:
            rows = self.table.read_rows(row_set=row_set)
        for row in rows:
            event = row.cells["Event"][b""][0]
            deploy_event = deploy_events_pb2.DeployEvent()
            deploy_event.ParseFromString(event.value)  # type: ignore
            events.append(deploy_event)
        return events
