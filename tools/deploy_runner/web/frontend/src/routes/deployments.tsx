import "../App.css";
import { LayoutComponent } from "../lib/layout";
import {
  DeploymentData,
  disableScheduledDeployment,
  enableScheduledDeployment,
  getDeployments,
  getDeploymentTracks,
  getScheduledDeploymentConfig,
} from "../lib/deployment";
import React, { useEffect, useState } from "react";
import {
  Button,
  Descriptions,
  Divider,
  Empty,
  Select,
  Spin,
  Switch,
  Table,
  Typography,
  message,
} from "antd";
import { ColumnType } from "antd/es/table";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import localizedFormat from "dayjs/plugin/localizedFormat";
import { DeploymentStateComponent } from "./deployment";
import axios from "axios";
const { Text, Link } = Typography;

dayjs.extend(utc);
dayjs.extend(localizedFormat);

type Paging = {
  newestDeployIdStack: string[];
  pageSize: number;
};

function ScheduledDeploymentConfigComponent({
  deploymentTrack,
}: {
  deploymentTrack: string;
}) {
  const [scheduledDeploymentConfig, setScheduledDeploymentConfig] = useState<
    boolean | null | undefined
  >(undefined);
  useEffect(() => {
    const fetchScheduledDeploymentConfig = async () => {
      try {
        const config = await getScheduledDeploymentConfig(deploymentTrack);
        console.log(
          `scheduled deployment config for ${deploymentTrack}: ${config}`,
        );
        setScheduledDeploymentConfig(config);
      } catch (e) {
        console.log(
          `Error while loading the scheduled deployment config: ${e}`,
        );
        setScheduledDeploymentConfig(null);
      }
    };
    fetchScheduledDeploymentConfig();
  }, [deploymentTrack]);

  const onScheduledDeploymentConfigChange = async (checked: boolean) => {
    if (checked) {
      await enableScheduledDeployment(deploymentTrack);
    } else {
      await disableScheduledDeployment(deploymentTrack);
    }
    setScheduledDeploymentConfig(checked);
  };
  return (
    <Switch
      checkedChildren="Enabled"
      unCheckedChildren="Disabled"
      disabled={scheduledDeploymentConfig === null}
      checked={scheduledDeploymentConfig || false}
      loading={scheduledDeploymentConfig === undefined}
      onChange={onScheduledDeploymentConfigChange}
    />
  );
}

function ScheduledDeploymentTracksConfigComponent() {
  const [deploymentTracks, setDeploymentTracks] = useState<
    string[] | undefined
  >(undefined);
  useEffect(() => {
    const fetchDeploymentTracks = async () => {
      const tracks = await getDeploymentTracks();
      console.log(`deployment tracks ${tracks}`);
      setDeploymentTracks(tracks);
    };
    fetchDeploymentTracks();
  }, []);

  console.log(`deployment tracks ${JSON.stringify(deploymentTracks)}`);

  if (deploymentTracks === undefined) {
    return <Spin />;
  }

  return (
    <div>
      <Descriptions title="Scheduled Deployments" bordered column={1}>
        {deploymentTracks.map((track: string) => {
          return (
            <Descriptions.Item
              label={`Scheduled Deployment: ${track}`}
              key={track}
            >
              <ScheduledDeploymentConfigComponent deploymentTrack={track} />
            </Descriptions.Item>
          );
        })}
      </Descriptions>{" "}
      <Divider />
    </div>
  );
}

function PageSizeSelector({
  pageSize,
  onChange,
}: {
  pageSize: number;
  onChange: (size: number) => void;
}) {
  return (
    <div
      style={{
        display: "inline-flex",
        alignItems: "center",
        marginLeft: "16px",
      }}
    >
      <span style={{ marginRight: "8px" }}>Items per page:</span>
      <Select
        value={pageSize}
        onChange={onChange}
        options={[
          { value: 10, label: "10" },
          { value: 20, label: "20" },
          { value: 50, label: "50" },
          { value: 100, label: "100" },
        ]}
        style={{ width: "80px" }}
      />
    </div>
  );
}

function DeploymentsPageComponent() {
  const [deploymentData, setDeploymentData] = useState<
    DeploymentData[] | null | undefined
  >(undefined);
  const [page, setPage] = useState<Paging>({
    newestDeployIdStack: [],
    pageSize: 20, // Default page size
  });
  console.log(`page ${JSON.stringify(page)}`);
  const [isLoading, setIsLoading] = useState(true);
  const [messageApi, contextHolder] = message.useMessage();

  const handlePageSizeChange = (newSize: number) => {
    // Reset to first page when changing page size
    setPage({
      newestDeployIdStack: [],
      pageSize: newSize,
    });
  };

  useEffect(() => {
    const fetchRun = async () => {
      console.log(`fetch deployment`);
      try {
        const deploymentInfo = await getDeployments(
          undefined,
          page.newestDeployIdStack.at(-1),
          page.pageSize,
        );
        console.log(`deployment ${JSON.stringify(deploymentInfo)}`);
        setDeploymentData(deploymentInfo);
        setIsLoading(false);
      } catch (e) {
        console.log(`Error while loading the deployment data: ${e}`);
        if (axios.isAxiosError(e)) {
          setDeploymentData(null);
          setIsLoading(false);
          messageApi.open({
            type: "error",
            content: `Error while loading the deployment data: ${e.message}`,
          });
        } else {
          setDeploymentData(null);
          setIsLoading(false);
          messageApi.open({
            type: "error",
            content: `Error while loading the deployment data`,
          });
        }
      }
    };
    fetchRun();
  }, [page]);

  const columns: ColumnType<DeploymentData>[] = [
    {
      title: "Deployment ID",
      key: "deployId",
      render: (t: DeploymentData) => {
        return (
          <Link href={`/deployment/${t.deployId}`}>
            <Text keyboard>{t.deployId}</Text>
          </Link>
        );
      },
      sorter: (a, b) => {
        return a.deployId.localeCompare(b.deployId);
      },
    },
    {
      title: "State",
      key: "state",
      render: (t: DeploymentData) => {
        return <DeploymentStateComponent state={t.state} />;
      },
      filters: [
        {
          text: "Success",
          value: "DEPLOYMENT_STATE_SUCCEEDED",
        },
        {
          text: "Failed",
          value: "DEPLOYMENT_STATE_FAILED",
        },
        {
          text: "Validation Failed",
          value: "VALIDATION_FAILED",
        },
        {
          text: "Scheduled",
          value: "DEPLOYMENT_STATE_SCHEDULED",
        },
        {
          text: "Running",
          value: "DEPLOYMENT_STATE_RUNNING",
        },
      ],
      onFilter: (value: string | number | boolean, record: DeploymentData) =>
        record.state === (value as string),
      sorter: (a, b) => {
        return a.state.localeCompare(b.state);
      },
    },
    {
      title: "Created Time",
      key: "createdTime",
      render: (t: DeploymentData) => {
        const ft = dayjs(t.createdTime).local().format("LLL");
        return <Text>{ft}</Text>;
      },
      sorter: (a, b) => {
        return a.createdTime.localeCompare(b.createdTime);
      },
    },
    {
      title: "Finished Time",
      key: "finishedTime",
      render: (t: DeploymentData) => {
        let ft: string | undefined;
        if (dayjs(t.finishedTime).valueOf() === 0) {
          return <div></div>;
        } else {
          ft = dayjs(t.finishedTime).local().format("LLL");
          return <Text>{ft}</Text>;
        }
      },
      sorter: (a, b) => {
        return a.finishedTime.localeCompare(b.finishedTime);
      },
    },
    {
      title: "Commit",
      key: "commit",
      render: (t: DeploymentData) => {
        return (
          <Text keyboard>
            {t.request.branch || `#${t.request.pullRequestNumber}`} /{" "}
            {t.request.commitRef}
          </Text>
        );
      },
      sorter: (a, b) => {
        return a.request.commitRef.localeCompare(b.request.commitRef);
      },
    },
    {
      title: "Track",
      key: "track",
      render: (t: DeploymentData) => {
        return <Text keyboard>{t.request.deploymentTrack || ""}</Text>;
      },
      sorter: (a, b) => {
        const ar = a.request.deploymentTrack || "";
        const br = b.request.deploymentTrack || "";
        return ar.localeCompare(br);
      },
    },
    {
      title: "Requestor",
      key: "requestor",
      render: (t: DeploymentData) => {
        return <Text keyboard>{t.request.adhoc?.requestor || ""}</Text>;
      },
      sorter: (a, b) => {
        const ar = a.request.adhoc?.requestor || "";
        const br = b.request.adhoc?.requestor || "";
        return ar.localeCompare(br);
      },
    },
  ];

  if (isLoading) {
    return (
      <>
        {contextHolder}
        <LayoutComponent
          selectedMenuKey={"deployments"}
          breadcrumbs={[{ label: "Deployments", link: "/deployments" }]}
        >
          <Spin />
        </LayoutComponent>
      </>
    );
  } else if (deploymentData === null) {
    <>
      {contextHolder}
      <LayoutComponent
        selectedMenuKey={"deployments"}
        breadcrumbs={[{ label: "Deployments", link: "/deployments" }]}
      >
        <Empty
          description="Failed to load the deployment data."
          style={{ paddingTop: "5em" }}
        />
      </LayoutComponent>
    </>;
  } else {
    let resultsTable = (
      <Table dataSource={deploymentData} columns={columns} pagination={false} />
    );
    let backAndLinks = (
      <>
        <Divider />
        <Button
          onClick={() => {
            const newPage: Paging = {
              newestDeployIdStack: [],
              pageSize: page.pageSize,
            };
            setPage(newPage);
          }}
        >
          Current
        </Button>
        <Divider type="vertical" />
        <Button
          disabled={page.newestDeployIdStack.length === 0}
          onClick={() => {
            const newPage: Paging = {
              newestDeployIdStack: page.newestDeployIdStack.slice(
                0,
                page.newestDeployIdStack.length - 1,
              ),
              pageSize: page.pageSize,
            };
            setPage(newPage);
          }}
        >
          Newer
        </Button>
        <Divider type="vertical" />
        <Button
          disabled={deploymentData.length === 0}
          onClick={() => {
            const newPage: Paging = {
              newestDeployIdStack: page.newestDeployIdStack.concat([
                deploymentData[deploymentData.length - 1].deployId,
              ]),
              pageSize: page.pageSize,
            };
            setPage(newPage);
          }}
        >
          Older
        </Button>
        <PageSizeSelector
          pageSize={page.pageSize}
          onChange={handlePageSizeChange}
        />
      </>
    );

    const scheduledConfig = <ScheduledDeploymentTracksConfigComponent />;

    const children = [scheduledConfig, resultsTable, backAndLinks];
    return (
      <>
        {contextHolder}
        <LayoutComponent
          selectedMenuKey={"deployments"}
          children={children}
          breadcrumbs={[{ label: "Deployments", link: "/deployments" }]}
        ></LayoutComponent>
      </>
    );
  }
}

export default DeploymentsPageComponent;
