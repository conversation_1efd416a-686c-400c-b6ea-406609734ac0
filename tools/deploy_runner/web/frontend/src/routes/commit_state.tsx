import "../App.css";
import { LayoutComponent } from "../lib/layout";
import { CommitStateData, getCommitState } from "../lib/deployment";
import React, { useState } from "react";
import { Link } from "react-router-dom";
import {
  Button,
  Form,
  Input,
  Radio,
  Spin,
  Timeline,
  Typography,
  message,
  Badge,
  Descriptions,
  Divider,
} from "antd";
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  ClockCircleOutlined,
  BranchesOutlined,
} from "@ant-design/icons";
import axios from "axios";
import dayjs from "dayjs";
import localizedFormat from "dayjs/plugin/localizedFormat";
import utc from "dayjs/plugin/utc";

dayjs.extend(utc);
dayjs.extend(localizedFormat);

const { Title, Text } = Typography;

type InputType = "commit" | "pr";

export default function CommitStatePageComponent() {
  const [form] = Form.useForm();
  const [inputType, setInputType] = useState<InputType>("commit");
  const [commitStateData, setCommitStateData] =
    useState<CommitStateData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [messageApi, contextHolder] = message.useMessage();

  const handleSubmit = async (values: any) => {
    setIsLoading(true);
    setCommitStateData(null);

    try {
      let commitRef: string | undefined;
      let pullRequestNumber: string | undefined;

      if (inputType === "commit") {
        commitRef = values.commitRef?.trim();
        if (!commitRef) {
          messageApi.error("Please enter a commit reference");
          setIsLoading(false);
          return;
        }
      } else {
        pullRequestNumber = values.pullRequestNumber?.trim();
        if (!pullRequestNumber) {
          messageApi.error("Please enter a pull request number");
          setIsLoading(false);
          return;
        }
      }

      const data = await getCommitState(commitRef, pullRequestNumber);
      setCommitStateData(data);
      messageApi.success("Commit state loaded successfully");
    } catch (e) {
      console.error("Error loading commit state:", e);
      if (axios.isAxiosError(e)) {
        if (e.response?.status === 404) {
          messageApi.error("Commit or pull request not found");
        } else {
          messageApi.error(`Error loading commit state: ${e.message}`);
        }
      } else {
        messageApi.error("Error loading commit state");
      }
    } finally {
      setIsLoading(false);
    }
  };

  const renderCommitStateInfo = () => {
    if (!commitStateData) return null;

    return (
      <div style={{ marginTop: 24 }}>
        <Descriptions title="Commit Information" bordered column={2}>
          <Descriptions.Item label="Is Merged" span={1}>
            <Badge
              status={commitStateData.isMerged ? "success" : "error"}
              text={commitStateData.isMerged ? "Yes" : "No"}
            />
          </Descriptions.Item>
          <Descriptions.Item label="Is Known Good" span={1}>
            <Badge
              status={commitStateData.isKnownGood ? "success" : "error"}
              text={commitStateData.isKnownGood ? "Yes" : "No"}
            />
          </Descriptions.Item>
        </Descriptions>

        <Divider />

        <Title level={4}>Deployment Schedule States</Title>
        {commitStateData.scheduleDeploymentStates.length === 0 ? (
          <Text type="secondary">No deployment schedule states found</Text>
        ) : (
          <Timeline
            mode="left"
            items={commitStateData.scheduleDeploymentStates.map(
              (state: any, index: number) => ({
                key: index,
                dot: state.containsRef ? (
                  <CheckCircleOutlined style={{ color: "#52c41a" }} />
                ) : (
                  <CloseCircleOutlined style={{ color: "#ff4d4f" }} />
                ),
                color: state.containsRef ? "green" : "red",
                children: (
                  <div>
                    <Text strong>{state.deploymentScheduleName}</Text>
                    <br />
                    <Text type="secondary">
                      Deploy ID:{" "}
                      <Link
                        to={`/deployment/${state.deployId}`}
                        style={{ fontFamily: "monospace" }}
                      >
                        {state.deployId}
                      </Link>
                    </Text>
                    <br />
                    {state.deploymentTime && (
                      <>
                        <Text type="secondary">
                          Deployment Time:{" "}
                          {dayjs(state.deploymentTime).local().format()}
                        </Text>
                        <br />
                      </>
                    )}
                    {state.deploymentCommit && (
                      <>
                        <Text type="secondary">
                          Commit:{" "}
                          <a
                            href={`https://github.com/augmentcode/augment/commit/${state.deploymentCommit}`}
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            <Text keyboard>{state.deploymentCommit}</Text>
                          </a>
                        </Text>
                        <br />
                      </>
                    )}
                    <Badge
                      status={state.containsRef ? "success" : "error"}
                      text={
                        state.containsRef
                          ? "Contains Commit"
                          : "Does Not Contain Commit"
                      }
                    />
                  </div>
                ),
              }),
            )}
          />
        )}
      </div>
    );
  };

  return (
    <LayoutComponent
      selectedMenuKey="commit_state"
      breadcrumbs={[{ label: "Commit State", link: "/commit/state" }]}
    >
      {contextHolder}
      <div style={{ padding: 24 }}>
        <Title level={2}>
          <BranchesOutlined /> Commit State Lookup
        </Title>
        <Text type="secondary">
          Look up the deployment state of a commit reference or pull request
          number.
        </Text>

        <div
          style={{
            marginTop: 24,
            padding: 24,
            border: "1px solid #d9d9d9",
            borderRadius: 6,
          }}
        >
          <Title level={4}>Search</Title>
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
            disabled={isLoading}
          >
            <Form.Item label="Search Type">
              <Radio.Group
                value={inputType}
                onChange={(e: any) => {
                  setInputType(e.target.value);
                  form.resetFields();
                }}
              >
                <Radio.Button value="commit">
                  <BranchesOutlined /> Commit Reference
                </Radio.Button>
                <Radio.Button value="pr">
                  <BranchesOutlined /> Pull Request Number
                </Radio.Button>
              </Radio.Group>
            </Form.Item>

            {inputType === "commit" ? (
              <Form.Item
                name="commitRef"
                label="Commit Reference"
                rules={[
                  {
                    required: true,
                    message: "Please enter a commit reference",
                  },
                ]}
              >
                <Input
                  placeholder="e.g., abc123def456 or main"
                  prefix={<BranchesOutlined />}
                />
              </Form.Item>
            ) : (
              <Form.Item
                name="pullRequestNumber"
                label="Pull Request Number"
                rules={[
                  {
                    required: true,
                    message: "Please enter a pull request number",
                  },
                ]}
              >
                <Input
                  placeholder="e.g., 1234"
                  prefix={<BranchesOutlined />}
                  type="number"
                />
              </Form.Item>
            )}

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                loading={isLoading}
                icon={isLoading ? <ClockCircleOutlined /> : undefined}
              >
                {isLoading ? "Loading..." : "Get Commit State"}
              </Button>
            </Form.Item>
          </Form>
        </div>

        {isLoading && (
          <div style={{ textAlign: "center", marginTop: 24 }}>
            <Spin size="large" />
          </div>
        )}

        {renderCommitStateInfo()}
      </div>
    </LayoutComponent>
  );
}
