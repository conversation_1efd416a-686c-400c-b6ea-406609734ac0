import {
  Button,
  Checkbox,
  Typography,
  Divider,
  Form,
  Input,
  message,
  Select,
} from "antd";
import React, { useState } from "react";
import { LayoutComponent } from "../lib/layout";
import { useNavigate } from "react-router-dom";
import {
  InfoCircleOutlined,
  BranchesOutlined,
  CodeOutlined,
  CloudOutlined,
  AppstoreOutlined,
  DeploymentUnitOutlined,
  RollbackOutlined,
  ThunderboltOutlined,
  EditOutlined,
  CheckCircleOutlined,
} from "@ant-design/icons";
import { DeploymentScheduleData, scheduleDeployment } from "../lib/deployment";
const { Text, Link } = Typography;

const ALLOWED_BRANCHES = ["bazel-last-known-good", "main"];

type DeploymentScheduleForm = {
  branch: string | undefined;
  pr: string | undefined;
  commitRef: string | undefined;
  cloud?: string[];
  namespace: string | undefined;
  targets: string;
  allowRollback?: boolean;
  shouldBypass?: boolean;
  reason: string;
  confirm: boolean;
};

function DeploymentScheduleComponent() {
  const [runScheduleForm] = Form.useForm();
  const [formDisabled, setFormDisabled] = useState<boolean>(false);
  const [pr, setPr] = useState<string | undefined>(undefined);

  const [messageApi, contextHolder] = message.useMessage();
  const navigate = useNavigate();

  const onFormFinish = (values: DeploymentScheduleForm) => {
    console.log("Success:", values);

    let targets = values.targets
      .split("\n")
      .filter((t: string) => t.length > 0);

    let namespace = (values.namespace || "")
      .split("\n")
      .filter((t: string) => t.length > 0);

    let pr = values.pr || null;
    let branch = values.branch || null;
    if (pr != null && pr.length > 0) {
      branch = null;
    }

    const schedule: DeploymentScheduleData = {
      branch: branch,
      pullRequestNumber: pr,
      commitRef: values.commitRef || "",
      cloud: values.cloud || [],
      namespace: namespace,
      targets: targets,
      allowRollback: values.allowRollback || false,
      shouldBypass: values.shouldBypass || false,
      reason: values.reason,
    };

    setFormDisabled(true);
    scheduleDeployment(schedule)
      .then((deployId) => {
        setFormDisabled(false);
        navigate(`/deployment/${deployId}`);
      })
      .catch((err) => {
        setFormDisabled(false);
        console.log(`Failed to schedule deployment: ${err}`);
        messageApi.open({
          type: "error",
          content: `Error while scheduling the deployment: ${err.message}`,
        });
      });
  };

  const onFormChange = (values: any) => {
    console.log("Change:", values);
    setPr(values.pr);
  };

  const onFinishFailed = (errorInfo: any) => {
    console.log("Failed:", errorInfo);
  };

  return (
    <>
      {contextHolder}
      <Text>
        <h3>Schedule an Adhoc Deployment.</h3>
        <p>
          Please fill out the form below to run an adhoc deployment. Access
          requests are logged and audited.
        </p>
        <p>
          See{" "}
          <Link
            href="https://www.notion.so/Runbook-Emergency-Deployment-ce59777e9e884a0f8ca495f6d3a4d7bf?pvs=4"
            target="_blank"
          >
            here
          </Link>{" "}
          for more details.
        </p>
      </Text>
      <Divider />
      <Form
        name="schedule"
        form={runScheduleForm}
        labelCol={{ span: 10 }}
        wrapperCol={{ span: 14 }}
        style={{ maxWidth: 700 }}
        onFinish={onFormFinish}
        onFinishFailed={onFinishFailed}
        onValuesChange={onFormChange}
        disabled={formDisabled}
        autoComplete="on"
      >
        <Form.Item
          label="Branch"
          name="branch"
          initialValue="bazel-last-known-good"
          tooltip={{
            title: "Name of the branch",
            icon: <InfoCircleOutlined />,
          }}
        >
          <Select
            disabled={(pr || "").length > 0}
            suffixIcon={<BranchesOutlined />}
            options={ALLOWED_BRANCHES.map((branch) => ({
              value: branch,
              label: branch,
            }))}
          />
        </Form.Item>

        <Form.Item
          label="Pull Request"
          name="pr"
          initialValue=""
          tooltip={{
            title: "Pull request number. The PR must be approved.",
            icon: <InfoCircleOutlined />,
          }}
        >
          <Input prefix={<BranchesOutlined />} />
        </Form.Item>

        <Form.Item
          label="Commit"
          name="commitRef"
          tooltip={{
            title:
              "Commit (sha or sha prefix). If not specified, the last commit on the branch will be used.",
            icon: <InfoCircleOutlined />,
          }}
        >
          <Input prefix={<CodeOutlined />} />
        </Form.Item>

        <Form.Item
          label={
            <span>
              <CloudOutlined style={{ marginRight: 8 }} />
              Cloud
            </span>
          }
          name="cloud"
          rules={[
            () => ({
              validator(_, value) {
                if (!value) {
                  return Promise.reject(new Error("Please select a cloud"));
                }
                return Promise.resolve();
              },
            }),
          ]}
        >
          <Checkbox.Group>
            <Checkbox value="GCP_US_CENTRAL1_DEV">GCP_US_CENTRAL1_DEV</Checkbox>
            <Checkbox value="GCP_US_CENTRAL1_PROD">
              GCP_US_CENTRAL1_PROD
            </Checkbox>
            <Checkbox value="GCP_EU_WEST4_PROD">GCP_EU_WEST4_PROD</Checkbox>
            <Checkbox value="GCP_US_CENTRAL1_GSC_PROD">
              GCP_US_CENTRAL1_GSC_PROD
            </Checkbox>
          </Checkbox.Group>
        </Form.Item>

        <Form.Item
          label={
            <span>
              <AppstoreOutlined style={{ marginRight: 8 }} />
              Namespace Filter
            </span>
          }
          name="namespace"
          tooltip={{
            title: "List of namespaces (one per line)",
            icon: <InfoCircleOutlined />,
          }}
        >
          <Input.TextArea />
        </Form.Item>

        <Form.Item
          name="targets"
          label={
            <span>
              <DeploymentUnitOutlined style={{ marginRight: 8 }} />
              Deployment Target Names
            </span>
          }
          tooltip={{
            title: "List of deployment target names (one per line)",
            icon: <InfoCircleOutlined />,
          }}
          rules={[
            {
              required: true,
              message: "Please input the deployment names from a METADATA file",
            },
          ]}
        >
          <Input.TextArea />
        </Form.Item>

        <Form.Item
          name="allowRollback"
          label={
            <span>
              <RollbackOutlined style={{ marginRight: 8 }} />
              Allow Rollback
            </span>
          }
          valuePropName="checked"
        >
          <Checkbox>Allow rolling back to a previous git version</Checkbox>
        </Form.Item>

        <Form.Item
          name="shouldBypass"
          label={
            <span>
              <ThunderboltOutlined style={{ marginRight: 8 }} />
              Bypass
            </span>
          }
          valuePropName="checked"
        >
          <Checkbox>
            Run concurrently with scheduled and non-bypass adhoc deployments
          </Checkbox>
        </Form.Item>

        <Form.Item
          name="reason"
          label={
            <span>
              <EditOutlined style={{ marginRight: 8 }} />
              Business Reason
            </span>
          }
          tooltip={{
            title: "Please describe the reason for the adhoc deployment.",
            icon: <InfoCircleOutlined />,
          }}
          rules={[
            {
              required: true,
              message: "Please describe the reason for the adhoc deployment.",
            },
          ]}
        >
          <Input.TextArea />
        </Form.Item>

        <Form.Item
          name="confirm"
          label={
            <span>
              <CheckCircleOutlined style={{ marginRight: 8 }} />
              Confirmation
            </span>
          }
          valuePropName="checked"
          rules={[
            () => ({
              validator(_, value) {
                if (!value) {
                  return Promise.reject(
                    new Error("Please confirm that you have read the rules."),
                  );
                }
                return Promise.resolve();
              },
            }),
          ]}
        >
          <Checkbox>
            I have read and agree to the{" "}
            <Link href="https://www.notion.so/Runbook-Emergency-Deployment-ce59777e9e884a0f8ca495f6d3a4d7bf?pvs=4">
              rules for adhoc deployments
            </Link>
            .
          </Checkbox>
        </Form.Item>

        <Form.Item wrapperCol={{ offset: 10, span: 14 }}>
          <Button type="primary" htmlType="submit">
            Submit
          </Button>
        </Form.Item>
      </Form>
    </>
  );
}

function DeploymentsSchedulePageComponent() {
  const items = <DeploymentScheduleComponent />;

  return (
    <LayoutComponent
      children={items}
      selectedMenuKey={"deployments"}
      breadcrumbs={[
        { label: "Schedule Deployments", link: "/deployments/schedule" },
      ]}
    />
  );
}

export default DeploymentsSchedulePageComponent;
