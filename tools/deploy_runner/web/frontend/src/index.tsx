import React from "react";
import ReactDOM from "react-dom/client";
import "./index.css";
import { createBrowserRouter, RouterProvider } from "react-router-dom";
import ErrorPage from "./error-page";
import DeploymentsPageComponent from "./routes/deployments";
import DeploymentPageComponent from "./routes/deployment";
import DeploymentsSchedulePageComponent from "./routes/schedule_deployments";
import DeploymentsSearchPageComponent from "./routes/search_deployments";
import DeploymentTaskPageComponent from "./routes/deployment_task";
import KubernetesStatePageComponent from "./routes/kubernetes_state";
import CommitStatePageComponent from "./routes/commit_state";

// sets up the different routes
const router = createBrowserRouter([
  {
    path: "/",
    element: <DeploymentsPageComponent />,
    errorElement: <ErrorPage />,
  },
  {
    path: "deployments",
    element: <DeploymentsPageComponent />,
  },
  {
    path: "deployment/:deployId",
    element: <DeploymentPageComponent />,
  },
  {
    path: "deployment/:deployId/task/:targetName/:cloud/:namespace",
    element: <DeploymentTaskPageComponent />,
  },
  {
    path: "deployment/search",
    element: <DeploymentsSearchPageComponent />,
  },
  {
    path: "deployment/schedule",
    element: <DeploymentsSchedulePageComponent />,
  },
  {
    path: "kubernetes/state",
    element: <KubernetesStatePageComponent />,
  },
  {
    path: "commit/state",
    element: <CommitStatePageComponent />,
  },
]);

const root = ReactDOM.createRoot(document.getElementById("root")!);
root.render(
  <React.StrictMode>
    <RouterProvider router={router} />
  </React.StrictMode>,
);
