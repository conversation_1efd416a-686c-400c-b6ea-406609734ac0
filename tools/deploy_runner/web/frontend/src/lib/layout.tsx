// react component containing the overall layout of the pages
import React from "react";
import { Layout } from "antd";
import { Breadcrumb, Menu, theme } from "antd";
import { Link } from "react-router-dom";

const { <PERSON><PERSON>, Footer, Content } = Layout;

type BreadcrumbItem = {
  label: string;
  link: string;
};

type Probs = {
  // key of the menu item to select
  selectedMenuKey?: string;
  // react children nodes to display
  children?: React.ReactNode;
  // breadcrumb items to display
  breadcrumbs: BreadcrumbItem[];
};

export const LayoutComponent = ({
  children,
  selectedMenuKey,
  breadcrumbs,
}: Probs) => {
  const {
    token: { colorBgContainer },
  } = theme.useToken();
  let defaultSelectedKeys: Array<string> = [];
  if (selectedMenuKey) {
    defaultSelectedKeys = [selectedMenuKey];
  }

  return (
    <Layout className="layout">
      <Header>
        <div className="logo" />
        <Menu
          theme="dark"
          mode="horizontal"
          defaultSelectedKeys={defaultSelectedKeys}
          items={[
            {
              key: "home",
              label: <Link to={"/"}>Deployments</Link>,
            },
            {
              key: "schedule_deployments",
              label: (
                <Link to={"/deployment/schedule"}>Schedule Deployments</Link>
              ),
            },
            {
              key: "search_deployments",
              label: <Link to={"/deployment/search"}>Search Deployments</Link>,
            },
            {
              key: "kubernetes",
              label: <Link to={"/kubernetes/state"}>Kubernetes State</Link>,
            },
            {
              key: "commit_state",
              label: <Link to={"/commit/state"}>Commit State</Link>,
            },
          ]}
        />
      </Header>
      <Content style={{ padding: "0 50px" }}>
        <Breadcrumb style={{ margin: "16px 0" }}>
          <Breadcrumb.Item>
            <Link to={"/"}>Home</Link>
          </Breadcrumb.Item>
          {breadcrumbs.map((b) => {
            return (
              <Breadcrumb.Item key={b.label}>
                <Link to={b.link}>{b.label}</Link>
              </Breadcrumb.Item>
            );
          })}
        </Breadcrumb>
        <div
          className="site-layout-content"
          style={{ background: colorBgContainer }}
        >
          {children}
        </div>
      </Content>
      <Footer style={{ textAlign: "center" }}>Augment Code - Internal</Footer>
    </Layout>
  );
};
