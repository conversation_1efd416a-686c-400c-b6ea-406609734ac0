import axios, { AxiosError } from "axios";

export type DeploymentScheduleData = {
  branch: string | null;
  pullRequestNumber: string | null;
  commitRef: string;
  cloud: string[];
  namespace: string[];
  targets: string[];
  allowRollback: boolean;
  shouldBypass: boolean;
  reason: string;
};

export async function cancelDeployment(deployId: string): Promise<void> {
  await axios.post(`/api/deployment/${deployId}/cancel`);
}

export async function scheduleDeployment(
  scheduleData: DeploymentScheduleData,
): Promise<string> {
  const { data: response }: { data: any } = await axios.post(
    `/api/deployment`,
    scheduleData,
  );
  return response["deployId"];
}

export type AdhocDeploymentData = {
  requestor: string;
  reason: string;
};

export type DeploymentRequestData = {
  branch: string | null;
  pullRequestNumber: string | null;
  commitRef: string;
  clouds: string[];
  namespaces: string[];
  targetNames: string[];
  adhoc?: AdhocDeploymentData;
  deploymentTrack: string;
  deploymentScheduleName?: string;
};

export type DeploymentData = {
  deployId: string;
  request: DeploymentRequestData;
  state: string;
  createdTime: string;
  updatedTime: string;
  finishedTime: string;
};

export async function getScheduledDeploymentConfig(
  deploymentTrack: string,
): Promise<boolean> {
  const { data: response }: { data: any } = await axios.get(
    `/api/scheduled_deployment/${deploymentTrack}`,
  );
  return response.enabled;
}

export async function disableScheduledDeployment(
  deploymentTrack: string,
): Promise<void> {
  await axios.post(`/api/scheduled_deployment/${deploymentTrack}/disable`);
}

export async function enableScheduledDeployment(
  deploymentTrack: string,
): Promise<void> {
  await axios.post(`/api/scheduled_deployment/${deploymentTrack}/enable`);
}

export async function getDeploymentTracks(): Promise<string[]> {
  const { data: response }: { data: any } = await axios.get(
    `/api/scheduled_deployment_tracks`,
  );
  return response.tracks;
}

// returns detailed information about the given deployment
export async function getDeployment(
  deployId: string,
): Promise<DeploymentData | null> {
  try {
    const { data: response }: { data: any } = await axios.get(
      `/api/deployment/${deployId}`,
    );
    return response.deployment;
  } catch (e) {
    if (axios.isAxiosError(e)) {
      const ae = e as AxiosError;
      if (ae.response?.status === 404) {
        return null;
      } else {
        throw e;
      }
    } else {
      throw e;
    }
  }
}

export async function getDeployments(
  oldestDeployId: string | undefined,
  newestDeployId: string | undefined,
  maxCount: number | undefined,
): Promise<DeploymentData[]> {
  const { data: response }: { data: any } = await axios.get(
    `/api/deployments`,
    {
      params: {
        oldest_deploy_id: oldestDeployId,
        newest_deploy_id: newestDeployId,
        max_count: maxCount,
      },
    },
  );
  return response.deployments;
}

export type DeployInstanceStartedData = {
  commit: string;
};

export type DeployTask = {
  targetName: string;
  namespace: string;
  cloud: string;
};

export type DeployPlannedData = {
  task: DeployTask;
};

export type DeployStartedData = {
  task: DeployTask;
};

export type DeployFinishedData = {
  task: DeployTask;
  status: string;
  message: string;
};

export type DeployProgressData = {
  task: DeployTask;
  message: string;
};

export type DeployOutputData = {
  task: DeployTask;
  command: string[];
  stdout: string;
  stderr: string;
  returnCode: number;
};

export type DeployConfigurationData = {
  task: DeployTask;
  configuration: string;
};

export type ValidationFinished = {
  task: DeployTask;
  status: string;
  message: string;
};

export type DeployInstanceFinished = {
  results: {
    task: DeployTask;
    status: string;
  }[];
};

export type DeploymentEventData = {
  deployId: string;
  sequenceNumber: number;
  time: string;
  deployInstanceStarted?: DeployInstanceStartedData;
  deployInstanceFinished?: DeployInstanceFinished;
  deployPlanned?: DeployPlannedData;
  deployStarted?: DeployStartedData;
  deployFinished?: DeployFinishedData;
  deployOutput?: DeployOutputData;
  deployProgress?: DeployProgressData;
  validationFinished?: ValidationFinished;
  deployConfiguration?: DeployConfigurationData;
};

export function getDeployTask(
  event: DeploymentEventData,
): DeployTask | undefined {
  if (event.deployPlanned !== undefined) {
    return event.deployPlanned.task;
  } else if (event.deployStarted !== undefined) {
    return event.deployStarted.task;
  } else if (event.deployFinished !== undefined) {
    return event.deployFinished.task;
  } else if (event.deployOutput !== undefined) {
    return event.deployOutput.task;
  } else if (event.deployProgress !== undefined) {
    return event.deployProgress.task;
  } else if (event.validationFinished !== undefined) {
    return event.validationFinished.task;
  } else if (event.deployConfiguration !== undefined) {
    return event.deployConfiguration.task;
  } else {
    return undefined;
  }
}

export function getTargetName(task: DeployTask): string {
  return `${task.namespace}/${task.targetName}@${task.cloud}`;
}

export async function getDeploymentEvents(
  deployId: string,
  minSequenceNumber: number,
  limit: number,
): Promise<DeploymentEventData[] | null> {
  try {
    const { data: response }: { data: any } = await axios.get(
      `/api/deployment/${deployId}/events`,
      {
        params: {
          min_sequence_number: minSequenceNumber,
          limit: limit,
        },
      },
    );
    return response;
  } catch (e) {
    if (axios.isAxiosError(e)) {
      const ae = e as AxiosError;
      if (ae.response?.status === 404) {
        return null;
      } else {
        throw e;
      }
    } else {
      throw e;
    }
  }
}

export type ScheduleDeploymentState = {
  deployId: string;
  deploymentScheduleName: string;
  containsRef: boolean;
  deploymentTime: string;
  deploymentCommit: string;
};

export type CommitStateData = {
  isMerged: boolean;
  isKnownGood: boolean;
  scheduleDeploymentStates: ScheduleDeploymentState[];
};

export async function getCommitState(
  commitRef?: string,
  pullRequestNumber?: string,
): Promise<CommitStateData> {
  if (!commitRef && !pullRequestNumber) {
    throw new Error("Either commitRef or pullRequestNumber must be provided");
  }

  if (commitRef && pullRequestNumber) {
    throw new Error(
      "Only one of commitRef or pullRequestNumber should be provided",
    );
  }

  const params: any = {};
  if (commitRef) {
    params.commit_ref = commitRef;
  } else {
    params.pull_request_number = pullRequestNumber;
  }

  const { data: response }: { data: any } = await axios.get(
    `/api/commit_state`,
    { params },
  );
  return response;
}
