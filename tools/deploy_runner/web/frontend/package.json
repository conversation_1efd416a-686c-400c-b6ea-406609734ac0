{"name": "deploy-viewer", "version": "0.1.0", "private": true, "dependencies": {"@ant-design/icons": "^6.0.0", "@testing-library/jest-dom": "4.2.4", "@testing-library/react": "13.4.0", "@testing-library/user-event": "14.4.3", "antd": "^5.25.3", "axios": "^1.8.2", "dayjs": "^1.11.7", "lodash": "^4.17.21", "match-sorter": "^6.3.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.10.0", "react-use": "^17.5.0", "strip-ansi": "^7.0.1", "uuid": "^9.0.0"}, "devDependencies": {"@types/jest": "29.2.3", "@types/lodash": "^4.14.202", "@typescript-eslint/eslint-plugin": "5.44.0", "@typescript-eslint/parser": "5.44.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.38.0", "eslint-config-react-app": "^7.0.1", "jest-cli": "29.5.0", "jest-environment-jsdom": "29.3.1", "jest-junit": "16.0.0", "jest-transform-stub": "2.0.0", "typescript": "4.9.3", "vite": "5.4.19"}, "proxy": "http://localhost:5000", "scripts": {"start": "vite", "build": "vite build", "preview": "vite preview", "eslint": "git ls-files -- . | xargs pre-commit run eslint --files", "eslint:fix": "git ls-files -- . | xargs pre-commit run eslint --hook-stage=manual --files", "prettier": "git ls-files -- . | xargs pre-commit run prettier --files", "prettier:fix": "git ls-files -- . | xargs pre-commit run prettier --hook-stage=manual --files"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "engines": {"npm": "please-use-pnpm", "yarn": "please-use-pnpm", "pnpm": "9"}}