load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:python.bzl", "py_binary", "py_oci_image")

py_binary(
    name = "backend",
    srcs = glob(["*.py"]),
    data = [
        "//tools/deploy:auth_kube_config_yaml",
        "//tools/deploy_runner/web/frontend",
        "@gke-gcloud-auth-plugin//:all",
    ],
    main = "app.py",
    deps = [
        requirement("gunicorn"),
        requirement("prometheus_flask_exporter"),
        requirement("flask"),
        requirement("structlog"),
        requirement("protobuf"),
        requirement("python-dateutil"),
        "//base/cloud/iap:iap_py",
        "//base/cloud/k8s:kubectl_factory",
        "//base/cloud/k8s:kubernetes_client",
        "//base/flask_util:flask_util_py",
        "//base/logging:struct_logging",
        "//base/python/grpc:client_options",
        "//base/python/signal_handler",
        "//tools/deploy_runner/server:deploy_py_proto",
    ],
)

py_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":backend",
    trivy_allow_list = [
        "CVE-2024-34156",  # AU-4115
    ],
    visibility = ["//tools/deploy_runner:__subpackages__"],
)
