"""Finds deployment targets in a workspace."""

# pylint: disable=logging-fstring-interpolation
from contextlib import contextmanager
import fcntl
import logging
import os
import re
import shlex
import subprocess
import typing
from collections import defaultdict
from dataclasses import dataclass
from datetime import timedelta
from pathlib import Path

import yaml

from tools.bazel_runner.git import checkout_pb2
from tools.bazel_runner.web.client.client import BazelRunnerClient
import tools.deploy_runner.metadata_pb2 as metadata_pb2  # pylint: disable=no-name-in-module
from tools.deploy_runner import deploy_events_pb2
from base.cloud.k8s.kubectl import <PERSON>beObject, <PERSON>bectl, KubectlException
from base.cloud.k8s.kubectl_factory import KubectlFactory
from base.cloud.k8s.kubernetes_client import KubernetesClient
from tools.kubecfg.kubecfg_deletion import KubecfgDeletion
from tools.bazel_lib import bazel

CLOUDS = [
    "GCP_US_CENTRAL1_PROD",
    "GCP_EU_WEST4_PROD",
    "GCP_US_CENTRAL1_GSC_PROD",
    "GCP_US_CENTRAL1_DEV",
]

AGENT_CLOUDS = [
    "GCP_AGENT_US_CENTRAL1_PROD",
    "GCP_AGENT_EU_WEST4_PROD",
]


@dataclass
class DeploymentInfo:
    """Environment information for a deployment."""

    # the workspace to deploy
    workspace: Path

    kubernetes_client: KubernetesClient

    kubectl_factory: KubectlFactory

    bazel_runner_client: BazelRunnerClient | None

    checkout: checkout_pb2.CheckoutSpec | None

    # the user or pod that deployed the target
    deployed_by: str | None = None

    # extra startup args to pass to bazel
    extra_bazel_startup_args: str | None = None

    extra_bazel_args: str | None = None

    ram_limit_gb: int | None = None

    cpu_limit: int | None = None

    # if set to true, allow rollbacks
    # rollbacks here refer to deploying a git version that is older than the current one
    allow_rollback: bool = False

    @contextmanager
    def with_workspace_lock(self):
        """Lock the workspace."""
        file_path = self.workspace / ".deploy_lock"
        file_lock = file_path.open("a")

        try:
            fcntl.flock(file_lock.fileno(), fcntl.LOCK_EX)
            yield
        finally:
            fcntl.flock(file_lock.fileno(), fcntl.LOCK_UN)
            file_lock.close()


def form_bazel_command_line(
    cmd: str,
    deployment_info: DeploymentInfo,
    cmd_args: list[str],
    target_args: typing.Sequence[str] = (),
):
    """Form a bazel command line.

    Args:
        cmd: The bazel command to run.
        deployment_info: The deployment information.
        cmd_args: The arguments to the command.
        target_args: The arguments to the target.

    Returns:
        The command line.
    """
    ret = ["bazel"]
    if deployment_info.extra_bazel_startup_args:
        ret.extend(shlex.split(deployment_info.extra_bazel_startup_args))
    ret.append(cmd)
    ret.extend(str(c) for c in cmd_args)
    if cmd in ["build", "test", "run", "cquery", "aquery"]:
        if deployment_info.ram_limit_gb:
            ret.append(f"--local_ram_resources={deployment_info.ram_limit_gb*1024}")
        if deployment_info.cpu_limit:
            ret.append(f"--local_cpu_resources={deployment_info.cpu_limit}")
    if target_args:
        ret.append("--")
        ret.extend(str(c) for c in target_args)
    return ret


class DeployNotifier(typing.Protocol):
    """Notifier that sends notifications to the Slack bot."""

    def planned(self, event: deploy_events_pb2.DeployPlanned):
        """Send a planned event."""
        raise NotImplementedError()

    def started(self, event: deploy_events_pb2.DeployStarted):
        """Send a started event."""
        raise NotImplementedError()

    def finished(self, event: deploy_events_pb2.DeployFinished):
        """Send a finished event."""
        raise NotImplementedError()

    def validation_finished(self, event: deploy_events_pb2.ValidationFinished):
        """Send a validation_finished event."""
        raise NotImplementedError()

    def output(self, event: deploy_events_pb2.DeployOutput):
        """Send a output event."""
        raise NotImplementedError()

    def progress(self, event: deploy_events_pb2.DeployProgress):
        """Send a progress event."""
        raise NotImplementedError()

    def configuration(self, event: deploy_events_pb2.DeployConfiguration):
        """Send a configuration event."""
        raise NotImplementedError()

    def deploy_instance_started(self, event: deploy_events_pb2.DeployInstanceStarted):
        """Send a deploy_instance_started event."""
        raise NotImplementedError()

    def deploy_instance_finished(self, event: deploy_events_pb2.DeployInstanceFinished):
        """Send a deploy_instance_finished event."""
        raise NotImplementedError()

    def deploy_gate_started(self, event: deploy_events_pb2.DeployGateStarted):
        """Send a deploy_gate_started event."""
        raise NotImplementedError()

    def deploy_gate_finished(self, event: deploy_events_pb2.DeployGateFinished):
        """Send a deploy_gate_finished event."""
        raise NotImplementedError()


class DeployNullNotifier(DeployNotifier):
    """Notifier that does nothing."""

    def planned(self, event: deploy_events_pb2.DeployPlanned):
        """Send an event."""
        logging.info("planned: %s", event)

    def started(self, event: deploy_events_pb2.DeployStarted):
        """Send an event."""
        logging.info("started: %s", event)

    def finished(self, event: deploy_events_pb2.DeployFinished):
        """Send an event."""
        logging.info("finished: %s", event)

    def validation_finished(self, event: deploy_events_pb2.ValidationFinished):
        """Send an event."""
        logging.info("validation_finished: %s", event)

    def output(self, event: deploy_events_pb2.DeployOutput):
        """Send an event."""
        logging.info("output: %s", event)

    def progress(self, event: deploy_events_pb2.DeployProgress):
        """Send an event."""
        logging.info("progress: %s", event)

    def configuration(self, event: deploy_events_pb2.DeployConfiguration):
        """Send an event."""
        logging.info("configuration: %s", event)

    def deploy_instance_started(self, event: deploy_events_pb2.DeployInstanceStarted):
        """Send an event."""
        logging.info("deploy_instance_started: %s", event)

    def deploy_instance_finished(self, event: deploy_events_pb2.DeployInstanceFinished):
        """Send an event."""
        logging.info("deploy_instance_finished: %s", event)

    def deploy_gate_started(self, event: deploy_events_pb2.DeployGateStarted):
        """Send an event."""
        logging.info("deploy_gate_started: %s", event)

    def deploy_gate_finished(self, event: deploy_events_pb2.DeployGateFinished):
        """Send an event."""
        logging.info("deploy_gate_finished: %s", event)


class TargetFilter(typing.Protocol):
    """Filter for targets."""

    def filter(
        self,
        task: metadata_pb2.KubeCfgTask | metadata_pb2.BazelDeployment,
        deployment_type: str,
        deployment_schedule_name: metadata_pb2.Deployment.DeploymentScheduleName.ValueType,
    ) -> bool:
        """Filter the targets."""
        raise NotImplementedError()


class AcceptAllFilter(TargetFilter):
    """Accept all targets."""

    def filter(
        self,
        task: metadata_pb2.KubeCfgTask | metadata_pb2.BazelDeployment,
        deployment_type: str,
        deployment_schedule_name: metadata_pb2.Deployment.DeploymentScheduleName.ValueType,
    ) -> bool:
        """Accept all targets."""
        del task, deployment_type, deployment_schedule_name
        return True


class MatchingTargetFilter(TargetFilter):
    """Filter for targets via regex."""

    def __init__(
        self,
        cloud_filter: str | None,
        namespace_filter: str | None,
        env_filter: str | None,
        deployment_type_filter: str | None,
        deployment_schedule_name: metadata_pb2.Deployment.DeploymentScheduleName.ValueType
        | None = None,
    ):
        self.cloud_filter = cloud_filter
        self.namespace_filter = namespace_filter
        self.env_filter = env_filter
        self.deployment_type_filter = deployment_type_filter
        self.deployment_schedule_name = deployment_schedule_name

    def filter(
        self,
        task: metadata_pb2.KubeCfgTask | metadata_pb2.BazelDeployment,
        deployment_type: str,
        deployment_schedule_name: metadata_pb2.Deployment.DeploymentScheduleName.ValueType,
    ) -> bool:
        logging.debug("Filter: %s", task)
        if isinstance(task, metadata_pb2.BazelDeployment):
            if self.deployment_type_filter:
                if not re.match(self.deployment_type_filter, deployment_type):
                    logging.debug(
                        "Deployment type filter %s does not match %s",
                        self.deployment_type_filter,
                        task,
                    )
                    return False
            if (
                self.deployment_schedule_name is not None
                and self.deployment_schedule_name != deployment_schedule_name
            ):
                logging.debug("Deployment train filter does not match %s", task)
                return False
            return True
        if self.cloud_filter:
            # cloud not by regex as they are only two valid values
            if (
                metadata_pb2.KubeCfgTask.Cloud.Name(task.cloud)
                != self.cloud_filter.upper()
            ):
                logging.debug(
                    "Cloud filter %s does not match %s", self.cloud_filter, task
                )
                return False
        if self.namespace_filter:
            if not re.match(self.namespace_filter, task.namespace):
                logging.debug(
                    "Namespace filter %s does not match %s", self.namespace_filter, task
                )
                return False
        if self.env_filter:
            if not re.match(
                self.env_filter, metadata_pb2.KubeCfgTask.Env.Name(task.env)
            ):
                logging.debug("Env filter %s does not match %s", self.env_filter, task)
                return False

        if self.deployment_type_filter:
            if not re.match(self.deployment_type_filter, deployment_type):
                logging.debug(
                    "Deployment type filter %s does not match %s",
                    self.deployment_type_filter,
                    task,
                )
                return False
        if (
            self.deployment_schedule_name is not None
            and self.deployment_schedule_name != deployment_schedule_name
        ):
            logging.debug("Deployment train filter does not match %s", task)
            return False
        logging.debug("Target filter accepted %s", task)
        return True


class EqualityTargetFilter(TargetFilter):
    """Filter for targets via equality."""

    def __init__(
        self,
        cloud: list[str],
        namespace: list[str],
        env: list[str],
        deployment_type: list[str],
        deployment_schedule_name: metadata_pb2.Deployment.DeploymentScheduleName.ValueType
        | None = None,
    ):
        self.cloud = cloud
        self.namespace = namespace
        self.env = env
        self.deployment_type = deployment_type
        self.deployment_schedule_name = deployment_schedule_name

    def filter(
        self,
        task: metadata_pb2.KubeCfgTask | metadata_pb2.BazelDeployment,
        deployment_type: str,
        deployment_schedule_name: metadata_pb2.Deployment.DeploymentScheduleName.ValueType,
    ) -> bool:
        logging.debug("Filter: %s", task)
        if isinstance(task, metadata_pb2.BazelDeployment):
            if self.deployment_type:
                if deployment_type not in self.deployment_type:
                    logging.debug(
                        "Deployment type filter %s does not match %s",
                        self.deployment_type,
                        task,
                    )
                    return False
            if (
                self.deployment_schedule_name is not None
                and self.deployment_schedule_name != deployment_schedule_name
            ):
                logging.debug("Deployment train filter does not match %s", task)
                return False
            return True
        if self.cloud:
            # cloud not by regex as they are only two valid values
            if metadata_pb2.KubeCfgTask.Cloud.Name(task.cloud) not in self.cloud:
                logging.debug("Cloud filter %s does not match %s", self.cloud, task)
                return False
        if self.namespace:
            if task.namespace not in self.namespace:
                logging.debug(
                    "Namespace filter %s does not match %s", self.namespace, task
                )
                return False
        if self.env:
            if metadata_pb2.KubeCfgTask.Env.Name(task.env) not in self.env:
                logging.debug("Env filter %s does not match %s", self.env, task)
                return False
        if self.deployment_type:
            if deployment_type not in self.deployment_type:
                logging.debug(
                    "Deployment type filter %s does not match %s",
                    self.deployment_type,
                    task,
                )
                return False
        if (
            self.deployment_schedule_name is not None
            and self.deployment_schedule_name != deployment_schedule_name
        ):
            logging.debug("Deployment train filter does not match %s", task)
            return False
        logging.debug("Target filter accepted %s", task)
        return True


class DeployTarget(typing.Protocol):
    """A deployment target."""

    def deploy(
        self,
        dry_run: bool,
        notifier: DeployNotifier,
    ) -> bool | None:
        """Run the deployment Bazel target.

        Usually, this will run an K8S apply target, but it can perform
        other operations that logically are deployments, e.g. upload a file to S3.

        Returns True if the deployment succeeded, False otherwise. None if the operation was skipped
        """
        raise NotImplementedError()

    def validate(self, notifier: DeployNotifier) -> bool:
        """Validates a prior deployment.

        This function can be called after a suceeding deploy call to validate the deployment.
        """
        raise NotImplementedError()

    def test(self) -> bool:
        """Tests a deployment target."""
        raise NotImplementedError()

    @property
    def name(self) -> str:
        """Return the descriptive name of the deployment target."""
        raise NotImplementedError()

    @property
    def target_name(self) -> str:
        """Return the target name of the deployment target."""
        raise NotImplementedError()

    @property
    def priority(self) -> int:
        """Return the priority of the deployment target."""
        raise NotImplementedError()

    @property
    def namespace(self) -> str | None:
        """Return the namespace of the deployment target."""
        raise NotImplementedError()

    @property
    def env(self) -> metadata_pb2.KubeCfgTask.Env.ValueType | None:
        """Return the env of the deployment target."""
        raise NotImplementedError()

    @property
    def cloud(self) -> metadata_pb2.KubeCfgTask.Cloud.ValueType | None:
        """Return the cloud of the deployment target."""
        raise NotImplementedError()


class BazelDeployTarget(DeployTarget):
    """A deployment target that runs a Bazel target."""

    def __init__(
        self,
        deployment_info: DeploymentInfo,
        target: metadata_pb2.Deployment,
    ):
        self.deployment_info = deployment_info
        self.target = target
        assert target.bazel

    @property
    def name(self):
        return self.target.name

    @property
    def target_name(self):
        return self.target.name

    @property
    def priority(self):
        return self.target.priority

    @property
    def namespace(self):
        return None

    @property
    def env(self):
        return None

    @property
    def cloud(self):
        return None

    def __repr__(self):
        return f"BazelDeployTarget({self.target})"

    def deploy(
        self,
        dry_run: bool,
        notifier: DeployNotifier,
    ) -> bool | None:
        """Run the deployment Bazel target.

        Usually, this will run an K8S apply target, but it can perform
        other operations that logically are deployments, e.g. upload a file to S3.

        Returns True if the deployment succeeded, False otherwise. None if the operation was skipped
        """
        logging.info(f"Deploying '{self.target.name}'")
        notifier.started(
            deploy_events_pb2.DeployStarted(
                task=deploy_events_pb2.DeployTask(target_name=self.target_name),
            )
        )

        with self.deployment_info.with_workspace_lock():
            cmd = form_bazel_command_line(
                cmd="run",
                deployment_info=self.deployment_info,
                cmd_args=["-c", "opt", self.target.bazel.target]
                + shlex.split(self.deployment_info.extra_bazel_args or ""),
                target_args=list(self.target.bazel.arguments),
            )
            logging.info(f"{shlex.join(cmd)}")
            if dry_run:
                return True
            r = subprocess.run(
                cmd,
                cwd=self.deployment_info.workspace,
                capture_output=True,
                env=bazel.get_bazel_env(),
                check=False,
                encoding="utf-8",
            )
        notifier.output(
            deploy_events_pb2.DeployOutput(
                task=deploy_events_pb2.DeployTask(target_name=self.target_name),
                command=cmd,
                stdout=r.stdout,
                stderr=r.stderr,
                return_code=r.returncode,
            )
        )
        if r.returncode:
            logging.warning(f"Deployment {self.target.name} failed")
            notifier.finished(
                deploy_events_pb2.DeployFinished(
                    task=deploy_events_pb2.DeployTask(target_name=self.target_name),
                    status=deploy_events_pb2.DeployStatus.DEPLOY_STATUS_FAILED,
                    message=f"Deployment failed with return code {r}",
                )
            )
            return False
        notifier.finished(
            deploy_events_pb2.DeployFinished(
                task=deploy_events_pb2.DeployTask(target_name=self.target_name),
                status=deploy_events_pb2.DeployStatus.DEPLOY_STATUS_SUCCESS,
            )
        )
        return True

    def test(self):
        assert self.target.bazel
        if not self.target.bazel.target:
            logging.error("No bazel target")
            return False
        t = self.target.bazel.target
        if not t.startswith("//"):
            logging.error("No a valid absolute bazel target")
            return False
        t = t[2:]
        t = t.replace(":", "/")

        # we don't know the exact file name, so we try both common cases
        found = False
        if Path(t).exists():
            found = True
        t += ".sh"
        if Path(t).exists():
            found = True
        if not found:
            logging.error("Missing or invalid bazel target")
            return False

        return True

    def validate(self, notifier: DeployNotifier) -> bool:
        return True


@dataclass
class Rollout:
    """A rollout."""

    name: str
    namespace: str
    timeout: timedelta


def get_deployment_rollouts(
    output_file: Path, default_timeout: timedelta
) -> typing.Iterable[Rollout]:
    """Return the deployment names from the given output file."""
    yaml_output = output_file.read_text(encoding="utf-8")
    d = list(yaml.safe_load_all(yaml_output))
    for item in d:
        if item.get("kind") == "Deployment":
            logging.debug(f"Found deployment {item['metadata']['name']}")
            timeout = default_timeout
            if "spec" in item and "progressDeadlineSeconds" in item["spec"]:
                logging.debug(
                    "Found timeout: %s", item["spec"]["progressDeadlineSeconds"]
                )
                timeout = timedelta(seconds=item["spec"]["progressDeadlineSeconds"])
            yield Rollout(
                item["metadata"]["name"],
                item["metadata"].get("namespace"),
                timeout=timeout,
            )


class KubecfgTombstoneDeployTarget(DeployTarget):
    """A deployment target that runs a KubecfgTombstone target."""

    def __init__(
        self,
        deployment_info: DeploymentInfo,
        target: metadata_pb2.Deployment,
        task: metadata_pb2.KubeCfgTask,
        kubectl: Kubectl,
    ):
        self.deployment_info = deployment_info
        self.target = target
        self.task = task
        assert target.kubecfg_tombstone
        self.deleter = KubecfgDeletion(kubectl)
        self.deploy_task = deploy_events_pb2.DeployTask(
            target_name=self.target.name,
            namespace=task.namespace or "",
            cloud=metadata_pb2.KubeCfgTask.Cloud.Name(task.cloud) if task.cloud else "",
        )

    @property
    def target_name(self):
        return self.target.name

    @property
    def name(self):
        if self.task.namespace:
            return f"{self.task.namespace}/{self.target.name}@{metadata_pb2.KubeCfgTask.Cloud.Name(self.task.cloud)}"
        else:
            return f"/{self.target.name}@{metadata_pb2.KubeCfgTask.Cloud.Name(self.task.cloud)}"

    @property
    def priority(self):
        return self.target.priority

    @property
    def namespace(self):
        if self.task.namespace:
            return self.task.namespace
        return None

    @property
    def env(self):
        if self.task.env:
            return self.task.env
        return None

    @property
    def cloud(self):
        return self.task.cloud

    def __repr__(self):
        return f"KubecfgTombstoneDeployTarget({self.target, self.task})"

    def find(self) -> list[KubeObject]:
        """Find all objects that should be deleted."""
        results = []
        for obj in self.target.kubecfg_tombstone.object:
            results.extend(
                self.deleter.find(
                    api_version=obj.api_version,
                    kind=obj.kind,
                    name=obj.name,
                    namespace=self.task.namespace,
                )
            )
        return results

    def apply_task(
        self,
        dry_run: bool,
        notifier: DeployNotifier,
    ) -> bool:
        """Run the deployment Bazel target for tombstones.

        A tombstone is a list of apps or objects that should be deleted.
        """
        assert self.target.kubecfg_tombstone

        found = False

        for obj in self.target.kubecfg_tombstone.object:
            logging.info("Found tombstoned object %s", obj)
            r = self.deleter.delete(
                api_version=obj.api_version,
                kind=obj.kind,
                name=obj.name,
                namespace=self.task.namespace,
                dry_run=dry_run,
            )
            for output in r.kubecfg_outputs:
                notifier.output(
                    deploy_events_pb2.DeployOutput(
                        task=self.deploy_task,
                        command=output.command or [],
                        stdout=output.stdout,
                        stderr=output.stderr,
                        return_code=output.returncode,
                    )
                )
            if r.deleted:
                found = True
        return found

    def deploy(
        self,
        dry_run: bool,
        notifier: DeployNotifier,
    ) -> bool | None:
        """Run the deployment Bazel target.

        Returns True if the deployment succeeded, False otherwise. None if the operation was skipped
        """
        notifier.started(
            deploy_events_pb2.DeployStarted(
                task=self.deploy_task,
            )
        )
        logging.info(f"Deleting '{self.name}'")
        cloud = metadata_pb2.KubeCfgTask.Cloud.Name(self.task.cloud)
        self.deployment_info.kubernetes_client.login(cloud)
        try:
            if not self.apply_task(dry_run, notifier):
                notifier.finished(
                    deploy_events_pb2.DeployFinished(
                        task=self.deploy_task,
                        status=deploy_events_pb2.DeployStatus.DEPLOY_STATUS_SKIPPED,
                    )
                )
                return None

            notifier.finished(
                deploy_events_pb2.DeployFinished(
                    task=self.deploy_task,
                    status=deploy_events_pb2.DeployStatus.DEPLOY_STATUS_SUCCESS,
                )
            )
            return True
        except Exception as ex:  # pylint: disable=broad-except
            notifier.finished(
                deploy_events_pb2.DeployFinished(
                    task=self.deploy_task,
                    status=deploy_events_pb2.DeployStatus.DEPLOY_STATUS_FAILED,
                    message=f"Deployment failed: {ex}",
                )
            )
            logging.exception(ex)
            logging.error(f"Failure during deployment '{self.name}'")
            if isinstance(ex, KubectlException):
                # stderr seems to be more useful and less spammy, but we should
                # change this if that's not true
                logging.debug(f"stdout: {ex.stdout}")
                logging.error(f"stderr: {ex.stderr}")
            return False

    def test(self):
        assert self.target.kubecfg_tombstone
        if not self.target.kubecfg_tombstone.object:
            logging.error("No kubecfg tombstone")
            return False
        for obj in self.target.kubecfg_tombstone.object:
            if not obj.name:
                logging.error("No kubecfg tombstone name")
                return False
            if not obj.kind:
                logging.error("No kubecfg tombstone kind")
                return False
            if obj.kind == "app":
                if not obj.name:
                    logging.error("No kubecfg tombstone app name")
                    return False
            else:
                if not obj.api_version:
                    logging.error("No kubecfg tombstone api version")
                    return False
                if not obj.name:
                    logging.error("No kubecfg tombstone name")
                    return False
        return True

    def validate(self, notifier: DeployNotifier) -> bool:
        return True


@dataclass
class DeployTargetGroup:
    """A group of targets to deploy."""

    targets: list[DeployTarget]
    env: metadata_pb2.KubeCfgTask.Env.ValueType | None
    priority: int


def _group_targets(
    targets: list[DeployTarget],
) -> list[DeployTargetGroup]:
    """Groups the targets by environment."""

    last_target = None
    current_target_group = None
    grouped_targets = []
    for target in targets:  # type: ignore
        if last_target and (
            last_target.env != target.env or last_target.priority != target.priority
        ):
            if current_target_group:
                grouped_targets.append(current_target_group)
                current_target_group = None
        if current_target_group is None:
            current_target_group = DeployTargetGroup(
                targets=[], env=target.env, priority=target.priority
            )
        current_target_group.targets.append(target)
        last_target = target
    if current_target_group:
        grouped_targets.append(current_target_group)
    return grouped_targets


def order_targets(targets: list[DeployTarget]) -> list[DeployTargetGroup]:
    """Groups the targets by environment."""
    target_by_env = defaultdict(list)
    for target in targets:
        target_by_env[target.env].append(target)

    key = key = lambda x: (x.priority, x.namespace or "", x.name)

    none_targets = []
    for target in target_by_env[None]:
        none_targets.append(target)
    none_targets = sorted(none_targets, key=key, reverse=True)

    dev_targets = []
    for target in target_by_env[metadata_pb2.KubeCfgTask.ENV_NOT_SET]:
        dev_targets.append(target)
    dev_targets = sorted(dev_targets, key=key, reverse=True)

    staging_targets = []
    for target in target_by_env[metadata_pb2.KubeCfgTask.STAGING]:
        staging_targets.append(target)
    staging_targets = sorted(staging_targets, key=key, reverse=True)

    prod_targets = []
    for target in target_by_env[metadata_pb2.KubeCfgTask.PROD]:
        prod_targets.append(target)
    prod_targets = sorted(prod_targets, key=key, reverse=True)

    result = []
    result.extend(_group_targets(none_targets))
    result.extend(_group_targets(dev_targets))
    result.extend(_group_targets(staging_targets))
    result.extend(_group_targets(prod_targets))
    return result
