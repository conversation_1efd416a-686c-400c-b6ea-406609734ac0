"""A METADATA file contains configuration about a piece of software, e.g. how it should be deployed."""

import logging
import subprocess
import sys
from pathlib import Path
from typing import List, Optional

from google.protobuf import json_format, text_format

from tools.deploy_runner import metadata_pb2

JSONNET_BIN = "../jsonnet_go~/cmd/jsonnet/jsonnet_/jsonnet"


def _get_jsonnet_path() -> str:
    """Returns the path to the jsonnet binary."""
    return JSONNET_BIN


def _run_jsonnet(args):
    """Runs jsonnet with the given arguments.

    Args:
      args: List of arguments to pass to jsonnet.

    Returns:
      The stdout of the jsonnet process.

    Raises:
      ValueError: If the jsonnet process returns a non-zero exit code.
    """
    cmd = [_get_jsonnet_path()] + args
    logging.debug("cmd %s", cmd)
    return subprocess.run(
        cmd,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        encoding="utf-8",
        check=False,
    )


def parse_metadata_file(metadata_path: Path) -> Optional[metadata_pb2.Metadata]:  # type: ignore
    """Parses a metadata file."""
    try:
        metadata = text_format.Parse(
            metadata_path.read_text(encoding="utf-8"), metadata_pb2.Metadata()
        )
        return metadata
    except text_format.ParseError as e:
        print("Failed to parse", metadata_path, file=sys.stderr)
        print(e, file=sys.stderr)
        # we ignore any parse errors so that a single invalid metadata file doesn't stop
        # all deployments
        return None


def parse_metadata_jsonnet_file(
    metadata_path: Path, imports_path: Path
) -> Optional[metadata_pb2.Metadata]:  # type: ignore
    """Parses a metadata file in jsonnet format."""
    logging.debug("Parsing %s", metadata_path)
    p = None
    try:
        p = _run_jsonnet(["-J", str(imports_path), str(metadata_path)])
        if p.returncode != 0:
            logging.warning("Failed to parse %s: %s", metadata_path, p.stderr)
            return None

        metadata = json_format.Parse(p.stdout, metadata_pb2.Metadata())
        logging.debug("Parsed %s", metadata)
        return metadata
    except json_format.ParseError as e:
        logging.exception(e)
        logging.warning("Failed to parse %s", metadata_path)
        # we ignore any parse errors so that a single invalid metadata file doesn't stop
        # all deployments
        return None


def find_metadata(workspace: Path) -> List[metadata_pb2.Metadata]:  # type: ignore
    """Finds all metadata files in a workspace."""
    # map from targets to deployments
    result = []
    for metadata_path in workspace.glob("**/METADATA"):
        t = parse_metadata_file(metadata_path)
        if t:
            result.append(t)
    for metadata_path in workspace.glob("**/METADATA.jsonnet"):
        # For simplicity, allow metadata to import from anywhere in the workspace.
        t = parse_metadata_jsonnet_file(metadata_path, workspace)
        if t:
            result.append(t)
    return result


def read_metadata_file(metadata_path: Path) -> metadata_pb2.Metadata | None:
    """Reads a metadata file.

    Args:
        metadata_path: The path to the metadata file.

    Returns:
        The parsed metadata file or None if the file could not be parsed.
    """
    if metadata_path.name.endswith(".jsonnet"):
        result = parse_metadata_jsonnet_file(metadata_path, Path("."))
    else:
        result = parse_metadata_file(metadata_path)
    return result
