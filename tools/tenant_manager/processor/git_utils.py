import argparse
import dataclasses
import logging
import os
import pathlib
import random
import re
import sqlite3
import uuid

import git
import github
from dataclasses_json import dataclass_json
from git import GitCommandError, Repo

from tools.bazel_runner.git import app_token
from tools.tenant_manager.processor.db import Database
from deploy.tenants.tenants_util import get_tenant_string


@dataclass_json
@dataclasses.dataclass
class GitUtilsConfig:
    """Information needed for git utils to do its job."""

    github_secret_path: str
    repo_url: str
    repo_dir: str
    pr_approvers: list[str]
    main_branch: str
    us_namespaces: list[str]
    db_path: str
    # Tier options from config, defined in deploy/common/lib.jsonnet
    tier_options: list[str]


class TenantAdder:
    """Class to add a tenant and create a PR."""

    def __init__(self, config: GitUtilsConfig, db: Database):
        self.config = config
        self.token_source = app_token.GitHubAppTokenSource.from_directory(
            pathlib.Path(config.github_secret_path)
        )
        self.db = db

        os.environ["GIT_ASKPASS"] = os.path.abspath(
            "tools/tenant_manager/processor/echo_token.sh"
        )
        os.environ["GIT_USERNAME"] = "x-access-token"
        self._refresh_gh_token()

        repo_dir = self.config.repo_dir
        repo_url = self.config.repo_url

        if not os.path.exists(repo_dir):
            os.makedirs(repo_dir, exist_ok=True)

        if not os.path.exists(os.path.join(repo_dir, ".git")):
            logging.info("Cloning repository from %s to %s", repo_url, repo_dir)
            Repo.clone_from(repo_url, repo_dir)

        self.repo = Repo(self.config.repo_dir)
        self.last_ns_index = random.randint(0, len(self.config.us_namespaces) - 1)

    def _get_abs_path(self, path) -> str:
        return os.path.join(self.config.repo_dir, path)

    def _refresh_gh_token(self) -> None:
        token = self.token_source.get_token()
        os.environ["GIT_PASSWORD"] = token

    def _clean_nested_refs(self, branch: str) -> None:
        """Clean up any nested refs that could conflict with the given branch."""
        try:
            # List all refs that start with the branch path
            refs = self.repo.git.for_each_ref(
                f"refs/remotes/origin/{branch}/"
            ).splitlines()
            for ref in refs:
                # Each line is in format: '<sha> <type> <refname>'
                ref_name = ref.split()[-1]
                self.repo.git.update_ref("-d", ref_name)
        except git.GitCommandError:
            pass  # Ignore errors during cleanup

    def _clean_all_nested_refs(self) -> None:
        """Clean up all nested refs that could cause conflicts."""
        try:
            # List all remote refs
            refs = self.repo.git.for_each_ref("refs/remotes/origin/").splitlines()
            processed_branches = set()

            for ref in refs:
                ref_name = ref.split()[-1]
                # Extract the first part of the branch name (e.g., 'nikita/AU-5573' from 'refs/remotes/origin/nikita/AU-5573/remove-defunct-i0')
                branch = "/".join(
                    ref_name.split("/")[3:-1]
                )  # Skip refs/remotes/origin/
                if branch and branch not in processed_branches:
                    self._clean_nested_refs(branch)
                    processed_branches.add(branch)
        except git.GitCommandError:
            pass  # Ignore errors during cleanup

    def _update_branch(self, branch: str) -> None:
        """Update the given branch from remote."""
        try:
            # Clean up any nested refs before fetching
            self._clean_nested_refs(branch)
            # Clean up stale remote tracking branches using git command directly
            self.repo.git.remote("prune", "origin")
            # Fetch from remote
            self.repo.remotes.origin.fetch()

            if branch in self.repo.heads:
                self.repo.git.checkout(branch)
                result = self.repo.git.pull("origin", branch)
                logging.debug(f"Pull {branch} result: {result}")
            elif f"origin/{branch}" in self.repo.refs:
                self.repo.git.checkout(branch)
                result = self.repo.git.pull("origin", branch)
                logging.debug(f"Pull {branch} result: {result}")
            else:
                raise Exception(f"Branch {branch} does not exist")
        except GitCommandError as git_error:
            logging.error(
                f"Git command error: {git_error.command}, {git_error.status}, {git_error.stderr}"
            )
            raise Exception(
                f"Failed to fetch and checkout branch {branch}: {git_error.stderr}"
            )
        except Exception as e:
            logging.error(f"Unexpected error in _update_branch: {str(e)}")
            raise Exception(f"Failed to fetch and checkout branch {branch}: {str(e)}")

    def _create_branch(self, branch: str) -> None:
        """Creates a new branch."""
        try:
            # Try to delete the branch locally if it exists
            try:
                self.repo.git.branch("-D", branch)
                logging.info(f"Deleted local branch: {branch}")
            except git.GitCommandError:
                pass  # Ignore if local branch doesn't exist

            # Check if remote branch exists before trying to delete it
            remote_refs = [ref.name for ref in self.repo.remote().refs]
            if f"origin/{branch}" in remote_refs:
                self.repo.git.push("origin", "--delete", branch)
                logging.info(f"Deleted remote branch: {branch}")

            # Create a new branch
            self.repo.remotes.origin.pull()
            self.repo.git.checkout("-b", branch)
            logging.info(f"Created new branch: {branch}")

        except GitCommandError as git_error:
            logging.error(
                f"Git command error: {git_error.command}, {git_error.status}, {git_error.stderr}"
            )
            raise Exception(f"Failed to manage branch {branch}: {git_error.stderr}")
        except Exception as e:
            logging.error(f"Unexpected error in _create_branch: {str(e)}")
            raise Exception(f"Failed to fetch and checkout branch {branch}: {str(e)}")

    def _db_tenant_exists(self, tenant_name, domains) -> bool:
        return self.db.check_tenant_or_domains_exist(tenant_name, domains)

    def _db_add_tenant(self, tenant_name, namespace, domains) -> None:
        self.db.insert_tenant(tenant_name, domains)

    def _commit_changes(self, file_path, commit_message) -> git.Commit:
        try:
            self.repo.git.add(file_path)
            self.repo.index.commit(commit_message)
        except Exception as e:
            raise Exception(f"Failed to commit changes: {str(e)}")
        return self.repo.head.commit

    def _push_branch(self, branch_name: str) -> None:
        try:
            try:
                pull_output = self.repo.git.pull(
                    "origin", branch_name, with_extended_output=True
                )
                logging.debug(f"Pull output: {pull_output}")
            except git.GitCommandError as pull_error:
                if "couldn't find remote ref" in str(pull_error):
                    logging.info(
                        f"Remote branch {branch_name} doesn't exist. Skipping pull."
                    )
                else:
                    raise

            push_output = self.repo.git.push(
                "origin", branch_name, with_extended_output=True
            )
            logging.debug(f"Push output: {push_output}")
        except git.GitCommandError as e:
            logging.error(f"Git command error: {e.command}, {e.status}, {e.stderr}")
            raise Exception(f"Failed to push branch {branch_name}: {str(e)}")
        except Exception as e:
            logging.error(f"Unexpected error in _push_branch: {str(e)}")
            raise Exception(f"Failed to push branch {branch_name}: {str(e)}")

    def _create_pull_request(self, pr_title, pr_body, branch_name) -> None:
        try:
            # Create a GitHub instance using the token
            g = github.Github(self.token_source.get_token())

            # Get the GitHub repository
            github_repo = g.get_repo(
                f"{self.config.repo_url.split('/')[-2]}/{self.config.repo_url.split('/')[-1].replace('.git', '')}"
            )
            logging.info(f"GitHub repo: {github_repo}")

            # Create the pull request
            pull_request = github_repo.create_pull(
                title=pr_title,
                body=pr_body,
                head=branch_name,
                base="main",
            )
            logging.info(f"Pull request: {pull_request}")

            # Add approvers as reviewers to the pull request
            if self.config.pr_approvers:
                pull_request.create_review_request(reviewers=self.config.pr_approvers)
                logging.info(f"Added reviewers: {self.config.pr_approvers}")

            logging.info(f"Successfully created pull request: {pull_request.html_url}")
        except github.GithubException as e:
            logging.error(f"GitHub API error: {e.status}, {e.data}")
            raise Exception(
                f"Failed to create pull request: {e.data.get('message', str(e))}"
            )
        except Exception as e:
            logging.error(f"Unexpected error in _create_pull_request: {str(e)}")
            raise Exception(f"Failed to create pull request: {str(e)}")

    def _kubectl_apply_tenant(self, tenant_name, namespace, domains):
        return NotImplemented

    def _add_tenant_file(
        self, tenant_name, namespace, domains, tier="ENTERPRISE"
    ) -> str:
        # Validate tier against allowed options
        if tier not in self.config.tier_options:
            logging.warning(f"Invalid tier: {tier}, using default ENTERPRISE")
            tier = "ENTERPRISE"

        domain = domains[0] if domains else ""
        args = argparse.Namespace(
            name=tenant_name,
            namespace=namespace,
            domain=domain,
            tenant_type=tier,
        )
        definition = get_tenant_string(args) + "\n"

        # Trim two spaces from start of each line
        definition = re.sub(r"^  ", "", definition, flags=re.MULTILINE)

        filename = f"deploy/tenants/prod_tenants/{tenant_name}.jsonnet"
        path = self._get_abs_path(filename)
        try:
            os.makedirs(os.path.dirname(path), exist_ok=True)
            with open(path, "w") as f:
                f.write(definition)
        except Exception as e:
            raise Exception(
                f"Failed to write tenant definition to {filename}: {str(e)}"
            )
        return filename

    def _get_tenants(self) -> list[str]:
        tenant_names = []
        prod_tenants_dir = self._get_abs_path("deploy/tenants/prod_tenants")
        tenants_file_path = self._get_abs_path("deploy/tenants/tenants.jsonnet")

        tenant_name_pattern = re.compile(r'^\s*name:\s*[\'"]([^\'"]+)[\'"],?\s*$')

        # Get tenants from tenants.jsonnet
        try:
            with open(tenants_file_path, "r") as file:
                for line in file:
                    match = tenant_name_pattern.match(line)
                    if match:
                        name = match.group(1)
                        tenant_names.append(name)
        except FileNotFoundError:
            logging.error(f"tenants.jsonnet file not found at {tenants_file_path}")
        except Exception as e:
            logging.error(f"Error reading tenants.jsonnet: {str(e)}")

        # Get tenants from prod_tenants directory
        try:
            for filename in os.listdir(prod_tenants_dir):
                if filename.endswith(".jsonnet"):
                    tenant_name = filename[:-8]  # Remove ".jsonnet" from the filename
                    tenant_names.append(tenant_name)
        except FileNotFoundError:
            logging.error(f"prod_tenants directory not found at {prod_tenants_dir}")
        except Exception as e:
            logging.error(f"Error reading prod_tenants directory: {str(e)}")

        return tenant_names

    def _get_domains(self) -> list[str]:
        domains = []
        prod_tenants_dir = self._get_abs_path("deploy/tenants/prod_tenants")
        tenants_file_path = self._get_abs_path("deploy/tenants/tenants.jsonnet")

        domain_pattern = re.compile(r"^\s*domain:\s*[\"']([^\"']+)[\"'],?\s*$")
        email_domains_pattern = re.compile(
            r"^\s*email_address_domains:\s*\[(.*?)\],?\s*$"
        )

        def extract_email_domains(line):
            return [
                domain.strip().strip("\"'")
                for domain in line.split(",")
                if domain.strip()
            ]

        # Get domains from tenants.jsonnet
        try:
            with open(tenants_file_path, "r") as file:
                for line in file:
                    domain_match = domain_pattern.match(line)
                    if domain_match:
                        domains.append(domain_match.group(1))
                    email_domains_match = email_domains_pattern.match(line)
                    if email_domains_match:
                        domains.extend(
                            extract_email_domains(email_domains_match.group(1))
                        )
        except FileNotFoundError:
            logging.error(f"tenants.jsonnet file not found at {tenants_file_path}")
        except Exception as e:
            logging.error(f"Error reading tenants.jsonnet: {str(e)}")

        # Get domains from prod_tenants directory
        try:
            for filename in os.listdir(prod_tenants_dir):
                if filename.endswith(".jsonnet"):
                    with open(os.path.join(prod_tenants_dir, filename), "r") as file:
                        for line in file:
                            domain_match = domain_pattern.match(line)
                            if domain_match:
                                domains.append(domain_match.group(1))
                            email_domains_match = email_domains_pattern.match(line)
                            if email_domains_match:
                                domains.extend(
                                    extract_email_domains(email_domains_match.group(1))
                                )
        except FileNotFoundError:
            logging.error(f"prod_tenants directory not found at {prod_tenants_dir}")
        except Exception as e:
            logging.error(f"Error reading prod_tenants directory: {str(e)}")

        return domains

    def _get_notify_url(self, notify={}) -> str | None:
        url = notify.pop("url", None)
        if url is None:
            return None
        url += "?"
        url += "&".join([f"{key}={value}" for key, value in notify.items()])
        return url

    def _get_notify_message(self, notify={}) -> str:
        url = self._get_notify_url(notify)
        if url is None:
            return ""
        return f"\n\nPlease <a href='{url}'>click here</a> to notify SFDC after merging this PR."

    def prepare_sandbox(self) -> None:
        """
        Prepare the sandbox environment by:
        1. Updating remote references
        2. Switching to main branch and updating it
        """
        self._update_remote_refs()
        self._update_branch(self.config.main_branch)

    def prepare_database(self) -> None:
        tenants = self._get_tenants()
        for tenant in tenants:
            try:
                self.db.insert_tenant(tenant)
            except sqlite3.IntegrityError:
                # It's okay if the tenant already exists
                pass
        domains = self._get_domains()
        for domain in domains:
            try:
                self.db.insert_domain(domain)
            except sqlite3.IntegrityError:
                # It's okay if the domain already exists
                pass

    def get_next_us_namespace(self) -> str:
        self.last_ns_index += 1
        if self.last_ns_index >= len(self.config.us_namespaces):
            self.last_ns_index = 0
        return self.config.us_namespaces[self.last_ns_index]

    def _update_remote_refs(self) -> None:
        """Update local references to remote branches."""
        try:
            # Clean up any nested refs before fetching
            self._clean_all_nested_refs()
            # Clean up stale remote tracking branches using git command directly
            self.repo.git.remote("prune", "origin")
            # Now fetch
            self.repo.remotes.origin.fetch()
            logging.info("Updated remote references")
        except GitCommandError as git_error:
            logging.error(f"Failed to update remote references: {git_error.stderr}")
            raise Exception(f"Failed to update remote references: {git_error.stderr}")

    def add_tenant_and_create_pr(
        self,
        tenant_name: str,
        namespace: str,
        domains: list[str],
        tier: str,
        msg: dict,
        notify: dict = {},
        debug: dict = {},
    ) -> bool:
        """
        Add a tenant and create a PR.

        Returns:
            bool: True if the tenant was added, False if the tenant already exists.
        """
        # Ensure desired tenant/domain isn't already in use
        if self._db_tenant_exists(tenant_name, domains):
            return False

        # Refresh github token
        self._refresh_gh_token()

        # Create new branch off main
        self._update_branch(self.config.main_branch)

        tenant_file = self._add_tenant_file(tenant_name, namespace, domains, tier)
        branch_name = f"launchbot-add-tenant-{tenant_name}"
        if debug.get("readonly", False):
            branch_name += f"-{uuid.uuid4()}"
            logging.info(f"Debug readonly branch: {branch_name}")
        self._create_branch(branch_name)
        self._commit_changes(tenant_file, f"Add tenant: {tenant_name}")

        # Add tenant to new branch and push
        self._push_branch(branch_name)

        comments = f"Created by launchbot action: {msg['action']}"
        if msg.get("comment", ""):
            comments += f"\n\n{msg['comment']}"
        if msg.get("requester", ""):
            comments += f"\n\nRequested by: {msg['requester']}"
        notify_message = self._get_notify_message(notify)
        if notify_message:
            comments += f"\n\n{notify_message}"

        # Create PR
        pr_title = f"{'[DO NOT MERGE] ' if debug else ''}Add tenant {tenant_name}"
        pr_body = f"Add new tenant:\n\nName: {tenant_name}\nNamespace: {namespace}\nTier: {tier}\nDomains: {domains}\n\n{comments}"
        self._create_pull_request(pr_title, pr_body, branch_name)

        if debug.get("readonly", False):
            logging.info("Debug readonly mode, skip database update")
            return True

        self._kubectl_apply_tenant(tenant_name, namespace, domains)
        self._db_add_tenant(tenant_name, namespace, domains)

        return True
