from dataclasses import dataclass, field

from deploy.tenants.tenants_util import DEFAULT_NAMESPACES
from tools.tenant_manager.processor.git_utils import TenantAdder


@dataclass
class TenantSpec:
    """
    Parse out the tenant spec from a message.
    """

    name: str = ""
    namespace: str = ""
    cloud: str = "GCP_US_CENTRAL1_PROD"
    tier: str = "ENTERPRISE"
    domains: list[str] = field(default_factory=list)


def get_tenantspec_from_webform(
    msg: dict, tenant_adder: TenantAdder
) -> TenantSpec | None:
    tenant_spec = TenantSpec()
    tenant_spec.name = msg.get("tenant_name", "")
    tenant_spec.domains = msg.get("domains", [])
    domain = msg.get("domain", None)
    if domain is not None and domain not in tenant_spec.domains:
        tenant_spec.domains.append(domain)
    tenant_spec.cloud = msg.get("cloud", "GCP_US_CENTRAL1_PROD")
    tenant_spec.tier = msg.get("tier", "ENTERPRISE")
    if tenant_spec.cloud == "GCP_US_CENTRAL1_PROD":
        tenant_spec.namespace = tenant_adder.get_next_us_namespace()
    else:
        tenant_spec.namespace = DEFAULT_NAMESPACES.get(tenant_spec.cloud, "")
    if tenant_spec.name == "" or tenant_spec.namespace == "":
        return None
    return tenant_spec


def get_tenantspec_from_direct_access(
    msg: dict, tenant_adder: TenantAdder
) -> TenantSpec | None:
    tenant_spec = TenantSpec()
    tenant_spec.name = msg.get("tenant_name", "")
    tenant_spec.domains = msg.get("domains", [])
    domain = msg.get("domain", None)
    if domain is not None and domain not in tenant_spec.domains:
        tenant_spec.domains.append(domain)
    tenant_spec.cloud = "GCP_US_CENTRAL1_PROD"
    tenant_spec.tier = msg.get("tier", "ENTERPRISE")
    tenant_spec.namespace = tenant_adder.get_next_us_namespace()
    if tenant_spec.name == "":
        return None
    return tenant_spec
