import argparse
import json
import logging
import pathlib
import re
import threading
from dataclasses import dataclass

import google.cloud.pubsub_v1 as pubsub_v1
import prometheus_client
import structlog
from dataclasses_json import dataclass_json

import base.tracing
import tools.tenant_manager.processor.git_utils
import tools.tenant_manager.processor.db
from tools.tenant_manager.processor.tenant_spec import (
    get_tenantspec_from_direct_access,
    get_tenantspec_from_webform,
)

from base.logging.struct_logging import setup_struct_logging
from base.python.signal_handler.signal_handler import GracefulSignalHandler

log = structlog.get_logger()

tracer = base.tracing.setup_opentelemetry()

PARSE_ERRORS = prometheus_client.Counter(
    "au_tenmgr_processor_messages_parse_errors", "Errors parsing messages from pubsub"
)

APPLY_ERRORS = prometheus_client.Counter(
    "au_tenmgr_processor_apply_errors", "Errors applying changes"
)


@dataclass_json
@dataclass
class Config:
    """Configuration for the processor."""

    prom_port: int
    project_id: str
    subscription_name: str
    git_utils_config: tools.tenant_manager.processor.git_utils.GitUtilsConfig

    @classmethod
    def load_config(cls, config_file: pathlib.Path):
        """Loads the configuration from a file."""
        return cls.schema().loads(  # pylint: disable=no-member # type: ignore
            config_file.read_text()
        )


def _validate_tenant_name(tenant_name) -> bool:
    if len(tenant_name) > 16 or len(tenant_name) < 1:
        return False
    return tenant_name[0].isalpha() and all(
        c.islower() or c.isdigit() or c == "-" for c in tenant_name
    )


def _validate_domains(domains):
    if not domains:
        return False
    pattern = (
        r"^(?:[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?\.)+[a-z0-9][a-z0-9-]{0,61}[a-z0-9]$"
    )
    return all(
        re.match(pattern, domain, re.IGNORECASE) is not None for domain in domains
    )


def _write_health_file(health_file_path):
    with open(health_file_path, "w") as f:
        f.write("healthy")


def run(config: Config, shutdown_event: threading.Event):
    db = tools.tenant_manager.processor.db.Database(config.git_utils_config.db_path)

    subscriber = pubsub_v1.SubscriberClient()
    subscription_path = subscriber.subscription_path(
        config.project_id, config.subscription_name
    )

    tenant_adder = tools.tenant_manager.processor.git_utils.TenantAdder(
        config.git_utils_config, db
    )

    tenant_adder.prepare_sandbox()
    tenant_adder.prepare_database()

    def callback(message: pubsub_v1.subscriber.message.Message) -> None:  # type: ignore
        try:
            log.info(f"Received message: {message.message_id}")
            msg = None

            try:
                msg = json.loads(message.data.decode("utf-8"))

                # Extract required fields, with fallbacks
                action = msg.get("action", "")
                if action == "add-tenant-webform":
                    tenant_spec = get_tenantspec_from_webform(msg, tenant_adder)
                    if tenant_spec is None:
                        raise ValueError("Invalid webform message")
                elif action == "add-tenant-direct-access":
                    tenant_spec = get_tenantspec_from_direct_access(msg, tenant_adder)
                    if tenant_spec is None:
                        raise ValueError("Invalid direct-access message")
                else:
                    raise ValueError(f"Invalid action: {action}")

                tenant_name = tenant_spec.name
                namespace = tenant_spec.namespace
                domains = tenant_spec.domains
                tier = tenant_spec.tier
                notify = msg.get("notify", {})
                debug = msg.get("debug", {})
                log.info(f"Debug flags received: {debug}")
                if not debug.get("enabled", False):
                    debug = {}

                if debug.get("noop", False):
                    log.info(f"Debug noop mode, skipping message {message.message_id}")
                    message.ack()
                    return

                if not _validate_tenant_name(tenant_name):
                    raise ValueError(f"Invalid tenant name: {tenant_name}")
                if not _validate_domains(domains):
                    raise ValueError(f"Invalid domains: {domains}")
            except Exception as e:
                log.error("Error parsing message %s: %s", message.message_id, e)
                PARSE_ERRORS.inc()
                raise

            log.info(
                f"Adding tenant {tenant_name} in namespace {namespace} with domains {domains}"
            )

            try:
                if tenant_adder.add_tenant_and_create_pr(
                    tenant_name,
                    namespace,
                    domains,
                    tier,
                    msg,
                    notify,
                    debug,
                ):
                    log.info(f"Tenant {tenant_name} added and PR created successfully")
                else:
                    log.error(f"Tenant {tenant_name} already exists")
                    APPLY_ERRORS.inc()
            except Exception as e:
                log.error("Error applying changes %s: %s", message.message_id, e)
                APPLY_ERRORS.inc()
                raise

            message.ack()
        except ValueError as e:  # Handle validation errors
            log.error(
                f"Permanent error processing message {message.message_id}: {str(e)}"
            )
            PARSE_ERRORS.inc()
            message.ack()  # Ack invalid messages since retrying won't help
        except Exception as e:  # pylint: disable=broad-except
            log.error(f"Error processing message {message.message_id}: {str(e)}")
            message.nack()  # Retry other errors that might be transient

    streaming_pull_future = subscriber.subscribe(subscription_path, callback=callback)
    log.info(f"Listening for messages on {subscription_path}")
    shutdown_event.wait()
    streaming_pull_future.cancel()
    subscriber.close()


def main():
    # Set up the signal handler
    # This will catch SIGTERM and SIGINT and exit gracefully
    handler = GracefulSignalHandler()

    # Set up the logging
    setup_struct_logging()

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--config-file", default=pathlib.Path("/config/config.json"), type=pathlib.Path
    )
    parser.add_argument("--health-file", type=pathlib.Path)
    args = parser.parse_args()
    logging.info("Args %s", args)

    config = Config.load_config(args.config_file)

    prometheus_client.start_http_server(config.prom_port)

    _write_health_file(args.health_file)

    run(config, handler.get_shutdown_event())


if __name__ == "__main__":
    main()
