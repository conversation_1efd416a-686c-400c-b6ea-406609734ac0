// K8S deployment file for the route guide service
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local telemetryLib = import 'deploy/common/telemetry-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local pubsubLib = import 'services/lib/pubsub/pubsub_lib.jsonnet';
local githubAppSealedLib = import 'tools/deploy/github_pr_app_sealed.jsonnet';


// the function that creates the deployment
// env: the environment (DEV, PROD, ...)
// namespace: the namespace that the deployment is created in
// cloud: the cloud (GCP_US_CENTRAL1_DEV, GCP_US_CENTRAL1_PROD, ...)
// namespace_config: the namespace config from //deploy/tenants/namespace_configs
function(env, namespace, cloud, namespace_config)
  // the app name is used in the kubernetes object names. It is also added as label to each object
  // so that we know which app an object belongs to
  local appName = 'tenmgr-proc';

  local githubSecret = githubAppSealedLib(cloud=cloud, namespace=namespace, appName=appName);

  // creates a service account for the pod
  // a service account is needed to access GCP resources or kubernetes resources
  local serviceAccount = gcpLib.createServiceAccount(
    appName, env, cloud, namespace, iam=true,
  );

  // Permissions to list and modify tenants
  local tenantAdminGrant = {
    apiVersion: 'rbac.authorization.k8s.io/v1',
    kind: 'RoleBinding',
    metadata: {
      name: '%s-role-binding' % appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    roleRef: {
      apiGroup: 'rbac.authorization.k8s.io',
      kind: 'ClusterRole',
      name: 'tenant-admin',
    },
    subjects: [
      {
        kind: 'ServiceAccount',
        name: serviceAccount.name,
      },
    ],
  };

  local subscriber = pubsubLib.namespaceSubscriber(
    env=env,
    namespace=namespace,
    appName=appName,
    cloud=cloud,
    publisherAppName='tenmgr-webhook',
    serviceAccount=serviceAccount,
    externalTopicRef=true,
  );

  // configuration that will be passed to the server as a JSON file
  local config = {
    prom_port: 9090,

    project_id: cloudInfo[cloud].projectId,
    subscription_name: subscriber.subscriptionName,
    git_utils_config: {
      github_secret_path: '/github-app',
      repo_dir: '/cache/repo',
      repo_url: 'https://github.com/augmentcode/augment.git',
      pr_approvers: ['a2chang', 'matt-ball', 'anshuman-augment', 'rob-aug', 'nikita-sirohi', 'amateurhuman'],
      main_branch: 'main',
      us_namespaces: ['e4', 'e5', 'e6', 'e7', 'e8', 'e9', 'e10', 'e11'],
      db_path: '/cache/tenman.db',
      tier_options: lib.tenant_tier_options,
    },
  };

  // a config map is a Kubernetes object that contains configuration data it is "mounted" into a pod
  local configMap = configMapLib.createConfigMap(appName=appName, namespace=namespace, config=config);

  // a cache disk for git checkouts
  local cacheDisk = {
    apiVersion: 'v1',
    kind: 'PersistentVolumeClaim',
    metadata: {
      name: 'tenmgr-proc-pvc',
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    spec: {
      accessModes: [
        'ReadWriteOnce',
      ],
      resources: {
        requests: {
          storage: '8Gi',
        },
      },
      storageClassName: 'premium-rwo',
    },
  };

  // creates a container that runs the server
  local container = {
    name: appName,
    // the target is the bazel target that builds the docker image
    target: {
      name: '//tools/tenant_manager/processor:image',
      dst: 'tenmgr-proc',
    },
    // the arguments that are passed to the server
    args: [
      '--config',
      configMap.filename,
      '--health-file',
      '/tmp/health',
    ],
    // ports that the pod exposes
    ports: [],
    // the environment variables that are passed to the server
    env: telemetryLib.telemetryEnv(appName, telemetryLib.collectorUri(env, namespace, cloud)),
    // the volumes that are mounted into the pod
    volumeMounts: [
      configMap.volumeMountDef,
      {
        mountPath: '/github-app',
        name: 'github-app-secret',
      },
      {
        mountPath: '/cache',
        name: 'cache-disk',
      },
    ],
    // the health check is used to determine if the pod is ready to receive traffic
    readinessProbe: {
      exec: {
        command: [
          '/bin/sh',
          '-c',
          'cat /tmp/health',
        ],
      },
      initialDelaySeconds: 5,
      periodSeconds: 10,
    },
    livenessProbe: {
      exec: {
        command: [
          '/bin/sh',
          '-c',
          'cat /tmp/health',
        ],
      },
      initialDelaySeconds: 15,
      periodSeconds: 20,
    },
    // the resource limits are used to determine how much CPU and memory the pod can use
    resources: {
      limits: {
        cpu: 1,
        memory: '512Mi',
      },
    },
  };
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);
  // the pod is the kubernetes object that runs the container
  local pod = {
    // the service account is used to access GCP resources or kubernetes resources
    serviceAccountName: serviceAccount.name,
    containers: [
      container,
    ],
    priorityClassName: cloudInfo.envToPriorityClass(env),
    affinity: affinity,
    tolerations: tolerations,
    // the volumes are mounted into the pod
    volumes: [
      // the config map is mounted into the pod
      configMap.podVolumeDef,
      {
        name: 'github-app-secret',
        secret: {
          secretName: githubSecret.metadata.name,  // pragma: allowlist secret
          optional: false,
        },
      },
      {
        name: 'cache-disk',
        persistentVolumeClaim: {
          claimName: cacheDisk.metadata.name,
        },
      },
    ],
  };

  // the tolerations and affinity are used to determine which nodes the pod can be scheduled on
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);
  local deployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'reloader.stakater.com/search': 'true',
      },
    },
    spec: {
      // the minimum amount of time that a pod needs to be ready before the deployment is considered successful
      minReadySeconds: if env == 'DEV' then 0 else 60,
      // the number of pods that are running at the same time
      replicas: 1,
      // the strategy is used to determine how the deployment is rolled out
      strategy: {
        type: 'RollingUpdate',
        rollingUpdate: {
          maxSurge: 1,
          maxUnavailable: 0,
        },
      },
      selector: {
        matchLabels: {
          app: appName,
        },
      },
      template: {
        metadata: {
          labels: {
            app: appName,
          },
        },
        spec: pod + {
          tolerations: tolerations,
          affinity: affinity,
        },
      },
    },
  };
  lib.flatten([
    configMap.objects,
    serviceAccount.objects,
    deployment,
    subscriber.objects,
    githubSecret,
    tenantAdminGrant,
    cacheDisk,
  ])
