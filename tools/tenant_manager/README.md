# Tenant Manager (aka launchbot)

This is a service to allow tenants to be created via a webhook or form.

## Overview

There are two parts to the tenant manager:
1. webhook
1. processor

In addition, the project includes a separate service/frontend/backend
which presents a webform that sends requests to the webhook.  The
webform requires authentication through <PERSON><PERSON>'s corporate domain.

## webhook

The webhook simply listens for requests on the `tenmgr-webhook/append`
endpoint and sends the request data to pubsub.  In order for the
request to be accepted, a valid `Authorization: Bearer` must be supplied.

## processor

The processor is a service that listens to pubsub messages and creates
a PR for creating a new tenant definition (using the scheme where each
tenant is defined in a separate file) under `deploy/tenants/prod_tenants`.

Upon startup, it pulls the latest main from the augment repository and
scans for existing tenant names and authorisation domains in use.  These
are stored in a sqlite database.  When a request is received from pubsub,
it uses the value of the `action` parameter to determine how to interpret
the remaining parameters.

For `add-tenant-*` actions:
* if the tenant name or authorisation domains are found in the database,
  then the request is acked with no action taken; otherwise
* a PR is created for the new tenant:
  * update main to latest
  * create new tenant file
  * create branch `launchbot-add-tenant-{tenant_name}`, commit, push
  * create new PR
  * add tenant to database
  * ack the message
* if any the PR is not successfully created, then the message is nacked.
  This will leave the message in the queue to be retried.

## Parameters

Service parameters should be provided as a json to the webhook endpoint.
Currently the following fields are supported:
* `action` - required; one of:
  * `add-tenant-webform`
  * `add-tenant-direct-access`
* `tenant_name` - required for `add-tenant-*` - maximum 16 characters
  matching `[a-z][a-z0-9-]{,15}`.
* `domain` - email domain to use for authentication
* `tier` - one of `ENTERPRISE`, `PROFESSIONAL-TEAM`, or `COMMUNITY` (defaults to `ENTERPRISE`)
* `comment` - comment to add to the PR body
* `notify` - map of parameters for the notification endpoint
  * `url` - endpoint to send notification upon successful tenant creation
  * all remaining parameters are passed back in the notification verbatim
* `debug` - map of parameters:
  * `enabled` - true to enable debug, false to disable all debug options
  * `readonly` - if true, then go as far as creating the PR but don't
    write to the database.  Also add a random suffix for branch name.
  * `noop` - if true, then don't do anything

Depending on the `action`, other parameters may be specified or inferred.

### Action: `add-tenant-webform`

The following parameters can be specified:
* `cloud` - `GCP_US_CENTRAL1_PROD` or `GCP_EU_WEST4_PROD`
* `requester` - username of the person requesting the tenant

The processor will also assign:
* `namespace` - will rotate between `e4` to `e11`

### Action: `add-tenant-direct-access`

The processor will assign:
* `namespace` - will rotate between `e4` to `e11`
* `cloud` - `GCP_US_CENTRAL1_PROD`

## Tenant creation notification

Once the tenant has been created, we want to be able to notify downstream
services.  If `notify` is a map and `notify.url` is specified, then tenmgr
will send a `GET` to the endpoint with the contents of `notify` parameters
(less `url`).

### Note on security

One issue with this solution is that the GET call into zapier isn't
authenticated, so anyone looking at the PR can click the link.  However,
the PR would only be visible to employees with github access, so the worse
case outcome is that someone else in the company clicks the link.

**TODO:** We will want to eliminate this callback mechanism sooner rather
than later.
