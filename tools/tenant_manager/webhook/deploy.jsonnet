// K8S deployment file for the route guide service
local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local telemetryLib = import 'deploy/common/telemetry-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local pubsubLib = import 'services/lib/pubsub/pubsub_lib.jsonnet';

// the function that creates the deployment
// env: the environment (DEV, PROD, ...)
// namespace: the namespace that the deployment is created in
// cloud: the cloud (GCP_US_CENTRAL1_DEV, GCP_US_CENTRAL1_PROD, ...)
// namespace_config: the namespace config from //deploy/tenants/namespace_configs
function(env, namespace, cloud, namespace_config)
  // the app name is used in the kubernetes object names. It is also added as label to each object
  // so that we know which app an object belongs to
  local appNames = {
    webhook: 'tenmgr-webhook',
    webform: 'tenmgr-webform',
  };

  local domainSuffix = cloudInfo[cloud].internalDomainSuffix;
  local ingressHostnames = {
    [app]: '%s.%s' % [appNames[app], if env == 'PROD' then domainSuffix else '%s.%s' % [namespace, domainSuffix]]
    for app in std.objectFields(appNames)
  };


  // creates a server certificate
  local serverCerts = {
    [app]: certLib.createPublicServerCert(
      name='%s-server-certificate' % appNames[app],
      env=env,
      namespace=namespace,
      appName=appNames[app],
      dnsNames=[ingressHostnames[app]],
      volumeName='https-certs-%s' % app,
    )
    for app in std.objectFields(appNames)
  };

  // creates a service account for the pod
  // a service account is needed to access GCP resources or kubernetes resources
  local serviceAccount = gcpLib.createServiceAccount(
    appNames.webhook, env, cloud, namespace, iam=true,
  );

  local tokensSecret = {
    apiVersion: 'bitnami.com/v1alpha1',
    kind: 'SealedSecret',
    metadata: {
      name: 'tenmgr-webhook-tokens',
      namespace: namespace,
      labels: {
        app: appNames.webhook,
        ingressHostname: ingressHostnames.webhook,
        ingressHostnameWebform: ingressHostnames.webform,
      },
      annotations: {
        'sealedsecrets.bitnami.com/cluster-wide': 'true',
      },
    },
    spec: {
      encryptedData: {
        GCP_US_CENTRAL1_DEV: {
          'tokens.txt': 'AgBsQTpMGAMl7MpkNcb9/LI0ZOFbDxvbW7xhtmWYWO6FY5U6x8LhtyOLK2bG/pHRPwazAUYJT0B8v2ZH7LCT3W9L0C2IuVDCxeQssn4kpScUJHcuuF2SGrcZ+/Bvih+/jV9UIk59I7258fLMK+LCE9cQqmRMtS/phXhKWFFb4GO/c86Eg2oupiuEwSPqe0kYIpKCSKmo+28fUsen/kS3B9r3UDWEA2apgf/sG6jVGoUk5nEp2fcYS//YgKx1Yj2TUaM6x/2XgokKy/hSJhxiqFdZX4G4Nj20blkRUCMxINnE5dQ6zl69lFnI58FxA+eq5QSQGWIjl6w6oeDRXpAk1tUvbykmHlTC7Z5rwo1K+ESiKE8tgJ5EeFXTgiANTb3XuienJjLqvUS8jHtU4Qy/6lZKS4t5mYhv7swCHTvFtdljRQd/0U9G5lwpROfhbRu9l6cpHmh3oQATSdvFR/Om4jIOkmR6Rd0wQb+K5G1MaRbne5BuyURkjTay134VPGyVaUvw7iotor0ZA4ai4CmklgsGnT8KhtHXlZgeGW1IMv9pOQryZD53wVZeXPBp/YDder+3t0ocjHct9Jv8NiWO5ykDXvK7MnAVtTt/clqyXIoLXpimNbT7RPv9jPZGWfwGUc54/GJzERHci4geHNXonBOPmFMlwtUuH6GSKCNUGWWilqhilMjDJgynL6xIPn0pP3bA8Q+3RveJFVFsZBkmteNJHLwXaNz90moj',
        },
        GCP_US_CENTRAL1_PROD: {
          'tokens.txt': 'AgC8BtT/xSbX1GNrWFLJspth3GzcywQCq73x23y6FKQzFDTrAdl4/qGLHfk7dElobfXhUAyOVVL1hwqlnnEuApT0CnCI8cwSJKsRhBumrKjW1aTzEzyFLgXVGAAszxvYyt6Vn5LabK6H9o3rWCtU7sV1niblBcGGK8MDWMwc9C+0C4f1usSCyZK6cwGmE8qAqlXYkhV2X0KGwpbF89hbqZy6+NjSpLv5MGueRewGiQdUoOHV/O5y21L7Jd6/Da9zucxRQtO2sqH8uqqptZKtKlT4G364RIjLJigxjktRl/7g+k3Wb0WuPw9ozBN9pgcJNj6P33pZ0BRYGKCCM+i7+3B4iCz7HthxrFyIKpnz+V9jn5j6/a1DdxF2Its22+0bfi2wqXNZyJ6VdxTTybTz/ewyKXdGNpKa9iSih3oxl2PTzklMtXXJRIKl1f+VjEotm17nZLPEtotBggtKxTwDTdbV1c5A2T3NVoTbv329hvO19/DgDLwrlurjeyyDtUxSwTyTZniJVD9JnK7j8Ax/HSbOjwFW9UEEqu06oyn6A+PKOR8iuUW2QvEa1sjM4SuGA3UYaNjqr9geoLfLEP37lFFzg6+hQVqwoeFBQnsINP4tjJdzB3VKv0+haOjS5PZmwcEkuLwuy+HpHPdYZqhfgBFv+vBXoXp1wwdrcrB58FvoscHT7iPq1XUQ0vaVEVMpgyJoRFZEttd9NvgRpO3ZRozg5wd9le7R277V',
        },
      }[cloud],
    },
  };

  local publisherTopic = pubsubLib.publisherTopic(
    cloud=cloud,
    env=env,
    namespace=namespace,
    appName=appNames.webhook,
    serviceAccount=serviceAccount,
    messageRetentionDurationSeconds=60 * 60 * 24 * 7,
  );

  // configuration that will be passed to the server as a JSON file
  local config = {
    webhook: {
      port: 8443,
      server_mtls: serverCerts.webhook.config,
      prom_port: 9090,
      token_file: '/tokens/tokens.txt',
      pubsub_topic: publisherTopic.topicName,
      project_id: cloudInfo[cloud].projectId,
    },
    webform: {
      port: 8444,
      server_mtls: serverCerts.webform.config,
      tier_options: lib.tenant_tier_options,
    },
  };

  // a config map is a Kubernetes object that contains configuration data it is "mounted" into a pod
  local configMap = configMapLib.createConfigMap(appName=appNames.webhook, namespace=namespace, config=config);

  local backendConfig = gcpLib.createBackendConfig(
    app=appNames.webhook,
    cloud=cloud,
    namespace=namespace,
    healthCheck={
      checkIntervalSec: 15,
      port: config.webhook.port,
      type: 'HTTPS',
      requestPath: '/health',
    }
  );

  local iapBackendConfig = gcpLib.createBackendConfig(
    app=appNames.webform,
    cloud=cloud,
    namespace=namespace,
    healthCheck={
      checkIntervalSec: 15,
      port: config.webform.port,
      type: 'HTTPS',
      requestPath: '/health',
    },
    iap=true,
  );

  local httpService = {
    apiVersion: 'v1',
    kind: 'Service',
    metadata: {
      name: '%s-svc' % appNames.webhook,
      namespace: namespace,
      annotations: {
        'cloud.google.com/backend-config': std.manifestJson({
          default: backendConfig.metadata.name,
        }),
        'cloud.google.com/app-protocols': std.manifestJson({
          default: 'HTTPS',
        }),
        'cloud.google.com/neg': '{"ingress": true}',
      },
      labels: {
        app: appNames.webhook,
      },
    },
    spec: {
      type: 'ClusterIP',
      selector: {
        app: appNames.webhook,
      },
      ports: [
        {
          protocol: 'TCP',
          port: 443,
          name: 'default',
          targetPort: config.webhook.port,
        },
      ],
    },
  };

  local iapHttpService = {
    apiVersion: 'v1',
    kind: 'Service',
    metadata: {
      name: '%s-svc' % appNames.webform,
      namespace: namespace,
      annotations: {
        'cloud.google.com/backend-config': std.manifestJson({
          default: iapBackendConfig.metadata.name,
        }),
        'cloud.google.com/app-protocols': std.manifestJson({
          'iap-https': 'HTTPS',
        }),
        'cloud.google.com/neg': '{"ingress": true}',
      },
      labels: {
        app: appNames.webform,
      },
    },
    spec: {
      type: 'ClusterIP',
      selector: {
        app: appNames.webhook,
      },
      ports: [
        {
          protocol: 'TCP',
          port: 8444,
          name: 'iap-https',
          targetPort: config.webform.port,
        },
      ],
    },
  };

  local frontendConfig = gcpLib.createFrontendConfig(app=appNames.webhook, cloud=cloud, namespace=namespace);
  local ingressObjects = [
    {
      apiVersion: 'networking.k8s.io/v1',
      kind: 'Ingress',
      metadata: {
        annotations: {
          'kubernetes.io/ingress.class': 'gce',
          'cert-manager.io/cluster-issuer': certLib.getIngressIssuer(env),
          'kubernetes.io/ingress.allow-http': 'false',
          'networking.gke.io/v1beta1.FrontendConfig': frontendConfig.metadata.name,
          'external-dns.alpha.kubernetes.io/hostname': ingressHostnames.webhook,
          'external-dns.alpha.kubernetes.io/ttl': '300',
        },
        labels: {
          app: appNames.webhook,
        },
        name: '%s-ingress' % appNames.webhook,
        namespace: namespace,
      },
      spec: {
        ingressClassName: 'gce',
        tls: [
          {
            secretName: '%s-ssl-cert' % appNames.webhook,  // pragma: allowlist secret
            hosts: [ingressHostnames.webhook],
          },
        ],
        rules: [
          {
            host: ingressHostnames.webhook,
            http: {
              paths: [
                {
                  path: '/append',
                  pathType: 'Prefix',
                  backend: {
                    service: {
                      name: httpService.metadata.name,
                      port: {
                        name: 'default',
                      },
                    },
                  },
                },
                {
                  path: '/metrics',
                  pathType: 'Prefix',
                  backend: {
                    service: {
                      name: httpService.metadata.name,
                      port: {
                        name: 'default',
                      },
                    },
                  },
                },
              ],
            },
          },
        ],
      },
    },
    {
      apiVersion: 'networking.k8s.io/v1',
      kind: 'Ingress',
      metadata: {
        annotations: {
          'kubernetes.io/ingress.class': 'gce',
          'cert-manager.io/cluster-issuer': certLib.getIngressIssuer(env),
          'kubernetes.io/ingress.allow-http': 'false',
          'networking.gke.io/v1beta1.FrontendConfig': frontendConfig.metadata.name,
          'external-dns.alpha.kubernetes.io/hostname': ingressHostnames.webform,
          'external-dns.alpha.kubernetes.io/ttl': '300',
        },
        labels: {
          app: appNames.webhook,
        },
        name: '%s-ingress' % appNames.webform,
        namespace: namespace,
      },
      spec: {
        ingressClassName: 'gce',
        tls: [
          {
            secretName: '%s-ssl-cert' % appNames.webform,  // pragma: allowlist secret
            hosts: [ingressHostnames.webform],
          },
        ],
        rules: [
          {
            host: ingressHostnames.webform,
            http: {
              paths: [
                {
                  path: '/',
                  pathType: 'Prefix',
                  backend: {
                    service: {
                      name: iapHttpService.metadata.name,
                      port: {
                        name: 'iap-https',
                      },
                    },
                  },
                },
              ],
            },
          },
        ],
      },
    },
    frontendConfig,
    backendConfig,
    iapBackendConfig,
  ];

  // creates a container that runs the server
  local container = {
    name: appNames.webhook,
    // the target is the bazel target that builds the docker image
    target: {
      name: '//tools/tenant_manager/webhook:image',
      dst: 'tenmgr-webhook',
    },
    // the arguments that are passed to the server
    args: [
      '--config',
      configMap.filename,
    ],
    // ports that the pod exposes
    ports: [
      {
        containerPort: config.webhook.port,
        name: 'default',
      },
      {
        containerPort: config.webform.port,
        name: 'iap-https',
      },
    ],
    // the environment variables that are passed to the server
    env: telemetryLib.telemetryEnv(appNames.webhook, telemetryLib.collectorUri(env, namespace, cloud)),
    // the volumes that are mounted into the pod
    volumeMounts: [
      configMap.volumeMountDef,
      serverCerts.webhook.volumeMountDef,
      serverCerts.webform.volumeMountDef,
      {
        name: 'tenmgr-webhook-tokens',
        mountPath: '/tokens',
        readOnly: true,
      },
    ],
    // the health check is used to determine if the pod is ready to receive traffic
    readinessProbe: {
      httpGet: {
        scheme: 'HTTPS',
        path: '/health',
        port: config.webhook.port,
      },
      initialDelaySeconds: 15,
      periodSeconds: 20,
    },
    livenessProbe: {
      httpGet: {
        scheme: 'HTTPS',
        path: '/health',
        port: config.webform.port,
      },
      initialDelaySeconds: 15,
      periodSeconds: 20,
    },
    // the resource limits are used to determine how much CPU and memory the pod can use
    resources: {
      limits: {
        cpu: 1,
        memory: '512Mi',
      },
    },
  };
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appNames.webhook);
  // the pod is the kubernetes object that runs the container
  local pod = {
    // the service account is used to access GCP resources or kubernetes resources
    serviceAccountName: serviceAccount.name,
    containers: [
      container,
    ],
    priorityClassName: cloudInfo.envToPriorityClass(env),
    affinity: affinity,
    tolerations: tolerations,
    // the volumes are mounted into the pod
    volumes: [
      // the config map is mounted into the pod
      configMap.podVolumeDef,
      // the server certificate is mounted into the pod
      serverCerts.webhook.podVolumeDef,
      serverCerts.webform.podVolumeDef,
      {
        name: 'tenmgr-webhook-tokens',
        secret: {
          secretName: tokensSecret.metadata.name,  // pragma: allowlist secret
          optional: false,
        },
      },
    ],
  };

  // the tolerations and affinity are used to determine which nodes the pod can be scheduled on
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appNames.webhook);
  local deployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: appNames.webhook,
      namespace: namespace,
      labels: {
        app: appNames.webhook,
      },
      annotations: {
        'reloader.stakater.com/search': 'true',
      },
    },
    spec: {
      // the minimum amount of time that a pod needs to be ready before the deployment is considered successful
      minReadySeconds: if env == 'DEV' then 0 else 60,
      // the number of pods that are running at the same time
      replicas: if env == 'DEV' then 1 else 2,
      // the strategy is used to determine how the deployment is rolled out
      strategy: {
        type: 'RollingUpdate',
        rollingUpdate: {
          maxSurge: 1,
          maxUnavailable: 0,
        },
      },
      selector: {
        matchLabels: {
          app: appNames.webhook,
        },
      },
      template: {
        metadata: {
          labels: {
            app: appNames.webhook,
          },
        },
        spec: pod + {
          tolerations: tolerations,
          affinity: affinity,
        },
      },
    },
  };
  lib.flatten([
    configMap.objects,
    serviceAccount.objects,
    serverCerts.webhook.objects,
    serverCerts.webform.objects,
    deployment,
    tokensSecret,
    ingressObjects,
    publisherTopic.objects,
    httpService,
    iapHttpService,
  ])
