package main

type TlsConfig struct {
	KeyPath  string `json:"key_path"`
	CertPath string `json:"cert_path"`
	CaPath   string `json:"ca_path"`
}

type WebhookConfig struct {
	Port            int        `json:"port"`
	ServerTlsConfig *TlsConfig `json:"server_mtls"`
	PromPort        int        `json:"prom_port"`
	TokenFile       string     `json:"token_file"`
	PubsubTopic     string     `json:"pubsub_topic"`
	ProjectId       string     `json:"project_id"`
}

type WebformConfig struct {
	Port            int        `json:"port"`
	ServerTlsConfig *TlsConfig `json:"server_mtls"`
	TierOptions     []string   `json:"tier_options"`
}

type Config struct {
	WebhookConfig *WebhookConfig `json:"webhook"`
	WebformConfig *WebformConfig `json:"webform"`
}
