package main

import (
	"bufio"
	"bytes"
	"crypto/tls"
	"embed"
	"encoding/json"
	"fmt"
	"html/template"
	"net/http"
	"os"
	"strings"

	"github.com/rs/zerolog/log"
)

//go:embed templates/*
var templates embed.FS

type WebformServer struct {
	webhookEndpoint string
	httpClient      *http.Client
	authToken       string
	tierOptions     []string
}

// Add this struct for template data
type TemplateData struct {
	TenantName string
	Domain     string
	Cloud      string
	Tier       string
	Comment    string
	Requester  string
	Debug      map[string]bool
}

// NewServer creates a new WebformServer instance
func NewServer(config Config) (*WebformServer, error) {
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{
			InsecureSkipVerify: true,
		},
	}
	client := &http.Client{Transport: tr}

	// Read the first token from the token file
	openFile, err := os.Open(config.WebhookConfig.TokenFile)
	if err != nil {
		return nil, fmt.Errorf("error opening token file: %v", err)
	}
	defer openFile.Close()

	scanner := bufio.NewScanner(openFile)
	var token string
	for scanner.Scan() {
		token = scanner.Text()
		if len(token) >= 8 {
			break
		}
	}
	if token == "" {
		return nil, fmt.Errorf("no valid token found in token file")
	}

	webhookEndpoint := fmt.Sprintf("https://localhost:%d/append", config.WebhookConfig.Port)

	// Get tier options from config
	tierOptions := config.WebformConfig.TierOptions

	return &WebformServer{
		webhookEndpoint: webhookEndpoint,
		httpClient:      client,
		authToken:       token,
		tierOptions:     tierOptions,
	}, nil
}

// HandleWebformGet handles GET requests to the webform endpoint
func (s *WebformServer) HandleWebformGet(w http.ResponseWriter, r *http.Request) {
	// Create a function map with the title and lower functions
	funcMap := template.FuncMap{
		"title": strings.Title, // or a custom title function
		"lower": strings.ToLower,
	}

	// Parse all templates with the function map
	tmpl, err := template.New("").Funcs(funcMap).ParseFS(
		templates,
		"templates/styles.tmpl",
		"templates/webform.html",
	)
	if err != nil {
		log.Error().Err(err).Msg("Failed to parse webform template")
		http.Error(w, "Internal Server Error", http.StatusInternalServerError)
		return
	}

	// Debug mode is only enabled when accessing /debug explicitly
	isDebug := r.URL.Path == "/debug"

	data := map[string]interface{}{
		"Debug": map[string]bool{
			"Enabled": isDebug,
		},
		"TierOptions": s.tierOptions,
	}

	// Execute the webform.html template specifically
	err = tmpl.ExecuteTemplate(w, "webform.html", data)
	if err != nil {
		log.Error().Err(err).Stack().Msg("Failed to execute webform template")
		http.Error(w, "Internal Server Error", http.StatusInternalServerError)
		return
	}
}

// HandleWebformPost handles POST requests to the webform endpoint
func (s *WebformServer) HandleWebformPost(w http.ResponseWriter, r *http.Request) {
	// Extract authenticated user from IAP header
	userEmail := r.Header.Get("X-Goog-Authenticated-User-Email")
	var username string
	if userEmail != "" && strings.HasSuffix(userEmail, "@augmentcode.com") && strings.HasPrefix(userEmail, "accounts.google.com:") {
		username = strings.TrimSuffix(strings.TrimPrefix(userEmail, "accounts.google.com:"), "@augmentcode.com")
	}
	if username == "" {
		log.Error().Msg("Unauthenticated request")
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	if err := r.ParseForm(); err != nil {
		log.Error().Err(err).Msg("Failed to parse form")
		http.Error(w, "Bad Request", http.StatusBadRequest)
		return
	}

	// Extract form data
	tenantName := r.FormValue("tenant_name")
	domain := r.FormValue("domain")
	cloud := r.FormValue("cloud")
	tier := r.FormValue("tier")
	comment := r.FormValue("comment")

	// Check for debug options
	debugReadonly := r.FormValue("debug_readonly") == "on"
	debugNoop := r.FormValue("debug_noop") == "on"

	// Trim whitespace from domain
	domain = strings.TrimSpace(domain)

	// Default tier to ENTERPRISE if not provided
	if tier == "" {
		tier = "ENTERPRISE"
	}

	// Create template data
	templateData := TemplateData{
		TenantName: tenantName,
		Domain:     domain,
		Cloud:      cloud,
		Tier:       tier,
		Comment:    comment,
		Requester:  username,
		Debug: map[string]bool{
			"Enabled":  debugReadonly || debugNoop,
			"Readonly": debugReadonly,
			"Noop":     debugNoop,
		},
	}

	// Prepare payload for webhook
	payload := map[string]interface{}{
		"action":      "add-tenant-webform",
		"tenant_name": tenantName,
		"domain":      domain,
		"cloud":       cloud,
		"tier":        tier,
		"comment":     comment,
		"requester":   username,
		"debug": map[string]bool{
			"enabled":  debugReadonly || debugNoop,
			"readonly": debugReadonly,
			"noop":     debugNoop,
		},
	}

	// Convert payload to JSON
	jsonData, err := json.Marshal(payload)
	if err != nil {
		log.Error().Err(err).Msg("Failed to marshal JSON")
		http.Error(w, "Internal Server Error", http.StatusInternalServerError)
		return
	}

	// Create the request
	req, err := http.NewRequest("POST", s.webhookEndpoint, bytes.NewBuffer(jsonData))
	if err != nil {
		log.Error().Err(err).Msg("Failed to create request")
		http.Error(w, "Internal Server Error", http.StatusInternalServerError)
		return
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", s.authToken))

	// Send POST request to webhook endpoint
	resp, err := s.httpClient.Do(req)
	if err != nil {
		log.Error().Err(err).Msg("Failed to send webhook request")
		http.Error(w, "Internal Server Error", http.StatusInternalServerError)
		return
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		log.Error().Int("status", resp.StatusCode).Msg("Webhook request failed")
		http.Error(w, "Internal Server Error", http.StatusInternalServerError)
		return
	}

	// Show confirmation page
	tmpl, err := template.ParseFS(templates, "templates/styles.tmpl", "templates/confirmation.html")
	if err != nil {
		log.Error().Err(err).Msg("Failed to parse confirmation template")
		http.Error(w, "Internal Server Error", http.StatusInternalServerError)
		return
	}

	err = tmpl.ExecuteTemplate(w, "confirmation.html", templateData)
	if err != nil {
		log.Error().Err(err).Msg("Failed to execute confirmation template")
		http.Error(w, "Internal Server Error", http.StatusInternalServerError)
		return
	}
}

func (s *WebformServer) HandleFavicon(w http.ResponseWriter, r *http.Request) {
	favicon, err := templates.ReadFile("templates/favicon.ico")
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}
	w.Header().Set("Content-Type", "image/x-icon")
	w.Write(favicon)
}
