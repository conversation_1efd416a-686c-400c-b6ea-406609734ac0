{{define "webform.html"}}
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Tenant Configuration</title>
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    {{template "styles"}}
    <script>
        function validateForm() {
            const tenantName = document.getElementById('tenant_name').value.trim();
            const domain = document.getElementById('domain').value.trim();

            // Validate tenant name:
            // - must start with a lowercase letter
            // - can only contain lowercase letters, numbers, and hyphens
            // - max 16 characters
            if (!/^[a-z][a-z0-9-]{0,15}$/.test(tenantName)) {
                alert('Tenant name must:\n- Start with a lowercase letter\n- Contain only lowercase letters, numbers, and hyphens\n- Be 16 characters or less');
                return false;
            }

            // Validate domain format
            if (!/^[a-zA-Z0-9][a-zA-Z0-9-_.]+\.[a-zA-Z]{2,}$/.test(domain)) {
                alert('Please enter a valid domain name');
                return false;
            }

            return true;
        }
    </script>
</head>
<body>
    <div class="container">
        {{if .Debug.Enabled}}
        <div class="debug-banner">
            Debug Mode - For Development Use Only
        </div>
        {{end}}

        <h1>Tenant Configuration</h1>

        <form method="POST" action="/" onsubmit="return validateForm()">
            <div class="form-group">
                <label for="tenant_name">Tenant Name: <span class="required">*</span></label>
                <input type="text" id="tenant_name" name="tenant_name" required
                       pattern="^[a-z][a-z0-9-]{0,15}$"
                       maxlength="16"
                       title="Must start with a lowercase letter, contain only lowercase letters, numbers, and hyphens, and be 16 characters or less">
            </div>

            <div class="form-group">
                <label for="domain">Domain: <span class="required">*</span></label>
                <input type="text" id="domain" name="domain" required
                       pattern="[a-zA-Z0-9][a-zA-Z0-9-_.]+\.[a-zA-Z]{2,}"
                       title="Please enter a valid domain name">
            </div>

            <div class="form-group">
                <label for="tier">Tier: <span class="required">*</span></label>
                <select id="tier" name="tier" required>
                    {{range $index, $option := .TierOptions}}
                    <option value="{{$option}}" {{if eq $option "ENTERPRISE"}}selected{{end}}>
                        {{title (lower $option)}}
                    </option>
                    {{end}}
                </select>
            </div>

            <div class="form-group">
                <label for="cloud">Cloud: <span class="required">*</span></label>
                <select id="cloud" name="cloud" required>
                    <option value="GCP_US_CENTRAL1_PROD" selected>GCP US Central 1 (Production)</option>
                    <option value="GCP_EU_WEST4_PROD">GCP EU West 4 (Production)</option>
                </select>
            </div>

            <div class="form-group">
                <label for="comment">Comment:</label>
                <textarea id="comment" name="comment" rows="3" maxlength="500"></textarea>
            </div>

            {{if .Debug.Enabled}}
            <div class="debug-options">
                <h2>Debug Options</h2>
                <div class="checkbox-group">
                    <input type="checkbox" id="debug_readonly" name="debug_readonly">
                    <label for="debug_readonly">Read-only Mode (Create PR but don't write to DB)</label>
                </div>
                <div class="checkbox-group">
                    <input type="checkbox" id="debug_noop" name="debug_noop">
                    <label for="debug_noop">No-op Mode (Don't perform any actions)</label>
                </div>
            </div>
            {{end}}

            <button type="submit">Submit Configuration</button>
        </form>
    </div>
</body>
</html>
{{end}}
