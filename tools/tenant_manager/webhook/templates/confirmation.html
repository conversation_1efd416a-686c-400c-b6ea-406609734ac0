{{define "confirmation.html"}}
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Configuration Submitted</title>
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    {{template "styles"}}
</head>
<body>
    <div class="container">
        {{if .Debug.Enabled}}
        <div class="debug-banner">
            Debug Mode - For Development Use Only
        </div>
        {{end}}

        <div class="success-banner">
            Configuration Submitted Successfully
        </div>

        <div class="success-icon">✓</div>

        <div class="details">
            <div class="detail-row">
                <div class="detail-label">Tenant Name:</div>
                <div class="detail-value">{{.TenantName}}</div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Domain:</div>
                <div class="detail-value">{{.Domain}}</div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Tier:</div>
                <div class="detail-value">{{.Tier}}</div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Cloud:</div>
                <div class="detail-value">{{.Cloud}}</div>
            </div>
            {{if .Comment}}
            <div class="detail-row">
                <div class="detail-label">Comment:</div>
                <div class="detail-value">{{.Comment}}</div>
            </div>
            {{end}}
            <div class="detail-row">
                <div class="detail-label">Requested By:</div>
                <div class="detail-value">{{.Requester}}</div>
            </div>
            {{if .Debug.Enabled}}
            <div class="detail-row">
                <div class="detail-label">Debug Options:</div>
                <div class="detail-value">
                    {{if .Debug.Readonly}}<span class="debug-chip">Read-only</span>{{end}}
                    {{if .Debug.Noop}}<span class="debug-chip">No-op</span>{{end}}
                </div>
            </div>
            {{end}}
        </div>
        {{if .Debug.Enabled}}
            <a href="/debug" class="back-link">Back to Form</a>
        {{else}}
            <a href="/" class="back-link">Back to Form</a>
        {{end}}
    </div>
</body>
</html>
{{end}}
