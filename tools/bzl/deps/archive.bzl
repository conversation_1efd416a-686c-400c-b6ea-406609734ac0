load("@cloud_archive//:cloud_archive.bzl", "gs_archive")
load("@bazel_tools//tools/build_defs/repo:http.bzl", "http_archive")

BUCKET_NAME = "augment-bazel-data"
BUCKET_URL = "https://storage.googleapis.com/" + BUCKET_NAME + "/"

_ALL_CONTENT = """\
filegroup(
    name = "all",
    srcs = glob(["**"]),
    visibility = ["//visibility:public"],
)
"""

def archive(name, sha256, object_name, build_file = None):
    gs_archive(
        name = name,
        build_file_contents = _ALL_CONTENT if build_file == None else None,
        sha256 = sha256,
        bucket = BUCKET_NAME,
        file_path = object_name,
        build_file = build_file,
    )

def http_archive_wrapper(name, sha256, object_name):
    http_archive(
        name = name,
        build_file_content = _ALL_CONTENT,
        url = BUCKET_URL + object_name,
        sha256 = sha256,
    )

def setup_archive():
    """Setup the archive download from the augment-bazel-data S3 bucket.

    The console link for this bucket is at https://console.cloud.google.com/storage/browser/augment-bazel-data.
    """

    # Uploaded from /mnt/efs/augment/checkpoints/codegen-350M-multi
    archive(
        name = "codegen-350M-multi",
        sha256 = "93cd8db1d52ab24566eacfbb46563a3bfe9919e46bcecf1ed9712040d84ce97a",  # pragma: allowlist secret
        object_name = "codegen-350M-multi.tar.gz",
    )

    # Uploaded from /mnt/efs/augment/external_models/starcoderbase/vocab.json
    http_archive_wrapper(
        name = "starcoder_base_vocab",
        sha256 = "1a8f51a1fd682bc9d7692a86f8ab541dcab068a5f88fe05e6a8a15b93272c6bc",  # pragma: allowlist secret
        object_name = "public/starcoder_base_vocab.tar.gz",
    )

    # Uploaded from /mnt/efs/augment/external_models/starcoder2base/vocab.json
    http_archive_wrapper(
        name = "starcoder2_base_vocab",
        sha256 = "a569bbdaf917aaa7b86b00b62c67997517984672f3a7f4140abbe1bb120f93db",  # pragma: allowlist secret
        object_name = "public/starcoder2_base_vocab.tar.gz",
    )

    # Uploaded from /mnt/efs/augment/checkpoints/starcoderbase-1b_neox/checkpoint
    archive(
        name = "starcoderbase_1b_deepspeed_checkpoint",
        sha256 = "7a6c1335b59243e17bf86bd311dcb1d3b41fa08ecea7ac44475df9458ecb3741",  # pragma: allowlist secret
        object_name = "starcoderbase_1b_deepspeed_checkpoint.tar.gz",
    )

    # Uploaded from /mnt/efs/augment/checkpoints/deepseek/deepseek-coder-33b-instruct/deepseek_coder_instruct.tar.gz
    http_archive_wrapper(
        name = "deepseek_coder_instruct",
        sha256 = "4e3908e7de06c88d4824d243ab1b0a542158e777a40d7b7ed6702f2dc24c4359",  # pragma: allowlist secret
        object_name = "public/deepseek_coder_instruct.tar.gz",
    )

    # Uploaded from /mnt/efs/augment/checkpoints/databricks/dbrx-instruct/public/dbrx_instruct_vocab.tar.gz
    http_archive_wrapper(
        name = "dbrx_instruct_vocab",
        sha256 = "cc97b4d01082995a1ab9f3a0580c99f1fc138c2fba352ff792da2e2118da181f",
        object_name = "public/dbrx_instruct_vocab.tgz",
    )

    # Uploaded from /mnt/efs/augment/checkpoints/llama3/hf/Meta-Llama-3-8B-adjusted-vocab/llama3_instruct.tar.gz
    http_archive_wrapper(
        name = "llama3_instruct",
        sha256 = "69b93d0d9228a90088085ee1a7c352bc7accb0a909a1cdfeefe6505ab802be6e",  # pragma: allowlist secret
        object_name = "public/llama3_instruct.tar.gz",
    )

    # Uploaded from /mnt/efs/augment/checkpoints/deepseek/DeepSeek-Coder-V2-Lite-Base/deepseek_coder_v2_base_vocab.tgz
    http_archive_wrapper(
        name = "deepseek_coder_v2_base_vocab",
        sha256 = "18cdc9d2e519d00bdb6ec5d386fbdb28ad16c4ec83e9fd99d12bcdbf17d1b4b4",  # pragma: allowlist secret
        object_name = "public/deepseek_coder_v2_base_vocab.tgz",
    )

    # Uploaded from /mnt/efs/augment/checkpoints/qwen25-coder/Qwen2.5-Coder-7B/qwen25coder_vocab.tar.gz
    http_archive_wrapper(
        name = "qwen25coder_vocab",
        sha256 = "48d493ff5684f5756184d79a3f59526e39dec828d1d2ec496ec487e6e78ed250",  # pragma: allowlist secret
        object_name = "public/qwen25coder_vocab.tar.gz",
    )

    # Uploaded from /mnt/efs/augment/checkpoints/qwen3/Qwen3-32B/qwen3_vocab.tar.gz
    http_archive_wrapper(
        name = "qwen3_vocab",
        sha256 = "52cfcfecbd2a00a1d217cbb5825b10e60c3eeacb9f79da6cb549c6e0c6a929e4",  # pragma: allowlist secret
        object_name = "public/qwen3_vocab.tar.gz",
    )

    archive(
        name = "bigtable_emulator_amd64",
        sha256 = "af4b01b0a3e8b07286533d99f2f25d10a47ef8fcf2627397f97b627484c87a5b",  # pragma: allowlist secret
        object_name = "bigtable-emulator-2024-02-02.tar.gz",
    )

    # From https://github.com/goccy/bigquery-emulator.
    # MIT License
    archive(
        name = "bigquery_emulator_amd64",
        sha256 = "5afbcdec61f2ec9eef2132a626bcad43626c26d16b1870652449a6a2806bd56e",  # pragma: allowlist secret
        object_name = "bigquery-emulator-0.6.2.tar.gz",
    )

    # From https://github.com/GoogleCloudPlatform/cloud-spanner-emulator.
    # Apache-2.0 license
    archive(
        name = "spanner_emulator_amd64",
        sha256 = "c5b1482ff8f58079f2a142db8d49419723783edf0c6bc41d7382228bb14f9cb3",  # pragma: allowlist secret
        object_name = "cloud-spanner-emulator_linux_amd64-1.5.29.tar.gz",
    )

    # From ~/.local/google-cloud-sdk/platform/pubsub-emulator/lib after installing the component
    archive(
        name = "cloud_pubsub_emulator_jar",
        sha256 = "d2edfe9bd60457ec50caab0bb95d7ce280b7d7698aabe44a615f4ddd6ee60c80",  # pragma: allowlist secret
        object_name = "cloud-pubsub-emulator-0.8.17-all.jar",
    )

    archive(
        name = "gke-gcloud-auth-plugin",
        sha256 = "4f1070f4b24397b519b0d41a71080bf284586e03c5a017f5777d9bd7b4dcf6ff",
        object_name = "gke-gcloud-auth-plugin-0.5.9.tar.gz",
    )

    archive(
        name = "lm_evaluation_harness",
        sha256 = "6cd89bc8bad24b6b9949d2193f04d7cd4149bbec519c85c2e62101015fc2b3b2",
        object_name = "lm_evaluation_harness.tar.gz",
    )

    archive(
        name = "prism_eldenv4_model",
        sha256 = "e7cc8eb5e622d01c741dc83e97b8295dc2f67dece5f77d02afcbc286519ab400",  # pragma: allowlist secret
        object_name = "prism_eldenv4.tar.gz",
    )

    archive(
        name = "vim_9_1_891",
        sha256 = "01e33ebdddd59abe84a515facc7151cb9428612d5bd68bb1e61be11bf5f3ad38",
        object_name = "vim_9_1_891_ubuntu_2004.tar.gz",
    )

    archive(
        name = "tf_archive",
        sha256 = "aabf7a9f41cfe8a3b3a010321962d173dc6d2320872bf4d7e0f5e5aeabf7bbbb",
        object_name = "tensorflow.tar.gz",
        build_file = "//third_party/scann:tf.BUILD",
    )
