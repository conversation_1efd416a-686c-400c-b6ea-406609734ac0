load(
    "@bazel_tools//tools/build_defs/repo:git.bzl",
    "git_repository",
    "new_git_repository",
)
load("@bazel_tools//tools/build_defs/repo:http.bzl", "http_archive", "http_file")

def setup_deps():
    http_file(
        name = "k8s_binary",
        downloaded_file_path = "kubectl",
        executable = True,
        sha256 = "aaa7e6ff3bd28c262f2d95c8c967597e097b092e9b79bcb37de699e7488e3e7b",
        urls = ["https://dl.k8s.io/release/v1.32.5/bin/linux/amd64/kubectl"],
    )

    http_file(
        name = "bazelisk",
        sha256 = "fd8fdff418a1758887520fa42da7e6ae39aefc788cf5e7f7bb8db6934d279fc4",
        downloaded_file_path = "bazel",
        url = "https://github.com/bazelbuild/bazelisk/releases/download/v1.25.0/bazelisk-linux-amd64",
        executable = True,
    )

    http_archive(
        name = "hedron_compile_commands",
        sha256 = "658122cfb1f25be76ea212b00f5eb047d8e2adc8bcf923b918461f2b1e37cdf2",  # pragma: allowlist secret
        url = "https://github.com/hedronvision/bazel-compile-commands-extractor/archive/4f28899228fb3ad0126897876f147ca15026151e.tar.gz",
        strip_prefix = "bazel-compile-commands-extractor-4f28899228fb3ad0126897876f147ca15026151e",
    )

    http_archive(
        name = "cloud_archive",
        strip_prefix = "cloud_archive-0.3.1",
        sha256 = "15f2022c8d5d925d1a91ab8caf0a0c74982c97550ea79c3216ba16696cf1420d",  # pragma: allowlist secret
        urls = ["https://github.com/1e100/cloud_archive/archive/refs/tags/0.3.1.tar.gz"],
    )

    http_archive(
        name = "trivy",
        sha256 = "a62f2b2f369535c59bd869caf98330a42c8068263385439354a469329a2b265c",
        urls = ["https://github.com/aquasecurity/trivy/releases/download/v0.55.2/trivy_0.55.2_Linux-64bit.tar.gz"],
        build_file_content = """filegroup(
    name = "trivy_file",
    srcs = [":trivy"],
)

genrule(name="trivy_bin", executable = 1, cmd = "cp $< $@", srcs = [":trivy_file"], outs = ["trivy.bin"],
    visibility = ["//visibility:public"])
""",
    )

    git_repository(
        name = "bazel_clang_tidy",
        commit = "41da3e63c5159a4f4e12d4477e41e84d623afcc4",
        remote = "https://github.com/dmeister/bazel_clang_tidy.git",
    )

    # to update visit https://chromium.googlesource.com/chromium/src/build/+/refs/heads/main/linux/sysroot_scripts/sysroots.json,
    # select new sysroot and update the configuration below.
    http_archive(
        name = "org_chromium_sysroot_linux_x64",
        build_file_content = """
filegroup(
  name = "sysroot",
  srcs = glob(["*/**"]),
  visibility = ["//visibility:public"],
)
""",
        sha256 = "1be60e7c456abc590a613c64fab4eac7632c81ec6f22734a61b53669a4407346",
        urls = ["https://commondatastorage.googleapis.com/chrome-linux-sysroot/toolchain/2028cdaf24259d23adcff95393b8cc4f0eef714b/debian_bullseye_amd64_sysroot.tar.xz"],
    )

    new_git_repository(
        name = "thrust",
        commit = "1ac51f2b6219ff17d15d93f2e0be85038556f346",  # pragma: allowlist secret
        recursive_init_submodules = True,
        remote = "https://github.com/NVIDIA/thrust.git",
        shallow_since = "1663087176 -0400",
        build_file = "//third_party:thrust.BUILD",
    )

    # mirror of torch-2.5.1-cp311-cp311-manylinux1_x86_64.whl
    http_archive(
        name = "libtorch_2_5_1_archive",
        build_file = "@//third_party:libtorch.BUILD",
        urls = [
            "https://storage.googleapis.com/augment-bazel-data/public/libtorch_2_5_1_archive.tar.gz",
        ],
    )

    http_archive(
        name = "libcudacxx",
        build_file = "//third_party:libcu++.BUILD",
        sha256 = "05b1435ad65f5bdef1bb8d1eb29dc8f0f7df34c01d77bf8686687a4603588bce",  # pragma: allowlist secret
        strip_prefix = "libcudacxx-1.9.0",
        urls = ["https://github.com/NVIDIA/libcudacxx/archive/refs/tags/1.9.0.tar.gz"],
    )

    http_archive(
        name = "cub",
        build_file = "//third_party:cub.BUILD",
        sha256 = "1013a595794548c359f22c07e1f8c620b97e3a577f7e8496d9407f74566a3e2a",  # pragma: allowlist secret
        strip_prefix = "cub-1.17.2",
        urls = ["https://github.com/NVIDIA/cub/archive/refs/tags/1.17.2.tar.gz"],
    )

    http_archive(
        sha256 = "00de635068cb293b073a09dff9f1f123cabab0eba704e622b66d6043990f62a0",  # pragma: allowlist secret
        name = "bazel",
        strip_prefix = "bazel-6.1.0",
        url = "https://github.com/bazelbuild/bazel/archive/refs/tags/6.1.0.tar.gz",
        repo_mapping = {"@com_google_protobuf": "@protobuf"},
    )

    http_file(
        name = "openai_gpt2_vocab",
        sha256 = "1ce1664773c50f3e0cc8842619a93edc4624525b728b188a9e0be33b7726adc5",  # pragma: allowlist secret
        url = "https://openaipublic.blob.core.windows.net/gpt-2/encodings/main/vocab.bpe",
    )

    http_archive(
        name = "pybind11_bazel",
        strip_prefix = "pybind11_bazel-2.12.0",
        sha256 = "dc14a960672babf6da2f283079a5b5c13e404a940ea7cdb8297b71f8f31643a5",
        urls = ["https://github.com/pybind/pybind11_bazel/releases/download/v2.12.0/pybind11_bazel-2.12.0.tar.gz"],
    )

    # We still require the pybind library.
    # Keep this in sync with tools/python_deps/requirements.txt
    http_archive(
        name = "pybind11",
        sha256 = "bf8f242abd1abcd375d516a7067490fb71abd79519a282d22b6e4d19282185a7",
        build_file = "@pybind11_bazel//:pybind11-BUILD.bazel",
        strip_prefix = "pybind11-2.12.0",
        urls = ["https://github.com/pybind/pybind11/archive/v2.12.0.tar.gz"],
    )
