local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
function(cloud, env, namespace, namespace_config)
  local appName = 'metrics-bes-rpc';
  local domainSuffix = cloudInfo[cloud].internalDomainSuffix;
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);
  local service = {
    apiVersion: 'v1',
    kind: 'Service',
    metadata: {
      name: appName + '-svc',
      namespace: namespace,
      annotations: if env == 'PROD' then {
        'networking.gke.io/load-balancer-type': 'Internal',
        'external-dns.alpha.kubernetes.io/hostname': 'bazel-metrics.%s' % domainSuffix,
      },
      labels: {
        app: appName,
      },
    },
    spec: {
      type: if env == 'PROD' then 'LoadBalancer' else null,
      selector: {
        app: appName,
      },
      ports: [
        {
          protocol: 'TCP',
          port: 50051,
          targetPort: 'grpc-svc',
        },
      ],
    },
  };
  local container =
    {
      name: 'metrics-bes',
      target: {
        name: '//tools/bzl/bes:image',
        dst: 'metrics-bes-server',
      },
      ports: [
        {
          containerPort: 50051,
          name: 'grpc-svc',
        },
      ],
      readinessProbe: grpcLib.grpcHealthCheck(null, tls=false) + {
        periodSeconds: 30,
      },
      livenessProbe: grpcLib.grpcHealthCheck(null, tls=false) + {
        periodSeconds: 30,
      },
      resources: {
        limits: {
          cpu: 0.5,
          memory: '128Mi',
        },
      },
    };
  local pod =
    {
      priorityClassName: cloudInfo.envToPriorityClass(env),
      tolerations: tolerations,
      affinity: affinity,
      containers: [
        container,
      ],
    };
  local deployment =
    {
      apiVersion: 'apps/v1',
      kind: 'Deployment',
      metadata: {
        name: appName,
        namespace: namespace,
        labels: {
          app: appName,
        },
        annotations: {
          'reloader.stakater.com/search': 'true',
        },
      },
      spec: {
        replicas: 1,
        strategy: {
          type: 'RollingUpdate',
          rollingUpdate: {
            maxSurge: 1,
            maxUnavailable: 0,
          },
        },
        selector: {
          matchLabels: {
            app: appName,
          },
        },
        template: {
          metadata: {
            labels: {
              app: appName,
            },
          },
          spec: pod,
        },
      },
    };
  [
    service,
    deployment,
  ]
