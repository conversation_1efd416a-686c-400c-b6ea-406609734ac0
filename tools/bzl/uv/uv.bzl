load("@bazel_tools//tools/build_defs/repo:http.bzl", "http_file")

_UV_VERSION = "0.7.2"
_UV_SHA256 = "cfaab1b5166a6439ff66f020333d3a12bbdf622deee3b510718283e8f06c9de7"

def _uv_extension_impl(mctx):
    http_file(
        name = "uv",
        url = "https://github.com/astral-sh/uv/releases/download/{}/uv-x86_64-unknown-linux-gnu.tar.gz".format(_UV_VERSION),
        sha256 = _UV_SHA256,
        downloaded_file_path = "uv-x86_64-unknown-linux-gnu.tar.gz",
    )

uv_extension = module_extension(implementation = _uv_extension_impl)
