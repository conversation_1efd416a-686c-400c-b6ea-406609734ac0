load("@aspect_bazel_lib//lib:tar.bzl", "tar")

package(default_visibility = ["//visibility:public"])

genrule(
    name = "uv",
    srcs = ["@uv//file"],
    outs = ["uv"],
    cmd_bash = """
    $(BSDTAR_BIN) -C '$(@D)' --strip-components=1 -xzf '$<' "uv-x86_64-unknown-linux-gnu/$$(basename '$@')"
    """,
    executable = True,
    toolchains = ["@bsd_tar_toolchains//:resolved_toolchain"],
)

genrule(
    name = "uvx",
    srcs = ["@uv//file"],
    outs = ["uvx"],
    cmd_bash = """
    $(BSDTAR_BIN) -C '$(@D)' --strip-components=1 -xzf '$<' "uv-x86_64-unknown-linux-gnu/$$(basename '$@')"
    """,
    executable = True,
    toolchains = ["@bsd_tar_toolchains//:resolved_toolchain"],
)

genrule(
    name = "uv_completions",
    outs = [
        "uv.bash",
        "uv.fish",
        "uv.zsh",
    ],
    cmd_bash = """
    for s in bash fish zsh; do
        $(location :uv) generate-shell-completion "$$s" > "$(@D)/uv.$$s"
    done
    """,
    tools = [":uv"],
)

tar(
    name = "uv_oci_layer",
    srcs = [
        ":uv",
        ":uv.bash",
        ":uv.fish",
        ":uv.zsh",
        ":uvx",
    ],
    out = "uv.oci-layer.tgz",
    mtree = [
        "usr               uid=0 gid=0 mode=0755 type=dir",
        "usr/local         uid=0 gid=0 mode=0755 type=dir",
        "usr/local/bin     uid=0 gid=0 mode=0755 type=dir",
        "usr/local/bin/uv  uid=0 gid=0 mode=0755 type=file time=0 content=$(location :uv)",
        "usr/local/bin/uvx uid=0 gid=0 mode=0755 type=file time=0 content=$(location :uvx)",
        "usr/local/share                                    uid=0 gid=0 mode=0755 type=dir",
        "usr/local/share/bash-completion                    uid=0 gid=0 mode=0755 type=dir",
        "usr/local/share/bash-completion/completions        uid=0 gid=0 mode=0755 type=dir",
        "usr/local/share/bash-completion/completions/uv     uid=0 gid=0 mode=0644 type=file time=0 content=$(location :uv.bash)",
        "usr/local/share/fish/vendor_completions.d          uid=0 gid=0 mode=0755 type=dir",
        "usr/local/share/fish/vendor_completions.d/uv.fish  uid=0 gid=0 mode=0644 type=file time=0 content=$(location :uv.fish)",
        "usr/local/share/zsh/site-functions                 uid=0 gid=0 mode=0755 type=dir",
        "usr/local/share/zsh/site-functions/_uv             uid=0 gid=0 mode=0644 type=file time=0 content=$(location :uv.zsh)",
    ],
)
