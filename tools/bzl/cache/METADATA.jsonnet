{
  deployment: [
    {
      name: 'bazel-cache',
      kubecfg: {
        target: '//tools/bzl/cache:kubecfg',
        task: [
          {
            cloud: 'GCP_US_CENTRAL1_PROD',
            env: 'PROD',
            namespace: 'devtools',
          },
          {
            cloud: 'GCP_US_CENTRAL1_DEV',
            env: 'PROD',
            namespace: 'devtools',
          },
        ],
      },
      health: {
        tier: 'TIER_1_B',
        experts: {
          users: ['dirk'],
          slack_channel: '#system-services',
        },
      },
    },
    // tombstone bazel-cache-2 via app in all clouds
    {
      name: 'bazel-cache-2-tombstone',
      kubecfg_tombstone: {
        object: [
          {
            kind: 'app',
            name: 'bazel-cache-2',
          },
        ],
        task: [
          {
            cloud: 'GCP_US_CENTRAL1_PROD',
            env: 'PROD',
            namespace: 'devtools',
          },
          {
            cloud: 'GCP_US_CENTRAL1_DEV',
            env: 'PROD',
            namespace: 'devtools',
          },
        ],
      },
    },
  ],
}
