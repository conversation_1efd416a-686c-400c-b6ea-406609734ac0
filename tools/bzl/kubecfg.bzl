load("@bazel_skylib//lib:paths.bzl", "paths")
load(
    "@rules_jsonnet//jsonnet:jsonnet.bzl",
    "JsonnetLibraryInfo",
)
load("//tools/bzl:jsonnet_lint.bzl", "jsonnet_lint_test")

_JSONNET_FILETYPE = [
    ".jsonnet",
    ".libsonnet",
    ".json",
]

_FILETYPE = [
    ".jsonnet",
    ".yaml",
]

def _get_import_paths(label, files, imports):
    return [
        # Implicitly add the workspace root as an import path.
        paths.join(".", file.root.path, label.workspace_root)
        for file in files
    ] + [
        # Explicitly provided import paths.
        paths.join(".", file.root.path, label.workspace_root, label.package, im)
        for file in files
        for im in imports
    ]

def _setup_deps(deps):
    """Collects source files and import flags of transitive dependencies.

    Args:
      deps: List of deps labels from ctx.attr.deps.

    Returns:
      Returns a struct containing the following fields:
        transitive_sources: List of Files containing sources of transitive
            dependencies
    """
    transitive_sources = []
    imports = []
    for dep in deps:
        transitive_sources.append(dep[JsonnetLibraryInfo].transitive_jsonnet_files)
        imports.append(dep[JsonnetLibraryInfo].imports)

    return struct(
        imports = depset(transitive = imports),
        transitive_sources = depset(transitive = transitive_sources, order = "postorder"),
    )

def _kubecfg_library_impl(ctx):
    """Implementation of the kubecfg_library rule."""
    depinfo = _setup_deps(ctx.attr.deps)
    sources = depset(ctx.files.srcs, transitive = [depinfo.transitive_sources])
    imports = depset(
        _get_import_paths(ctx.label, ctx.files.srcs, ctx.attr.imports),
        transitive = [depinfo.imports],
    )
    transitive_data = depset(
        transitive = [dep.data_runfiles.files for dep in ctx.attr.deps],
    )

    return [
        DefaultInfo(
            files = depset(ctx.files.srcs),
            runfiles = ctx.runfiles(
                transitive_files = transitive_data,
                collect_data = True,
            ),
        ),
        JsonnetLibraryInfo(
            imports = imports,
            transitive_jsonnet_files = sources,
        ),
    ]

KubecfgDataInfo = provider("KubeCfg outout", fields = ["srcs", "cluster_wide", "cloud"])

def _kubecfg_impl(ctx):
    """Implementation of the kubecfg_ rule."""

    depinfo = _setup_deps(ctx.attr.deps)

    kubecfg_command = " ".join(
        [ctx.executable.kubecfg.short_path],
    )

    args = []
    args.append("--file")
    args.append(ctx.file.src.short_path)
    if ctx.attr.cluster_wide:
        args.append("--cluster-wide")
    for cloud in ctx.attr.cloud:
        args.append("--allowed-cloud")
        args.append(cloud)
    if ctx.attr.norewrite:
        args.append("--no-rewrite")
    for arg in ctx.attr.extra_kubectl_args:
        args.append("--extra-kubectl-args=\"%s\"" % arg)

    command = """#!/bin/bash
set -eu
 # --- begin runfiles.bash initialization v3 ---
       # Copy-pasted from the Bazel Bash runfiles library v3.
       set -uo pipefail; set +e; f=bazel_tools/tools/bash/runfiles/runfiles.bash
       source "${RUNFILES_DIR:-/dev/null}/$f" 2>/dev/null || \
         source "$(grep -sm1 "^$f " "${RUNFILES_MANIFEST_FILE:-/dev/null}" | cut -f2- -d' ')" 2>/dev/null || \
         source "$0.runfiles/$f" 2>/dev/null || \
         source "$(grep -sm1 "^$f " "$0.runfiles_manifest" | cut -f2- -d' ')" 2>/dev/null || \
         source "$(grep -sm1 "^$f " "$0.exe.runfiles_manifest" | cut -f2- -d' ')" 2>/dev/null || \
         { echo>&2 "ERROR: cannot find $f"; exit 1; }; f=; set -e
  # --- end runfiles.bash initialization v3 ---

""".split("\n") + [
        '%s %s "$@"' % (kubecfg_command, " ".join(args)),
    ]
    output_sh = ctx.actions.declare_file("%s.sh" % ctx.label.name)

    ctx.actions.write(
        output = output_sh,
        content = "\n".join(command),
        is_executable = True,
    )

    depinfo = _setup_deps(ctx.attr.deps)
    sources = depset(ctx.files.src, transitive = [depinfo.transitive_sources])
    imports = depset(
        _get_import_paths(ctx.label, ctx.files.src, ctx.attr.imports),
        transitive = [depinfo.imports],
    )
    transitive_data = depset(
        transitive = [dep.data_runfiles.files for dep in ctx.attr.deps],
    )

    inputs = (
        [ctx.executable.kubecfg] + [ctx.file.src] +
        transitive_data.to_list() +
        depinfo.transitive_sources.to_list() +
        ctx.attr.kubecfg[DefaultInfo].data_runfiles.files.to_list()
    )

    return [
        DefaultInfo(
            files = depset([output_sh]),
            runfiles = ctx.runfiles(
                files = inputs,
                transitive_files = transitive_data,
                collect_data = True,
            ),
            executable = output_sh,
        ),
        JsonnetLibraryInfo(
            imports = imports,
            transitive_jsonnet_files = sources,
        ),
        KubecfgDataInfo(srcs = [ctx.file.src], cluster_wide = ctx.attr.cluster_wide, cloud = ctx.attr.cloud),
    ]

def _kubecfg_json_impl(ctx):
    """Implementation of the kubecfg_json_ rule."""

    output_json = ctx.actions.declare_file("%s.json" % ctx.label.name)
    ctx.actions.write(
        output = output_json,
        content = json.encode({
            "cluster_wide": ctx.attr.cluster_wide,
            "allowed_cloud": ctx.attr.cloud,
            "src": ctx.file.src.short_path,
            "norewrite": ctx.attr.norewrite,
            "lint": ctx.attr.lint,
            "lint_allow_multi_apps": ctx.attr.lint_allow_multi_apps,
            "extra_kubectl_args": ctx.attr.extra_kubectl_args,
        }),
    )

    return [DefaultInfo(
        files = depset([output_json]),
    )]

def _kubecfg_multi_impl(ctx):
    """Implementation of the kubecfg multi rule."""

    kubecfg_command = " ".join(
        [ctx.executable.kubecfg.short_path],
    )

    jsonent_src = []
    for dep in ctx.attr.deps:
        jsonent_src.extend(dep[KubecfgDataInfo].srcs)

    command = """#!/bin/bash
set -eu
 # --- begin runfiles.bash initialization v3 ---
       # Copy-pasted from the Bazel Bash runfiles library v3.
       set -uo pipefail; set +e; f=bazel_tools/tools/bash/runfiles/runfiles.bash
       source "${RUNFILES_DIR:-/dev/null}/$f" 2>/dev/null || \
         source "$(grep -sm1 "^$f " "${RUNFILES_MANIFEST_FILE:-/dev/null}" | cut -f2- -d' ')" 2>/dev/null || \
         source "$0.runfiles/$f" 2>/dev/null || \
         source "$(grep -sm1 "^$f " "$0.runfiles_manifest" | cut -f2- -d' ')" 2>/dev/null || \
         source "$(grep -sm1 "^$f " "$0.exe.runfiles_manifest" | cut -f2- -d' ')" 2>/dev/null || \
         { echo>&2 "ERROR: cannot find $f"; exit 1; }; f=; set -e
  # --- end runfiles.bash initialization v3 ---

""".split("\n") + [
        '%s %s "$@"' % (kubecfg_command, " ".join(["--file %s" % src.short_path for src in jsonent_src])),
    ]
    output_sh = ctx.actions.declare_file("%s.sh" % ctx.label.name)

    ctx.actions.write(
        output = output_sh,
        content = "\n".join(command),
        is_executable = True,
    )

    transitive_data = depset(transitive = [dep.data_runfiles.files for dep in ctx.attr.deps] +
                                          [ctx.attr.kubecfg[DefaultInfo].files])
    inputs = (
        [ctx.executable.kubecfg] +
        transitive_data.to_list() +
        ctx.attr.kubecfg[DefaultInfo].data_runfiles.files.to_list()
    )

    return [DefaultInfo(
        runfiles = ctx.runfiles(
            files = inputs,
            transitive_files = transitive_data,
            collect_data = True,
        ),
        executable = output_sh,
    ), KubecfgDataInfo(srcs = jsonent_src)]

def _kubecfg_test_impl(ctx):
    """Implementation of the kubecfg test rule."""

    kubecfg_command = " ".join(
        [ctx.executable.kubecfg_test_util.short_path],
    )

    jsonent_src = []
    jsonent_src.extend(ctx.attr.src[KubecfgDataInfo].srcs)

    all_transitive_deps = depset(transitive = [ctx.attr.src.data_runfiles.files])

    deps_txt = ctx.actions.declare_file("%s.txt" % ctx.label.name)
    ctx.actions.write(
        output = deps_txt,
        content = "\n".join([f.path for f in all_transitive_deps.to_list()]),
    )

    args = []
    for src in jsonent_src:
        args.append("--file")
        args.append(src.short_path)
    if ctx.attr.src[KubecfgDataInfo].cluster_wide:
        args.append("--cluster-wide")
    for cloud in ctx.attr.src[KubecfgDataInfo].cloud:
        args.append("--allowed-cloud")
        args.append(cloud)
    if ctx.attr.allow_multi_apps:
        args.append("--allow-multi-apps")

    args.append("--deps-file")
    args.append(deps_txt.short_path)

    if ctx.attr.kubecfg_test_args:
        args.extend(ctx.attr.kubecfg_test_args)

    command = """#!/bin/bash
set -eu
 # --- begin runfiles.bash initialization v3 ---
       # Copy-pasted from the Bazel Bash runfiles library v3.
       set -uo pipefail; set +e; f=bazel_tools/tools/bash/runfiles/runfiles.bash
       source "${RUNFILES_DIR:-/dev/null}/$f" 2>/dev/null || \
         source "$(grep -sm1 "^$f " "${RUNFILES_MANIFEST_FILE:-/dev/null}" | cut -f2- -d' ')" 2>/dev/null || \
         source "$0.runfiles/$f" 2>/dev/null || \
         source "$(grep -sm1 "^$f " "$0.runfiles_manifest" | cut -f2- -d' ')" 2>/dev/null || \
         source "$(grep -sm1 "^$f " "$0.exe.runfiles_manifest" | cut -f2- -d' ')" 2>/dev/null || \
         { echo>&2 "ERROR: cannot find $f"; exit 1; }; f=; set -e
  # --- end runfiles.bash initialization v3 ---

""".split("\n") + [
        '%s %s "$@"' % (kubecfg_command, " ".join(args)),
    ]
    output_sh = ctx.actions.declare_file("%s.sh" % ctx.label.name)

    ctx.actions.write(
        output = output_sh,
        content = "\n".join(command),
        is_executable = True,
    )

    transitive_data = depset(transitive = [ctx.attr.src.data_runfiles.files] +
                                          [ctx.attr.kubecfg_test_util[DefaultInfo].files])
    inputs = (
        [ctx.executable.kubecfg_test_util] +
        transitive_data.to_list() +
        ctx.attr.kubecfg_test_util[DefaultInfo].data_runfiles.files.to_list() +
        [deps_txt]
    )

    return [DefaultInfo(
        runfiles = ctx.runfiles(
            files = inputs,
            transitive_files = transitive_data,
            collect_data = True,
        ),
        executable = output_sh,
    )]

_kubecfg_common_attrs = {
    "data": attr.label_list(
        allow_files = True,
    ),
    "deps": attr.label_list(
        doc = "List of targets that are required by the `srcs` Jsonnet files.",
        providers = [JsonnetLibraryInfo],
        allow_files = False,
    ),
    "imports": attr.string_list(
        doc = "List of import `-J` flags to be passed to the `jsonnet` compiler.",
    ),
}

_kubecfg_library_attrs = {
    "srcs": attr.label_list(
        doc = "List of `.jsonnet` files that comprises this kubecfg library",
        allow_files = _JSONNET_FILETYPE,
    ),
}

kubecfg_library_ = rule(
    implementation = _kubecfg_library_impl,
    attrs = dict(_kubecfg_library_attrs.items() +
                 _kubecfg_common_attrs.items()),
    doc = """kubecfg_library

    A list of jsonnet library files that will be used by kubecfg rules.
    The difference between a plain jsonnet_library and a kubecfg_library is that a kubecfg can
    also depend on data, i.e. 'py_oci_image' targets of any container image used by the library.

    A kubecfg_library target cannot be deployed independently, but it is only useful as dependency of
    kubecfg targets.

    Args:
        srcs: Jsonnet files
        deps: All jsonnet and kubecfg_library targets that the kubecfg rule depends on.
        data: All oci image targets that the kubecfg target depends on, i.e. in a container target configuration.
    """,
)

_kubecfg_attrs = {
    "src": attr.label(
        doc = "The `.jsonnet` file containing the kubernetes generation script.",
        allow_single_file = _FILETYPE,
    ),
    "kubecfg": attr.label(
        default = Label("//tools/kubecfg:kubecfg"),
        cfg = "target",
        executable = True,
    ),
    "cloud": attr.string_list(
        default = ["GCP_US_CENTRAL1_PROD", "GCP_EU_WEST4_PROD", "GCP_US_CENTRAL1_DEV", "GCP_US_CENTRAL1_GSC_PROD"],
    ),
    "cluster_wide": attr.bool(
        doc = "Cluster-wide configuration. No env or namespace is configured for the target",
    ),
    "norewrite": attr.bool(
        doc = "Don't rewrite or stamp this target",
    ),
    "extra_kubectl_args": attr.string_list(
    ),
}

kubecfg_ = rule(
    _kubecfg_impl,
    executable = True,
    attrs = dict(_kubecfg_attrs.items() +
                 _kubecfg_common_attrs.items()),
    doc = """kubecfg

    A rule to expose a kubecfg deployment target.
    A kubecfg deployment target consists of a deployment jsonnet file with a top-level function
    with two top-level arguments: env and namespace

    The jsonnet file should generate a list of objects that represent the Kubernetes configuration
    as documented in https://kubernetes.io/docs/concepts/overview/working-with-objects/.
    However, instead of `image` (https://kubernetes.io/docs/concepts/containers/images/) it contains
    a "target" object. The target has two field:
    - name: The name of a Bazel 'oci_image" bazel target that builds the container image to be used,
    e.g. //services/api_proxy/server:image
    - dst: The container registry name to push to, e.g. '829650725646.dkr.ecr.us-west-2.amazonaws.com/api_proxy_server:dev'.

    The output of the rule is a runnable binary that can be used to deploy to Kuberenetes.

    Args:
        src: The jsonnet src file
        deps: All jsonnet and kubecfg_library targets that the kubecfg rule depends on.
        data: All oci image targets that the kubecfg target depends on, i.e. in a container target configuration.
        cluster_wide: Cluster-wide configuration. No env or namespace is configured for the target. It shoudl be used non-code related deploymnets, e.g. new roles or namespaces.
        norewrite: Don't rewrite or stamp this target. Needed for third-party (usually CRD or helm) kubernetes objects. Usually combined with cluster_wide=True.
        extra_kubectl_args: Extra arguments to pass to kubectl.
    """,
)

_kubecfg_json_attrs = {
    "lint": attr.bool(
        doc = "Run jsonnet lint on the target",
    ),
    "lint_allow_multi_apps": attr.bool(
        doc = "Allow multiple apps in the target",
    ),
}

kubecfg_json_ = rule(
    _kubecfg_json_impl,
    attrs = dict(_kubecfg_attrs.items() +
                 _kubecfg_common_attrs.items() +
                 _kubecfg_json_attrs.items()),
    doc = """kubecfg_json

    """,
)

_kubecfg_multi_attrs = {
    "kubecfg": attr.label(
        default = Label("//tools/kubecfg:kubecfg"),
        cfg = "target",
        executable = True,
    ),
    "deps": attr.label_list(
        doc = "List of kubecfg targets.",
        allow_files = False,
    ),
}

kubecfg_multi = rule(
    _kubecfg_multi_impl,
    executable = True,
    attrs = dict(_kubecfg_multi_attrs.items()),
    doc = """kubecfg_multi

    Combines multi kubecfg targets into a single deployable target.

    Args:
        deps: List of kubecfg targets
    """,
)

_kubecfg_test_attrs = {
    "kubecfg_test_util": attr.label(
        default = Label("//tools/kubecfg:kubecfg_test_util"),
        cfg = "target",
        executable = True,
    ),
    "src": attr.label(
        doc = "A kubecfg targets.",
        allow_files = False,
    ),
    "allow_multi_apps": attr.bool(
        doc = "Allow multiple apps in the target",
    ),
    "kubecfg_test_args": attr.string_list(
        doc = "Extra arguments to pass to the kubecfg_test_util.py script",
    ),
}

kubecfg_test = rule(
    _kubecfg_test_impl,
    test = True,
    attrs = dict(_kubecfg_test_attrs.items()),
    doc = """kubecfg_test

    Tests a kubecfg targets

    Args:
        src: A kubecfg target
    """,
)

def kubecfg_library(
        name,
        srcs,
        **kwargs):
    """kubecfg

    Macro calling kubecfg_library_ and kubecfg_test.

    Args:
        name: The name of the target
        srcs: The jsonnet srcs files
        **kwargs: All other arguments to kubecfg_internal
    """

    kubecfg_library_(
        name = name,
        srcs = srcs,
        **kwargs
    )

def kubecfg(
        name,
        src,
        deps = [],
        data = [],
        lint = True,
        lint_allow_multiple_apps = False,
        kubecfg_test_args = [],
        **kwargs):
    """kubecfg

    Macro calling kubecfg_ and kubecfg_test.

    Args:
        name: The name of the target
        src: The jsonnet src file
        deps: All jsonnet and kubecfg_library targets that the kubecfg rule depends on.
        data: All oci image targets that the kubecfg target depends on, i.e. in a container target configuration.
        lint: Whether to run lint on the target
        lint_allow_multiple_apps: if the kubecfg is allow to have multiple apps in the target.
            Allowing multiple application removes linting. Only use multiple apps if there is a good reason.
            Only enable this flag if there is a good reason.
        kubecfg_test_args: Extra arguments to pass to the kubecfg_test_util.py script
        **kwargs: All other arguments to kubecfg_internal
    """

    kubecfg_json_(
        name = "%s_json" % name,
        src = src,
        lint = lint,
        lint_allow_multi_apps = lint_allow_multiple_apps,
        **kwargs
    )

    kubecfg_(
        name = name,
        src = src,
        deps = deps,
        data = data + [":%s_json" % name],
        **kwargs
    )

    if lint:
        kubecfg_test(
            name = "%s_test" % name,
            src = ":%s" % name,
            tags = ["lint"],
            allow_multi_apps = lint_allow_multiple_apps,
            kubecfg_test_args = kubecfg_test_args,
        )

        if src.endswith(".jsonnet"):
            jsonnet_lint_test(
                name = "%s_lint_test" % name,
                srcs = [src],
                tags = ["lint"],
                deps = deps,
                data = data,
            )
