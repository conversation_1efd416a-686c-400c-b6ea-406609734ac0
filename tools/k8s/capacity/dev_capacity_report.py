import json
import random
import subprocess
from dataclasses import dataclass

DEV_CONTEXT = "gke_system-services-dev_us-central1_us-central1-dev"

_KUBECTL_BIN = "../k8s_binary/file/kubectl"


def get_kubectl_json(command):
    """Runs a kubectl command and returns the JSON output."""
    result = subprocess.run(command, stdout=subprocess.PIPE, check=True)
    return json.loads(result.stdout)


def get_deployments_in_namespace(context, namespace):
    """Get all deployments in a specific namespace."""
    command = [
        _KUBECTL_BIN,
        "--context",
        context,
        "get",
        "deployments",
        "-n",
        namespace,
        "-o",
        "json",
    ]
    return get_kubectl_json(command)


def get_nodes_in_cluster(context):
    """Get all nodes in a cluster."""
    command = [
        _KUBECTL_BIN,
        "--context",
        context,
        "get",
        "nodes",
        "-o",
        "json",
    ]
    return get_kubectl_json(command)


def get_namespaces(context):
    """Get all namespaces in the cluster."""
    command = [
        _KUBECTL_BIN,
        "--context",
        context,
        "get",
        "namespaces",
        "-o",
        "json",
    ]
    return get_kubectl_json(command)


@dataclass
class GPUUsage:
    namespace: str
    deployment: str
    gpus: int
    replicas: int


def main():
    """Main function."""
    gpu_usages = []
    namespaces = get_namespaces(DEV_CONTEXT)
    for namespace in namespaces["items"]:
        namespace_name = namespace["metadata"]["name"]
        deployments = get_deployments_in_namespace(DEV_CONTEXT, namespace_name)

        for deployment in deployments["items"]:
            containers = deployment["spec"]["template"]["spec"]["containers"]

            gpus = sum(
                [
                    int(
                        container.get("resources", {})
                        .get("limits", {})
                        .get("nvidia.com/gpu", "0")
                    )
                    for container in containers
                ]
            )

            replicas = deployment["spec"]["replicas"]

            if gpus > 0 and replicas > 0:
                gpu_usages.append(
                    GPUUsage(
                        namespace=namespace_name,
                        deployment=deployment["metadata"]["name"],
                        gpus=gpus,
                        replicas=replicas,
                    )
                )

        row_format = "{:>40} {:>15} {:>15} {:>15}"
        namespace_gpu_usages = [x for x in gpu_usages if x.namespace == namespace_name]
        if len(namespace_gpu_usages) == 0:
            continue
        print(namespace_name)
        print(row_format.format("Deployment", "GPUs Per Pod", "Pods", "Total GPUs"))
        for gpu_usage in namespace_gpu_usages:
            print(
                row_format.format(
                    gpu_usage.deployment[:40],
                    gpu_usage.gpus,
                    gpu_usage.replicas,
                    gpu_usage.gpus * gpu_usage.replicas,
                )
            )
        print(
            row_format.format(
                "[total]",
                "",
                sum(gpu_usage.replicas for gpu_usage in namespace_gpu_usages),
                sum(
                    gpu_usage.gpus * gpu_usage.replicas
                    for gpu_usage in namespace_gpu_usages
                ),
            )
        )
    total_format = "{:>40} {:>15}"
    print("Totals")

    gpus_required = sum(gpu_usage.gpus * gpu_usage.replicas for gpu_usage in gpu_usages)
    print(total_format.format("GPUs Required", gpus_required))

    nodes = get_nodes_in_cluster(DEV_CONTEXT)
    allocatable_gpus = 0
    for node in nodes["items"]:
        allocatable_gpus += int(
            node["status"]["allocatable"].get("nvidia.com/gpu", "0")
        )
    print(total_format.format("Allocatable GPUs", allocatable_gpus))
    print()


if __name__ == "__main__":
    main()
