load("//tools/bzl:python.bzl", "py_binary")

py_binary(
    name = "gpu_capacity_report",
    srcs = [
        "gpu_capacity_report.py",
    ],
    data = [
        "@k8s_binary//file:kubectl",
    ],
    deps = [
        "//base/logging:struct_logging",
    ],
)

py_binary(
    name = "dev_capacity_report",
    srcs = [
        "dev_capacity_report.py",
    ],
    data = [
        "@k8s_binary//file:kubectl",
    ],
    deps = [
        "//base/logging:struct_logging",
    ],
)
