# Cleanup Dev

A job that performs the following cleanup tasks:

1. Scales down GPU deployments in dev namespaces that have been running for more than 72 hours.
2. Removes dev namespaces that belong to engineers who are no longer with the company (not in the eng.json list).

The job runs every day at 3am PST.

## Usage

The script can be run with the following options:

```
--dry-run               Don't actually perform any actions, just log what would be done
--skip-gpu-cleanup      Skip cleaning up old GPU deployments
--skip-namespace-cleanup Skip cleaning up inactive engineer namespaces
--skip-api-cleanup      Skip cleaning up API group resources before deleting namespaces
```

## How it works

The script:
1. Loads the list of active engineers from `//deploy/common:eng_json`
2. Gets all namespaces with the prefix `dev-`
3. For GPU cleanup: Scales down deployments with GPU resources that haven't been updated in 72+ hours
4. For namespace cleanup:
   - Identifies namespaces where the username part (after `dev-`) doesn't match any active engineer username
   - Skips namespaces that are already in the "Terminating" state
   - Before deleting a namespace, cleans up resources from the following API groups to prevent namespace deletion from hanging:
     ```
     artifactregistry.cnrm.cloud.google.com
     bigquery.cnrm.cloud.google.com
     bigtable.cnrm.cloud.google.com
     container.cnrm.cloud.google.com
     customize.core.cnrm.cloud.google.com
     dns.cnrm.cloud.google.com
     eng.augmentcode.com
     filestore.cnrm.cloud.google.com
     monitoring.cnrm.cloud.google.com
     networking.gke.io
     networking.k8s.io
     pubsub.cnrm.cloud.google.com
     spanner.cnrm.cloud.google.com
     storage.cnrm.cloud.google.com
     ```
   - Deletes namespaces that belong to inactive engineers
