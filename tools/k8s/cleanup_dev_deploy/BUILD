load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:metadata.bzl", "metadata_test")
load("//tools/bzl:python.bzl", "py_binary", "py_oci_image")

py_binary(
    name = "cleanup_dev_deploy",
    srcs = [
        "cleanup_dev_deploy.py",
    ],
    data = [
        "@k8s_binary//file:kubectl",
        "//deploy/common:eng_json",
    ],
    deps = [
        requirement("python-dateutil"),
        "//base/logging:struct_logging",
    ],
)

py_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":cleanup_dev_deploy",
    trivy_allow_list = [
    ],
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        ":image",
    ],
    deps = [
        "//deploy/common:cloud_info",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/gcp:gcp-lib",
    ],
)

metadata_test(
    name = "metadata_test",
    src = "METADATA.jsonnet",
    deps = [
        ":kubecfg",
    ],
)
