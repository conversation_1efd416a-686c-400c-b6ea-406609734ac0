#!/usr/bin/env python3
"""Wrapper around a bazel call to add dynamic situation specific arguments and checks.

The path //tools/bazel is a magic path in Bazel. The real Bazel binary is provided in
the REAL_BAZEL env variable.

Note: tools/bazel needs to run
- without any dependencies installed
- using Python3.8 or up
"""

import argparse
import os
import re
import signal
import subprocess
import sys
from functools import lru_cache  # cache was only added in 3.9
from pathlib import Path
from typing import List, Optional, <PERSON><PERSON>


def get_distro() -> Optional[str]:
    """Returns the distro of the host."""
    try:
        output = subprocess.check_output(
            ["lsb_release", "-a"], stderr=subprocess.DEVNULL
        )
        m = re.search(r"Codename:\s*(\S+)", output.decode())
        return m.groups()[0]
    except OSError:
        # if lsb_release is not installed, we cannot know which distro we are on
        return None


CW_BAZEL_CACHE_URL = "grpc://bazel-cache:9092"
GCP_BAZEL_CACHE_URL = "grpc://bazel-cache-3.us-central1.dev.augmentcode.com:9092"
GCP_RESEARCH_BAZEL_CACHE_URL = "grpc://bazel-cache.bazel-cache.svc.cluster.local:9092"
GCP_BES_URL = "grpc://bazel-metrics.us-central1.dev.augmentcode.com:50051"


@lru_cache(maxsize=None)
def get_gpu_count():
    """Returns the number GPU available.

    This function tries to return the GPU available.
    This is used to determine which unit tests to execute.

    This function tries to infer the number of GPUs from the following sources:
    - the AU_GPU_COUNT env variable
    - the number of GPUs available in torch
    - the number of GPUs attached to the machine
    """

    # allow explicit setting via env variable
    gpu_count = os.environ.get("AU_GPU_COUNT")
    large_gpu = os.environ.get("AU_LARGE_GPU", "0") == "1"
    if gpu_count:
        return int(gpu_count), large_gpu

    # infer from torch
    try:
        import torch.cuda

        device_count = torch.cuda.device_count()
        if device_count > 0:
            large_gpu = torch.cuda.mem_get_info(0)[1] > 24 * 1024 * 1024 * 1024
            return device_count, large_gpu
        return 0, False
    except Exception:  # pylint: disable=broad-exception
        # this will work well unless torch is not installed
        pass

    # we are not getting in an odd place
    # let's try the attached PCI devices as last fallback
    try:
        output = subprocess.run(
            ["lspci"],
            stderr=subprocess.DEVNULL,
            stdout=subprocess.PIPE,
            encoding="utf-8",
            check=False,
        )
        if output.returncode == 0:
            return len(
                list(
                    line
                    for line in output.stdout.splitlines()
                    if "NVIDIA Corporation" in line
                )
            ), False
    except OSError:
        # if lspci is not installed, we assume we are not in a GPU machine
        pass
    return 0, False


@lru_cache(maxsize=None)
def detect_cloud() -> str:
    """Returns the cloud environment.

    Projects numbers:
        system-services-dev: 1035750215372
        system-services-prod: 835723878709
        augment-387916: 416356615362
        augment-research-gsc: 670297456363
    """
    prod_projects = (
        "system-services-dev",
        "system-services-prod",
        "system-services-prod-gsc",
    )
    project = subprocess.run(
        [
            "curl",
            "metadata.google.internal/computeMetadata/v1/project/project-id",
            "-H",
            "Metadata-Flavor: Google",
        ],
        stderr=subprocess.DEVNULL,
        stdout=subprocess.PIPE,
        encoding="utf-8",
        check=False,
    )
    # Anything prod is "gcp"
    if project.returncode == 0:
        project = project.stdout.strip()
        if project in prod_projects:
            return "gcp"
        return project

    return "CW"


hosted_cmd_set = set(["build", "run", "test", "aquery"])


def _find_command(argv) -> Tuple[Optional[argparse.Namespace], List[str], List[str]]:
    args = _parse_arguments(argv)
    if not args:
        return None, [], argv
    for i, arg in enumerate(argv):
        if arg == args[0].command:
            return args[0], argv[:i], argv[i + 1 :]
    return args[0], [], argv


def _parse_arguments(argv: List[str]):
    try:
        parser = argparse.ArgumentParser(add_help=False)
        # parts of the bazel arguments to mirror the parsing behavior
        parser.add_argument("--output_user_root")
        parser.add_argument("--test_tag_filters")
        parser.add_argument("--max_idle_secs")
        parser.add_argument("--version", action="store_true")
        parser.add_argument("command", nargs="?")
        parser.add_argument("--config")
        args = parser.parse_known_args(args=argv)
        return args
    except SystemExit:
        return None


def _get_test_tag_filters(
    args: argparse.Namespace, gpu_count: int, cloud: Optional[str], large_gpu: bool
):
    """Calculate the right tag filters for the given argument."""
    if args.test_tag_filters is not None:
        filters = [a.strip() for a in args.test_tag_filters.split(",")]
    elif args.config == "ci" or args.config == "runner":
        filters = []
    else:
        # disable postmerge tests unless requested
        filters = ["-postmerge-test", "-system-test"]

    # filter dependent on the number of GPUs
    if gpu_count == 0:
        filters.append("-gpu")
    elif gpu_count == 1:
        filters.append("-multi-gpu")

    if not large_gpu:
        filters.append("-large-gpu")

    # filter dependent on the cloud
    if cloud != "gcp":
        filters.append("-gcp")
    return ",".join(filters)


def log(msg: str):
    """Log a message."""
    if os.environ.get("NO_BAZEL_PRINT"):
        return
    print(msg, file=sys.stderr)


def validate_cache_state():
    """Check if the link is valid.

    If the bazel-bin link contains augment, then the cache is not valid as was
    created before bzlmod was enabled.
    """
    bazel_real = os.environ.get("BAZEL_REAL")
    assert bazel_real
    p = subprocess.run(
        [bazel_real, "info", "bazel-bin"],
        encoding="utf-8",
        stdout=subprocess.PIPE,
        check=False,
    )
    if "execroot/augment" in p.stdout:
        print("ERROR: Please call 'bazel clean --async'", file=sys.stderr)
        sys.exit(36)


def check_shm() -> List[str]:
    """Check if shared memory is available.

    If possible, we run the sanbox on /dev/shm to speed up the sandboxing.
    """
    if os.environ.get("NO_BAZEL_SHM"):
        return []
    try:
        if not os.path.exists("/dev/shm"):
            return []
        s = os.statvfs("/dev/shm")
        # enforce 8 GB of shmem
        # we have seen errors with 4 GB due to the size of container images
        if s.f_bavail * s.f_bsize < 8_000_000_000:
            return []
        if not os.path.exists("/dev/shm/bazel"):
            os.makedirs("/dev/shm/bazel")
        # place sandboxes in /dev/shm to speed up sandboxing
        return ["--sandbox_base=/dev/shm/bazel"]
    except OSError:
        return []


def has_wild_card_targets(post_cmd) -> bool:
    """Check if the command has wild card targets.

    Arguments:
        post_cmd: The command line after the bazel command.

    Returns:
        True if the command has wild card targets.
    """
    if "--target_pattern_file" in post_cmd:
        return True
    targets = [c for c in post_cmd if c.startswith("//")]
    if not targets:
        return False
    return any(t.endswith("...") or t.endswith(":all") for t in targets)


def main():
    bazel_real = os.environ.get("BAZEL_REAL")
    if not bazel_real:
        print("ERROR: tools/bazel needs to be called by bazelisk", file=sys.stderr)
        sys.exit(36)

    if len(sys.argv) < 2:
        print("""INFO: bazel build tool

bazel build => build artifacts, e.g. bazel build //clients/...
bazel test => runs tests and validation, e.g. bazel test //clients/...

see https://www.notion.so/Bazel-Snippets-0235fb12e8ae4aec8332c610516ed5e2?pvs=4
""")
        sys.exit(36)

    distro = get_distro()
    if distro not in ["focal", "jammy"]:
        print(
            "WARNING: Only Ubuntu 20.04 and 22.04 is supported as host platform",
            file=sys.stderr,
        )

    # always add the right GPU filter
    gpu_count, large_gpu = get_gpu_count()
    cloud = detect_cloud()
    if cloud == "gcp":
        log(
            f"INFO: Detected GCP cloud with {gpu_count}{' (large)' if large_gpu else ''} GPU"
        )
    elif cloud == "augment-research-gsc":
        log(
            f"INFO: Detected GCP-US1 cloud with {gpu_count}{' (large)' if large_gpu else ''} GPU"
        )
    elif cloud == "cw":
        log(
            f"INFO: Detected CW cloud with {gpu_count}{' (large)' if large_gpu else ''} GPU"
        )
    else:
        log("WARNING: Detected unsupported environment")

    args, pre_cmd, post_cmd = _find_command(sys.argv[1:])

    cmd = [bazel_real]
    cmd.extend(pre_cmd)
    if args:
        if args.command:
            cmd.append(args.command)
        if args.command in hosted_cmd_set:
            cloud = detect_cloud()
            if cloud == "gcp":
                if not os.environ.get("NO_BAZEL_CACHE"):
                    cmd.append(f"--remote_cache={GCP_BAZEL_CACHE_URL}")
                    cmd.append(f"--remote_instance_name=cache-{distro}")
                if os.environ.get("BAZEL_BES"):
                    cmd.append(f"--bes_backend={GCP_BES_URL}")
                if distro == "focal":
                    cmd.extend(
                        ["--host_platform=//tools/bzl:gcp_ubuntu2004_linux_x86_64"]
                    )
                    cmd.extend(["--platforms=//tools/bzl:gcp_ubuntu2004_linux_x86_64"])
                elif distro == "jammy":
                    cmd.extend(
                        ["--host_platform=//tools/bzl:gcp_ubuntu2204_linux_x86_64"]
                    )
                    cmd.extend(["--platforms=//tools/bzl:gcp_ubuntu2204_linux_x86_64"])
            elif cloud == "augment-research-gsc":
                if not os.environ.get("NO_BAZEL_CACHE"):
                    cmd.append(f"--remote_cache={GCP_RESEARCH_BAZEL_CACHE_URL}")
                if distro == "focal":
                    cmd.extend(["--host_platform=//tools/bzl:cw_ubuntu2004_linux_x86"])
                    cmd.extend(["--platforms=//tools/bzl:cw_ubuntu2004_linux_x86"])
                elif distro == "jammy":
                    cmd.extend(["--host_platform=//tools/bzl:cw_ubuntu2204_linux_x86"])
                    cmd.extend(["--platforms=//tools/bzl:cw_ubuntu2204_linux_x86"])
            elif distro:
                # if not GCP, but Linux, we assume CW
                if not os.environ.get("NO_BAZEL_CACHE"):
                    cmd.append(f"--remote_cache={CW_BAZEL_CACHE_URL}")
                if distro == "focal":
                    cmd.extend(["--host_platform=//tools/bzl:cw_ubuntu2004_linux_x86"])
                    cmd.extend(["--platforms=//tools/bzl:cw_ubuntu2004_linux_x86"])
                elif distro == "jammy":
                    cmd.extend(["--host_platform=//tools/bzl:cw_ubuntu2204_linux_x86"])
                    cmd.extend(["--platforms=//tools/bzl:cw_ubuntu2204_linux_x86"])
            elif sys.platform == "darwin":
                cmd.extend(["--host_platform=//tools/bzl:darwin_aarch64"])
                cmd.extend(["--platforms=//tools/bzl:darwin_aarch64"])
            cmd.append(f"--action_env=/usr/local/{distro}")

        # if gcp context (in any cloud) is used, many commands (or even library calls) require the binaries in that path to be available in the PATH env
        if args.command in ("run", "test", "build"):
            validate_cache_state()  # validate the cache-state

            extra_paths = []
            # to make transformer_engine work with hermetic cuda, we need to add the cuda libs to the library path
            extra_library_path = [
                "../rules_python~~pip~python_pip_311_torch/site-packages/torch/lib",
                "../rules_python~~pip~python_pip_311_nvidia_nvtx_cu12/site-packages/nvidia/nvtx/lib",
                "../rules_python~~pip~python_pip_311_nvidia_cuda_nvrtc_cu12/site-packages/nvidia/cuda_nvrtc/lib/",
            ]
            if Path.home().joinpath(".local/google-cloud-sdk/bin").exists():
                extra_paths.append(
                    str(Path.home().joinpath(".local/google-cloud-sdk/bin"))
                )

            # the CI runner might want to inject extra paths into the bazel sandbox
            if os.environ.get("BAZEL_EXTRA_PATH"):
                extra_paths.extend(os.environ["BAZEL_EXTRA_PATH"].split(":"))
            if os.environ.get("BAZEL_EXTRA_LD_LIBRARY_PATH"):
                extra_library_path.extend(
                    os.environ["BAZEL_EXTRA_LD_LIBRARY_PATH"].split(":")
                )
            if extra_paths:
                cmd.append(
                    "--test_env=PATH=.:/bin:/usr/bin:/usr/local/bin:{}".format(
                        ":".join(extra_paths)
                    )
                )
                cmd.append(
                    "--action_env=PATH=.:/bin:/usr/bin:/usr/local/bin:{}".format(
                        ":".join(extra_paths)
                    )
                )
            if extra_library_path:
                cmd.append(
                    "--test_env=LD_LIBRARY_PATH={}".format(":".join(extra_library_path))
                )

            cmd.extend(check_shm())
    cmd.extend(post_cmd)
    if args and args.command == "test" and has_wild_card_targets(post_cmd):
        # by default we filter out postmerge tests and tests that require GPUs if the host doesn't have
        # any.
        # However, if a test is uniquely and directly specified, we don't filter it.
        filters = _get_test_tag_filters(
            args, gpu_count, cloud=cloud, large_gpu=large_gpu
        )
        cmd.append(f"--test_tag_filters={filters}")
        if gpu_count == 0:
            log(
                f"INFO: Suppressing postmerge and GPU tests via --test_tag_filters={filters}"
            )
        else:
            log(f"INFO: Suppressing postmerge tests via --test_tag_filters={filters}")

    p = None
    try:
        p = subprocess.Popen(cmd, start_new_session=True)
        r = p.wait()
        sys.exit(r)
    except KeyboardInterrupt:
        if p:
            os.killpg(os.getpgid(p.pid), signal.SIGINT)
        sys.exit(8)


if __name__ == "__main__":
    main()
