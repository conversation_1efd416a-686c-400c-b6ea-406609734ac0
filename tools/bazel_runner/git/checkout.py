"""Tool to perform the checkout from branches and pull requests for the Bazel runner."""

import logging
import re
import shutil
import subprocess
import typing
from contextlib import contextmanager
from pathlib import Path

import git
import grpc
from github import Github, Repository, PullRequest

from tools.bazel_runner.git import checkout_pb2
from tools.bazel_runner.git.token_source import TokenSource

# update whenever there is a major change in the checkout to wipe the current checkout
EXPECTED_CHECKOUT_VERSION = "2"


def wipe(directory: Path):
    """Wipes the complete working directory, but doesn't remove the directory.

    Args:
        directory: The directory to wipe.
    """
    logging.info("Wipe %s", directory)
    if not directory.exists():
        logging.info("Directory %s does not exist", directory)
        return

    for entry in directory.iterdir():
        # 1) use sudo because sometimes somebody places a file is invalid permissions
        # that shouldn't happen, but it does.
        # 2) to ensure that we do not end up with a half-wiped checkout, we rename the file or directory
        r = subprocess.run(["sudo", "mv", f"{entry}", f"{entry}.old"], check=False)
        if r.returncode != 0:
            logging.error("Wipe %s failed", entry)
            raise CheckoutException(
                f"Wipe {entry} failed",
                status_code=grpc.StatusCode.INTERNAL,
            )
        r = subprocess.run(["sudo", "rm", "-rf", f"{entry}.old"], check=False)
        if r.returncode != 0:
            logging.error("Wipe %s failed", entry)
            raise CheckoutException(
                f"Wipe {entry} failed",
                status_code=grpc.StatusCode.INTERNAL,
            )


class CheckoutException(Exception):
    """Exception that should be thrown in the runner when a test execution should be aborted.

    The execution will NOT be retried and will NOT proceed to the next step.
    The abort message will be displayed to the user.
    """

    def __init__(self, message: str, status_code: grpc.StatusCode):
        self.message = message
        self.status_code = status_code


class Checkout:
    """Class to control the checkout of a repository."""

    def __init__(
        self,
        base_wd: Path,
        github_user: str,
        token_source: TokenSource,
        home_path: Path,
        require_approval_on_pull_request: bool = False,
    ):
        """Initializes the checkout class."""
        self.base_wd = base_wd
        assert self.base_wd.exists()
        self.github_user = github_user
        assert self.github_user
        self.token_source = token_source
        self.home_path = home_path
        assert self.home_path.exists()
        self.require_approval_on_pull_request = require_approval_on_pull_request

    def setup(self):
        """Sets up the git credentials.

        Setup needs to be called once before any cloning or checkout operations
        can be called.
        """
        self.home_path.joinpath(".gitconfig").write_text(
            "[credential]\n\thelper = store\n", encoding="utf-8"
        )

    def _is_valid_checkout(self, repo_wd: Path):
        """Checks if the checkout is valid."""
        version_path = repo_wd.parent / "checkout-version.txt"
        logging.info("Check for checkout version at %s", version_path)
        if not version_path.exists():
            logging.info("No checkout version file found.")
            return False
        logging.info("Found checkout version file")
        if version_path.read_text() != EXPECTED_CHECKOUT_VERSION:
            logging.info("Checkout version file is not valid")
            return False
        return True

    def _write_checkout_version_file(self, repo_wd: Path):
        """Writes the checkout version file."""
        version_path = repo_wd.parent / "checkout-version.txt"
        version_path.write_text(EXPECTED_CHECKOUT_VERSION, encoding="utf-8")

    def clone(self, owner: str, repo_name: str):
        """Clones a repository to a working directory.

        Args:
            owner: The owner of the repository.
            repo_name: The name of the repository.
        """
        repo_wd = self.base_wd / repo_name
        if (repo_wd / ".git" / "index.lock").exists():
            logging.warning(".git/index.lock exists. Wiping repository directory")
            wipe(repo_wd)

        with self.prepare():
            self._clone(
                repo_wd=repo_wd,
                owner=owner,
                repo_name=repo_name,
            )

    def _clone(
        self,
        repo_wd: Path,
        owner: str,
        repo_name: str,
    ) -> git.Repo:  # type: ignore
        """Clones a repository to a working directory."""
        if repo_wd.exists():
            logging.info("Check for cloning %s:%s at %s", owner, repo_name, repo_wd)
            if self._is_valid_checkout(repo_wd):
                try:
                    repo = git.Repo(repo_wd)  # type: ignore

                    if owner != "augmentcode":
                        if owner not in repo.remotes:
                            repo.create_remote(
                                owner,
                                f"https://github.com/{owner}/{repo_name}.git",
                            )
                    logging.info("Found valid clone")
                    return repo
                except git.exc.InvalidGitRepositoryError as ex:  # type: ignore # pylint: disable=no-member
                    # something left the git workspace in an invalid state
                    logging.error("Invalid git workspace")
                    logging.exception(ex)
                    wipe(repo_wd)
            else:
                logging.info("Removing outdated checkout directory")
                wipe(repo_wd)
        logging.info("Cloning %s:%s", owner, repo_name)
        repo_wd.mkdir(parents=True, exist_ok=True)
        repo = git.Repo.clone_from(  # type: ignore
            f"https://github.com/augmentcode/{repo_name}.git",
            str(repo_wd),
            branch="main",
        )

        if owner != "augmentcode":
            if owner not in repo.remotes:
                repo.create_remote(
                    owner,
                    f"https://github.com/{owner}/{repo_name}.git",
                )
        self._write_checkout_version_file(repo_wd)
        return repo

    @contextmanager
    def prepare(self) -> typing.Generator[Github, None, None]:
        """Prepare the environment.

        This sets the git credentials to a valid token and removes the token after the context is left.
        """
        github_token = self.token_source.get_token()
        self.home_path.joinpath(".git-credentials").write_text(
            f"https://{self.github_user.strip()}:{github_token.strip()}@github.com",
            encoding="utf-8",
        )
        yield Github(github_token)
        self.home_path.joinpath(".git-credentials").write_text(
            f"https://{self.github_user.strip()}:<EMAIL>",
            encoding="utf-8",
        )

    def _prepare_checkout(
        self,
        repo: git.Repo,  # type: ignore
        owner: str,
        branch: str,
        ref: str,
        checkout_id: str | None,
    ):
        """Fetches and checks out a commit."""
        assert repo.working_dir
        if ref:
            if not re.match(r"^[0-9a-f]{7,40}$", ref):
                raise CheckoutException(
                    f"Ref does not seem to be a hash: {ref}",
                    status_code=grpc.StatusCode.INVALID_ARGUMENT,
                )
        logging.info("Commit %s/%s", branch, ref)
        logging.info(repo.git.clean("-xdf"))
        repo.heads.main.checkout(force=True)
        if owner == "augmentcode":
            owner = "origin"
        assert owner in repo.remotes, repo.remotes
        try:
            repo.remotes[owner].fetch(branch)
            if not ref:
                ref = f"{owner}/{branch}"
            else:
                is_on_branch = repo.is_ancestor(
                    repo.commit(ref),
                    repo.commit(f"{owner}/{branch}"),
                )
                if not is_on_branch:
                    logging.error("Ref %s is not on branch %s", ref, branch)
                    raise CheckoutException(
                        f"Ref {ref} is not on branch {branch}",
                        status_code=grpc.StatusCode.INVALID_ARGUMENT,
                    )

            if checkout_id:
                work_branch = repo.create_head(checkout_id, ref, force=True)
                repo.head.reset(index=True, working_tree=True)
                repo.heads[checkout_id].checkout(force=True)
            else:
                work_branch = None
                repo.git.checkout(ref, force=True)
            for untracked_file in repo.untracked_files:
                assert untracked_file
                logging.info("untracked file: %s", untracked_file)
                Path(repo.working_dir).joinpath(untracked_file).unlink(missing_ok=True)
            return repo, work_branch
        except ValueError as e:
            if ref and f"{ref} missing":
                logging.error("Ref %s does not exist", ref)
                raise CheckoutException(
                    f"Reference {ref} does not exist",
                    status_code=grpc.StatusCode.INVALID_ARGUMENT,
                ) from e
            else:
                raise

    def _has_pr_approval(self, pr: PullRequest) -> bool:  # type: ignore
        """Checks if a pull request has approval."""
        reviews = pr.get_reviews()  # type: ignore
        for review in reviews:
            if review.state == "APPROVED":
                return True
        return False

    def _prepare_pull_request_checkout(
        self,
        repo: git.Repo,  # type: ignore
        github_repo: Repository,  # type: ignore
        owner: str,
        pull_request_number: int,
        ref: str | None,
        checkout_id: str | None,
    ) -> tuple[git.Repo, git.Head | None]:  # type: ignore
        """Fetches and checks out a pull request commit."""
        assert repo.working_dir
        try:
            pr = github_repo.get_pull(pull_request_number)  # type: ignore
            logging.info("Pull request %s", pr)
            if pr.merged:  # type: ignore
                logging.error("Pull request %s is already merged", pull_request_number)
                raise CheckoutException(
                    f"Pull request {pull_request_number} is already merged",
                    status_code=grpc.StatusCode.INVALID_ARGUMENT,
                )
            # check if pr is closed
            if pr.state == "closed":
                logging.error("Pull request %s is closed", pull_request_number)
                raise CheckoutException(
                    f"Pull request {pull_request_number} is closed",
                    status_code=grpc.StatusCode.INVALID_ARGUMENT,
                )
            if pr.mergeable is None:
                # backoff
                logging.error(
                    "Pull request %s has unknown merge state", pull_request_number
                )
                raise CheckoutException(
                    "Pull request has unknown merge state",
                    status_code=grpc.StatusCode.UNAVAILABLE,
                )
            if pr.mergeable is False:
                logging.error("Pull request %s is not mergeable", pull_request_number)
                raise CheckoutException(
                    f"Pull request {pull_request_number} is not mergeable",
                    status_code=grpc.StatusCode.INVALID_ARGUMENT,
                )
            if self.require_approval_on_pull_request:
                if not self._has_pr_approval(pr):
                    logging.error(
                        "Pull request %s is not approved", pull_request_number
                    )
                    raise CheckoutException(
                        f"Pull request {pull_request_number} is not approved",
                        status_code=grpc.StatusCode.INVALID_ARGUMENT,
                    )

            if not ref:
                ref = pr.head.sha

            logging.info(repo.git.clean("-xdf"))
            pr_ref = f"pull/{pull_request_number}/merge"
            if owner == "augmentcode":
                owner = "origin"
            logging.info("Fetch %s", pr_ref)
            pr_fetch_commit = repo.remotes[owner].fetch([pr_ref])[0].commit
            logging.info("Fetch info %s", pr_fetch_commit)
            if checkout_id:
                work_branch = repo.create_head(checkout_id, pr_fetch_commit, force=True)  # type: ignore
                repo.heads[checkout_id].checkout(force=True)
                repo.head.reset(commit=pr_fetch_commit, index=True, working_tree=True)
            else:
                work_branch = None
                repo.git.checkout(pr_fetch_commit, force=True)
            logging.info("Status %s", repo.git.status())
            for untracked_file in repo.untracked_files:
                assert untracked_file
                logging.info("untracked file: %s", untracked_file)
                Path(repo.working_dir).joinpath(untracked_file).unlink(missing_ok=True)
            logging.info("Status %s", repo.git.status())
            return repo, work_branch
        except ValueError as e:
            if ref and f"{ref} missing":
                logging.error("Ref %s does not exist", ref)
                raise CheckoutException(
                    f"Reference {ref} does not exist",
                    status_code=grpc.StatusCode.INVALID_ARGUMENT,
                ) from e
            else:
                raise
        except git.exc.GitCommandError as e:  # type: ignore
            if (e.stderr and "did not send all necessary objects" in e.stderr) or (
                e.stdout and "did not send all necessary objects" in e.stdout
            ):
                logging.error("Invalid local git state: %s", e)
                raise CheckoutException(
                    "Invalid local git state",
                    status_code=grpc.StatusCode.DATA_LOSS,
                ) from e

    def checkout(
        self, checkout_config: checkout_pb2.CheckoutSpec, checkout_id: str | None
    ) -> tuple[Path, str | None]:
        """Checks out a repository.

        Args:
            checkout_config: The checkout configuration.
            checkout_id: The id of the checkout. The checkout id is used as a branch name.

        Returns:
            The path to the checked out repository.
        """
        assert checkout_config.repo_name
        assert checkout_config.owner
        try:
            with self.prepare() as github_client:
                github_repo = github_client.get_repo(
                    f"{checkout_config.owner}/{checkout_config.repo_name}"
                )

                repo_wd = self.base_wd / checkout_config.repo_name
                # help to debug the occasional IO/error when looking for the .git/index.lock file
                if repo_wd.exists():
                    if (repo_wd / ".git" / "index.lock").exists():
                        logging.warning(
                            ".git/index.lock exists. Wiping repository directory"
                        )
                        wipe(repo_wd)

                repo = self._clone(
                    repo_wd=repo_wd,
                    owner=checkout_config.owner,
                    repo_name=checkout_config.repo_name,
                )
                if checkout_config.HasField("commit_checkout"):
                    repo, branch = self._prepare_checkout(
                        repo=repo,
                        branch=checkout_config.commit_checkout.branch,
                        owner=checkout_config.owner,
                        ref=checkout_config.commit_checkout.ref,
                        checkout_id=checkout_id,
                    )
                elif checkout_config.HasField("pull_request_checkout"):
                    repo, branch = self._prepare_pull_request_checkout(
                        repo=repo,
                        github_repo=github_repo,  # type: ignore
                        owner=checkout_config.owner,
                        ref=checkout_config.pull_request_checkout.ref,
                        pull_request_number=checkout_config.pull_request_checkout.pull_request_number,
                        checkout_id=checkout_id,
                    )
                else:
                    raise CheckoutException(
                        "Invalid checkout config",
                        status_code=grpc.StatusCode.INVALID_ARGUMENT,
                    )
                logging.info("Finished checkout")
                return (Path(repo.working_dir), branch.name if branch else None)  # type: ignore
        except CheckoutException as ex:
            if ex.status_code == grpc.StatusCode.DATA_LOSS:
                wipe(self.base_wd / checkout_config.repo_name)
                if self.base_wd.joinpath(checkout_config.repo_name).exists():
                    shutil.rmtree(self.base_wd / checkout_config.repo_name)
            raise

    def delete_checkout(
        self, checkout_config: checkout_pb2.CheckoutSpec, checkout_id: str
    ):
        """Deletes the checkout branch."""
        repo_dir = self.base_wd / checkout_config.repo_name
        repo = git.Repo(repo_dir)  # type: ignore
        logging.info(repo.git.clean("-xdf"))
        repo.heads.main.checkout(force=True)
        repo.delete_head(checkout_id, force=True)
