// page displaying the details information about a request
import {
  Badge,
  Descriptions,
  Typography,
  Tabs,
  List,
  Spin,
  Table,
  Empty,
  Alert,
  message,
  Tag,
} from "antd";
import { useParams } from "react-router-dom";
import React, { ReactNode, useEffect, useState } from "react";
import {
  getTestRun,
  TestRunData,
  getConsoleLog,
  TargetData,
  isFinalState,
  getBuildEvents,
  BuildEventItem,
  fillTargetDataFromJobBuildEvents,
  compareTargetDataByStatus,
  restartRun,
  cancelRun,
  BuildEventResult,
} from "../lib/run";
import { LayoutComponent } from "../lib/layout";
import { Tooltip } from "antd";
import { ClockCircleOutlined } from "@ant-design/icons";
import { useNavigate } from "react-router-dom";
import type { TabsProps } from "antd";
import Link from "antd/es/typography/Link";
import dayjs, { Dayjs } from "dayjs";
import utc from "dayjs/plugin/utc";
import localizedFormat from "dayjs/plugin/localizedFormat";
import { ColumnType } from "antd/es/table";
import axios from "axios";
import { ReloadOutlined, CloseCircleOutlined } from "@ant-design/icons";
import { FloatButton } from "antd";

dayjs.extend(utc);
dayjs.extend(localizedFormat);

const { Text } = Typography;

function freq(nums): object {
  return nums.reduce((acc, curr) => {
    acc[curr] = -~acc[curr];
    return acc;
  }, {});
}

export function TargetStatusComponent({
  targetData,
  isFinalState,
}: {
  targetData: TargetData;
  isFinalState: boolean;
}) {
  if (targetData.status === "PASSED") {
    return <Badge status="success" text="Passed" />;
  } else if (targetData.status === "FAILED") {
    return <Badge status="error" text="Failed" />;
  } else if (targetData.status === "TIMEOUT") {
    return <Badge status="error" text="Timeout" />;
  } else if (targetData.status === "FLAKY") {
    return <Badge status="warning" text="Flaky" />;
  } else if (targetData.status === "" || targetData.status === "NO_STATUS") {
    if (isFinalState) {
      return <Badge status="warning" text="No Status" />;
    } else {
      return <Badge status="processing" text="Pending" />;
    }
  } else {
    return <Text>{targetData.status}</Text>;
  }
}

function StatusComponent({
  run,
  testResults,
}: {
  run: TestRunData;
  testResults: TargetData[];
}) {
  const testResultsFreq = freq(testResults.map((t) => t.status));
  console.log(`freq ${JSON.stringify(testResultsFreq)}`);
  const badges: ReactNode[] = [];
  if (testResultsFreq["FAILED"]) {
    badges.push(
      <Tooltip title="Failed">
        <Badge
          count={testResultsFreq["FAILED"]}
          overflowCount={1000}
          style={{ marginRight: "0.5em" }}
        />
      </Tooltip>,
    );
  }
  if (testResultsFreq["TIMEOUT"]) {
    badges.push(
      <Tooltip title="Timeout">
        <Badge
          count={testResultsFreq["TIMEOUT"]}
          overflowCount={1000}
          style={{ marginRight: "0.5em" }}
        />
      </Tooltip>,
    );
  }
  if (testResultsFreq["FLAKY"]) {
    badges.push(
      <Tooltip title="Flaky">
        <Badge
          count={testResultsFreq["FLAKY"]}
          overflowCount={1000}
          style={{ backgroundColor: "#ff9506", marginRight: "0.5em" }}
        />
      </Tooltip>,
    );
  }
  if (testResultsFreq["PASSED"]) {
    badges.push(
      <Tooltip title="Passed">
        <Badge
          count={testResultsFreq["PASSED"]}
          overflowCount={1000}
          style={{ backgroundColor: "#52c41a", marginRight: "0.5em" }}
        />
      </Tooltip>,
    );
  }
  const pendingCount =
    (testResultsFreq[""] || 0) + (testResultsFreq["NO_STATUS"] || 0);
  if (pendingCount) {
    badges.push(
      <Tooltip title="Pending">
        <Badge
          count={pendingCount}
          overflowCount={1000}
          style={{ backgroundColor: "#3385ff", marginRight: "0.5em" }}
        />
      </Tooltip>,
    );
  }
  let cancellationSuffix = "";
  if (run.cancellationRequested) {
    cancellationSuffix = " / Cancelling";
  }
  if (run.state === "RUN_STATE_INIT") {
    return <Badge status="processing" text={`Created${cancellationSuffix}`} />;
  } else if (run.state === "RUN_STATE_CHECKOUT") {
    return (
      <Badge status="processing" text={`Checking out${cancellationSuffix}`} />
    );
  } else if (run.state === "RUN_STATE_RUN") {
    let text = "Running";
    if (run.cancellationRequested) {
      text = "Cancelling";
    }
    return (
      <div>
        <Badge
          status="processing"
          text={text}
          style={{ paddingRight: "2em" }}
        />
        {badges}
      </div>
    );
  } else if (run.state === "RUN_STATE_RUN_WAITING") {
    let text = "Waiting";
    if (run.cancellationRequested) {
      text = "Cancelling";
    }
    return (
      <div>
        <Badge
          status="processing"
          text={text}
          style={{ paddingRight: "2em" }}
        />
        {badges}
      </div>
    );
  } else if (run.state === "RUN_STATE_POSTPROCESSING") {
    return (
      <Badge
        status="processing"
        text={`Post processing${cancellationSuffix}`}
      />
    );
  } else if (run.state === "RUN_STATE_PASSED") {
    return <div>{badges}</div>;
  } else if (run.state === "RUN_STATE_ERROR") {
    return (
      <Badge
        count={<ClockCircleOutlined style={{ color: "#f5222d" }} />}
        text={"Test Infrastructure Error"}
      />
    );
  } else if (run.state === "RUN_STATE_ABORT") {
    return (
      <Badge
        count={<ClockCircleOutlined style={{ color: "#f5222d" }} />}
        text={`Aborted: ${run.message}`}
      />
    );
  } else if (run.state === "RUN_STATE_FAILURE") {
    if (testResultsFreq["FAILED"] === undefined || testResultsFreq[""]) {
      return (
        <Badge
          count={<ClockCircleOutlined style={{ color: "#f5222d" }} />}
          text={"Failure: see Console for details"}
        />
      );
    } else {
      return <div>{badges}</div>;
    }
  } else if (run.state === "RUN_STATE_CANCEL") {
    return <Badge status="warning" text="Cancelled" />;
  } else {
    return <Badge status="warning" text={`Invalid state ${run.state}`} />;
  }
}

export function TagsComponent({ value }: { value?: string[] }) {
  let children: React.ReactNode[] = [];
  if (value !== undefined) {
    for (const tag of value) {
      children.push(<Tag>{tag}</Tag>);
    }
  }
  return (
    <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px' }}>
      {children}
    </div>
  );
}

// the component to display log data after the test finished
function ConsoleLogComponent({
  consoleLogs,
  isConsoleLoading,
}: {
  consoleLogs?: string[] | null;
  isConsoleLoading: boolean;
}) {
  if (isConsoleLoading) {
    return <Spin />;
  } else if (consoleLogs === undefined) {
    return <div />;
  } else if (consoleLogs === null) {
    return <Alert message="Error while loading the console" type="error" />;
  } else {
    let texts = consoleLogs.map((t) => {
      return (
        <Text style={{ whiteSpace: "pre-wrap" }}>
          <pre className="language-plaintext">{t}</pre>
        </Text>
      );
    });
    return <div>{texts}</div>;
  }
}

function ConsoleInProgressComponent({ jobData }: { jobData: JobData[] }) {
  const consoleData = jobData.map((jd) =>
    jd.buildEvents
      .map(
        (be) =>
          `${be.buildEvent?.progress?.stdout || ""}${
            be.buildEvent?.progress?.stderr || ""
          }`,
      )
      .join(""),
  );
  let texts = consoleData.map((t) => {
    return (
      <Text style={{ whiteSpace: "pre-wrap" }}>
        <pre className="language-plaintext">{t}</pre>
      </Text>
    );
  });
  return <div>{texts}</div>;
}

function ArchiveComponent({
  runId,
  archiveFiles,
}: {
  runId: string;
  archiveFiles: string[][];
}) {
  return (
    <List
      size="small"
      bordered
      dataSource={archiveFiles}
      renderItem={(item) => (
        <li>
          <a
            href={`/api/run/${runId}/${item[0]}/archive/${encodeURIComponent(
              item[1],
            )}`}
          >
            {item[1]}
          </a>
        </li>
      )}
    />
  );
}

type JobData = {
  jobId: string;
  buildEvents: BuildEventItem[];
};

function getTargetDataFromJobData(jobData: JobData[]): TargetData[] {
  const targetData: TargetData[] = [];
  for (const job of jobData) {
    fillTargetDataFromJobBuildEvents(targetData, job.jobId, job.buildEvents);
  }
  return targetData;
}

function TestTargetComponent({
  runId,
  targetName,
  jobId,
  skipLink,
}: {
  runId: string;
  targetName: string;
  jobId: string;
  skipLink: boolean;
}) {
  if (skipLink) {
    return (
      <Text keyboard copyable={{ text: `bazel run ${targetName}` }}>
        {targetName}
      </Text>
    );
  } else {
    return (
      <Link
        href={`/run/${runId}/job/${jobId}/target/${encodeURIComponent(
          targetName,
        )}`}
      >
        <Text keyboard copyable={{ text: `bazel run ${targetName}` }}>
          {targetName}
        </Text>
      </Link>
    );
  }
}

export default function RunComponent() {
  const [runData, setRunData] = useState<TestRunData | null | undefined>(
    undefined,
  );
  const [isLoading, setIsLoading] = useState(true);
  const [consoleData, setConsoleData] = useState<string[] | null | undefined>(
    undefined,
  );
  const [jobData, setJobData] = useState<JobData[]>([]);
  const [isJobsLoading, setJobsIsLoading] = useState(false);
  // set to true once the get events call returned no new data once
  const [isJobsDrained, setJobsDrained] = useState(false);
  const [isConsoleLoading, setIsConsoleLoading] = useState(false);
  const [isConsoleTab, setIsConsoleTab] = useState(false);
  const { runId }: any = useParams();
  console.log(`request id ${runId}`);

  const navigate = useNavigate();
  const [messageApi, contextHolder] = message.useMessage();

  useEffect(() => {
    const fetchRun = async () => {
      if (runData !== undefined) {
        return;
      }
      setIsLoading(true);
      const runInfo = await getTestRun(runId);
      console.log(`run ${JSON.stringify(runInfo)}`);
      setRunData(runInfo);

      setIsLoading(false);
    };
    fetchRun();
  }, [runId, runData]);

  useEffect(() => {
    const fetchRuninBackground = async () => {
      const runInfo = await getTestRun(runId);
      console.log(`background ${JSON.stringify(runInfo)}`);
      setRunData(runInfo);
    };

    if (runData == undefined) {
      fetchRuninBackground();
      return;
    }

    if (!isFinalState(runData?.state || "")) {
      const timerID = setTimeout(fetchRuninBackground, 10000);
      return () => {
        clearTimeout(timerID);
      };
    }
  }, [runId, runData]);

  useEffect(() => {
    const fetchBuildEventsInBackground = async () => {
      setJobsIsLoading(true);
      let newJobData: JobData[] = [...jobData];
      let jobDataChange = false;
      if (runData?.jobs !== undefined) {
        try {
          const promises: Promise<BuildEventResult>[] = [];
          for (const job of runData?.jobs) {
            let existingJobData = newJobData.find((j) => j.jobId === job.jobId);
            let minSequenceNumber =
              (existingJobData?.buildEvents.at(-1)?.sequenceNumber || -1) + 1;
            const p = getBuildEvents(runId, job.jobId, minSequenceNumber, 1000);
            promises.push(p);
          }

          for (const jobInfo of await Promise.all(promises)) {
            console.log(`events ${jobInfo.buildEvents.length}`);
            if (jobInfo.buildEvents.length > 0) {
              jobDataChange = true;
              const i = newJobData.findIndex((j) => j.jobId === jobInfo.jobId);
              if (i < 0) {
                newJobData.push({
                  jobId: jobInfo.jobId,
                  buildEvents: jobInfo.buildEvents,
                });
              } else {
                let existingJobData = newJobData.find(
                  (j) => j.jobId === jobInfo.jobId,
                );
                let newBuildEvents = existingJobData?.buildEvents || [];
                newBuildEvents = newBuildEvents.concat(jobInfo.buildEvents);
                newJobData[i] = {
                  jobId: jobInfo.jobId,
                  buildEvents: newBuildEvents,
                };
              }
            }
          }
        } catch (e) {
          console.log(`Error while loading the test job data: ${e}`);
          if (axios.isAxiosError(e)) {
            messageApi.open({
              type: "error",
              content: `Error while loading the test job data: ${e.message}`,
            });
          } else {
            messageApi.open({
              type: "error",
              content: `Error while loading the test job data`,
            });
          }
        }
      }
      if (jobDataChange) {
        setJobData(newJobData);
      } else if (!isJobsDrained) {
        console.log(`no new data, drained`);
        setJobsDrained(true);
      }
      setJobsIsLoading(false);
    };
    console.log(
      `use effect hasRuNData=${
        runData === undefined
      } isJobsLoading=${isJobsLoading} isJobsDrained=${isJobsDrained} isFinal=${isFinalState(
        runData?.state || "",
      )}`,
    );
    if (runData === undefined) {
      // without run data, we cannot fetch anything
      return;
    }
    if (isJobsLoading) {
      // do not have two calls in parallel
      return;
    }

    if (!isJobsDrained) {
      // the current evetns were not drained yet
      fetchBuildEventsInBackground();
    } else if (!isFinalState(runData?.state || "")) {
      // we are not in the final state yet, if we are in the final state and we are drained, we stop.
      // if not, we fetch in background every 10 seconds
      const timerID = setTimeout(fetchBuildEventsInBackground, 10000);
      return () => {
        clearTimeout(timerID);
      };
    }
  }, [runId, runData, jobData, isJobsLoading]);

  useEffect(() => {
    const fetchConsole = async () => {
      try {
        if (
          runData === undefined ||
          runData === null ||
          consoleData !== undefined ||
          isConsoleTab === false
        ) {
          return;
        }
        if (!isFinalState(runData.state || "") || runData.jobs === undefined) {
          return;
        }
        setIsConsoleLoading(true);
        const newConsoleData: string[] = [];
        for (const jobData of runData.jobs) {
          const jobConsoleLog = await getConsoleLog(runId, jobData.jobId);
          newConsoleData.push(jobConsoleLog);
        }
        setConsoleData(newConsoleData);
        setIsConsoleLoading(false);
      } catch (e) {
        console.log(`Error while loading the console data: ${e}`);
        if (axios.isAxiosError(e)) {
          if (e.response?.status === 404) {
            setIsConsoleLoading(false);
          } else {
            setIsConsoleLoading(false);
            setConsoleData(null);
          }
        } else {
          setIsConsoleLoading(false);
          setConsoleData(null);
        }
      }
    };
    fetchConsole();
  }, [runId, runData, isConsoleTab, consoleData]);

  if (isLoading) {
    return (
      <>
        {contextHolder}
        <LayoutComponent
          selectedMenuKey={"runs"}
          breadcrumbs={[
            { label: "Runs", link: "/runs" },
            { label: `Run ${runId}`, link: `/run/${runId}` },
          ]}
        >
          <Spin />
        </LayoutComponent>
      </>
    );
  } else if (runData === undefined || runData === null) {
    return (
      <>
        {contextHolder}
        <LayoutComponent
          selectedMenuKey={"runs"}
          breadcrumbs={[
            { label: "Runs", link: "/runs" },
            { label: `Run ${runId}`, link: `/run/${runId}` },
          ]}
        >
          <Text>Run not found.</Text>
        </LayoutComponent>
      </>
    );
  } else {
    let r = runData;

    const checkout: React.ReactNode[] = [];
    checkout.push(
      <Descriptions.Item label="Repo" span={1}>
        <Link
          href={`https://github.com/${r.testExecution.checkout.owner}/${r.testExecution.checkout.repoName}`}
        >
          {r.testExecution.checkout.owner}:{r.testExecution.checkout.repoName}
        </Link>
      </Descriptions.Item>,
    );
    if (r.testExecution.checkout.pullRequestCheckout !== undefined) {
      checkout.push(
        <Descriptions.Item label="Pull Request" span={1}>
          <Link
            href={`https://github.com/${r.testExecution.checkout.owner}/${r.testExecution.checkout.repoName}/pull/${r.testExecution.checkout.pullRequestCheckout.pullRequestNumber}`}
          >
            {r.testExecution.checkout.pullRequestCheckout.pullRequestNumber}
          </Link>
        </Descriptions.Item>,
      );
    } else if (r.testExecution.checkout.commitCheckout !== undefined) {
      if (r.testExecution.checkout.commitCheckout.ref !== undefined) {
        checkout.push(
          <Descriptions.Item label="Branch / Commit" span={1}>
            <Link
              href={`https://github.com/${r.testExecution.checkout.owner}/${r.testExecution.checkout.repoName}/tree/${r.testExecution.checkout.commitCheckout.branch}`}
            >
              {r.testExecution.checkout.commitCheckout.branch}
            </Link>
            &nbsp;/&nbsp;
            <Link
              href={`https://github.com/${r.testExecution.checkout.owner}/${r.testExecution.checkout.repoName}/commit/${r.testExecution.checkout.commitCheckout.ref}`}
            >
              {r.testExecution.checkout.commitCheckout.ref}
            </Link>
          </Descriptions.Item>,
        );
      } else {
        checkout.push(
          <Descriptions.Item label="Branch" span={1}>
            <Link
              href={`https://github.com/${r.testExecution.checkout.owner}/${r.testExecution.checkout.repoName}/tree/${r.testExecution.checkout.commitCheckout.branch}`}
            >
              {r.testExecution.checkout.commitCheckout.branch}
            </Link>
          </Descriptions.Item>,
        );
      }
    }
    checkout.push(
      <Descriptions.Item label="Tags" span={3}>
        <TagsComponent value={r.tags} />
      </Descriptions.Item>,
    );

    const isFinal = isFinalState(r.state || "");
    const columns: ColumnType<TargetData>[] = [
      {
        title: "Test Target",
        key: "targetName",
        render: (t: TargetData) => {
          return (
            <TestTargetComponent
              runId={runId}
              targetName={t.targetName}
              jobId={t.jobId}
              skipLink={t.status === ""}
            />
          );
        },
        sorter: (a, b) => {
          return a.targetName.localeCompare(b.targetName);
        },
      },
      {
        title: "Status",
        key: "status",
        render: (r: TargetData) => {
          return (
            <TargetStatusComponent targetData={r} isFinalState={isFinal} />
          );
        },
        filters: [
          {
            text: "Passed",
            value: "PASSED",
          },
          {
            text: "Failed",
            value: "FAILED",
          },
          {
            text: "Pending",
            value: "",
          },
        ],
        onFilter: (value: string | number | boolean, record: TargetData) =>
          record.status === (value as string),
        defaultSortOrder: "ascend",
        sorter: (a: TargetData, b: TargetData) =>
          compareTargetDataByStatus(a, b),
      },
      {
        title: "Run Duration",
        key: "duration",
        render: (r: TargetData) => {
          if (r.duration === undefined) {
            return "";
          } else if (r.cached) {
            return `${r.duration} (cached)`;
          } else {
            return `${r.duration}`;
          }
        },
        sorter: (a, b) => {
          let durationLeft = a.duration || "0s";
          let l = parseFloat(
            durationLeft.substring(0, durationLeft.length - 1),
          );
          let durationRight = b.duration || "0s";
          let r = parseFloat(
            durationRight.substring(0, durationRight.length - 1),
          );
          return l - r;
        },
      },
    ];

    let testResults = getTargetDataFromJobData(jobData);

    let tabs = (
      <Empty
        description="Test run is preparing. No results available yet."
        style={{ paddingTop: "5em" }}
      />
    );
    if (r.state === "RUN_STATE_ABORT") {
      tabs = (
        <Empty
          description="Test run was aborted. No results available."
          style={{ paddingTop: "5em" }}
        />
      );
    } else if (r.jobs) {
      let resultsTable = (
        <Table
          dataSource={testResults}
          columns={columns}
          loading={!isJobsDrained || isLoading}
        />
      );
      if (r.state === "RUN_STATE_FAILURE" && testResults.length === 0) {
        resultsTable = (
          <Empty
            description="Test run failed without test results. See Console for details."
            style={{ paddingTop: "5em" }}
          />
        );
      }

      let consoleComponent = (
        <Empty
          description="Test is not running. Console not available yet."
          style={{ paddingTop: "5em" }}
        />
      );
      let archiveComponent = (
        <Empty
          description="Test is not done. Archive not available yet."
          style={{ paddingTop: "5em" }}
        />
      );
      if (isFinal) {
        consoleComponent = (
          <ConsoleLogComponent
            consoleLogs={consoleData}
            isConsoleLoading={isConsoleLoading || isLoading}
          />
        );
        let archiveData = r.jobs.flatMap(
          (j) => j.archiveFiles?.map((a) => [j.jobId, a]) || [],
        );
        archiveComponent = (
          <ArchiveComponent runId={runId} archiveFiles={archiveData} />
        );
      } else if (
        r.state !== "RUN_STATE_CHECKOUT" &&
        r.state !== "RUN_STATE_INIT"
      ) {
        consoleComponent = <ConsoleInProgressComponent jobData={jobData} />;
      }
      let rawComponent = (
        <Text style={{ whiteSpace: "pre-wrap" }}>
          <pre className="language-json">{JSON.stringify(r, null, "\t")}</pre>
        </Text>
      );
      const items: TabsProps["items"] = [
        {
          key: "1",
          label: `Results`,
          children: resultsTable,
        },
        {
          key: "2",
          label: `Console`,
          children: consoleComponent,
        },
        {
          key: "3",
          label: `Archive`,
          children: archiveComponent,
        },
        {
          key: "4",
          label: `Raw`,
          children: rawComponent,
        },
      ];

      tabs = (
        <Tabs
          defaultActiveKey="1"
          items={items}
          onChange={(activeKey) => {
            setIsConsoleTab(activeKey === "2");
          }}
        />
      );
    }

    let finishedTime = <div />;
    if (isFinalState(r.state)) {
      finishedTime = (
        <Tooltip title={r.lastStateChangeTime}>
          {dayjs(r.lastStateChangeTime).local().format("LLL")}
        </Tooltip>
      );
    }

    const children = (
      <div>
        <Descriptions title="Run Info" bordered column={2}>
          <Descriptions.Item label="Run Id" span={2}>
            {runId}
          </Descriptions.Item>
          <Descriptions.Item label="Status" span={1}>
            {" "}
            <StatusComponent run={r} testResults={testResults} />
          </Descriptions.Item>
          <Descriptions.Item label="Requestor" span={1}>
            {r.requestor || ""}
          </Descriptions.Item>
          <Descriptions.Item label="Created At" span={1}>
            <Tooltip title={r.createTime}>
              {dayjs(r.createTime).local().format("LLL")}
            </Tooltip>
          </Descriptions.Item>
          <Descriptions.Item label="Finished At" span={1}>
            {finishedTime}
          </Descriptions.Item>
          {checkout}
        </Descriptions>
        {tabs}
      </div>
    );
    let restartFloat = <div />;
    let cancelFloat = <div />;
    if (isFinalState(runData?.state || "")) {
      restartFloat = (
        <FloatButton
          icon={<ReloadOutlined />}
          tooltip="Restart"
          onClick={() => {
            restartRun(runId).then((newRunId) => {
              setRunData(undefined);
              setConsoleData(undefined);
              setJobData([]);
              navigate(`/run/${newRunId}`);
            });
          }}
          type="primary"
          style={{ right: 94 }}
        />
      );
    } else {
      cancelFloat = (
        <FloatButton
          icon={<CloseCircleOutlined />}
          tooltip="Cancel"
          onClick={() => {
            cancelRun(runId).then(() => {
              setRunData(undefined);
              setConsoleData(undefined);
              setJobData([]);
            });
          }}
          type="primary"
          style={{ right: 94 }}
        />
      );
    }
    return (
      <>
        {contextHolder}
        <LayoutComponent
          children={children}
          selectedMenuKey={"runs"}
          breadcrumbs={[
            { label: "Runs", link: "/runs" },
            { label: `Run ${runId}`, link: `/run/${runId}` },
          ]}
        />
        {restartFloat}
        {cancelFloat}
      </>
    );
  }
}
