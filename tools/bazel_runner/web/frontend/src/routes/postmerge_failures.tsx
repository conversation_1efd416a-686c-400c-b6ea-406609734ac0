// Page displaying an overview of postmerge test failures
import "../App.css";
import { LayoutComponent } from "../lib/layout";
import { TestRunData, getRuns, getTestRun, isFinalState } from "../lib/run";
import React, { useEffect, useState } from "react";
import {
  Badge,
  Empty,
  Progress,
  Spin,
  Table,
  Typography,
  message,
  Tooltip,
  Tag,
} from "antd";
import type { TableColumnType, TableProps as AntdTableProps } from "antd";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import localizedFormat from "dayjs/plugin/localizedFormat";
import { Link } from "react-router-dom";
import { SimpleStatusComponent } from "./pending_runs";
import axios from "axios";

const { Text } = Typography;

dayjs.extend(utc);
dayjs.extend(localizedFormat);

// Type for the matrix data structure
// Type to store test information for linking
interface TestInfo {
  jobId: string;
}

interface FailureMatrixData {
  runId: string;
  createTime: string;
  state: string;
  commit?: string;
  failedTargets: Record<string, TestInfo>;
  flakyTargets: Record<string, TestInfo>;
  passedTargets: Record<string, TestInfo>;
}

// Type for the column definition
type FailureColumn = TableColumnType<FailureMatrixData> & {
  targetName?: string;
};

// Type for table props
type TableProps = AntdTableProps<FailureMatrixData>;

// Loading phase enum
type LoadingPhase =
  | "initial"
  | "fetchingRuns"
  | "processingMatrix"
  | "complete"
  | "error";

function PostmergeFailuresComponent() {
  const [runData, setRunData] = useState<TestRunData[] | undefined | null>(
    undefined,
  );
  const [matrixData, setMatrixData] = useState<FailureMatrixData[] | undefined>(
    undefined,
  );
  const [failedTargets, setFailedTargets] = useState<string[]>([]);
  const [failureCounts, setFailureCounts] = useState<
    Record<string, { failed: number; flaky: number }>
  >({});
  const [loadingPhase, setLoadingPhase] = useState<LoadingPhase>("initial");
  const [loadingProgress, setLoadingProgress] = useState<number>(0);
  const [messageApi, contextHolder] = message.useMessage();

  // Fetch postmerge runs until we have 50
  useEffect(() => {
    const fetchPostmergeRuns = async () => {
      try {
        setLoadingPhase("fetchingRuns");
        let allPostmergeRuns: TestRunData[] = [];
        let oldestRunId: string | undefined = undefined;
        const batchSize = 50;
        const targetCount = 20;

        // Keep fetching batches until we have enough postmerge runs or no more runs to fetch
        while (allPostmergeRuns.length < targetCount) {
          const runs = await getRuns(undefined, oldestRunId, batchSize);

          // If no more runs to fetch, break
          if (runs.length === 0) break;

          console.log(`Found ${runs.length} runs`);

          // Filter for postmerge runs
          const postmergeRuns = runs.filter(
            (runItem) =>
              runItem.tags &&
              runItem.tags.includes("post-merge") &&
              isFinalState(runItem.state),
          );

          console.log(`Found ${postmergeRuns.length} postmerge runs`);

          // Add to our collection
          allPostmergeRuns = [...allPostmergeRuns, ...postmergeRuns];

          // Update progress
          setLoadingProgress(
            Math.min(
              100,
              Math.round((allPostmergeRuns.length / targetCount) * 50),
            ),
          );

          // Update the oldest run ID for the next batch
          oldestRunId = runs[runs.length - 1].runId;
        }

        // Take only the first 50 postmerge runs
        const finalRuns = allPostmergeRuns.slice(0, targetCount);
        console.log(`Final runs: ${JSON.stringify(finalRuns)}`);
        setRunData(finalRuns);
        setLoadingProgress(50); // 50% progress after fetching runs
      } catch (e) {
        console.log(`Error while loading the run data: ${e}`);
        setLoadingPhase("error");
        if (axios.isAxiosError(e)) {
          setRunData(null);
          messageApi.open({
            type: "error",
            content: `Error while loading the run data: ${e.message}`,
          });
        } else {
          setRunData(null);
          messageApi.open({
            type: "error",
            content: `Error while loading the run data`,
          });
        }
      }
    };
    fetchPostmergeRuns();
  }, [messageApi]);

  // Process the run data to extract failed targets and build the matrix
  useEffect(() => {
    if (!runData) return;

    const processRunData = async () => {
      try {
        console.log(`Processing run data`);
        setLoadingPhase("processingMatrix");
        setLoadingProgress(50); // Start at 50% after fetching runs
        // Create a set to track all failed targets
        const failedTargetsSet = new Set<string>();
        const matrixDataArray: FailureMatrixData[] = [];

        // Track failure and flaky counts for each target
        const failureCountByTarget: Record<
          string,
          { failed: number; flaky: number }
        > = {};

        // Process each run to extract failed targets
        for (const run of runData) {
          console.log(`Processing run ${run.runId}`);

          // Get detailed run info to access test results
          const detailedRun = await getTestRun(run.runId);
          if (!detailedRun || !detailedRun.jobs) continue;

          // Extract failed, flaky, and passed targets from the run
          const runFailedTargets: Record<string, TestInfo> = {};
          const runFlakyTargets: Record<string, TestInfo> = {};
          const runPassedTargets: Record<string, TestInfo> = {};

          for (const job of detailedRun.jobs) {
            if (!job.tests) continue;

            for (const test of job.tests) {
              if (test.status === "FAILED" || test.status === "TIMEOUT") {
                failedTargetsSet.add(test.targetName);
                runFailedTargets[test.targetName] = {
                  jobId: job.jobId,
                };

                // Initialize or update failure count for this target
                if (!failureCountByTarget[test.targetName]) {
                  failureCountByTarget[test.targetName] = {
                    failed: 0,
                    flaky: 0,
                  };
                }
                failureCountByTarget[test.targetName].failed += 1;
              } else if (test.status === "FLAKY") {
                failedTargetsSet.add(test.targetName); // Add flaky tests to the failed targets set for display
                runFlakyTargets[test.targetName] = {
                  jobId: job.jobId,
                };

                // Initialize or update flaky count for this target
                if (!failureCountByTarget[test.targetName]) {
                  failureCountByTarget[test.targetName] = {
                    failed: 0,
                    flaky: 0,
                  };
                }
                failureCountByTarget[test.targetName].flaky += 1;
              } else if (test.status === "PASSED") {
                runPassedTargets[test.targetName] = {
                  jobId: job.jobId,
                };
              }
            }
          }

          // Get commit info
          let commit: string | undefined;

          if (detailedRun.testExecution?.checkout?.commitCheckout) {
            commit = detailedRun.testExecution.checkout.commitCheckout
              .ref as string;
          }

          const data = {
            runId: run.runId,
            createTime: run.createTime,
            state: run.state,
            commit,
            failedTargets: runFailedTargets,
            flakyTargets: runFlakyTargets,
            passedTargets: runPassedTargets,
          };
          console.log(`Data: ${JSON.stringify(data)}`);

          matrixDataArray.push(data);
        }

        // Sort matrix data by creation time (newest first)
        matrixDataArray.sort(
          (a, b) =>
            dayjs(b.createTime).valueOf() - dayjs(a.createTime).valueOf(),
        );

        // Convert failed targets set to array and sort by failure count (most failures first)
        const failedTargetsArray = Array.from(failedTargetsSet).sort((a, b) => {
          const aCount = failureCountByTarget[a] || { failed: 0, flaky: 0 };
          const bCount = failureCountByTarget[b] || { failed: 0, flaky: 0 };
          // Sort primarily by failed count, then by flaky count
          return bCount.failed !== aCount.failed
            ? bCount.failed - aCount.failed
            : bCount.flaky - aCount.flaky;
        });

        // Store the failure counts for use in the UI
        setFailureCounts(failureCountByTarget);

        setFailedTargets(failedTargetsArray);
        setMatrixData(matrixDataArray);
        setLoadingPhase("complete");
        setLoadingProgress(100);
      } catch (e) {
        console.log(`Error while processing run data: ${e}`);
        setLoadingPhase("error");
        messageApi.open({
          type: "error",
          content: `Error while processing run data: ${e}`,
        });
      }
    };

    processRunData();
  }, [runData, messageApi]);

  // Generate table columns based on failed targets
  const generateColumns = (): FailureColumn[] => {
    const baseColumns: FailureColumn[] = [
      {
        title: "Run ID",
        dataIndex: "runId",
        key: "runId",
        fixed: "left" as const,
        ellipsis: true,
        render: (_, record) => (
          <Link to={`/run/${record.runId}`} target="_blank">
            <Tooltip title={record.runId}>
              <Text keyboard> {record.runId.substring(0, 16)}...</Text>
            </Tooltip>
          </Link>
        ),
      },
      {
        title: "Created",
        dataIndex: "createTime",
        key: "createTime",
        fixed: "left" as const,
        render: (_, record) => (
          <Tooltip
            title={dayjs(record.createTime)
              .local()
              .format("YYYY-MM-DD HH:mm:ss")}
          >
            <div style={{ whiteSpace: "nowrap" }}>
              {dayjs(record.createTime).local().format("MM-DD HH:mm")}
            </div>
          </Tooltip>
        ),
        sorter: (a, b) =>
          dayjs(a.createTime).valueOf() - dayjs(b.createTime).valueOf(),
        defaultSortOrder: "descend",
      },
      {
        title: "State",
        dataIndex: "state",
        key: "state",
        fixed: "left" as const,
        render: (_, record) => {
          // Create a minimal TestRunData object with just the properties needed by SimpleStatusComponent
          const runData = {
            state: record.state,
            message: "",
          } as TestRunData;
          return <SimpleStatusComponent run={runData} />;
        },
        filters: [
          {
            text: "Passed",
            value: "RUN_STATE_PASSED",
          },
          {
            text: "Error",
            value: "RUN_STATE_ERROR",
          },
          {
            text: "Failure",
            value: "RUN_STATE_FAILURE",
          },
          {
            text: "Aborted",
            value: "RUN_STATE_ABORT",
          },
        ],
        onFilter: (
          value: string | number | boolean,
          record: FailureMatrixData,
        ) => record.state === (value as string),
        sorter: (a, b) => a.state.localeCompare(b.state),
      },
    ];

    // Add columns for each failed target
    const targetColumns: FailureColumn[] = failedTargets.map((target) => ({
      title: (
        <Tooltip title={target}>
          <div
            style={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
            }}
          >
            {/* Test name */}
            <div>
              {(() => {
                // Extract the most meaningful part of the target path
                const parts = target.split("/");
                const lastPart = parts[parts.length - 1];

                // If the last part contains a colon (e.g., "package:target"), extract the target name
                if (lastPart.includes(":")) {
                  return lastPart.split(":").pop();
                }

                // If it's a very long name, try to abbreviate it
                if (lastPart.length > 15) {
                  // Try to find a meaningful abbreviation
                  // For example, convert "very_long_test_name" to "v.l.t.name"
                  const words = lastPart.split("_");
                  if (words.length > 2) {
                    // Keep the first letter of each word except the last word
                    return (
                      words
                        .slice(0, -1)
                        .map((word) => word.charAt(0) + ".")
                        .join("") + words[words.length - 1]
                    );
                  }
                  // If we can't abbreviate it well, just truncate
                  return lastPart.substring(0, 12) + "...";
                }

                return lastPart;
              })()}
            </div>

            {/* Failure counts */}
            {failureCounts[target] && (
              <div style={{ marginTop: 4 }}>
                <div style={{ display: "flex", gap: 4, fontSize: "11px" }}>
                  {failureCounts[target].failed > 0 && (
                    <Badge
                      count={failureCounts[target].failed}
                      size="small"
                      style={{ backgroundColor: "#ff4d4f" }}
                    />
                  )}
                  {failureCounts[target].flaky > 0 && (
                    <Badge
                      count={failureCounts[target].flaky}
                      size="small"
                      style={{ backgroundColor: "#faad14" }}
                    />
                  )}
                </div>
              </div>
            )}
          </div>
        </Tooltip>
      ),
      dataIndex: `target_${target}`,
      key: `target_${target}`,
      targetName: target,
      fixed: "center" as const,
      render: (_, record) => {
        // Helper function to create a link to the test case
        const createTestLink = (
          status: string,
          badgeType: "error" | "warning" | "success",
          testInfo: TestInfo,
        ) => {
          let linkTo = `/run/${record.runId}/job/${testInfo.jobId}/target/${encodeURIComponent(target)}`;

          return (
            <Link to={linkTo} target="_blank">
              <Tag color={badgeType}>{status}</Tag>
            </Link>
          );
        };

        if (record.failedTargets[target]) {
          return createTestLink(
            "Failed",
            "error",
            record.failedTargets[target],
          );
        }
        if (record.flakyTargets[target]) {
          return createTestLink(
            "Flaky",
            "warning",
            record.flakyTargets[target],
          );
        }
        if (record.passedTargets[target]) {
          return createTestLink(
            "Passed",
            "success",
            record.passedTargets[target],
          );
        }
        return <div />;
      },
    }));

    return [...baseColumns, ...targetColumns];
  };

  if (loadingPhase !== "complete" && loadingPhase !== "error") {
    // Show loading state with progress and phase information
    let loadingMessage = "Loading...";

    if (loadingPhase === "fetchingRuns") {
      loadingMessage = "Fetching postmerge test runs...";
    } else if (loadingPhase === "processingMatrix") {
      loadingMessage = "Processing test failure data...";
    }

    return (
      <>
        {contextHolder}
        <LayoutComponent
          selectedMenuKey={"postmerge_failures"}
          breadcrumbs={[
            { label: "Postmerge Failures", link: "/postmerge/failures" },
          ]}
        >
          <div style={{ textAlign: "center", padding: "50px 0" }}>
            <Spin size="large" />
            <div style={{ marginTop: 20 }}>
              <Text strong>{loadingMessage}</Text>
            </div>
            <div style={{ width: "50%", margin: "20px auto" }}>
              <Progress percent={loadingProgress} status="active" />
            </div>
          </div>
        </LayoutComponent>
      </>
    );
  } else if (loadingPhase === "error" || !runData || !matrixData) {
    return (
      <>
        {contextHolder}
        <LayoutComponent
          selectedMenuKey={"postmerge_failures"}
          breadcrumbs={[
            { label: "Postmerge Failures", link: "/postmerge/failures" },
          ]}
        >
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={
              <span>
                <Text type="danger" strong>
                  Failed to load postmerge test data
                </Text>
                <br />
                <Text type="secondary">Please try refreshing the page</Text>
              </span>
            }
          />
        </LayoutComponent>
      </>
    );
  } else if (matrixData.length === 0) {
    return (
      <>
        {contextHolder}
        <LayoutComponent
          selectedMenuKey={"postmerge_failures"}
          breadcrumbs={[
            { label: "Postmerge Failures", link: "/postmerge/failures" },
          ]}
        >
          <Empty description="No postmerge test failures found" />
        </LayoutComponent>
      </>
    );
  } else {
    const columns = generateColumns();

    // Configure table scroll properties based on the number of columns
    const tableScroll = {
      x: Math.max(1200, 260 + failedTargets.length * 40),
      y: "calc(100vh - 300px)",
    };

    // Configure table size and other properties
    const tableProps: Partial<AntdTableProps<FailureMatrixData>> = {
      size: "small",
      bordered: true,
      pagination: { pageSize: 50, position: ["bottomCenter"] },
    };

    return (
      <>
        {contextHolder}
        <LayoutComponent
          selectedMenuKey={"postmerge_failures"}
          breadcrumbs={[
            { label: "Postmerge Failures", link: "/postmerge/failures" },
          ]}
        >
          <div style={{ marginBottom: 16 }}>
            <Text>
              Showing postmerge test failures from the last {matrixData.length}{" "}
              runs. Each column represents a test target that failed in at least
              one run.
            </Text>
          </div>
          <Table
            dataSource={matrixData}
            columns={columns}
            rowKey="runId"
            scroll={tableScroll}
            {...tableProps}
          />
        </LayoutComponent>
      </>
    );
  }
}

export default PostmergeFailuresComponent;
