import "../App.css";
import { LayoutComponent } from "../lib/layout";
import { TestRunData, getRuns, isFinalState } from "../lib/run";
import React, { useEffect, useState } from "react";
import {
  Button,
  Divider,
  Empty,
  Select,
  Spin,
  Table,
  Typography,
  message,
} from "antd";
import { ColumnType } from "antd/es/table";
import dayjs, { Dayjs } from "dayjs";
import utc from "dayjs/plugin/utc";
import localizedFormat from "dayjs/plugin/localizedFormat";
import { useParams } from "react-router-dom";
import axios from "axios";
import { SimpleStatusComponent, SummaryComponent } from "./pending_runs";
import { TagsComponent } from "./run";
const { Text, Link } = Typography;

dayjs.extend(utc);
dayjs.extend(localizedFormat);

type Paging = {
  newestRunIdStack: string[];
  pageSize: number;
};

function getUniqueTags(data: TestRunData[]): string[] {
  const tagSet = new Set<string>();
  data.forEach((item) => {
    if (item.tags) {
      item.tags.forEach((tag) => tagSet.add(tag));
    }
  });
  return Array.from(tagSet);
}

function PageSizeSelector({
  pageSize,
  onChange,
}: {
  pageSize: number;
  onChange: (size: number) => void;
}) {
  return (
    <div
      style={{
        display: "inline-flex",
        alignItems: "center",
        marginLeft: "16px",
      }}
    >
      <span style={{ marginRight: "8px" }}>Items per page:</span>
      <Select
        value={pageSize}
        onChange={onChange}
        options={[
          { value: 5, label: "5" },
          { value: 10, label: "10" },
          { value: 20, label: "20" },
          { value: 50, label: "50" },
          { value: 100, label: "100" },
        ]}
        style={{ width: "80px" }}
      />
    </div>
  );
}

function RunsPageComponent() {
  const [runData, setRunData] = useState<TestRunData[] | undefined | null>(
    undefined,
  );
  const [page, setPage] = useState<Paging>({
    newestRunIdStack: [],
    pageSize: 20, // Default page size
  });
  console.log(`page ${JSON.stringify(page)}`);
  const [isLoading, setIsLoading] = useState(true);
  const [messageApi, contextHolder] = message.useMessage();

  useEffect(() => {
    const fetchRun = async () => {
      console.log(`fetch run`);
      try {
        const runInfo = await getRuns(
          undefined,
          page.newestRunIdStack.at(-1),
          page.pageSize, // Pass the page size to the API
        );
        console.log(`run ${JSON.stringify(runInfo)}`);
        setRunData(runInfo);
        setIsLoading(false);
      } catch (e) {
        console.log(`Error while loading the run data: ${e}`);
        if (axios.isAxiosError(e)) {
          setIsLoading(false);
          setRunData(null);
          messageApi.open({
            type: "error",
            content: `Error while loading the run data: ${e.message}`,
          });
        } else {
          setIsLoading(false);
          setRunData(null);
          messageApi.open({
            type: "error",
            content: `Error while loading the run data`,
          });
        }
      }
    };
    fetchRun();
  }, [page]);

  const columns: ColumnType<TestRunData>[] = [
    {
      title: "Run ID",
      key: "runId",
      render: (t: TestRunData) => {
        return (
          <Link href={`/run/${t.runId}`}>
            <Text keyboard>{t.runId}</Text>
          </Link>
        );
      },
      sorter: (a, b) => {
        return a.runId.localeCompare(b.runId);
      },
    },
    {
      title: "Summary",
      key: "summary",
      render: (data: TestRunData) => {
        return <SummaryComponent run={data} />;
      },
    },
    {
      title: "State",
      key: "state",
      render: (t: TestRunData) => {
        return <SimpleStatusComponent run={t} />;
      },
      filters: [
        {
          text: "Created",
          value: "RUN_STATE_INIT",
        },
        {
          text: "Checking out",
          value: "RUN_STATE_CHECKOUT",
        },
        {
          text: "Running",
          value: "RUN_STATE_RUN",
        },
        {
          text: "Waiting",
          value: "RUN_STATE_RUN_WAITING",
        },
        {
          text: "Post processing",
          value: "RUN_STATE_POSTPROCESSING",
        },
        {
          text: "Passed",
          value: "RUN_STATE_PASSED",
        },
        {
          text: "Error",
          value: "RUN_STATE_ERROR",
        },
        {
          text: "Aborted",
          value: "RUN_STATE_ABORT",
        },
      ],
      onFilter: (value: string | number | boolean, record: TestRunData) =>
        record.state === (value as string),
      sorter: (a, b) => {
        return a.state.localeCompare(b.state);
      },
    },
    {
      title: "Created Time",
      key: "createdTime",
      render: (t: TestRunData) => {
        const ft = dayjs(t.createTime).local().format("LLL");
        return <Text>{ft}</Text>;
      },
      sorter: (a, b) => {
        return a.createTime.localeCompare(b.createTime);
      },
    },
    {
      title: "Finished Time",
      key: "finishedTime",
      render: (t: TestRunData) => {
        let ft: string | undefined;
        if (
          !isFinalState(t.state) ||
          dayjs(t.lastStateChangeTime).valueOf() === 0
        ) {
          return <div></div>;
        } else {
          ft = dayjs(t.lastStateChangeTime).local().format("LLL");
          return <Text>{ft}</Text>;
        }
      },
      sorter: (a, b) => {
        return a.lastStateChangeTime.localeCompare(b.lastStateChangeTime);
      },
    },
    {
      title: "Tags",
      key: "tags",
      render: (t: TestRunData) => {
        return <TagsComponent value={t.tags} />;
      },
      filters: runData
        ? getUniqueTags(runData).map((tag) => ({ text: tag, value: tag }))
        : [],
      onFilter: (value: string | number | boolean, t: TestRunData) =>
        t.tags !== undefined && t.tags.includes(value as string),
    },
    {
      title: "Requestor",
      key: "requestor",
      render: (t: TestRunData) => {
        return (
          <Text keyboard ellipsis={true}>
            {t.requestor || ""}
          </Text>
        );
      },
      sorter: (a, b) => {
        const ar = a.requestor || "";
        const br = b.requestor || "";
        return ar.localeCompare(br);
      },
    },
  ];

  if (isLoading) {
    return (
      <>
        {contextHolder}
        <LayoutComponent
          selectedMenuKey={"runs"}
          breadcrumbs={[{ label: "Runs", link: "/runs" }]}
        >
          <Spin />
        </LayoutComponent>
      </>
    );
  } else if (runData == null) {
    return (
      <LayoutComponent
        selectedMenuKey={"runs"}
        breadcrumbs={[{ label: "Runs", link: "/runs" }]}
      >
        <Empty>Failed to load runs</Empty>
      </LayoutComponent>
    );
  } else {
    let resultsTable = (
      <Table dataSource={runData} columns={columns} pagination={false} />
    );
    // Add the page size change handler
    const handlePageSizeChange = (newSize: number) => {
      // Reset to first page when changing page size
      setPage({
        newestRunIdStack: [],
        pageSize: newSize,
      });
    };

    // Update the pagination controls to include pageSize and the selector
    let backAndLinks = (
      <>
        <Divider />
        <Button
          onClick={() => {
            const newPage: Paging = {
              newestRunIdStack: [],
              pageSize: page.pageSize,
            };
            setPage(newPage);
          }}
        >
          Current
        </Button>
        <Divider type="vertical" />
        <Button
          disabled={page.newestRunIdStack.length === 0}
          onClick={() => {
            const newPage: Paging = {
              newestRunIdStack: page.newestRunIdStack.slice(
                0,
                page.newestRunIdStack.length - 1,
              ),
              pageSize: page.pageSize,
            };
            setPage(newPage);
          }}
        >
          Newer
        </Button>
        <Divider type="vertical" />
        <Button
          disabled={runData.length === 0}
          onClick={() => {
            const newPage: Paging = {
              newestRunIdStack: page.newestRunIdStack.concat([
                runData[runData.length - 1].runId,
              ]),
              pageSize: page.pageSize,
            };
            setPage(newPage);
          }}
        >
          Older
        </Button>
        <PageSizeSelector
          pageSize={page.pageSize}
          onChange={handlePageSizeChange}
        />
      </>
    );

    const children = [resultsTable, backAndLinks];
    return (
      <>
        {contextHolder}
        <LayoutComponent
          selectedMenuKey={"runs"}
          children={children}
          breadcrumbs={[{ label: "Runs", link: "/runs" }]}
        ></LayoutComponent>
      </>
    );
  }
}

export default RunsPageComponent;
