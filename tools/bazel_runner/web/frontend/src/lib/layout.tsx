// react component containing the overall layout of the pages
import React from "react";
import { Layout } from "antd";
import { Breadcrumb, Menu, theme } from "antd";
import { Link } from "react-router-dom";

const { <PERSON><PERSON>, Footer, Content } = Layout;

type BreadcrumbItem = {
  label: string;
  link: string;
};

type Probs = {
  // key of the menu item to select
  selectedMenuKey?: string;
  // react children nodes to display
  children?: React.ReactNode;
  // breadcrumb items to display
  breadcrumbs: BreadcrumbItem[];
};

export const LayoutComponent = ({
  children,
  selectedMenuKey,
  breadcrumbs,
}: Probs) => {
  const {
    token: { colorBgContainer },
  } = theme.useToken();
  let defaultSelectedKeys: Array<string> = [];
  if (selectedMenuKey) {
    defaultSelectedKeys = [selectedMenuKey];
  }

  return (
    <Layout className="layout">
      <Header>
        <div className="logo" />
        <Menu
          theme="dark"
          mode="horizontal"
          defaultSelectedKeys={defaultSelectedKeys}
          items={[
            {
              key: "home",
              label: <Link to={"/"}>Test Runner</Link>,
            },
            {
              key: "schedule_runs",
              label: <Link to={"/runs/schedule"}>Schedule Runs</Link>,
            },
            {
              key: "search_runs",
              label: <Link to={"/runs/search"}>Search Runs</Link>,
            },
            {
              key: "pending_runs",
              label: <Link to={"/runs/pending"}>Pending Runs</Link>,
            },
            {
              key: "postmerge_failures",
              label: <Link to={"/postmerge/failures"}>Postmerge Failures</Link>,
            },
          ]}
        />
      </Header>
      <Content style={{ padding: "0 50px" }}>
        <Breadcrumb style={{ margin: "16px 0" }}>
          <Breadcrumb.Item>
            <Link to={"/"}>Home</Link>
          </Breadcrumb.Item>
          {breadcrumbs.map((b) => {
            return (
              <Breadcrumb.Item key={b.label}>
                <Link to={b.link}>{b.label}</Link>
              </Breadcrumb.Item>
            );
          })}
        </Breadcrumb>
        <div
          className="site-layout-content"
          style={{ background: colorBgContainer }}
        >
          {children}
        </div>
      </Content>
      <Footer style={{ textAlign: "center" }}>Augment Code - Internal</Footer>
    </Layout>
  );
};
