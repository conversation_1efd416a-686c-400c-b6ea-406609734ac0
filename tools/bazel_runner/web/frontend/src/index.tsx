import React from "react";
import ReactDOM from "react-dom/client";
import "./index.css";
import { createBrowserRouter, RouterProvider } from "react-router-dom";
import App from "./App";
import ErrorPage from "./error-page";
import RunComponent from "./routes/run";
import TestLogComponent from "./routes/test_log";
import RunsSchedulePageComponent from "./routes/schedule_runs";
import RunsSearchPageComponent from "./routes/search_runs";
import PendingRunsPageComponent from "./routes/pending_runs";
import RunsPageComponent from "./routes/runs";
import PostmergeFailuresComponent from "./routes/postmerge_failures";

// sets up the different routes
const router = createBrowserRouter([
  {
    path: "/",
    element: <RunsPageComponent />,
    errorElement: <ErrorPage />,
  },
  {
    path: "runs",
    element: <RunsPageComponent />,
  },
  {
    path: "run/:runId",
    element: <RunComponent />,
  },
  {
    path: "run/:runId/job/:jobId/target/:target",
    element: <TestLogComponent />,
  },
  {
    path: "runs/schedule",
    element: <RunsSchedulePageComponent />,
  },
  {
    path: "runs/search",
    element: <RunsSearchPageComponent />,
  },
  {
    path: "runs/pending",
    element: <PendingRunsPageComponent />,
  },
  {
    path: "postmerge/failures",
    element: <PostmergeFailuresComponent />,
  },
]);

const root = ReactDOM.createRoot(document.getElementById("root")!);
root.render(
  <React.StrictMode>
    <RouterProvider router={router} />
  </React.StrictMode>,
);
