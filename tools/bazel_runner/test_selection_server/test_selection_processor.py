"""Test Selection Server.

The test selection server managed which test targets to execute, e.g. for a pull request.
"""

import argparse
import logging
import os
import pathlib
import typing
import sys
import time
import uuid

import git
import github
import grpc
import structlog
import prometheus_client
from prometheus_client import Counter, Gauge, Histogram
from google.cloud import pubsub_v1  # type: ignore

import tools.bazel_runner.test_selection_server.test_selection_pb2 as test_selection_pb2
from base.logging.struct_logging import setup_struct_logging
from tools.bazel_runner.git import app_token, checkout, checkout_pb2
from tools.bazel_runner.test_selection_server import (
    target_finder,
    target_info,
    test_selection,
)
from tools.bazel_runner.test_selection_server.config import load_config
from tools.bazel_runner.test_selection_server.persistence import Persistence

log = structlog.get_logger()

# Define Prometheus metrics
# Exponentially increasing buckets with ~20% width. Min is ~10ms and max is ~20s.
_buckets = tuple((1.2**i - 1) / 25.0 for i in range(36)) + (float("inf"),)
print(_buckets)
# Track how long it takes to process operations
OPERATION_PROCESSING_TIME = Histogram(
    "au_test_selection_operation_processing_time_seconds",
    "Time taken to process a test selection operation in seconds",
    buckets=_buckets,
)

# Track successful and failed operations
OPERATION_RESULT = Counter(
    "au_test_selection_operation_result_total",
    "Result of test selection operations",
    ["result"],  # success or failure
)

# Track the number of targets found
TARGETS_FOUND = Histogram(
    "au_test_selection_targets_found",
    "Number of test targets found by the test selection processor",
    buckets=[0, 1, 2, 5, 10, 20, 50, 100, 200, 500, 1000, float("inf")],
)


def target_info_to_proto(t: target_info.TargetInfo) -> test_selection_pb2.TargetInfo:
    """Convert a target info to a proto."""
    return test_selection_pb2.TargetInfo(name=t.name, kind=t.kind, tags=t.tags)


class TestSelection:
    """Class to find test targets impacted by changes between two git commits."""

    def __init__(
        self,
        test_selection_config: test_selection.TestSelectionConfig,
        run_checkout: checkout.Checkout,
        tf: target_finder.TargetFinder,
        github_client_fn: typing.Callable[[], github.Github],
        extra_startup_args: str,
        subscriber: pubsub_v1.SubscriberClient,
        subscription_path: str,
        persistence: Persistence,
    ):
        """Initialize the class.

        Args:
            run_checkout: The checkout to use for the run.
            tf: The target finder.
            github_client_fn: The github client function. This returns a github client with a fresh token when called
            extra_startup_args: The extra startup args.
            subscriber: The subscriber.
            subscription_path: The subscription path.
            persistence: The persistence.

        """
        self.config = test_selection_config
        self.checkout = run_checkout
        self.target_finder = tf
        self.github_client_fn = github_client_fn
        self.extra_startup_args = extra_startup_args
        self.subscriber = subscriber
        self.subscription_path = subscription_path
        self.persistence = persistence

    def _handle_test_selection_operation(self, operation_id: str) -> bool:
        """Performs a test selectio operations.

        This method gets the operation and finds the impacted targets, performs the
        test selection and updates the operation.
        """
        # Increment active operations counter
        start_time = time.time()
        success = False

        logging.info("Run Operation: %s", operation_id)
        structlog.contextvars.bind_contextvars(job_id=str(operation_id))
        operation = self.persistence.get_operation(uuid.UUID(operation_id))
        logging.info("Operation: %s", operation)
        if not operation:
            logging.error("Failed to find operation: %s", operation_id)
            OPERATION_RESULT.labels(result="failure").inc()
            return True
        request = operation.request
        try:
            policy = test_selection.get_test_selection(request.policy, self.config)
            impacted = policy.pre_filter_impacted
            if impacted:
                if not request.HasField(
                    "start_checkout"
                ) and not request.end_checkout.HasField("pull_request_checkout"):
                    # nothing to diff against
                    impacted = False
            if impacted:
                targets = self._get_impacted_targets(
                    (
                        request.start_checkout
                        if request.HasField("start_checkout")
                        else None
                    ),
                    request.end_checkout,
                    policy,
                    request.extra_query,
                )
            else:
                targets = self._get_all_test_targets(
                    request.end_checkout,
                    request.extra_query,
                )

            logging.info("Targets: %s", targets)

            targets = [target for target in targets if policy.filter(target)]
            logging.info("Filtered targets: %s", targets)

            # Record the number of targets found
            TARGETS_FOUND.observe(len(targets))

            operation.result.targets.extend([target_info_to_proto(t) for t in targets])
            self.persistence.update_operation(operation)
            success = True
        except github.GithubException as ex:  # type: ignore
            # if this happens, we want to take down the server
            if ex.status == 401:
                raise
            else:
                logging.error("Failed to get impacted targets: %s", ex)
                logging.exception(ex)
                operation.error.code = ex.status_code.value[0]  # type: ignore
                operation.error.message = ex.message  # type: ignore
                self.persistence.update_operation(operation)
        except checkout.CheckoutException as ex:
            if ex.status_code == grpc.StatusCode.UNAVAILABLE:
                # we will retry the operation later
                return False
            else:
                logging.error("Failed to get impacted targets: %s", ex)
                logging.exception(ex)
                operation.error.code = ex.status_code.value[0]  # type: ignore
                operation.error.message = ex.message  # type: ignore
                self.persistence.update_operation(operation)
        except target_finder.TargetFinderException as ex:
            logging.error("Failed to get impacted targets: %s", ex)
            logging.exception(ex)
            operation.error.code = ex.status_code.value[0]  # type: ignore
            operation.error.message = ex.message  # type: ignore
            self.persistence.update_operation(operation)
        except Exception as ex:  # pylint: disable=broad-exception-caught
            logging.error("Failed to get impacted targets: %s", ex)
            logging.exception(ex)
            operation.error.code = grpc.StatusCode.UNKNOWN.value[0]  # type: ignore
            operation.error.message = str(ex)  # type: ignore
            self.persistence.update_operation(operation)
        finally:
            # Record operation result
            if success:
                OPERATION_RESULT.labels(result="success").inc()
            else:
                OPERATION_RESULT.labels(result="failure").inc()

            # Record operation processing time
            OPERATION_PROCESSING_TIME.observe(time.time() - start_time)

            structlog.contextvars.clear_contextvars()
        return True

    def run(self):
        """Run the job processing thread."""
        logging.info("Run")
        with self.subscriber:
            log.info("Listening for messages on %s", self.subscription_path)

            while True:
                message_count = 0
                try:
                    response = self.subscriber.pull(
                        request={
                            "subscription": self.subscription_path,
                            "max_messages": 1,
                        },
                        timeout=60,
                    )

                    message_count = len(response.received_messages)
                    if message_count == 0:
                        log.info("No more messages.")
                        continue

                    log.info("Received %d messages", message_count)

                    for message in response.received_messages:
                        operation_id = message.message.data.decode("utf-8")
                        logging.info("Received message: %s", operation_id)
                        r = self._handle_test_selection_operation(operation_id)
                        if r:
                            self.subscriber.acknowledge(
                                request={
                                    "subscription": self.subscription_path,
                                    "ack_ids": [message.ack_id],
                                }
                            )
                        else:
                            self.subscriber.modify_ack_deadline(
                                request={
                                    "subscription": self.subscription_path,
                                    "ack_ids": [message.ack_id],
                                    "ack_deadline_seconds": 0,
                                }
                            )
                            log.info("Failed to process message, will retry later")

                except Exception as ex:  # pylint: disable=broad-exception-caught
                    log.error("Error processing BigQuery pub/sub message: %s", ex)
                    log.exception(ex)
                    os._exit(1)

    def _get_impacted_targets(
        self,
        start_checkout_spec: checkout_pb2.CheckoutSpec | None,
        end_checkout_spec: checkout_pb2.CheckoutSpec,
        policy: test_selection.TestSelection,
        filter_query: str | None,
    ) -> list[target_info.TargetInfo]:
        """Returns the list of test targets impacted by changes between two git commits.

        Args:
            start_checkout_spec: The start checkout spec.
            end_checkout_spec: The end checkout spec.

        Returns:
            The list of test targets impacted by changes between two git commits.
        """
        logging.info(
            "Get impacted targets: start %s, end %s",
            start_checkout_spec,
            end_checkout_spec,
        )
        checkout_id = uuid.uuid4()

        if start_checkout_spec is None:
            logging.info("No start checkout spec, using PR base")
            assert end_checkout_spec.HasField("pull_request_checkout")
            github_client = self.github_client_fn()
            github_repo = github_client.get_repo(
                f"{end_checkout_spec.owner}/{end_checkout_spec.repo_name}"
            )
            pr = github_repo.get_pull(
                end_checkout_spec.pull_request_checkout.pull_request_number
            )  # type: ignore
            logging.info("Found PR base %s", pr.base)

            new_start_checkout_spec = checkout_pb2.CheckoutSpec()
            new_start_checkout_spec.owner = end_checkout_spec.owner
            new_start_checkout_spec.repo_name = end_checkout_spec.repo_name
            new_start_checkout_spec.commit_checkout.branch = pr.base.ref
            # we do not set the base.sha as the base doesn't reflect the current state
            start_checkout_spec = new_start_checkout_spec
        assert start_checkout_spec

        start_repo_dir, _ = self.checkout.checkout(
            start_checkout_spec, f"{checkout_id}-start"
        )
        repo = git.Repo(str(start_repo_dir))  # type: ignore

        logging.info("Generate hash for start: SHA %s", repo.head.commit.hexsha)
        start_hash_file = self.target_finder.generate_hash(
            start_checkout_spec.repo_name
        )
        end_repo_dir, _ = self.checkout.checkout(
            end_checkout_spec, f"{checkout_id}-end"
        )
        end_repo = git.Repo(str(end_repo_dir))  # type: ignore
        logging.info("Generate hash for end: SHA %s", end_repo.head.commit.hexsha)
        final_hash_file = self.target_finder.generate_hash(end_checkout_spec.repo_name)
        target_names = self.target_finder.get_impacted_targets(
            start_hash_file,
            final_hash_file,
        )

        for t in policy.extra_test_targets(target_names):
            if t not in target_names:
                logging.info("Adding extra target %s", t)
                target_names.append(t)

        if not filter_query:
            filter_query = target_info.TEST_ONLY_QUERY
        target_infos = target_info.get_target_info(
            end_repo_dir,
            target_names,
            extra_startup_args=self.extra_startup_args,
            filter_query=filter_query,
        )
        logging.info("Target infos %s", target_infos)

        return target_infos

    def _get_all_test_targets(
        self,
        end_checkout_spec: checkout_pb2.CheckoutSpec,  # type: ignore
        filter_query: str | None,
    ) -> list[target_info.TargetInfo]:
        checkout_id = uuid.uuid4()
        end_repo, _ = self.checkout.checkout(end_checkout_spec, f"{checkout_id}-end")
        target_names = self.target_finder.get_all_targets(end_checkout_spec.repo_name)
        if not filter_query:
            filter_query = target_info.TEST_ONLY_QUERY
        target_infos = target_info.get_target_info(
            end_repo,
            target_names,
            extra_startup_args=self.extra_startup_args,
            filter_query=filter_query,
        )
        logging.info("Target infos %s", target_infos)

        return target_infos


def _run(config):
    token_gen = app_token.GitHubAppTokenSource.from_directory(
        pathlib.Path(config.github_app_path)
    )

    persistence = Persistence.create(config)

    subscriber = pubsub_v1.SubscriberClient()
    subscription_path = subscriber.subscription_path(
        config.project_id, config.subscription_name
    )

    run_checkout = checkout.Checkout(
        base_wd=pathlib.Path(config.base_wd),
        github_user="app",
        token_source=token_gen,
        home_path=pathlib.Path.home(),
    )
    run_checkout.setup()
    run_checkout.clone(config.default_repo_owner, config.default_repo_name)
    # ensure that the old bazel server is removed at some point. Limit the amount of main memory to 4G.
    # --max_idle_secs=30 ensures that the old bazel server is removed at some point.
    extra_startup_args = (
        "--output_user_root=/cache/bazel-root --max_idle_secs=30 --host_jvm_args=-Xmx4g"
    )
    tf = target_finder.TargetFinder(
        workspace=run_checkout.base_wd, extra_startup_args=extra_startup_args
    )

    def github_client_fn():
        logging.info("Creating github client")
        return github.Github(token_gen.get_token())

    current_test_selection = TestSelection(
        config.test_selection_config,
        run_checkout,
        tf=tf,
        github_client_fn=github_client_fn,
        extra_startup_args=extra_startup_args,
        subscriber=subscriber,
        subscription_path=subscription_path,
        persistence=persistence,
    )
    current_test_selection.run()


def main():
    # tini will reap zombies for us. Bazel gets confused by zombie bazel processes.
    if os.getpid() == 1:
        os.execv(  # nosec (B606)
            "/usr/bin/tini-static",
            ["/usr/bin/tini-static", "-g", "--", sys.executable] + sys.argv[:],
        )

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--config-file", default=pathlib.Path("/config/config.json"), type=pathlib.Path
    )
    parser.add_argument(
        "--metrics-port",
        default=9090,
        type=int,
        help="Port for Prometheus metrics server",
    )
    args = parser.parse_args()

    setup_struct_logging()
    config = load_config(args.config_file)
    log.info("Config %s", config)

    # Start Prometheus metrics server
    prometheus_client.start_http_server(args.metrics_port)
    log.info("Started Prometheus metrics server on port %d", args.metrics_port)

    _run(config)


if __name__ == "__main__":
    main()
