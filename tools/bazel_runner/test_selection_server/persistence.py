"""Persistence for Test Selection based on GCP BigTable."""

import datetime
import logging
import uuid

from google.cloud import bigtable  # type: ignore

from tools.bazel_runner.test_selection_server import (
    test_selection_pb2,
    test_selection_store_pb2,
)
from tools.bazel_runner.test_selection_server.config import Config


class Persistence:
    """Persistence for Test Selection Server based on GCP BigTable.

    The information is stored in a single bigtable table, usually called "request-insight-main".

    Events:
    Key: "operation#{operation_id}"
    Column Family: Operation
    Column Value: The binary serialization of the GetTestTargetsRequest protobuf

    Args:
        client: BigTable client
        config: Configuration for the request insight server
    """

    def __init__(self, client: bigtable.Client, config: Config):
        self.client = client
        self.config = config
        self.instance = client.instance(self.config.instance_id)
        self.main_table = self.instance.table(self.config.table_name)

    def create_operation(
        self, operation_id: str, request: test_selection_pb2.GetTestTargetsRequest
    ):
        rows = []

        row_key = f"operation#{operation_id}"
        operation = test_selection_store_pb2.TestSelectionOperation()
        operation.operation_id = operation_id
        operation.request.CopyFrom(request)

        now = datetime.datetime.now(datetime.timezone.utc)
        operation.create_time.FromDatetime(now)

        row = self.main_table.direct_row(row_key=row_key)

        value = operation.SerializeToString()
        row.set_cell("Operation", "", value=value)
        rows.append(row)

        response = self.main_table.mutate_rows(rows)
        for status in response:
            if status.code != 0:  # type: ignore
                logging.error("Error writing row: %s", status.message)  # type: ignore
                raise ValueError("Error writing row")
        logging.info("Created operation %s", operation)

    def update_operation(
        self, operation: test_selection_store_pb2.TestSelectionOperation
    ):
        rows = []
        row_key = f"operation#{operation.operation_id}"
        row = self.main_table.direct_row(row_key=row_key)
        rows.append(row)

        now = datetime.datetime.now(datetime.timezone.utc)
        operation.update_time.FromDatetime(now)

        value = operation.SerializeToString()
        row.set_cell("Operation", "", value=value)
        response = self.main_table.mutate_rows(rows)
        logging.info("Response %s", response)
        for status in response:
            if status.code != 0:  # type: ignore
                logging.error("Error writing row: %s", status.message)  # type: ignore
                raise ValueError("Error writing row")
        logging.info("Updated operation %s", operation)

    def get_operation(
        self, operation_id: uuid.UUID
    ) -> test_selection_store_pb2.TestSelectionOperation | None:
        row_key = f"operation#{operation_id}"
        row = self.main_table.read_row(row_key)
        if not row:
            return None

        for event_type, cells in row.cells["Operation"].items():
            event_type = event_type.decode("utf-8")
            for cell in cells:
                info = test_selection_store_pb2.TestSelectionOperation()
                info.ParseFromString(cell.value)  # type: ignore
                return info
        return None

    @classmethod
    def create(cls, config: Config):
        client = bigtable.Client()
        return cls(client, config)
