syntax = "proto3";

import "tools/bazel_runner/git/checkout.proto";

// service for test selection.
service TestSelection {
  // Get all impacted bazel targets and information about them between two git commits.
  //
  // This is a central part of the test selection as usually one doesn't want to execute
  // all tests.
  rpc GetTestTargets(GetTestTargetsRequest) returns (GetTestTargetsResponse) {}
}

enum TestSelectionPolicy {
  // all change impacted tests
  DEFAULT = 0;

  // all tests that a local `bazel test //...` would execute.
  FULL = 1;

  // all tests that should run in pre-merge.
  PRE_MERGE = 2;

  // all tests that should run in post-merge.
  POST_MERGE = 3;
}

message GetTestTargetsRequest {
  // the start commit for the impact comparision
  //
  // if not specified, the base of the end_checkout pull request is used.
  // if not specified, and the end_checkout is not a pull request, all targets that match the policy are returned.
  CheckoutSpec start_checkout = 1;

  // the end commit for the impact comparision.
  CheckoutSpec end_checkout = 2;

  // the test selection policy to use to filter the tests
  TestSelectionPolicy policy = 3;

  // extra query to filter the tests with.
  // this is in bazel query syntax. with $1 being replaced by the set of all impacted targets.
  // e.g. `kind(".*_test", set($1)) union kind("test_suite", set($1))` for only returning tests.
  // if not set, all tests are returned.
  string extra_query = 4;
}

// information about a bazel target
message TargetInfo {
  // name of the bazel target
  string name = 1;

  // the kind of a bazel target.
  // by convention all test kinds end with `_test`.
  string kind = 2;

  // all tags of the bazel targes
  repeated string tags = 3;
}

// Returns the operation id
// The result itself is returned via
// standard longrunning operations API
// via the TestTargetList message
message GetTestTargetsResponse {
  // id of the operation to be used for the long running operations API
  //
  // The operation id will be lost if the server is restarted.
  // The client needs to handle these situations.
  string operation_id = 1;
}

// message returns by GetOperation
message TestTargetList {
  // list of all impacted bazel targets between two commits.
  repeated TargetInfo targets = 1;
}
