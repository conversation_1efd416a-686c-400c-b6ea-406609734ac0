"""Module to find information about targets."""

import logging
import pathlib
import shlex
import subprocess
import tempfile
from dataclasses import dataclass

from defusedxml.ElementTree import fromstring

from tools.bazel_lib import bazel


@dataclass
class TargetInfo:
    """Class storing information about bazel targets."""

    name: str
    kind: str
    tags: list[str]

    def is_test(self) -> bool:
        """Returns True if the target is a test target."""
        return self.kind.endswith("_test")


# query to filter out only tests
TEST_ONLY_QUERY = 'kind(".*_test",set($1)) union kind("test_suite", set($1))'


def get_target_info(
    workspace: pathlib.Path,
    targets: list[str],
    extra_startup_args: str | None,
    filter_query: str = TEST_ONLY_QUERY,
) -> list[TargetInfo]:
    """Returns extended information about targets."""
    if not targets:
        return []
    with tempfile.NamedTemporaryFile(mode="w+", encoding="utf-8") as f:
        t = " ".join(f'"{tc}"' for tc in targets)
        if filter_query:
            query = filter_query.replace("$1", t)
            print(query, file=f)
        else:
            print(f"set({t})", file=f)
        f.flush()
        cmd = [bazel.get_bazel_path()]
        if extra_startup_args:
            cmd.extend(shlex.split(extra_startup_args))
        cmd += ["query", "--output=xml", "--query_file", f.name]
        logging.info("Command %s", shlex.join([c for c in cmd]))
        result = subprocess.run(
            cmd,
            cwd=workspace,
            stdout=subprocess.PIPE,
            check=False,
            env=bazel.get_bazel_env(),
        )
        if result.returncode:
            logging.info("Query: %s", pathlib.Path(f.name).read_text(encoding="utf-8"))
            for line in result.stdout.decode("utf-8").splitlines():
                logging.info(line.strip())
            raise ValueError("Failed to get target info")
        return parse_target_info(result.stdout)


def parse_target_info(xml_output: bytes) -> list[TargetInfo]:
    """Parses the bazel query xml output for target information."""
    xml_node = fromstring(xml_output)

    results = []
    for rule in xml_node.findall(".//rule"):
        name = rule.attrib["name"]
        kind = rule.attrib["class"]
        tags = []
        for list_value in rule.findall(".//list"):
            if list_value.attrib["name"] == "tags":
                for s in list_value.findall(".//string"):
                    tag = s.attrib["value"]
                    tags.append(tag)
        # filter node modules out as these are defacto external dependencies
        if name.startswith("//:.aspect_rules_js/node_modules/"):
            continue
        results.append(TargetInfo(name, kind, tags))

    return results
