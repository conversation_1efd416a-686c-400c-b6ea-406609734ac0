// K8S deployment file for the test selection runner
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local githubAppSealedLib = import 'tools/deploy/github_app_sealed.jsonnet';
function(cloud, env, namespace, namespace_config)
  assert cloud == 'GCP_US_CENTRAL1_DEV';
  local appName = 'test-selection';
  local githubSecret = githubAppSealedLib(cloud=cloud, namespace=namespace, appName=appName);
  local serviceAccount = gcpLib.createServiceAccount(
    app=appName,
    env=env,
    namespace=namespace,
    cloud=cloud,
    iam=true
  );
  local bigtable = gcpLib.createBigtableTable(
    app=appName,
    cloud=cloud,
    env=env,
    namespace=namespace,
    tableName='test-selection-table',
    columnFamily=[
      {
        family: 'Operation',
      },
    ],
    iamServiceAccountName=serviceAccount.iamServiceAccountName
  );

  local gcpObjects = [
    {
      apiVersion: 'pubsub.cnrm.cloud.google.com/v1beta1',
      kind: 'PubSubTopic',
      metadata: {
        name: 'test-selection-%s-topic' % namespace,
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
      },
    },
    {
      apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
      kind: 'IAMPartialPolicy',
      metadata: {
        name: 'test-selection-topic-policy',
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        resourceRef: {
          kind: 'PubSubTopic',
          name: 'test-selection-%s-topic' % namespace,
        },
        bindings: [
          {
            role: 'roles/pubsub.publisher',
            members: [
              {
                memberFrom: {
                  serviceAccountRef: {
                    name: '%s-test-selection-iam' % namespace,
                  },
                },
              },
            ],
          },
        ],
      },
    },
    {
      apiVersion: 'pubsub.cnrm.cloud.google.com/v1beta1',
      kind: 'PubSubSubscription',
      metadata: {
        name: 'test-selection-%s-sub' % namespace,
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        ackDeadlineSeconds: 300,
        retryPolicy: {
          minimumBackoff: '5s',
          maximumBackoff: '300s',
        },
        topicRef: {
          name: 'test-selection-%s-topic' % namespace,
        },
      },
    },
    {
      apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
      kind: 'IAMPartialPolicy',
      metadata: {
        name: 'test-selection-sub-policy',
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        resourceRef: {
          kind: 'PubSubSubscription',
          name: 'test-selection-%s-sub' % namespace,
        },
        bindings: [
          {
            role: 'roles/pubsub.subscriber',
            members: [
              {
                memberFrom: {
                  serviceAccountRef: {
                    name: serviceAccount.iamServiceAccountName,
                  },
                },
              },
            ],
          },
        ],
      },
    },
  ];
  local service = {
    apiVersion: 'v1',
    kind: 'Service',
    metadata: {
      name: 'test-selection-svc',
      namespace: namespace,
      labels: {
        app: appName,
        'eng.augmentcode.com/service-type': 'grpc',
        'eng.augmentcode.com/grpc-insecure': 'true',
      },
    },
    spec: {
      selector: {
        app: appName,
      },
      ports: [
        {
          protocol: 'TCP',
          port: 50051,
          targetPort: 'grpc-svc',
        },
      ],
    },
  };
  local config =
    {
      apiVersion: 'v1',
      kind: 'ConfigMap',
      metadata: {
        name: 'test-selection-config',
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      data: {
        'config.json': std.manifestJson({
          base_wd: '/cache/',
          github_app_path: '/github-app',
          default_repo_owner: 'augmentcode',
          default_repo_name: 'augment',
          instance_id: bigtable.instanceName,
          table_name: bigtable.tableName,
          project_id: bigtable.projectId,
          topic_name: 'test-selection-%s-topic' % namespace,
          subscription_name: 'test-selection-%s-sub' % namespace,
          test_selection_config: {
            pre_merge_extra_target_names: [
              '//tools:format_test',
              '//tools/generate_proto_typestubs:validate_target_list_test',
            ],
            post_merge_extra_target_names: [
              '//tools:format_test',
              '//:go.trivy_test',
              '//:pnpm-lock.trivy_test',
              '//:Cargo.trivy_test',
              '//tools/python_deps:requirements.trivy_test',
              '//tools/generate_proto_typestubs:validate_target_list_test',
            ],
          },
        }),
      },
    };
  local serverContainer =
    {
      name: 'test-selection-server',
      target: {
        name: '//tools/bazel_runner/test_selection_server:server_image',
        dst: 'test-selection-server',
      },
      ports: [
        {
          containerPort: 50051,
          name: 'grpc-svc',
        },
      ],
      volumeMounts: [
        {
          name: 'config',
          mountPath: '/config',
          readOnly: true,
        },
      ],
      readinessProbe: grpcLib.grpcHealthCheck(null, tls=false) + {
        periodSeconds: 30,
      },
      livenessProbe: grpcLib.grpcHealthCheck(null, tls=false) + {
        periodSeconds: 30,
      },
      resources: {
        limits: {
          cpu: 0.1,
          memory: '1Gi',
        },
      },
    };
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);
  local serverPod = {
    serviceAccountName: 'test-selection-sa',
    containers: [
      serverContainer,
    ],
    priorityClassName: cloudInfo.envToPriorityClass(env),
    tolerations: tolerations,
    affinity: affinity,
    volumes: [
      {
        name: 'config',
        configMap: {
          name: 'test-selection-config',
          items: [
            {
              key: 'config.json',
              path: 'config.json',
            },
          ],
        },
      },
    ],
  };
  local serverDeployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    spec: {
      replicas: 1,
      minReadySeconds: if env == 'DEV' then 0 else 60,
      strategy: {
        type: 'RollingUpdate',
        rollingUpdate: {
          maxSurge: 1,
          maxUnavailable: 0,
        },
      },
      selector: {
        matchLabels: {
          app: appName,
        },
      },
      template: {
        metadata: {
          labels: {
            app: appName,
          },
        },
        spec: serverPod,
      },
    },
  };
  local processorContainer =
    {
      name: 'test-selection-server',
      target: {
        name: '//tools/bazel_runner/test_selection_server:processor_image',
        dst: 'test-selection-processor',
      },
      env: [
        {
          name: 'AU_GPU_COUNT',
          value: '0',
        },
      ],
      volumeMounts: [
        {
          name: 'config',
          mountPath: '/config',
          readOnly: true,
        },
        {
          name: 'cache-volume',
          mountPath: '/cache',
        },
        {
          mountPath: '/github-app',
          name: 'github-app-secret',
        },
      ],
      resources: {
        limits: {
          cpu: 4,
          memory: '8Gi',
        },
      },
    };

  local processorPod = {
    serviceAccountName: serviceAccount.name,
    securityContext: {
      // bazel cannot be run as root
      runAsUser: 1000,
      fsGroup: 1000,
      fsGroupChangePolicy: 'OnRootMismatch',
    },
    priorityClassName: cloudInfo.envToPriorityClass(env),
    tolerations: tolerations,
    affinity: affinity,
    containers: [
      processorContainer,
    ],
    volumes: [
      {
        name: 'cache-volume',
        emptyDir: {
          sizeLimit: '32Gi',
        },
      },
      // add extra large shared memory
      {
        name: 'dshm',
        emptyDir: {
          medium: 'Memory',
          sizeLimit: '4Gi',
        },
      },
      {
        name: 'github-app-secret',
        secret: {
          secretName: githubSecret.metadata.name,  // pragma: allowlist secret
          optional: false,
        },
      },
      {
        name: 'config',
        configMap: {
          name: 'test-selection-config',
          items: [
            {
              key: 'config.json',
              path: 'config.json',
            },
          ],
        },
      },
    ],
  };
  local processorDeployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: 'test-selection-processor',
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    spec: {
      replicas: if env == 'DEV' then 1 else 2,
      strategy: {
        type: 'RollingUpdate',
        rollingUpdate: {
          maxSurge: 0,
          maxUnavailable: 1,
        },
      },
      selector: {
        matchLabels: {
          app: appName,
        },
      },
      template: {
        metadata: {
          labels: {
            app: appName,
          },
        },
        spec: processorPod,
      },
    },
  };
  lib.flatten([
    githubSecret,
    config,
    service,
    serverDeployment,
    processorDeployment,
    serviceAccount.objects,
    bigtable.objects,
    gcpObjects,
  ])
