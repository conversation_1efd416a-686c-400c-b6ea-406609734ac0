"""Client to access the test selection server."""

import logging
import pathlib

import certifi
import grpc
from google.longrunning import (
    operations_pb2,  # type: ignore
    operations_pb2_grpc,
)

from base.python.grpc import client_options
import tools.bazel_runner.test_selection_server.test_selection_pb2 as test_selection_pb2
import tools.bazel_runner.test_selection_server.test_selection_pb2_grpc as test_selection_pb2_grpc
from tools.bazel_runner.git import checkout_pb2


def setup_clients(
    endpoint: str, insecure: bool = False
) -> tuple[
    test_selection_pb2_grpc.TestSelectionStub, operations_pb2_grpc.OperationsStub
]:
    """Setup the client stub for the test selection RPC."""
    if not insecure:
        creds = grpc.ssl_channel_credentials(pathlib.Path(certifi.where()).read_bytes())
        channel = grpc.secure_channel(endpoint, creds, options=client_options.create())
    else:
        channel = grpc.insecure_channel(endpoint, options=client_options.create())
    stub = test_selection_pb2_grpc.TestSelectionStub(channel)
    long_running_stub = operations_pb2_grpc.OperationsStub(channel)
    return (stub, long_running_stub)


class TestSelectionException(grpc.RpcError):
    """Exception thrown during test selection.

    Note that subclasses of grpc.RpcError are expected to have code() and details() methods even
    though RpcError itself does not."""

    def __init__(self, status_code: grpc.StatusCode, msg: str = ""):
        self.status_code = status_code
        self.message = msg

    def code(self) -> grpc.StatusCode:
        return self.status_code

    def details(self) -> str:
        return self.message

    def __str__(self) -> str:
        return f"TestSelectionException({self.status_code}: {self.message})"


class TestSelectionClient:
    """Client to start test selection and wait for the results."""

    def __init__(self, endpoint: str, insecure: bool = False):
        (self.rpc_client, self.long_running_client) = setup_clients(endpoint, insecure)

    def get_test_targets(
        self,
        start_commit: checkout_pb2.CheckoutSpec | None,
        end_commit: checkout_pb2.CheckoutSpec,
        policy: test_selection_pb2.TestSelectionPolicy.ValueType,
        extra_query: str | None = None,
    ) -> str:
        """Schedules a get impacted targets operation and returns the operation id."""
        request = test_selection_pb2.GetTestTargetsRequest()
        if start_commit:
            request.start_checkout.CopyFrom(start_commit)
        request.end_checkout.CopyFrom(end_commit)
        request.policy = policy
        if extra_query:
            request.extra_query = extra_query
        response = self.rpc_client.GetTestTargets(request)
        return response.operation_id

    def get_results(
        self, operation_id: str
    ) -> test_selection_pb2.TestTargetList | None:
        request = operations_pb2.GetOperationRequest()  # type: ignore
        request.name = operation_id  # type: ignore
        response = self.long_running_client.GetOperation(request)
        if response.done:
            if response.error and response.error.code != 0:
                logging.debug(
                    "Operation failed: code%s, message=%s",
                    response.error.code,
                    response.error.message,
                )
                code = [
                    c for c in grpc.StatusCode if c.value[0] == response.error.code
                ][0]
                raise TestSelectionException(code, response.error.message)
            msg = test_selection_pb2.TestTargetList()
            response.response.Unpack(msg)
            return msg
        return None
