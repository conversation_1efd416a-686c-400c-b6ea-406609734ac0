// K8S deployment file for the bazel runner scheduler
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';

function(cloud, env, namespace, namespace_config)
  local appName = 'bazel-runner-scheduler';
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=null);
  local domainSuffix = cloudInfo[cloud].internalDomainSuffix;
  local testViewerUrl = if env == 'DEV' then 'https://test-viewer.%s.%s' % [namespace, domainSuffix] else 'https://test-viewer.%s' % domainSuffix;

  // Create a ConfigMap for the scheduler configuration
  local configMap = {
    apiVersion: 'v1',
    kind: 'ConfigMap',
    metadata: {
      name: appName + '-config',
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'reloader.stakater.com/match': 'true',
      },
    },
    data: {
      'config.json': std.manifestJson({
        bazel_runner_endpoint: 'test-runner-svc.devtools:50051',
        bot_endpoint: 'bot:50051',
        test_viewer_url: testViewerUrl,
        insecure: true,
        default_timeout_minutes: 120,
        jobs: [
          {
            schedule_name: 'hourly_tools_tests',
            requestor: 'scheduler',
            timeout_minutes: 120,
            cron_schedule: '* * * *',  // Run every hour
            notify_slack: false,
            execution_group_spec: std.manifestJson({
              checkout: {
                owner: 'augmentcode',
                repo_name: 'augment',
                commit_checkout: {
                  branch: 'main',
                },
              },
              runs: [
                {
                  targets: [
                    '//tools/...',
                  ],
                },
              ],
            }),
          },
        ],
      }),
    },
  };

  local container = {
    name: appName,
    target: {
      name: '//tools/bazel_runner/scheduler:image',
      dst: 'bazel_runner_scheduler',
    },
    args: [
      '--config=/config/config.json',
    ],
    volumeMounts: [
      {
        name: 'config-volume',
        mountPath: '/config',
        readOnly: true,
      },
    ],
    resources: {
      requests: {
        memory: '256Mi',
        cpu: '100m',
      },
      limits: {
        memory: '512Mi',
        cpu: '500m',
      },
    },
  };

  local pod = {
    restartPolicy: 'OnFailure',
    containers: [
      container,
    ],
    volumes: [
      {
        name: 'config-volume',
        configMap: {
          name: appName + '-config',
        },
      },
    ],
    tolerations: tolerations,
    affinity: affinity,
  };

  local cronjob = {
    apiVersion: 'batch/v1',
    kind: 'CronJob',
    metadata: {
      name: appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'reloader.stakater.com/search': 'true',
      },
    },
    spec: {
      // Suspend by default in dev environments
      suspend: env != 'PROD',
      schedule: '*/10 * * * *',  // Run every 10 minutes
      // Don't start jobs that were skipped when this job was suspended
      startingDeadlineSeconds: 120,
      successfulJobsHistoryLimit: 16,
      failedJobsHistoryLimit: 4,
      concurrencyPolicy: 'Forbid',
      timeZone: 'America/Los_Angeles',
      jobTemplate: {
        metadata: {
          labels: {
            app: appName,
          },
        },
        spec: {
          // Disable backoff
          backoffLimit: 0,
          template: {
            metadata: {
              labels: {
                app: appName,
              },
            },
            spec: pod,
          },
        },
      },
    },
  };

  lib.flatten([
    configMap,
    cronjob,
  ])
