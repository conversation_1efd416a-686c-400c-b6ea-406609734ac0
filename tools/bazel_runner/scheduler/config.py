import json
from dataclasses_json import dataclass_json
from dataclasses import dataclass


@dataclass_json
@dataclass
class JobConfig:
    """Configuration for a scheduled job."""

    # name of the schedule. used as tag for the test run.
    schedule_name: str

    # serialized BazelRunnerExecutionGroupSpec in json format
    execution_group_spec: str

    # requestor for the test run
    requestor: str

    # additional tags for the test run
    tags: list[str] | None = None

    # timeout for the test run in minutes
    timeout_minutes: int = 60

    # cron-like schedule for the job (hour day_of_month month day_of_week)
    # Default is "* * * *" which runs every hour
    cron_schedule: str = "* * * *"

    # timezone for the job schedule (IANA timezone name)
    # Default is "America/Los_Angeles"
    timezone: str = "America/Los_Angeles"

    # whether to notify about the test run via slack
    notify_slack: bool = True


@dataclass_json
@dataclass
class Config:
    """Configuration for the scheduler."""

    # endpoints and other config for the scheduler
    bazel_runner_endpoint: str

    # endpoint for the bot
    bot_endpoint: str

    # url for the test viewer
    test_viewer_url: str

    # whether to use insecure connections
    insecure: bool

    # default timeout for the test run in minutes
    jobs: list[JobConfig]

    # default timeout for the test run in minutes
    default_timeout_minutes: int = 60

    # poll interval in seconds for checking the test run status
    poll_interval_seconds: int = 30


def load_config(config_path: str) -> Config:
    with open(config_path, "r") as f:
        config = json.load(f)
    return Config.from_dict(config)  # type: ignore
