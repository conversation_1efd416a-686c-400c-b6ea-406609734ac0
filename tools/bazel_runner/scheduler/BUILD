load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:python.bzl", "py_binary", "py_library", "py_oci_image")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

py_library(
    name = "scheduler_lib",
    srcs = [
        "config.py",
        "scheduler.py",
    ],
    deps = [
        "//base/logging:struct_logging",
        "//tools/bazel_runner/client",
        "//tools/bazel_runner/control:bazel_runner_py_proto",
        "//tools/bazel_runner/server:test_runner_py_proto",
        "//tools/bot:bot_py_proto",
        requirement("dataclasses_json"),
        requirement("pytz"),
    ],
)

py_binary(
    name = "scheduler",
    srcs = ["main.py"],
    main = "main.py",
    deps = [
        ":scheduler_lib",
        "//base/logging:struct_logging",
    ],
)

py_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":scheduler",
    tars = ["//tools/docker:grpc_health_probe_tar"],
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        ":image",
    ],
    deps = [
        "//deploy/common:cloud_info",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
    ],
)

pytest_test(
    name = "scheduler_test",
    srcs = ["scheduler_test.py"],
    deps = [
        ":scheduler_lib",
        "//third_party/proto/bazel_build:build_event_stream_py_proto",
    ],
)
