"""Scheduler for running tests on a regular basis and reporting results to Slack."""

import datetime
import logging
import time
import uuid
import pytz
from datetime import timezone

import grpc

from tools.bazel_runner.client.bazel_runner_client import (
    BazelRunnerClient,
    ExecutionResult,
)
from tools.bazel_runner.control import bazel_runner_pb2
from tools.bazel_runner.server import test_runner_pb2
from tools.bazel_runner.scheduler.config import Config, JobConfig
from tools.bot import bot_pb2, bot_pb2_grpc
from third_party.proto.bazel_build import build_event_stream_pb2
import google.protobuf.json_format as json_format


def get_timezone(tz_name: str) -> datetime.tzinfo:
    """Get the timezone object for the given timezone name.

    Args:
        tz_name: The timezone name (IANA timezone database name).

    Returns:
        The timezone object for the given timezone name.
        If the timezone name is invalid, returns the Los Angeles timezone.
    """

    # Default to Los Angeles timezone
    default_tz = "America/Los_Angeles"

    if not tz_name:
        return pytz.timezone(default_tz)

    try:
        return pytz.timezone(tz_name)
    except pytz.exceptions.UnknownTimeZoneError:
        logging.warning(f"Unknown timezone: {tz_name}, using {default_tz} instead")
        return pytz.timezone(default_tz)


def should_run_job(cron_schedule: str, current_time: datetime.datetime) -> bool:
    """Check if a job should run based on its cron schedule.

    Args:
        cron_schedule: The cron schedule string (hour day_of_month month day_of_week).
        current_time: The current time to check against (defaults to now).
        tz: The timezone to use for the check (defaults to Los Angeles).

    Returns:
        True if the job should run, False otherwise.
    """

    # Parse the cron schedule (hour day_of_month month day_of_week)
    hour, day_of_month, month, day_of_week = cron_schedule.split()

    # Check each field
    def matches(value, pattern):
        if pattern == "*":
            return True

        # Handle comma-separated values
        if "," in pattern:
            return any(matches(value, p) for p in pattern.split(","))

        # Handle ranges (e.g., 1-5)
        if "-" in pattern:
            start, end = map(int, pattern.split("-"))
            return start <= int(value) <= end

        # Handle step values (e.g., */5)
        if "/" in pattern:
            wildcard, step = pattern.split("/")
            if not wildcard == "*":
                raise ValueError(f"Invalid cron schedule: {cron_schedule}")
            return int(value) % int(step) == 0

        # Simple value match
        return int(value) == int(pattern)

    # Check if the current time matches the cron schedule
    # We ignore the minute field since we only run hourly
    return (
        matches(current_time.hour, hour)
        and matches(current_time.day, day_of_month)
        and matches(current_time.month, month)
        and matches(current_time.weekday(), day_of_week)
    )


class ScheduledTestRunner:
    """Class to run scheduled tests and report results to Slack."""

    def __init__(
        self,
        config: Config,
    ):
        """Initialize the scheduled test runner.

        Args:
            config: The configuration for the scheduler.
        """
        self.config = config

        # Initialize clients
        bazel_runner_endpoint = self.config.bazel_runner_endpoint
        bot_endpoint = self.config.bot_endpoint
        insecure = self.config.insecure

        self.bazel_runner_client = BazelRunnerClient(bazel_runner_endpoint, insecure)
        self.bot_client = self._setup_bot_client(bot_endpoint, insecure)

    def _setup_bot_client(
        self, endpoint: str, insecure: bool
    ) -> bot_pb2_grpc.DevtoolsBotStub:
        """Set up the bot client.

        Args:
            endpoint: The endpoint of the bot service.
            insecure: Whether to use insecure connections.

        Returns:
            The bot client.
        """
        if insecure:
            channel = grpc.insecure_channel(endpoint)
        else:
            channel = grpc.secure_channel(endpoint, grpc.ssl_channel_credentials())
        return bot_pb2_grpc.DevtoolsBotStub(channel)

    def schedule_test(
        self,
        job_config: JobConfig,
        timeout_minutes: int | None = None,
    ) -> tuple[uuid.UUID, str, datetime.timedelta]:
        """Schedule a test without waiting for it to complete.

        Args:
            job_config: The configuration for the job.
            timeout_minutes: The timeout in minutes (overrides config).

        Returns:
            A tuple of (run_id, schedule_name, timeout) for the scheduled test.
        """
        schedule_name = job_config.schedule_name

        # Use timeout from config if not provided
        if timeout_minutes is None:
            timeout_mins = (
                job_config.timeout_minutes or self.config.default_timeout_minutes
            )
        else:
            timeout_mins = timeout_minutes

        # Create the test execution config
        te = bazel_runner_pb2.BazelRunnerExecutionGroupSpec()
        json_format.Parse(job_config.execution_group_spec, te)
        for run in te.runs:
            run.command = run.command or "test"

        tags = job_config.tags or []
        tags.append("scheduled")
        requestor = job_config.requestor

        # Schedule the test
        logging.info(f"Scheduling test for schedule {schedule_name} with {te}")
        run_id = self.bazel_runner_client.schedule_run(
            te, requestor=requestor, tags=tags
        )
        logging.info(f"Scheduled test with run_id {run_id}")

        timeout = datetime.timedelta(minutes=timeout_mins)
        return run_id, schedule_name, timeout

    def run_scheduled_tests(
        self,
        job_configs: list[JobConfig],
        timeout_minutes: int | None,
        current_time: datetime.datetime,
    ) -> None:
        """Run multiple scheduled tests in parallel and report their results to Slack.

        Args:
            job_configs: The configurations for the jobs.
            timeout_minutes: The timeout in minutes (overrides config).
            current_time: The current time to check against cron schedules (defaults to now).
        """

        # Filter jobs based on their cron schedule
        filtered_job_configs = []
        for job_config in job_configs:
            tz = get_timezone(job_config.timezone)
            job_current_time = current_time.astimezone(tz)
            if should_run_job(job_config.cron_schedule, job_current_time):
                filtered_job_configs.append(job_config)
                logging.info(
                    f"Job {job_config.schedule_name} matches cron schedule {job_config.cron_schedule} at {current_time} in timezone {job_config.timezone}"
                )
            else:
                logging.info(
                    f"Job {job_config.schedule_name} does not match cron schedule {job_config.cron_schedule} at {current_time} in timezone {job_config.timezone}"
                )

        if not filtered_job_configs:
            logging.info("No jobs match the current time based on their cron schedules")
            return

        # Schedule all tests first
        scheduled_tests: list[tuple[uuid.UUID, str, datetime.datetime, JobConfig]] = []
        for job_config in filtered_job_configs:
            try:
                scheduled_test = self.schedule_test(job_config, timeout_minutes)
                ttl = datetime.datetime.now(timezone.utc) + scheduled_test[2]
                scheduled_tests.append(
                    (scheduled_test[0], scheduled_test[1], ttl, job_config)
                )
                logging.info(f"Scheduled test for {job_config.schedule_name}")
            except Exception as e:
                logging.error(f"Failed to schedule job {job_config.schedule_name}: {e}")
                # Continue with the next job even if one fails

        # Wait for all tests to complete and report results
        while scheduled_tests:
            for scheduled_test in scheduled_tests[:]:
                run_id, schedule_name, timeout, job_config = scheduled_test
                result = self.bazel_runner_client.get_run_state(run_id)
                logging.info(f"Result for {run_id}: {result}")
                if result.is_done():
                    logging.info(
                        f"Test {schedule_name} completed with result: {result}"
                    )
                    self._report_results_to_slack(
                        run_id, schedule_name, result, job_config
                    )
                    scheduled_tests.remove(scheduled_test)
                else:
                    # check timeout
                    if datetime.datetime.now(timezone.utc) > timeout:
                        logging.info(f"Test {schedule_name} timed out")
                        self._report_results_to_slack(
                            run_id, schedule_name, result, job_config
                        )
                        scheduled_tests.remove(scheduled_test)
            time.sleep(self.config.poll_interval_seconds)

    def _report_results_to_slack(
        self,
        run_id: uuid.UUID,
        schedule_name: str,
        result: ExecutionResult,
        job: JobConfig,
    ) -> None:
        """Report the test results to Slack.

        Args:
            run_id: The ID of the test run.
            schedule_name: The name of the scheduled test.
            result: The test result.
        """
        if not job.notify_slack:
            return

        logging.info(f"Reporting results for run {run_id} to Slack")

        # Determine the status
        if result.state == test_runner_pb2.RUN_STATE_PASSED:
            status = "Passed"
        elif result.state == test_runner_pb2.RUN_STATE_FAILURE:
            status = "Failed"
        elif result.state == test_runner_pb2.RUN_STATE_CANCEL:
            status = "Cancelled"
        else:
            status = "Unknown"

        # Get the non-success test targets
        non_success_targets = []
        flaky_targets = []

        # Extract failed and flaky targets from the result
        for job_info in result.jobs:
            for test_info in job_info.tests:
                if test_info.status == build_event_stream_pb2.TestStatus.FLAKY:
                    flaky_targets.append(test_info.target_name)
                elif test_info.status != build_event_stream_pb2.TestStatus.PASSED:
                    non_success_targets.append(test_info.target_name)

        # Create the request
        request = bot_pb2.NotifyScheduledTestResultRequest()
        request.run_id = str(run_id)
        request.schedule_name = schedule_name
        request.status = status
        request.details_url = f"{self.config.test_viewer_url}/run/{run_id}"
        request.non_success_test_targets.extend(non_success_targets)
        request.flaky_test_targets.extend(flaky_targets)
        request.schedule_date = datetime.datetime.now().strftime("%Y-%m-%d")

        # Send the notification
        try:
            self.bot_client.NotifyScheduledTestResult(request)
            logging.info("Successfully sent notification to Slack")
        except Exception as e:
            logging.error(f"Failed to send notification to Slack: {e}")
            raise
