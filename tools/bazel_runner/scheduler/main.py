"""Daily scheduler for running tests and reporting results to Slack."""

import argparse
import datetime
import logging
import sys

from tools.bazel_runner.scheduler.config import load_config
from tools.bazel_runner.scheduler.scheduler import ScheduledTestRunner
from base.logging.struct_logging import setup_struct_logging


def main():
    """Main entry point for the daily scheduler."""
    parser = argparse.ArgumentParser(description="Run daily scheduled tests")
    parser.add_argument(
        "--config",
        default="/config/config.json",
        help="Path to the configuration file",
    )
    parser.add_argument(
        "--timeout-minutes",
        type=int,
        help="Override the timeout in minutes",
    )

    args = parser.parse_args()
    setup_struct_logging()

    logging.info(f"Starting daily scheduler with config: {args.config}")

    try:
        config = load_config(args.config)
        logging.info(f"Config: {config}")

        # Initialize the runner with the config file
        runner = ScheduledTestRunner(config)

        # Load the job configurations from the config file
        job_configs = runner.config.jobs

        # Run all jobs in parallel
        logging.info(f"Running {len(job_configs)} scheduled jobs in parallel")
        # Use UTC time as the base, timezone conversion will be done per job
        current_time = datetime.datetime.now(datetime.timezone.utc)
        runner.run_scheduled_tests(job_configs, args.timeout_minutes, current_time)
    except Exception as e:
        logging.error(f"Failed to run daily tests: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
