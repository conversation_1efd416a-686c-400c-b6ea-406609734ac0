"""Tests for the scheduler module."""

import datetime
import unittest
from unittest import mock

from third_party.proto.bazel_build import build_event_stream_pb2
from tools.bazel_runner.client.bazel_runner_client import ExecutionResult
from tools.bazel_runner.scheduler.config import Config, JobConfig
from tools.bazel_runner.scheduler.scheduler import ScheduledTestRunner
from tools.bazel_runner.server import test_runner_pb2


class ScheduledTestRunnerTest(unittest.TestCase):
    """Tests for the ScheduledTestRunner class."""

    def setUp(self):
        """Set up the test."""
        self.config = Config(
            bazel_runner_endpoint="localhost:50051",
            bot_endpoint="localhost:50052",
            test_viewer_url="http://localhost:8080",
            insecure=True,
            default_timeout_minutes=60,
            jobs=[],
        )
        self.scheduler = ScheduledTestRunner(self.config)
        # Mock the clients
        self.scheduler.bazel_runner_client = mock.MagicMock()
        self.scheduler.bot_client = mock.MagicMock()

    def test_report_results_to_slack_with_failed_and_flaky_tests(self):
        """Test reporting results to <PERSON>lack with failed and flaky tests."""
        # Create a mock result with failed and flaky tests
        job_info1 = test_runner_pb2.JobInfo(
            job_id="test-job-id-1",
            return_code=0,
            tests=[
                test_runner_pb2.TestInfo(
                    target_name="//path/to/test1",
                    status=build_event_stream_pb2.TestStatus.FAILED,
                ),
                test_runner_pb2.TestInfo(
                    target_name="//path/to/test2",
                    status=build_event_stream_pb2.TestStatus.FLAKY,
                ),
                test_runner_pb2.TestInfo(
                    target_name="//path/to/test3",
                    status=build_event_stream_pb2.TestStatus.PASSED,
                ),
            ],
        )
        job_info2 = test_runner_pb2.JobInfo(
            job_id="test-job-id-2",
            return_code=0,
            tests=[
                test_runner_pb2.TestInfo(
                    target_name="//path/to/test4",
                    status=build_event_stream_pb2.TestStatus.TIMEOUT,
                ),
                test_runner_pb2.TestInfo(
                    target_name="//path/to/test5",
                    status=build_event_stream_pb2.TestStatus.PASSED,
                ),
            ],
        )

        # Create a mock result
        mock_result = mock.MagicMock(spec=ExecutionResult)
        mock_result.state = test_runner_pb2.RUN_STATE_FAILURE
        mock_result.jobs = [job_info1, job_info2]

        # Call the method
        self.scheduler._report_results_to_slack(
            run_id="test-run-id",
            schedule_name="test-schedule",
            result=mock_result,
            job=JobConfig(
                schedule_name="test-schedule",
                execution_group_spec='{"runs": [{"targets": ["//path/to/target"]}]}',
                requestor="requestor",
                tags=["tag1"],
                timeout_minutes=60,
                cron_schedule="* * * *",
                timezone="America/Los_Angeles",
            ),
        )

        # Verify the bot client was called with the correct arguments
        self.scheduler.bot_client.NotifyScheduledTestResult.assert_called_once()
        request = self.scheduler.bot_client.NotifyScheduledTestResult.call_args[0][0]

        # Check that the non-success and flaky targets are correct
        self.assertEqual(
            set(request.non_success_test_targets),
            {"//path/to/test1", "//path/to/test4"},
        )
        self.assertEqual(set(request.flaky_test_targets), {"//path/to/test2"})

    def test_run_scheduled_tests_parallel(self):
        """Test running multiple scheduled tests in parallel."""
        # Create mock job configs
        job_config1 = JobConfig(
            schedule_name="test-schedule-1",
            execution_group_spec='{"runs": [{"targets": ["//path/to/target1"]}]}',
            requestor="requestor",
            tags=["tag1"],
            timeout_minutes=60,
            cron_schedule="* * * *",
            timezone="America/Los_Angeles",
        )
        job_config2 = JobConfig(
            schedule_name="test-schedule-2",
            execution_group_spec='{"runs": [{"targets": ["//path/to/target2"]}]}',
            requestor="requestor",
            tags=["tag2"],
            timeout_minutes=60,
            cron_schedule="* * * *",
            timezone="America/Los_Angeles",
        )

        # Mock the schedule_test method to return test IDs
        self.scheduler.schedule_test = mock.MagicMock()
        self.scheduler.schedule_test.side_effect = [
            ("test-run-id-1", "test-schedule-1", datetime.timedelta(minutes=60)),
            ("test-run-id-2", "test-schedule-2", datetime.timedelta(minutes=60)),
        ]

        # Mock the get_run_state method
        mock_result1 = mock.MagicMock(spec=ExecutionResult)
        mock_result1.state = test_runner_pb2.RUN_STATE_PASSED
        mock_result1.jobs = []
        mock_result1.is_done = mock.MagicMock(return_value=True)

        mock_result2 = mock.MagicMock(spec=ExecutionResult)
        mock_result2.state = test_runner_pb2.RUN_STATE_FAILURE
        mock_result2.jobs = []
        mock_result2.is_done = mock.MagicMock(return_value=True)

        self.scheduler.bazel_runner_client.get_run_state = mock.MagicMock()
        self.scheduler.bazel_runner_client.get_run_state.side_effect = [
            mock_result1,
            mock_result2,
        ]

        # Mock the _report_results_to_slack method
        self.scheduler._report_results_to_slack = mock.MagicMock()

        # Call the method
        self.scheduler.run_scheduled_tests(
            [job_config1, job_config2],
            timeout_minutes=60,
            current_time=datetime.datetime.now(datetime.timezone.utc),
        )

        # Verify that schedule_test was called for both job configs
        self.assertEqual(self.scheduler.schedule_test.call_count, 2)
        # Use mock.ANY to allow any object for the parameters
        self.scheduler.schedule_test.assert_any_call(mock.ANY, mock.ANY)
        self.scheduler.schedule_test.assert_any_call(mock.ANY, mock.ANY)

        # Verify that get_run_state was called for both run IDs
        self.assertEqual(self.scheduler.bazel_runner_client.get_run_state.call_count, 2)
        self.scheduler.bazel_runner_client.get_run_state.assert_any_call(
            "test-run-id-1"
        )
        self.scheduler.bazel_runner_client.get_run_state.assert_any_call(
            "test-run-id-2"
        )

        # Verify that _report_results_to_slack was called for both results
        self.assertEqual(self.scheduler._report_results_to_slack.call_count, 2)
        self.scheduler._report_results_to_slack.assert_any_call(
            "test-run-id-1", "test-schedule-1", mock_result1, mock.ANY
        )
        self.scheduler._report_results_to_slack.assert_any_call(
            "test-run-id-2", "test-schedule-2", mock_result2, mock.ANY
        )


if __name__ == "__main__":
    unittest.main()
