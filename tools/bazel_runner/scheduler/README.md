# Bazel Runner Scheduler

This module provides a scheduler for running tests on a regular basis and reporting results to <PERSON><PERSON>ck.

## Overview

The scheduler is designed to run tests on a scheduled basis (e.g., daily) and report the results to <PERSON><PERSON>ck via the DevtoolsBot service. It uses the Bazel Runner service to schedule and run the tests.

## Components

- `scheduler.py`: Core implementation of the scheduled test runner
- `daily_scheduler.py`: Script for running daily scheduled tests
- `deploy.jsonnet`: Kubernetes deployment configuration for the scheduler cronjob
- `config.json`: Sample configuration file for the scheduler

## Usage

### Running a single scheduled test

```bash
bazel run //tools/bazel_runner/scheduler:scheduler -- \
  --config=tools/bazel_runner/scheduler/config.json \
  --job-name="daily_core_tests"
```

### Running all scheduled tests

```bash
bazel run //tools/bazel_runner/scheduler:daily_scheduler -- \
  --config=tools/bazel_runner/scheduler/config.json
```

### Deploying the CronJob

The scheduler is deployed as a Kubernetes CronJob using the standard deployment process:

```bash
bazel run //tools/deploy_runner/client:deploy_client -- \
  --target=//tools/bazel_runner/scheduler:kubecfg \
  --cloud=GCP_US_CENTRAL1_PROD \
  --env=PROD \
  --namespace=devtools
```

## Configuration

The scheduler is configured using a JSON configuration file. The configuration file includes:

- Connection settings for the Bazel Runner and Bot services
- Default timeout settings
- Job configurations

Example configuration:

```json
{
  "bazel_runner_endpoint": "bazel-runner.devtools:50051",
  "bot_endpoint": "bot.devtools:50051",
  "test_viewer_url": "https://test-viewer.augmentcode.com",
  "insecure": false,
  "default_timeout_minutes": 120,
  "jobs": [
    {
      "job_name": "daily_core_tests",
      "targets": [
        "//core/..."
      ],
      "command": "test",
      "owner": "augmentcode",
      "repo_name": "augment",
      "branch": "main",
      "requestor": "scheduler",
      "tags": ["daily", "core"],
      "timeout_minutes": 120
    },
    {
      "job_name": "daily_tools_tests",
      "targets": [
        "//tools/..."
      ],
      "command": "test",
      "owner": "augmentcode",
      "repo_name": "augment",
      "branch": "main",
      "requestor": "scheduler",
      "tags": ["daily", "tools"],
      "timeout_minutes": 120
    }
  ]
}
```

In the Kubernetes deployment, this configuration is stored in a ConfigMap and mounted into the container.

## Slack Notifications

The scheduler sends notifications to Slack when tests are completed. The notifications include:

- Test job name
- Test status (Passed, Failed, Cancelled)
- Link to test details
- List of failed and flaky tests (if any)
- Scheduled date

The notifications are sent via the DevtoolsBot service using the `NotifyScheduledTestResult` RPC.
