"""Build Event Service Server.

Any CI bazel build will report live-events about the build to the BES server.
The server will transform the results and store them in a key/value store.
"""

import argparse
import logging
import pathlib
import threading
import uuid
from concurrent.futures import Thread<PERSON>oolExecutor
from typing import Optional

import google.protobuf.empty_pb2 as empty_pb2
import grpc
import structlog
from grpc_health.v1 import health, health_pb2_grpc
from grpc_reflection.v1alpha import reflection
from prometheus_client import Counter, start_http_server

import third_party.proto.bazel_build.build_event_stream_pb2 as build_event_stream_pb2
import third_party.proto.bazel_build.publish_build_event_pb2 as publish_build_event_pb2
import third_party.proto.bazel_build.publish_build_event_pb2_grpc as publish_build_event_pb2_grpc
from base.logging.struct_logging import setup_struct_logging
from base.python.cloud import gcp
from base.python.signal_handler.signal_handler import <PERSON><PERSON><PERSON>ignalH<PERSON><PERSON>
from tools.bazel_runner.bep_parser.bep_parser import get_test_summary, test_summary_pb2
from tools.bazel_runner.server import (
    bazel_runner_server_gcp_lib,
    bazel_runner_server_lib,
)
from tools.bazel_runner.server.config import Config

log = structlog.get_logger()

_bes_events_created = Counter(
    "bazel_runner_bes_events_created",
    "Gauge for the number of bes events recorded",
)
_bes_test_summary_created = Counter(
    "bazel_runner_bes_test_summary_created",
    "Gauge for the number of bes test summary recorded",
    ["target_name", "state", "overall_status"],
)


class PublishBuildEventServices(publish_build_event_pb2_grpc.PublishBuildEventServicer):
    """Implementation of the Build Event Service protocol."""

    def __init__(
        self,
        config: Config,
        persistence: bazel_runner_server_lib.BuildEventPersistence,
    ):
        self.config = config
        self.persistence = persistence

    def PublishLifecycleEvent(
        self, request: publish_build_event_pb2.PublishLifecycleEventRequest, context
    ):  # pylint: disable=unused-argument
        return empty_pb2.Empty()  # type: ignore

    def PublishBuildToolEventStream(self, request_iterator, context):  # pylint: disable=unused-argument
        for request in request_iterator:
            log.debug("PublishBuildToolEventStream %s", request)
            event = request.ordered_build_event

            _bes_events_created.inc()

            build_event: Optional[build_event_stream_pb2.BuildEvent] = None
            if event.event.WhichOneof("event") == "bazel_event":
                build_event = build_event_stream_pb2.BuildEvent()
                event.event.bazel_event.Unpack(build_event)

            job_id = uuid.UUID(event.stream_id.build_id)
            invocation_id = uuid.UUID(event.stream_id.invocation_id)
            structlog.contextvars.bind_contextvars(job_id=str(job_id))
            test_summary: Optional[test_summary_pb2.TestSummary] = None
            is_progress = False
            if build_event:
                test_summary = get_test_summary(build_event)
                if test_summary:
                    log.info("Found test summary: %s", test_summary)
                is_progress = build_event.WhichOneof("payload") == "progress"

            # we log the event only if it is a test summary or if there is a progress (aka console output)
            if test_summary or is_progress:
                assert build_event
                self.persistence.store(
                    job_id,
                    invocation_id,
                    event.sequence_number,
                    build_event,
                    test_summary,
                    event_time=event.event.event_time.ToDatetime(),
                )

                if test_summary:
                    _bes_test_summary_created.labels(
                        test_summary.target_name,
                        test_summary_pb2.TestState.Name(test_summary.state),
                        build_event_stream_pb2.TestStatus.Name(
                            test_summary.overall_status
                        ),
                    ).inc()

            response = publish_build_event_pb2.PublishBuildToolEventStreamResponse()
            response.stream_id.MergeFrom(event.stream_id)
            response.sequence_number = event.sequence_number
            structlog.contextvars.clear_contextvars()
            yield response


def _serve(config: Config, shutdown_event: threading.Event):
    if config.gcp:
        persistence = bazel_runner_server_gcp_lib.GcpBuildEventPersistence.create(
            config
        )
    else:
        raise ValueError("Invalid config")

    server = grpc.server(ThreadPoolExecutor(max_workers=config.bes_max_rpc_threads))
    health_pb2_grpc.add_HealthServicer_to_server(health.HealthServicer(), server)
    publish_build_event_pb2_grpc.add_PublishBuildEventServicer_to_server(
        PublishBuildEventServices(config, persistence), server
    )
    service_names = (
        publish_build_event_pb2.DESCRIPTOR.services_by_name[
            "PublishBuildEvent"
        ].full_name,
        reflection.SERVICE_NAME,
    )
    reflection.enable_server_reflection(service_names, server)
    server.add_insecure_port("[::]:50051")
    server.start()
    log.info("Listening on 50051")
    shutdown_event.wait()
    logging.info("Shutting down server")
    server.stop(grace=config.shutdown_grace_period_s).wait()
    logging.info("Server shutdown complete")


def main():
    handler = GracefulSignalHandler()

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--config-file", default=pathlib.Path("/config/config.json"), type=pathlib.Path
    )
    args = parser.parse_args()

    setup_struct_logging()
    config = Config.load_config(args.config_file)
    log.info("Config %s", config)
    if config.gcp:
        log.info("Service Account %s", gcp.get_active_gcp_service_account())

    # begin listening for Prometheus requests
    start_http_server(9090)

    _serve(config, handler.get_shutdown_event())


if __name__ == "__main__":
    main()
