"""Module containing code shared between rpc server and processor."""

import pathlib
from dataclasses import dataclass
from typing import Op<PERSON>

from dataclasses_json import dataclass_json


@dataclass_json
@dataclass
class GcpConfig:  # pylint: disable=too-many-instance-attributes
    """Configuration for the GCP specific parameters."""

    # GCP project id
    project_id: str

    # GCP region
    region: str

    # name of the internal pub/sub topic
    topic: str

    # name of the public pub/sub topic
    result_topic: str

    # name of the subscription
    subscription: str

    # instance id of the bigtable instance
    bigtable_instance_id: str

    # bigtable table names
    bigtable_bes_table_name: str
    bigtable_main_table_name: str

    # name of the storage bucket
    bucket: str

    # prefix in the storage bucket
    storage_output_path: str


@dataclass_json
@dataclass
class Config:  # pylint: disable=too-many-instance-attributes
    """Configuration for the server."""

    port: int
    test_namespace: str
    image_tag_file: str

    # test shards available for all tests
    test_shards: list[int]

    # test shards available for system tests
    system_test_shards: list[int]

    # test shards available for system tests that require GPU
    system_test_gpu_shards: list[int]

    runner_test_service_account_name: str
    bazel_cache_endpoint: str
    bes_endpoint: str
    test_selection_endpoint: str

    gcp: GcpConfig

    kube_config: Optional[str] = None

    shutdown_grace_period_s: float = 20.0

    # Maximum number of threads for the BES server.
    bes_max_rpc_threads: int = 32

    @classmethod
    def load_config(cls, config_file: pathlib.Path):
        """Loads the configuration from a file."""
        return cls.schema().loads(  # pylint: disable=no-member # type: ignore
            config_file.read_text()
        )
