{"port": 50051, "image_tag": "dev", "test_namespace": "test", "volume_count": 4, "runner_test_service_account_name": "bazel-runner-test-dev-dirk-sa", "bazel_cache_endpoint": "grpc://bazel-cache.devtools:9092", "bes_endpoint": "grpc://test-runner-bes-svc.dev-dirk:50051", "gcp": {"project_id": "system-services-dev", "region": "us-central1", "topic": "bazel-runner-dev-dirk-topic", "subscription": "bazel-runner-dev-dirk-sub", "bigtable_instance_id": "bigtable-dev-dirk", "bigtable_bes_table_name": "bazel-runner-bes-table", "bigtable_main_table_name": "bazel-runner-main-table", "bucket": "system-services-dev-dev-dirk-test-results", "storage_output": "augment-test-results/results/", "image_uri_prefix": "us-central1-docker.pkg.dev/system-services-dev/devtools-repository/devtools_bazel_runner"}, "kube_config": "/home/<USER>/.kube/config"}