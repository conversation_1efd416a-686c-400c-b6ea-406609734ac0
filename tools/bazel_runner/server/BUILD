load("@python_pip//:requirements.bzl", "requirement")
load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:go.bzl", "go_grpc_library", "go_proto_library")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:metadata.bzl", "metadata_test")
load("//tools/bzl:python.bzl", "py_binary", "py_grpc_library", "py_library", "py_oci_image", "py_proto_library", "pytest_test")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

proto_library(
    name = "test_runner_proto",
    srcs = ["test_runner.proto"],
    deps = [
        "//third_party/proto/bazel_build:build_event_stream_proto",
        "//tools/bazel_runner/bep_parser:test_summary_proto",
        "//tools/bazel_runner/control:bazel_runner_proto",
        "//tools/bazel_runner/git:checkout_proto",
        "//tools/bazel_runner/test_selection_server:test_selection_proto",
        "@protobuf//:timestamp_proto",
    ],
)

py_grpc_library(
    name = "test_runner_py_proto",
    protos = [":test_runner_proto"],
    visibility = ["//tools:__subpackages__"],
    deps = [
        "//third_party/proto/bazel_build:build_event_stream_py_proto",
        "//tools/bazel_runner/bep_parser:test_summary_py_proto",
        "//tools/bazel_runner/control:bazel_runner_py_proto",
        "//tools/bazel_runner/git:checkout_py_proto",
        "//tools/bazel_runner/test_selection_server:test_selection_py_proto",
    ],
)

go_grpc_library(
    name = "test_runner_go_proto",
    importpath = "github.com/augmentcode/augment/tools/bazel_runner/server/proto",
    proto = ":test_runner_proto",
    visibility = ["//tools:__subpackages__"],
    deps = [
        "//third_party/proto/bazel_build:build_event_stream_go_proto",
        "//tools/bazel_runner/bep_parser:test_summary_go_proto",
        "//tools/bazel_runner/control:bazel_runner_go_proto",
        "//tools/bazel_runner/git:checkout_go_proto",
        "//tools/bazel_runner/test_selection_server:test_selection_go_proto",
    ],
)

proto_library(
    name = "bazel_runner_store_proto",
    srcs = ["bazel_runner_store.proto"],
    deps = [
        ":test_runner_proto",
        "//third_party/proto/bazel_build:build_event_stream_proto",
        "//tools/bazel_runner/bep_parser:test_summary_proto",
        "//tools/bazel_runner/control:bazel_runner_proto",
        "@protobuf//:timestamp_proto",
    ],
)

py_proto_library(
    name = "bazel_runner_store_py_proto",
    protos = [":bazel_runner_store_proto"],
    visibility = ["//tools:__subpackages__"],
    deps = [
        ":test_runner_py_proto",
        "//third_party/proto/bazel_build:build_event_stream_py_proto",
        "//tools/bazel_runner/bep_parser:test_summary_py_proto",
        "//tools/bazel_runner/control:bazel_runner_py_proto",
    ],
)

py_library(
    name = "config",
    srcs = [
        "config.py",
    ],
    deps = [
        requirement("dataclasses_json"),
    ],
)

py_library(
    name = "bazel_runner_server_lib",
    srcs = [
        "bazel_runner_server_lib.py",
    ],
    deps = [
        ":bazel_runner_store_py_proto",
        ":config",
        ":test_runner_py_proto",
        "//tools/bazel_runner/bep_parser",
        requirement("grpcio"),
    ],
)

pytest_test(
    name = "bazel_runner_server_lib_test",
    srcs = [
        "bazel_runner_server_lib_test.py",
    ],
    deps = [
        ":bazel_runner_server_lib",
    ],
)

py_library(
    name = "bazel_runner_server_gcp_lib",
    srcs = [
        "bazel_runner_server_gcp_lib.py",
    ],
    deps = [
        ":bazel_runner_server_lib",
        ":bazel_runner_store_py_proto",
        ":test_runner_py_proto",
        "//tools/bazel_runner/bep_parser",
        requirement("google-cloud-bigtable"),
        requirement("google-cloud-pubsub"),
        requirement("google-cloud-storage"),
    ],
)

pytest_test(
    name = "bazel_runner_server_gcp_lib_test",
    srcs = [
        "bazel_runner_server_gcp_lib_test.py",
    ],
    deps = [
        requirement("python-ulid"),
        ":bazel_runner_server_gcp_lib",
    ],
)

py_binary(
    name = "bazel_runner_rpc_server",
    srcs = [
        "bazel_runner_rpc_server.py",
    ],
    deps = [
        ":bazel_runner_server_gcp_lib",
        ":bazel_runner_server_lib",
        ":test_runner_py_proto",
        "//base/logging:struct_logging",
        "//base/python/cloud:gcp",
        "//tools/bazel_runner/bep_parser:test_case_parser",
        requirement("dataclasses_json"),
        requirement("python-ulid"),
        requirement("grpcio"),
        requirement("grpcio-health-checking"),
        requirement("grpcio-reflection"),
        requirement("protobuf"),
        requirement("prometheus_client"),
    ],
)

pytest_test(
    name = "bazel_runner_rpc_server_test",
    srcs = [
        "bazel_runner_rpc_server_test.py",
    ],
    deps = [
        ":bazel_runner_rpc_server",
    ],
)

py_oci_image(
    name = "bazel_runner_rpc_server-image",
    package_name = package_name(),
    binary = ":bazel_runner_rpc_server",
    tars = ["//tools/docker:grpc_health_probe_tar"],
)

py_binary(
    name = "bazel_runner_processor_server",
    srcs = [
        "bazel_runner_processor.py",
    ],
    deps = [
        ":bazel_runner_server_gcp_lib",
        ":bazel_runner_server_lib",
        "//base/logging:struct_logging",
        "//base/python/cloud:gcp",
        "//base/python/signal_handler",
        "//base/tracing:tracing_py",
        # "//services/lib/grpc/metrics",
        "//tools/bazel_runner/bep_parser",
        "//tools/bazel_runner/control:client",
        "//tools/bazel_runner/test_selection_server:client",
        requirement("dataclasses_json"),
        requirement("grpcio"),
        requirement("grpcio-health-checking"),
        requirement("grpcio-reflection"),
        requirement("opentelemetry-instrumentation-grpc"),
        requirement("prometheus_client"),
        requirement("protobuf"),
    ],
)

pytest_test(
    name = "bazel_runner_server_gcp_lib_manual_test",
    srcs = [
        "bazel_runner_server_gcp_lib_manual_test.py",
        "conftest.py",
    ],
    tags = ["manual"],
    deps = [
        ":bazel_runner_server_gcp_lib",
        ":bazel_runner_server_lib",
        "//base/logging:struct_logging",
        "//base/python/cloud:gcp",
        "//tools/bazel_runner/bep_parser",
        "//tools/bazel_runner/control:client",
        requirement("protobuf"),
        requirement("prometheus_client"),
    ],
)

py_oci_image(
    name = "bazel_runner_processor_server-image",
    package_name = package_name(),
    binary = ":bazel_runner_processor_server",
    tars = ["//tools/docker:grpc_health_probe_tar"],
    trivy_allow_list = [
    ],
)

py_binary(
    name = "bazel_runner_bes_server",
    srcs = [
        "bazel_runner_bes_server.py",
    ],
    deps = [
        ":bazel_runner_server_gcp_lib",
        ":bazel_runner_server_lib",
        "//base/logging:struct_logging",
        "//base/python/cloud:gcp",
        "//base/python/signal_handler",
        "//third_party/proto/bazel_build:bes_py_proto",
        "//tools/bazel_runner/bep_parser",
        requirement("grpcio"),
        requirement("grpcio-reflection"),
        requirement("grpcio-health-checking"),
        requirement("protobuf"),
        requirement("certifi"),
        requirement("prometheus_client"),
    ],
)

py_oci_image(
    name = "bazel_runner_bes_server-image",
    package_name = package_name(),
    binary = ":bazel_runner_bes_server",
    tars = ["//tools/docker:grpc_health_probe_tar"],
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    cloud = ["GCP_US_CENTRAL1_DEV"],
    data = [
        ":bazel_runner_bes_server-image",
        ":bazel_runner_processor_server-image",
        ":bazel_runner_rpc_server-image",
        "//tools/bazel_runner/control:image",
    ],
    visibility = ["//tools/bazel_runner:__subpackages__"],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:mounts-lib",
        "//deploy/common:namespaces-lib",
        "//deploy/common:node-lib",
        "//deploy/common:telemetry-lib",
        "//deploy/gcp:gcp-lib",
        "//tools/deploy:github_readonly_token_lib",
    ],
)

pytest_test(
    name = "bazel_runner_manual_test",
    srcs = [
        "bazel_runner_manual_test.py",
        "conftest.py",
    ],
    tags = ["manual"],
    deps = [
        "//tools/bazel_runner/client",
    ],
)

kubecfg(
    name = "monitoring_kubecfg",
    src = "monitoring.jsonnet",
    cluster_wide = True,
    deps = [
        "//deploy/gcp:monitoring-lib",
    ],
)

kubecfg(
    name = "secrets_kubecfg",
    src = "deploy_secrets.jsonnet",
    cloud = ["GCP_US_CENTRAL1_DEV"],
    cluster_wide = True,
    deps = [
        "//deploy/common:cloud_info",
        "//deploy/common:eng-lib",
        "//deploy/gcp:gcp-lib",
    ],
)

metadata_test(
    name = "metadata_test",
    src = "METADATA.jsonnet",
    deps = [
        ":kubecfg",
        ":monitoring_kubecfg",
        ":secrets_kubecfg",
    ],
)
