local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local mountsLib = import 'deploy/common/mounts-lib.jsonnet';
local namespacesLib = import 'deploy/common/namespaces-lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local github_app_sealed = import 'tools/deploy/github_readonly_app_sealed.jsonnet';

function(namespace, env, cloud, namespace_config)
  assert cloud == 'GCP_US_CENTRAL1_DEV';
  local appName = 'bazel-runner';
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);

  local testNamespace = if env == 'DEV' && !cloudInfo.isKubecfgTestNamespace(namespace) then namespace_config.flags.testNamespace else 'test';
  assert testNamespace != '' : 'testNamespace must be set in your namespace configs flags';

  local gcpIamServiceAccounts = [
    {
      apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
      kind: 'IAMServiceAccount',
      metadata: {
        name: '%s-test-runner-iam' % namespace,
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        displayName: '%s-test-runner-iam' % namespace,
      },
    },
    {
      apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
      kind: 'IAMServiceAccount',
      metadata: {
        name: '%s-test-runner-bes-iam' % namespace,
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        displayName: '%s-test-runner-bes-iam' % namespace,
      },
    },
    // IAM service account used by the bazel runner control to execute the tests itself
    {
      apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
      kind: 'IAMServiceAccount',
      metadata: {
        name: '%s-bazel-test-iam' % namespace,
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        displayName: '%s-bazel-test-iam' % namespace,
      },
    },
  ];
  local gcpIamWorkloadIdenities = [
    // bind kubernetes and GCP services accounts together
    {
      apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
      kind: 'IAMPolicy',
      metadata: {
        name: 'bazel-runner-workload-identity',
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        resourceRef: {
          apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
          kind: 'IAMServiceAccount',
          name: '%s-test-runner-iam' % namespace,
        },
        bindings: [
          {
            role: 'roles/iam.workloadIdentityUser',
            members: [
              'serviceAccount:system-services-dev.svc.id.goog[%s/test-runner-sa]' % namespace,
            ],
          },
        ],
      },
    },
    {
      apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
      kind: 'IAMPolicy',
      metadata: {
        name: 'bazel-runner-bes-workload-identity',
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        resourceRef: {
          apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
          kind: 'IAMServiceAccount',
          name: '%s-test-runner-bes-iam' % namespace,
        },
        bindings: [
          {
            role: 'roles/iam.workloadIdentityUser',
            members: [
              'serviceAccount:system-services-dev.svc.id.goog[%s/test-runner-bes-sa]' % namespace,
            ],
          },
        ],
      },
    },
    {
      apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
      kind: 'IAMPolicy',
      metadata: {
        name: 'bazel-test-workload-identity',
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        resourceRef: {
          apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
          kind: 'IAMServiceAccount',
          name: '%s-bazel-test-iam' % namespace,
        },
        bindings: [
          {
            role: 'roles/iam.workloadIdentityUser',
            members: [
              'serviceAccount:system-services-dev.svc.id.goog[%s/bazel-runner-test-%s-sa]' % [testNamespace, namespace],
            ],
          },
        ],
      },
    },
  ];
  local besBigtable = gcpLib.createBigtableTable(
    cloud=cloud,
    env=env,
    app=appName,
    namespace=namespace,
    tableName='bazel-runner-bes-table',
    columnFamily=[
      {
        family: 'Event',
      },
    ],
    iamServiceAccountName=null
  );
  local mainBigtable = gcpLib.createBigtableTable(
    cloud=cloud,
    env=env,
    app=appName,
    namespace=namespace,
    tableName='bazel-runner-main-table',
    columnFamily=[
      {
        family: 'Main',
      },
      {
        family: 'RunId',
      },
      {
        family: 'Timestamp',
      },
    ],
    iamServiceAccountName=null
  );
  local gpcObjects = gcpIamServiceAccounts + gcpIamWorkloadIdenities + [
    // bigtable tables and GC polcies
    {
      apiVersion: 'bigtable.cnrm.cloud.google.com/v1beta1',
      kind: 'BigtableGCPolicy',
      metadata: {
        name: 'bazel-runner-bes-gc',
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        tableRef: {
          name: besBigtable.tableName,
        },
        columnFamily: 'Event',
        instanceRef: {
          name: besBigtable.instanceName,
          namespace: mainBigtable.instanceNamespace,
        },
        maxAge: [
          {
            days: 400,
          },
        ],
      },
    },
    {
      apiVersion: 'bigtable.cnrm.cloud.google.com/v1beta1',
      kind: 'BigtableGCPolicy',
      metadata: {
        name: 'bazel-runner-main-gc',
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        tableRef: {
          name: mainBigtable.tableName,
        },
        columnFamily: 'Main',
        instanceRef: {
          name: mainBigtable.instanceName,
          namespace: mainBigtable.instanceNamespace,
        },
        maxAge: [
          {
            days: 400,
          },
        ],
      },
    },
    // bucket for test artifacts
    {
      apiVersion: 'storage.cnrm.cloud.google.com/v1beta1',
      kind: 'StorageBucket',
      metadata: {
        annotations: if env != 'DEV' then {
          'cnrm.cloud.google.com/force-destroy': 'false',
        } else {},
        name: 'system-services-dev-%s-test-results' % namespace,
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        lifecycleRule: [
          {
            action: {
              type: 'Delete',
            },
            condition: {
              age: 400,
              withState: 'ANY',
            },
          },
        ],
        uniformBucketLevelAccess: true,
      },
    },
    // pub/sub objects for internal notifications
    {
      apiVersion: 'pubsub.cnrm.cloud.google.com/v1beta1',
      kind: 'PubSubTopic',
      metadata: {
        name: 'bazel-runner-%s-topic' % namespace,
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
      },
    },
    {
      apiVersion: 'pubsub.cnrm.cloud.google.com/v1beta1',
      kind: 'PubSubSubscription',
      metadata: {
        name: 'bazel-runner-%s-sub' % namespace,
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        topicRef: {
          name: 'bazel-runner-%s-topic' % namespace,
        },
        ackDeadlineSeconds: 10,
        retryPolicy: {
          minimumBackoff: '5s',
          maximumBackoff: '300s',
        },
      },
    },
    // give the processor access to pub/sub topic
    {
      apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
      kind: 'IAMPolicy',
      metadata: {
        name: 'bazel-runner-sa-topic-policy',
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        resourceRef: {
          kind: 'PubSubTopic',
          name: 'bazel-runner-%s-topic' % namespace,
        },
        bindings: [
          {
            role: 'roles/pubsub.publisher',
            members: [
              'serviceAccount:%<EMAIL>' % namespace,
            ],
          },
        ],
      },
    },
    // give the processor access to pub/sub subscription
    {
      apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
      kind: 'IAMPolicy',
      metadata: {
        name: 'bazel-runner-sa-sub-policy',
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        resourceRef: {
          kind: 'PubSubSubscription',
          name: 'bazel-runner-%s-sub' % namespace,
        },
        bindings: [
          {
            role: 'roles/pubsub.subscriber',
            members: [
              'serviceAccount:%<EMAIL>' % namespace,
            ],
          },
        ],
      },
    },
    // pub/sub topic for async communication with other services, e.g. CI
    {
      apiVersion: 'pubsub.cnrm.cloud.google.com/v1beta1',
      kind: 'PubSubTopic',
      metadata: {
        name: 'bazel-runner-%s-result-topic' % namespace,
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
      },
    },
    {
      apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
      kind: 'IAMPolicy',
      metadata: {
        name: 'bazel-runner-result-sa-topic-policy',
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        resourceRef: {
          kind: 'PubSubTopic',
          name: 'bazel-runner-%s-result-topic' % namespace,
        },
        bindings: [
          {
            role: 'roles/pubsub.publisher',
            members: [
              'serviceAccount:%<EMAIL>' % namespace,
            ],
          },
        ],
      },
    },
    // give processor and BES server access to bigtable
    gcpLib.grantBigtableAccess(
      cloud=cloud,
      env=env,
      namespace=namespace,
      app=appName,
      table=besBigtable,
      iamServiceAccountNames=[
        '%s-test-runner-bes-iam' % namespace,
        '%s-test-runner-iam' % namespace,
      ],
      nameSuffix='bazel-runner-grant'
    ),
    gcpLib.grantBigtableAccess(
      cloud=cloud,
      env=env,
      namespace=namespace,
      app=appName,
      table=mainBigtable,
      iamServiceAccountNames=[
        '%s-test-runner-iam' % namespace,
      ],
      nameSuffix='bazel-runner-grant'
    ),
    // give server and the test services access to the bucket
    {
      apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
      kind: 'IAMPartialPolicy',
      metadata: {
        name: 'bazel-runner-sa-bucket-policy-2',
        namespace: namespace,
        labels: {
          app: appName,
        },
        annotations: {
          'cnrm.cloud.google.com/reconcile-interval-in-seconds': '120',
        },
      },
      spec: {
        resourceRef: {
          kind: 'StorageBucket',
          name: 'system-services-dev-%s-test-results' % namespace,
        },
        bindings: [
          {
            role: 'roles/storage.objectUser',
            members: [
              {
                memberFrom: {
                  serviceAccountRef: {
                    name: '%s-test-runner-iam' % namespace,
                  },
                },
              },
              {
                memberFrom: {
                  serviceAccountRef: {
                    name: '%s-bazel-test-iam' % namespace,
                  },
                },
              },
            ],
          },
        ],
      },
    },
    // the processor pod needs to be able to read the artifacts
    {
      apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
      kind: 'IAMPartialPolicy',
      metadata: {
        name: 'bazel-runner-sa-artifact-reader',
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        resourceRef: {
          kind: 'ArtifactRegistryRepository',
          external: 'projects/system-services-dev/locations/us-central1/repositories/devtools-repository',
        },
        bindings: [
          {
            role: 'roles/artifactregistry.reader',
            members: [
              {
                memberFrom: {
                  serviceAccountRef: {
                    name: '%s-test-runner-iam' % namespace,
                  },
                },
              },
            ],
          },
        ],
      },
    },
    // the test runner needs to be able to push and pull artifacts
    {
      apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
      kind: 'IAMPartialPolicy',
      metadata: {
        name: 'bazel-runner-sa-artifact-writer',
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        resourceRef: {
          kind: 'ArtifactRegistryRepository',
          external: 'projects/system-services-dev/locations/us-central1/repositories/build-images',
        },
        bindings: [
          {
            role: 'roles/artifactregistry.writer',
            members: [
              {
                memberFrom: {
                  serviceAccountRef: {
                    name: '%s-bazel-test-iam' % namespace,
                  },
                },
              },
            ],
          },
        ],
      },
    },
    // the test runner needs to be able to pull base images
    {
      apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
      kind: 'IAMPartialPolicy',
      metadata: {
        name: 'bazel-runner-sa-base-image-reader',
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        resourceRef: {
          kind: 'ArtifactRegistryRepository',
          external: 'projects/system-services-dev/locations/us-central1/repositories/base-images',
        },
        bindings: [
          {
            role: 'roles/artifactregistry.reader',
            members: [
              {
                memberFrom: {
                  serviceAccountRef: {
                    name: '%s-bazel-test-iam' % namespace,
                  },
                },
              },
            ],
          },
        ],
      },
    },
    {
      apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
      kind: 'IAMPartialPolicy',
      metadata: {
        name: 'bazel-runner-sa-bazel-data-bucket-policy',
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        resourceRef: {
          kind: 'StorageBucket',
          external: 'augment-bazel-data',
        },
        bindings: [
          {
            role: 'roles/storage.objectUser',
            members: [
              {
                memberFrom: {
                  serviceAccountRef: {
                    name: '%s-bazel-test-iam' % namespace,
                  },
                },
              },
            ],
          },
        ],
      },
    },
    // The test runner needs to be able to truncate bigtable tables in the dev and test instances
    {
      apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
      kind: 'IAMPartialPolicy',
      metadata: {
        name: 'bazel-runner-sa-bigtable-truncate',
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        resourceRef: {
          apiVersion: 'bigtable.cnrm.cloud.google.com/v1beta1',
          kind: 'BigtableInstance',
          name: mainBigtable.instanceName,
          namespace: mainBigtable.instanceNamespace,
        },
        bindings: [
          {
            role: 'roles/bigtable.user',
            members: [
              {
                memberFrom: {
                  serviceAccountRef: {
                    name: '%s-bazel-test-iam' % namespace,
                  },
                },
              },
            ],
          },
        ],
      },
    },
    {
      apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
      kind: 'IAMPartialPolicy',
      metadata: {
        name: 'bazel-runner-sa-bigtable-truncate-test',
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        resourceRef: {
          apiVersion: 'bigtable.cnrm.cloud.google.com/v1beta1',
          kind: 'BigtableInstance',
          name: 'bigtable-central-test',
          namespace: mainBigtable.instanceNamespace,
        },
        bindings: [
          {
            role: 'roles/bigtable.user',
            members: [
              {
                memberFrom: {
                  serviceAccountRef: {
                    name: '%s-bazel-test-iam' % namespace,
                  },
                },
              },
            ],
          },
        ],
      },
    },
    // we only use gc policy for non-dev
  ] + if env == 'DEV' then [] else [
    {
      apiVersion: 'bigtable.cnrm.cloud.google.com/v1beta1',
      kind: 'BigtableGCPolicy',
      metadata: {
        name: 'bazel-runner-runid-gc',
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        tableRef: {
          name: mainBigtable.tableName,
        },
        columnFamily: 'RunId',
        instanceRef: {
          name: mainBigtable.instanceName,
          namespace: mainBigtable.instanceNamespace,
        },
        maxAge: [
          {
            days: 31,
          },
        ],
      },
    },
  ];
  local cloudObjects = gpcObjects;
  local rpcService = {
    apiVersion: 'v1',
    kind: 'Service',
    metadata: {
      name: 'test-runner-svc',
      namespace: namespace,
      labels: {
        app: appName,
        'eng.augmentcode.com/service-type': 'grpc',
        'eng.augmentcode.com/grpc-insecure': 'true',
      },
    },
    spec: {
      selector: {
        app: 'test-runner-rpc',
      },
      ports: [
        {
          protocol: 'TCP',
          port: 50051,
          targetPort: 'grpc-svc',
        },
      ],
    },
  };
  local besService = {
    apiVersion: 'v1',
    kind: 'Service',
    metadata: {
      name: 'test-runner-bes-svc',
      namespace: namespace,
      labels: {
        app: appName,
        'eng.augmentcode.com/service-type': 'grpc',
        'eng.augmentcode.com/grpc-insecure': 'true',
      },
    },
    spec: {
      selector: {
        app: 'test-runner-bes-rpc',
      },
      ports: [
        {
          protocol: 'TCP',
          port: 50051,
          targetPort: 'grpc-svc',
        },
      ],
    },
  };
  local namespaceService = {
    apiVersion: 'v1',
    kind: 'Service',
    metadata: {
      name: 'test-runner-namespace-svc',
      namespace: namespace,
      labels: {
        app: appName,
        'eng.augmentcode.com/service-type': 'grpc',
        'eng.augmentcode.com/grpc-insecure': 'true',
      },
    },
    spec: {
      selector: {
        app: 'test-runner-processor',
      },
      ports: [
        {
          protocol: 'TCP',
          port: 50051,
          targetPort: 'grpc-svc',
        },
      ],
    },
  };
  local testShardCount = if env == 'DEV' then 2 else 128;
  local systemTestShardCount = if env == 'DEV' then 1 else 48;
  local systemTestGpuShardCount = if env == 'DEV' then 1 else 16;
  local config = {
    apiVersion: 'v1',
    kind: 'ConfigMap',
    metadata: {
      name: 'test-runner-config',
      namespace: namespace,
      annotations: {
        'reloader.stakater.com/match': 'true',
      },
      labels: {
        app: appName,
      },
    },
    data: {
      'config.json': std.manifestJson({
        port: 50051,
        bes_max_rpc_threads: 64,
        test_namespace: testNamespace,
        test_shards: std.range(0, testShardCount - 1),
        system_test_shards: std.range(0, systemTestShardCount - 1),
        system_test_gpu_shards: std.range(systemTestShardCount, systemTestGpuShardCount + systemTestShardCount - 1),
        image_tag_file: '/config/image_tag.txt',
        runner_test_service_account_name: 'bazel-runner-test-%s-sa' % namespace,
        bazel_cache_endpoint: 'grpc://bazel-cache-3.devtools:9092',
        bes_endpoint: 'grpc://test-runner-bes-svc.%s:50051' % namespace,
        test_selection_endpoint: 'test-selection-svc:50051',
        gcp: {
          project_id: mainBigtable.projectId,
          region: 'us-central1',
          topic: 'bazel-runner-%s-topic' % namespace,
          result_topic: 'bazel-runner-%s-result-topic' % namespace,
          subscription: 'bazel-runner-%s-sub' % namespace,
          bigtable_instance_id: mainBigtable.instanceName,
          bigtable_bes_table_name: besBigtable.tableName,
          bigtable_main_table_name: mainBigtable.tableName,
          bucket: 'system-services-dev-%s-test-results' % namespace,
          storage_output_path: 'augment-test-results/results/',
        },
      }),
      // this has to be in its own config item as kubecfg rewriting doesn't kick in within
      // a manifestJson string
      'image_tag.txt': {
        kubecfg: {
          image_push: '//tools/bazel_runner/control:image',
          image_name: 'devtools_bazel_runner',
        },
      },
    },
  };
  local rpcContainer =
    {
      name: 'rpc',
      image: {
        kubecfg: {
          image_push: '//tools/bazel_runner/server:bazel_runner_rpc_server-image',
          image_name: 'devtools_bazel_runner_server',
        },
      },
      ports: [
        {
          containerPort: 50051,
          name: 'grpc-svc',
        },
      ],
      volumeMounts: [
        {
          name: 'config',
          mountPath: '/config',
          readOnly: true,
        },
      ],
      readinessProbe: grpcLib.grpcHealthCheck(null, tls=false) + {
        periodSeconds: 30,
      },
      livenessProbe: grpcLib.grpcHealthCheck(null, tls=false) + {
        periodSeconds: 30,
      },
      resources: {
        limits: {
          cpu: 1,
          memory: '512Mi',
        },
      },
    };
  local besContainer =
    {
      name: 'bes',
      target: {
        name: '//tools/bazel_runner/server:bazel_runner_bes_server-image',
        dst: 'devtools_bazel_runner_bes_server',
      },
      ports: [
        {
          containerPort: 50051,
          name: 'grpc-svc',
        },
      ],
      volumeMounts: [
        {
          name: 'config',
          mountPath: '/config',
          readOnly: true,
        },
      ],
      readinessProbe: grpcLib.grpcHealthCheck(null, tls=false) + {
        periodSeconds: 30,
      },
      livenessProbe: grpcLib.grpcHealthCheck(null, tls=false) + {
        periodSeconds: 30,
      },
      resources: {
        limits: {
          cpu: 1,
          memory: '512Mi',
        },
      },
    };
  local processorContainer =
    {
      name: 'processor',
      target: {
        name: '//tools/bazel_runner/server:bazel_runner_processor_server-image',
        dst: 'devtools_bazel_runner_processor',
      },
      ports: [
        {
          containerPort: 50051,
          name: 'grpc-svc',
        },
      ],
      readinessProbe: grpcLib.grpcHealthCheck(null, tls=false) + {
        periodSeconds: 30,
      },
      livenessProbe: grpcLib.grpcHealthCheck(null, tls=false) + {
        periodSeconds: 30,
      },
      volumeMounts: [
        {
          name: 'config',
          mountPath: '/config',
          readOnly: true,
        },
      ],
      resources: {
        limits: {
          cpu: 1,
          memory: '512Mi',
        },
      },
    };
  local rpcPod =
    {
      priorityClassName: cloudInfo.envToPriorityClass(env),
      tolerations: tolerations,
      affinity: affinity,
      serviceAccountName: 'test-runner-sa',
      containers: [
        rpcContainer,
      ],
      volumes: [
        {
          name: 'config',
          configMap: {
            name: 'test-runner-config',
            items: [
              {
                key: 'config.json',
                path: 'config.json',
              },
            ],
          },
        },
      ],
    };
  local besPod =
    {
      priorityClassName: cloudInfo.envToPriorityClass(env),
      tolerations: tolerations,
      affinity: affinity,
      serviceAccountName: 'test-runner-bes-sa',
      containers: [
        besContainer,
      ],
      volumes: [
        {
          name: 'config',
          configMap: {
            name: 'test-runner-config',
            items: [
              {
                key: 'config.json',
                path: 'config.json',
              },
            ],
          },
        },
      ],
    };
  local processorPod =
    {
      priorityClassName: cloudInfo.envToPriorityClass(env),
      tolerations: tolerations,
      affinity: affinity,
      serviceAccountName: 'test-runner-sa',
      containers: [
        processorContainer,
      ],
      volumes: [
        {
          name: 'config',
          configMap: {
            name: 'test-runner-config',
            items: [
              {
                key: 'config.json',
                path: 'config.json',
              },
              {
                key: 'image_tag.txt',
                path: 'image_tag.txt',
              },
            ],
          },
        },
      ],
    };
  local serviceAccount = {
    apiVersion: 'v1',
    kind: 'ServiceAccount',
    metadata: {
      name: 'test-runner-sa',
      namespace: namespace,
      annotations: {
        'iam.gke.io/gcp-service-account': '%<EMAIL>' % namespace,
      },
      labels: {
        app: appName,
      },
    },
  };
  local besServiceAccount = {
    apiVersion: 'v1',
    kind: 'ServiceAccount',
    metadata: {
      name: 'test-runner-bes-sa',
      namespace: namespace,
      annotations: {
        'iam.gke.io/gcp-service-account': '%<EMAIL>' % namespace,
      },
      labels: {
        app: appName,
      },
    },
  };
  local roleBindings = [
    {
      apiVersion: 'rbac.authorization.k8s.io/v1',
      kind: 'RoleBinding',
      metadata: {
        name: '%s-test-read-pods' % namespace,
        namespace: testNamespace,
        labels: {
          app: appName,
        },
      },
      subjects: [
        {
          kind: 'ServiceAccount',
          namespace: namespace,
          name: 'test-runner-sa',
        },
      ],
      roleRef: {
        kind: 'Role',
        name: 'test-role',
        apiGroup: 'rbac.authorization.k8s.io',
      },
    },
  ];
  // service account used by the bazel runner control to execute the tests itself
  local runnerTestServiceAccount =
    {
      apiVersion: 'v1',
      kind: 'ServiceAccount',
      metadata: {
        name: 'bazel-runner-test-%s-sa' % namespace,
        namespace: testNamespace,
        annotations: {
          'iam.gke.io/gcp-service-account': '%<EMAIL>' % namespace,
        },
        labels: {
          app: appName,
        },
      },
    };
  local runnerRoleBindings = [
                               {
                                 apiVersion: 'rbac.authorization.k8s.io/v1',
                                 kind: 'RoleBinding',
                                 metadata: {
                                   name: '%s-read-pods' % namespace,
                                   namespace: testNamespace,
                                   labels: {
                                     app: appName,
                                   },
                                 },
                                 subjects: [
                                   {
                                     kind: 'ServiceAccount',
                                     namespace: testNamespace,
                                     name: 'bazel-runner-%s-sa' % namespace,
                                   },
                                 ],
                                 roleRef: {
                                   kind: 'Role',
                                   name: 'test-role',
                                   apiGroup: 'rbac.authorization.k8s.io',
                                 },
                               },
                             ] +
                             [
                               {
                                 apiVersion: 'rbac.authorization.k8s.io/v1',
                                 kind: 'RoleBinding',
                                 metadata: {
                                   name: '%s-read-pods' % namespace,
                                   namespace: '%s-%s' % [testNamespace, i],
                                   labels: {
                                     app: appName,
                                   },
                                 },
                                 subjects: [
                                   {
                                     kind: 'ServiceAccount',
                                     namespace: testNamespace,
                                     name: 'bazel-runner-test-%s-sa' % namespace,
                                   },
                                   {
                                     kind: 'ServiceAccount',
                                     namespace: testNamespace,
                                     name: 'bazel-runner-%s-sa' % namespace,
                                   },
                                 ],
                                 roleRef: {
                                   kind: 'Role',
                                   name: 'test-role',
                                   apiGroup: 'rbac.authorization.k8s.io',
                                 },
                               }
                               for i in std.range(0, testShardCount - 1)
                             ];
  local besDeployment =
    {
      apiVersion: 'apps/v1',
      kind: 'Deployment',
      metadata: {
        name: 'test-runner-bes-rpc',
        namespace: namespace,
        labels: {
          app: appName,
        },
        annotations: {
          'reloader.stakater.com/search': 'true',
        },
      },
      spec: {
        replicas: if env == 'DEV' then 1 else 2,
        minReadySeconds: if env == 'DEV' then 0 else 60,
        strategy: {
          type: 'RollingUpdate',
          rollingUpdate: {
            maxSurge: 1,
            maxUnavailable: 0,
          },
        },
        selector: {
          matchLabels: {
            app: 'test-runner-bes-rpc',
          },
        },
        template: {
          metadata: {
            labels: {
              app: 'test-runner-bes-rpc',
            },
          },
          spec: besPod,
        },
      },
    };
  local runnerDeployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: 'test-runner-rpc',
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'reloader.stakater.com/search': 'true',
      },
    },
    spec: {
      replicas: if env == 'DEV' then 1 else 2,
      minReadySeconds: if env == 'DEV' then 0 else 60,
      strategy: {
        type: 'RollingUpdate',
        rollingUpdate: {
          maxSurge: 1,
          maxUnavailable: 0,
        },
      },
      selector: {
        matchLabels: {
          app: 'test-runner-rpc',
        },
      },
      template: {
        metadata: {
          labels: {
            app: 'test-runner-rpc',
          },
        },
        spec: rpcPod,
      },
    },
  };
  local processorDeployment =
    {
      apiVersion: 'apps/v1',
      kind: 'Deployment',
      metadata: {
        name: 'test-runner-processor',
        namespace: namespace,
        labels: {
          app: appName,
        },
        annotations: {
          'reloader.stakater.com/search': 'true',
        },
      },
      spec: {
        // replica == 1 even in prod as there should only be a singler processor replica making state changes
        replicas: 1,
        // don't allow surge to ensure that there is always only a single processor pod up.
        strategy: {
          type: 'RollingUpdate',
          rollingUpdate: {
            maxSurge: 0,
            maxUnavailable: 1,
          },
        },
        selector: {
          matchLabels: {
            app: 'test-runner-processor',
          },
        },
        template: {
          metadata: {
            labels: {
              app: 'test-runner-processor',
            },
          },
          spec: processorPod,
        },
      },
    };
  local apiGroups = [
    '',
    'apps',
    'batch',
    'networking.k8s.io',
    'cert-manager.io',
    'rbac.authorization.k8s.io',
    'eng.augmentcode.com',
    'iam.cnrm.cloud.google.com',
    'bigtable.cnrm.cloud.google.com',
    'storage.cnrm.cloud.google.com',
    'pubsub.cnrm.cloud.google.com',
    'artifactregistry.cnrm.cloud.google.com',
    'cloudidentity.cnrm.cloud.google.com',
    'networking.gke.io',
    'cloud.google.com',
    'bitnami.com',
    'keda.k8s.io',
    'keda.sh',
    'policy',  // PodDisruptionBudget
    'autoscaling',
    'secrets-store.csi.x-k8s.io',
    'monitoring.googleapis.com',
  ];
  local rules = [
    // all rights in a single namespace
    {
      apiGroups: apiGroups,  // "" indicates the core API group
      resources: ['*'],
      verbs: ['*'],
    },
  ];
  local namespaces = [testNamespace] +
                     ['%s-%s' % [testNamespace, i] for i in std.range(0, testShardCount - 1)];

  local namespaceObjects = std.flatMap(function(ns)
                                         namespacesLib.createNamespace(cloud=cloud, namespace={ name: ns }, appName=appName),
                                       namespaces);

  local roles = [
    {
      apiVersion: 'rbac.authorization.k8s.io/v1',
      kind: 'Role',
      metadata: {
        namespace: testNamespace,
        labels: {
          app: appName,
        },
        name: 'test-role',
      },
      rules: [
        // all rights in a single namespace
        {
          apiGroups: ['', 'apps', 'batch', 'networking.k8s.io'],  // "" indicates the core API group
          resources: ['*'],
          verbs: ['*'],
        },
      ],
    },
  ] + [
    {
      apiVersion: 'rbac.authorization.k8s.io/v1',
      kind: 'Role',
      metadata: {
        labels: {
          app: appName,
        },
        namespace: '%s-%s' % [testNamespace, i],
        name: 'test-role',
      },
      rules: rules,
    }
    for i in std.range(0, testShardCount - 1)
  ];
  local cacheVolumes = [{
    apiVersion: 'v1',
    kind: 'PersistentVolumeClaim',
    metadata: {
      labels: {
        app: appName,
      },
      name: 'bazel-runner-%s-pvc' % i,
      namespace: testNamespace,
    },
    spec: {
      accessModes: [
        'ReadWriteOnce',
      ],
      storageClassName: 'premium-rwo',
      resources: {
        requests: {
          storage: '512Gi',
        },
      },
    },
  } for i in std.range(0, testShardCount - 1)];
  lib.flatten([
    namespaceObjects,
    std.map(function(ns) certLib.createNamespaceIssuer(ns, appName=appName), namespaces),
    std.map(function(ns) gcpLib.createIapSecret(cloud, ns, appName=appName), namespaces),
    std.map(function(ns) mountsLib.createNamespaceMounts(cloud, ns, appName=appName), namespaces),
    roles,
    cacheVolumes,
    github_app_sealed(cloud, namespace=testNamespace, appName=appName),
    config,
    rpcService,
    besService,
    namespaceService,
    serviceAccount,
    besServiceAccount,
    runnerTestServiceAccount,
    besDeployment,
    runnerDeployment,
    processorDeployment,
    runnerRoleBindings,
    roleBindings,
    cloudObjects,
    mainBigtable.objects,
    besBigtable.objects,
  ])
