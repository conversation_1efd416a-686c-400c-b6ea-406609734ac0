"""Implementations of the server library protocols for GCP."""

import datetime
import json
import logging
import pathlib
import sys
import time
import typing
import uuid
from typing import IO, BinaryIO, Generator, Optional, TextIO, Tuple

import google.api_core.exceptions
import grpc
import third_party.proto.bazel_build.build_event_stream_pb2 as build_event_stream_pb2
from google.cloud import bigtable  # type: ignore
from google.cloud import pubsub_v1, storage  # type: ignore
from google.cloud.bigtable.row_set import RowSet
from google.protobuf import timestamp_pb2

import tools.bazel_runner.bep_parser.test_summary_pb2 as test_summary_pb2
from tools.bazel_runner.control import bazel_runner_pb2
from tools.bazel_runner.server import (
    bazel_runner_server_lib,
    bazel_runner_store_pb2,
    test_runner_pb2,
)
from tools.bazel_runner.server.config import Config


def _read_proto_from_cell(cells, column: str, proto, fn=lambda proto: proto):
    column_bytes = column.encode("utf-8")
    if column_bytes not in cells:
        return None
    cell = cells[column_bytes][0]
    proto.ParseFromString(cell.value)
    return fn(proto)


def _mutate_and_check(table, rows):
    response = table.mutate_rows(rows)
    for status in response:
        if status.code != 0:
            logging.error("Error writing row: %s", status.message)
            raise ValueError("Error writing row")


class GcpResultPublisher(bazel_runner_server_lib.ResultPublisher):
    """Wrapper around low-level queue handling."""

    def __init__(
        self,
        config: Config,
        publisher: pubsub_v1.PublisherClient,
    ):
        self.config = config
        self.publisher = publisher
        assert config.gcp
        topic_name = "projects/{project_id}/topics/{topic}".format(
            project_id=config.gcp.project_id,
            topic=config.gcp.result_topic,
        )
        self.topic_name = topic_name
        assert config.gcp

    @classmethod
    def create(cls, config):
        assert config.gcp
        publisher = pubsub_v1.PublisherClient()
        return cls(config, publisher)

    def notify(self, run_info: test_runner_pb2.TestRunInfo):
        """Notify the queue about a change in the given run."""
        future = self.publisher.publish(
            self.topic_name,
            run_info.SerializeToString(),
        )
        _ = future.result()


class GcpQueue(bazel_runner_server_lib.Queue):
    """Wrapper around low-level queue handling."""

    def __init__(
        self,
        config: Config,
        publisher: pubsub_v1.PublisherClient,
        subscriber: pubsub_v1.SubscriberClient,
    ):
        self.config = config
        self.publisher = publisher
        self.subscriber = subscriber
        assert config.gcp
        topic_name = "projects/{project_id}/topics/{topic}".format(
            project_id=config.gcp.project_id,
            topic=config.gcp.topic,
        )
        subscription_name = "projects/{project_id}/subscriptions/{sub}".format(
            project_id=config.gcp.project_id,
            sub=config.gcp.subscription,
        )
        self.topic_name = topic_name
        self.subscription_name = subscription_name
        assert config.gcp

    @classmethod
    def create(cls, config):
        assert config.gcp
        publisher = pubsub_v1.PublisherClient()
        subscriber = pubsub_v1.SubscriberClient()
        return cls(config, publisher, subscriber)

    def notify(self, run_id: uuid.UUID):
        """Notify the queue about a change in the given run."""
        future = self.publisher.publish(
            self.topic_name,
            json.dumps({"run_id": str(run_id)}).encode("utf-8"),
        )
        _ = future.result()

    def receive(self) -> typing.Iterable[uuid.UUID]:
        """Listen to run notification events."""
        while True:
            try:
                response = self.subscriber.pull(
                    request=pubsub_v1.types.PullRequest(  # type: ignore
                        subscription=self.subscription_name,
                        max_messages=1,
                    )
                )

                for message in response.received_messages:
                    logging.debug("message %s", message)
                    yield uuid.UUID(
                        json.loads(message.message.data.decode("utf-8"))["run_id"]
                    )

                ack_ids = [message.ack_id for message in response.received_messages]
                if ack_ids:
                    ack_request = pubsub_v1.types.AcknowledgeRequest(  # type: ignore
                        subscription=self.subscription_name,
                        ack_ids=ack_ids,
                    )
                    self.subscriber.acknowledge(request=ack_request)
            except google.api_core.exceptions.DeadlineExceeded:
                pass
            except google.api_core.exceptions.ServiceUnavailable:
                logging.info("Service Unavailable, sleeping")
                time.sleep(1)


def get_timestamp_key(run_id: uuid.UUID) -> bytes:
    """Returns the BigTable timestamp key for the given run id.
    The run id is actually a ulid (https://github.com/ulid/spec) and thus sortable as timestamp.
    This function uses that property and reverses the sort order

    The key is setup so that newer runs have a lower timestamp key then older runs.

    Args:
        run_id: the run id

    Returns:
        The BigTable key for the given run id
    """
    return b"timestamp#v2#" + bytes(~byte & 0xFF for byte in run_id.bytes)


class GcpBuildEventPersistence(bazel_runner_server_lib.BuildEventPersistence):
    """Handles the persistence of build event information in BigTable."""

    def __init__(self, config: Config, client: bigtable.Client):
        self.config = config
        assert config.gcp
        self.gcp_config = config.gcp
        self.client = client

        self.instance = client.instance(self.gcp_config.bigtable_instance_id)
        self.bes_table = self.instance.table(self.gcp_config.bigtable_bes_table_name)

    @classmethod
    def create(cls, config: Config):
        client = bigtable.Client()
        return cls(config, client)

    def store(
        self,
        job_id: uuid.UUID,
        invocation_id: uuid.UUID,
        sequence_number: int,
        build_event: build_event_stream_pb2.BuildEvent,
        test_summary: test_summary_pb2.TestSummary | None,
        event_time: datetime.datetime,
    ):
        logging.info(
            "Storing build event: job_id=%s, invocation_id=%s, sequence_number=%s, is_summary=%s, stdout_len=%s, stderr_len=%s",
            job_id,
            invocation_id,
            sequence_number,
            test_summary is not None,
            len(build_event.progress.stdout),
            len(build_event.progress.stderr),
        )

        row_key = f"bes#{job_id}#{sequence_number:08d}"
        row = self.bes_table.direct_row(row_key=row_key)

        bes_event = bazel_runner_store_pb2.BesEvent()
        bes_event.job_id = str(job_id)
        bes_event.invocation_id = str(invocation_id)
        bes_event.sequence_number = sequence_number
        bes_event.time.FromDatetime(event_time)
        if build_event:
            bes_event.build_event.CopyFrom(build_event)
        if test_summary:
            bes_event.test_summary.CopyFrom(test_summary)
        row.set_cell("Event", "", bes_event.SerializeToString())

        _mutate_and_check(self.bes_table, [row])

    def get_build_events(
        self, job_id: uuid.UUID, min_sequence_number: int, limit: int
    ) -> typing.Iterator[
        Tuple[
            int,
            build_event_stream_pb2.BuildEvent,
            test_summary_pb2.TestSummary | None,
        ],
    ]:
        """Generates all the build events stored for the given job.

        Only events with a sequence_number greater than the number provided are returned.
        """
        start_key = f"bes#{job_id}#{min_sequence_number:08d}"
        end_key = f"bes#{job_id}$"

        row_set = RowSet()
        row_set.add_row_range_from_keys(
            start_key.encode("utf-8"), end_key.encode("utf-8")
        )

        if limit > 0:
            rows = self.bes_table.read_rows(row_set=row_set, limit=limit)
        else:
            rows = self.bes_table.read_rows(row_set=row_set)
        for row in rows:
            event = row.cells["Event"][b""][0]
            logging.debug("%s", event)
            bes_event = bazel_runner_store_pb2.BesEvent()
            bes_event.ParseFromString(event.value)
            yield (
                bes_event.sequence_number,
                bes_event.build_event,
                bes_event.test_summary,
            )


class GcpPersistence(bazel_runner_server_lib.Persistence):
    """Handles the persistence of state information in BigTable."""

    def __init__(self, config: Config, client: bigtable.Client):
        self.config = config
        assert config.gcp
        self.gcp_config = config.gcp
        self.client = client

        self.instance = client.instance(self.gcp_config.bigtable_instance_id)
        self.main_table = self.instance.table(self.gcp_config.bigtable_main_table_name)

    @classmethod
    def create(cls, config: Config):
        client = bigtable.Client()
        return cls(config, client)

    def _mutate_and_check(self, rows):
        _mutate_and_check(self.main_table, rows)

    def _parse_row(self, row: bigtable.row.Row) -> bazel_runner_server_lib.RunInfo:  # type: ignore
        run_id = uuid.UUID(row.row_key.decode("utf-8").split("#")[1])
        main_cells = row.cells["Main"]
        main_cell = main_cells["main".encode("utf-8")][0]
        main_item_proto = bazel_runner_store_pb2.RunMainItem()
        main_item_proto.ParseFromString(main_cell.value)  # type: ignore

        state_proto = bazel_runner_store_pb2.StateItem()
        state_proto.ParseFromString(main_cells["STATE".encode("utf-8")][0].value)  # type: ignore

        test_spec_item: bazel_runner_store_pb2.TestSpecItem | None = (
            _read_proto_from_cell(
                main_cells,
                "TEST_SPEC",
                bazel_runner_store_pb2.TestSpecItem(),
            )
        )
        assert test_spec_item

        init: bazel_runner_store_pb2.InitItem | None = _read_proto_from_cell(
            main_cells,
            "INIT",
            bazel_runner_store_pb2.InitItem(),
        )

        run: bazel_runner_store_pb2.RunItem | None = _read_proto_from_cell(
            main_cells, "RUN", bazel_runner_store_pb2.RunItem(), lambda proto: proto
        )

        error: bazel_runner_store_pb2.ErrorItem | None = _read_proto_from_cell(
            main_cells,
            "ERROR",
            bazel_runner_store_pb2.ErrorItem(),
        )

        abort: bazel_runner_store_pb2.AbortItem | None = _read_proto_from_cell(
            main_cells,
            "ABORT",
            bazel_runner_store_pb2.AbortItem(),
        )

        processing_error: bazel_runner_store_pb2.ProcessingErrorItem | None = (
            _read_proto_from_cell(
                main_cells,
                "PROCESSING_ERROR",
                bazel_runner_store_pb2.ProcessingErrorItem(),
            )
        )

        done: bazel_runner_store_pb2.DoneItem | None = _read_proto_from_cell(
            main_cells,
            "DONE",
            bazel_runner_store_pb2.DoneItem(),
        )

        cancel: bazel_runner_store_pb2.CancelItem | None = _read_proto_from_cell(
            main_cells,
            "CANCEL",
            bazel_runner_store_pb2.CancelItem(),
        )

        run_info = bazel_runner_server_lib.RunInfo(
            run_id=run_id,
            state=bazel_runner_server_lib.StateItem(current_state=state_proto.state),
            create_time=main_item_proto.create_time.ToDatetime().replace(
                tzinfo=datetime.timezone.utc
            ),
            last_state_change_time=state_proto.time.ToDatetime().replace(
                tzinfo=datetime.timezone.utc
            ),
            requestor=main_item_proto.requestor,  # type: ignore
            tags=main_item_proto.tags,  # type: ignore
            supersedes=main_item_proto.supersedes,  # type: ignore
            test_selection=main_item_proto.test_selection
            if main_item_proto.HasField("test_selection")
            else None,  # type: ignore
            notification=main_item_proto.notification
            if main_item_proto.HasField("notification")
            else None,  # type: ignore
            test_spec=test_spec_item,
            init=init,
            run=run,
            error=error,
            abort=abort,
            processing_error=processing_error,
            done=done,
            cancel=cancel,
        )
        return run_info

    def batch_get_run_info(
        self, run_ids: list[uuid.UUID]
    ) -> typing.Iterable[bazel_runner_server_lib.RunInfo]:
        """Returns information about a given run id."""

        rowset = bigtable.row_set.RowSet()  # type: ignore
        for deploy_id in run_ids:
            rowset.add_row_key(f"run#{deploy_id}".encode("utf-8"))
        rows = self.main_table.read_rows(row_set=rowset)
        for row in rows:
            run_info = self._parse_row(row)
            yield run_info

    def get_run_info(
        self, run_id: uuid.UUID
    ) -> Optional[bazel_runner_server_lib.RunInfo]:
        """Returns information about a given run id."""

        row_key = f"run#{run_id}"
        row = self.main_table.read_row(row_key)
        if not row:
            return None
        return self._parse_row(row)

    def create_run(
        self,
        run_id: uuid.UUID,
        test_execution: bazel_runner_pb2.BazelRunnerExecutionGroupSpec,
        requestor: str,
        tags: typing.Sequence[str],
        supersedes: bool,
        test_selection: test_runner_pb2.TestSelectionSpec | None,
        notification: test_runner_pb2.NotificationSpec | None,
    ):
        """Creates a new run."""
        create_time = datetime.datetime.now(datetime.timezone.utc)
        main_item = bazel_runner_store_pb2.RunMainItem()
        main_item.create_time.FromDatetime(create_time)
        main_item.requestor = requestor
        main_item.tags.extend(tags)
        main_item.supersedes = supersedes
        if notification:
            main_item.notification.MergeFrom(notification)

        if test_selection:
            main_item.test_selection.MergeFrom(test_selection)

        rows = []
        row_key = f"run#{run_id}".encode("utf-8")
        row = self.main_table.direct_row(row_key=row_key)
        rows.append(row)
        row.set_cell("Main", "main", main_item.SerializeToString())

        state = bazel_runner_store_pb2.StateItem()
        state.state = "INIT"
        state.time.FromDatetime(create_time)
        row.set_cell(
            "Main",
            "STATE",
            value=state.SerializeToString(),
        )

        row.set_cell(
            "Main",
            "TEST_SPEC",
            bazel_runner_store_pb2.TestSpecItem(
                test_spec=test_execution
            ).SerializeToString(),
        )

        row_key = b"pending#"
        row = self.main_table.direct_row(row_key=row_key)
        rows.append(row)
        run_id_bytes = str(run_id).encode("utf-8")
        row.set_cell("RunId", run_id_bytes, run_id_bytes)

        reversed_timestamp = hex(sys.maxsize - int(create_time.timestamp() * 1000))
        for tag in tags:
            row_key = f"tag#{tag}#{reversed_timestamp}".encode("utf-8")
            row = self.main_table.direct_row(row_key=row_key)
            rows.append(row)
            row.set_cell("RunId", "run_id", run_id_bytes)

        row_key = f"requestor#{requestor}#{reversed_timestamp}".encode("utf-8")
        row = self.main_table.direct_row(row_key=row_key)
        rows.append(row)
        row.set_cell("RunId", "run_id", run_id_bytes)

        row_key: bytes = get_timestamp_key(run_id)
        row = self.main_table.direct_row(row_key=row_key)
        rows.append(row)
        row.set_cell("Timestamp", "timestamp", run_id.bytes)

        self._mutate_and_check(rows)

    def get_runs(
        self,
        oldest_run_id: uuid.UUID | None,
        newest_run_id: uuid.UUID | None,
        oldest_inclusive: bool,
        newest_inclusive: bool,
        max_count: int,
    ) -> typing.Iterator[bazel_runner_server_lib.RunInfo]:
        """Returns the previous runs.

        Args:
            start_run_id: the earliest run id to return
            end_run_id: the latest runs id to return
            max_count: the maximum number of runs to return

        Returns:
            The runs. The runs are returned in reverse order,
            i.e., the latest runs is returned first.
        """
        logging.info(
            "Getting runs from %s to %s: max count %s",
            oldest_run_id,
            newest_run_id,
            max_count,
        )

        row_set = bigtable.row_set.RowSet()  # type: ignore
        if newest_run_id:
            start_key = get_timestamp_key(newest_run_id)
        else:
            start_key = b"timestamp#v2#"
        if oldest_run_id:
            end_key = get_timestamp_key(oldest_run_id)
        else:
            end_key = b"timestamp#v2$"
        logging.info("Start key: %s", start_key)
        logging.info("End key: %s", end_key)
        row_set.add_row_range_from_keys(
            start_key=start_key,
            end_key=end_key,
            start_inclusive=newest_inclusive,
            end_inclusive=oldest_inclusive,
        )
        rows = self.main_table.read_rows(
            row_set=row_set,
        )
        count = 0
        run_ids = []
        for row in rows:
            logging.info("Row: %s", row.row_key.hex())
            cells = row.cells["Timestamp"]
            main_cell = cells["timestamp".encode("utf-8")][0]
            deploy_id = uuid.UUID(bytes=main_cell.value)
            run_ids.append(deploy_id)
            count += 1
            if count >= max_count:
                break

        logging.info("run ids: %s", run_ids)
        if not run_ids:
            return
        infos = list(self.batch_get_run_info(run_ids))
        infos.sort(key=lambda info: info.run_id, reverse=True)
        for info in infos:
            yield info

    def update_init_state(self, run_id: uuid.UUID, operation_id: str):
        """Updates the init state of a run."""

        current_time = datetime.datetime.now(datetime.timezone.utc)
        current_time_proto = timestamp_pb2.Timestamp()  # type: ignore
        current_time_proto.FromDatetime(current_time)  # type: ignore

        rows = []
        row_key = f"run#{run_id}"
        row = self.main_table.direct_row(row_key=row_key)
        rows.append(row)
        row.set_cell(
            "Main",
            "INIT",
            bazel_runner_store_pb2.InitItem(
                operation_id=operation_id, time=current_time_proto
            ).SerializeToString(),
        )
        self._mutate_and_check(rows)

    def update_test_spec(
        self,
        run_id: uuid.UUID,
        test_spec: bazel_runner_pb2.BazelRunnerExecutionGroupSpec,
    ):
        """Updates the test spec of a run."""
        rows = []
        row_key = f"run#{run_id}"
        row = self.main_table.direct_row(row_key=row_key)
        rows.append(row)
        row.set_cell(
            "Main",
            "TEST_SPEC",
            bazel_runner_store_pb2.TestSpecItem(
                test_spec=test_spec
            ).SerializeToString(),
        )
        self._mutate_and_check(rows)

    def move_to_run_state(
        self,
        run_id: uuid.UUID,
        jobs: typing.Sequence[bazel_runner_store_pb2.RunJobItem],
    ):
        rows = []
        row_key = f"run#{run_id}"
        row = self.main_table.direct_row(row_key=row_key)
        rows.append(row)
        current_time = datetime.datetime.now(datetime.timezone.utc)
        current_time_proto = timestamp_pb2.Timestamp()  # type: ignore
        current_time_proto.FromDatetime(current_time)  # type: ignore

        state = bazel_runner_store_pb2.StateItem()
        state.state = "RUN"
        state.time.MergeFrom(current_time_proto)
        row.set_cell(
            "Main",
            "STATE",
            value=state.SerializeToString(),
        )

        row.set_cell(
            "Main",
            "RUN",
            bazel_runner_store_pb2.RunItem(
                run_jobs=jobs,
                time=current_time_proto,
            ).SerializeToString(),
        )

        self._mutate_and_check(rows)

    def _remove_from_pending(self, run_id: uuid.UUID):
        pending_row_key = "pending#"
        run_id_bytes = str(run_id).encode("utf-8")
        pending_row = self.main_table.row(pending_row_key)
        pending_row.delete_cell(column_family_id="RunId", column=run_id_bytes)  # type: ignore
        pending_row.commit()

    def move_to_error_state(self, run_id: uuid.UUID, message: str):
        rows = []
        row_key = f"run#{run_id}"
        row = self.main_table.direct_row(row_key=row_key)
        rows.append(row)
        current_time = datetime.datetime.now(datetime.timezone.utc)
        current_time_proto = timestamp_pb2.Timestamp()  # type: ignore
        current_time_proto.FromDatetime(current_time)  # type: ignore

        state = bazel_runner_store_pb2.StateItem()
        state.state = "ERROR"  # type: ignore
        state.time.MergeFrom(current_time_proto)  # type: ignore
        row.set_cell(
            "Main",
            "STATE",
            value=state.SerializeToString(),  # type: ignore
        )

        row.set_cell(
            "Main",
            "ERROR",
            bazel_runner_store_pb2.ErrorItem(  # type: ignore
                message=message, time=current_time_proto
            ).SerializeToString(),
        )

        self._mutate_and_check(rows)

        self._remove_from_pending(run_id)

    def cancel_run(self, run_id: uuid.UUID, cancelled_by: str | None = None):
        rows = []
        row_key = f"run#{run_id}"
        row = self.main_table.direct_row(row_key=row_key)
        rows.append(row)
        current_time = datetime.datetime.now(datetime.timezone.utc)
        current_time_proto = timestamp_pb2.Timestamp()  # type: ignore
        current_time_proto.FromDatetime(current_time)  # type: ignore

        row.set_cell(
            "Main",
            "CANCEL",
            bazel_runner_store_pb2.CancelItem(  # type: ignore
                cancelled=True,
                time=current_time_proto,
                cancelled_by=cancelled_by if cancelled_by else "",
            ).SerializeToString(),  # type: ignore
        )

        self._mutate_and_check(rows)

    def move_to_abort_state(self, run_id: uuid.UUID, message: str):
        rows = []
        row_key = f"run#{run_id}"
        row = self.main_table.direct_row(row_key=row_key)
        rows.append(row)
        current_time = datetime.datetime.now(datetime.timezone.utc)
        current_time_proto = timestamp_pb2.Timestamp()  # type: ignore
        current_time_proto.FromDatetime(current_time)  # type: ignore

        state = bazel_runner_store_pb2.StateItem()
        state.state = "ABORT"  # type: ignore
        state.time.MergeFrom(current_time_proto)  # type: ignore
        row.set_cell(
            "Main",
            "STATE",
            value=state.SerializeToString(),  # type: ignore
        )

        row.set_cell(
            "Main",
            "ABORT",
            bazel_runner_store_pb2.AbortItem(  # type: ignore
                message=message, time=current_time_proto
            ).SerializeToString(),  # type: ignore
        )

        self._mutate_and_check(rows)

        self._remove_from_pending(run_id)

    def mark_processing_error(
        self, run_id: uuid.UUID, run_info: bazel_runner_server_lib.RunInfo
    ):
        rows = []
        row_key = f"run#{run_id}"
        row = self.main_table.direct_row(row_key=row_key)
        rows.append(row)
        current_time = datetime.datetime.now(datetime.timezone.utc)
        current_time_proto = timestamp_pb2.Timestamp()  # type: ignore
        current_time_proto.FromDatetime(current_time)  # type: ignore

        new_count = (
            1 if not run_info.processing_error else run_info.processing_error.count + 1
        )

        row.set_cell(
            "Main",
            "PROCESSING_ERROR",
            bazel_runner_store_pb2.ProcessingErrorItem(
                count=new_count
            ).SerializeToString(),  # type: ignore
        )

        self._mutate_and_check(rows)

    def move_to_cancelled_state(
        self,
        run_id: uuid.UUID,
    ) -> datetime.datetime:
        logging.info("Move to cancelled state: run_id=%s", run_id)
        rows = []
        row_key = f"run#{run_id}"
        row = self.main_table.direct_row(row_key=row_key)
        rows.append(row)
        current_time = datetime.datetime.now(datetime.timezone.utc)
        current_time_proto = timestamp_pb2.Timestamp()  # type: ignore
        current_time_proto.FromDatetime(current_time)  # type: ignore

        state = bazel_runner_store_pb2.StateItem()
        state.state = "CANCELLED"  # type: ignore
        state.time.MergeFrom(current_time_proto)  # type: ignore
        row.set_cell(
            "Main",
            "STATE",
            value=state.SerializeToString(),  # type: ignore
        )

        row.set_cell(
            "Main",
            "DONE",
            bazel_runner_store_pb2.DoneItem(
                time=current_time_proto
            ).SerializeToString(),  # type: ignore
        )
        self._mutate_and_check(rows)

        self._remove_from_pending(run_id)
        return current_time

    def move_to_done_state(
        self,
        run_id: uuid.UUID,
    ) -> datetime.datetime:
        logging.info("Move to done state: run_id=%s", run_id)
        rows = []
        row_key = f"run#{run_id}"
        row = self.main_table.direct_row(row_key=row_key)
        rows.append(row)
        current_time = datetime.datetime.now(datetime.timezone.utc)
        current_time_proto = timestamp_pb2.Timestamp()  # type: ignore
        current_time_proto.FromDatetime(current_time)  # type: ignore

        state = bazel_runner_store_pb2.StateItem()
        state.state = "DONE"
        state.time.MergeFrom(current_time_proto)
        row.set_cell(
            "Main",
            "STATE",
            value=state.SerializeToString(),
        )

        row.set_cell(
            "Main",
            "DONE",
            bazel_runner_store_pb2.DoneItem(
                time=current_time_proto
            ).SerializeToString(),
        )

        self._mutate_and_check(rows)

        self._remove_from_pending(run_id)
        return current_time

    def get_pending_runs(self) -> list[uuid.UUID]:
        """Returns the run ids of all pending runs."""

        prefix = "pending#"
        end_key = "pending$"

        row_set = RowSet()
        row_set.add_row_range_from_keys(prefix.encode("utf-8"), end_key.encode("utf-8"))

        rows = self.main_table.read_rows(row_set=row_set)
        result = []
        for row in rows:
            for cell_key in row.cells["RunId"]:
                run_id = cell_key.decode("utf-8")
                try:
                    u = uuid.UUID(run_id)
                except ValueError:
                    u = None
                if u:
                    result.append(u)
        return result

    def search_runs_by_tag(
        self, tag: str, max_results: int
    ) -> typing.Generator[uuid.UUID, None, None]:
        """Generates all runs that match the given tag in time-reserved order, the newest first."""

        prefix = f"tag#{tag}#"
        end_key = f"tag#{tag}$"

        row_set = RowSet()
        row_set.add_row_range_from_keys(prefix.encode("utf-8"), end_key.encode("utf-8"))

        rows = self.main_table.read_rows(row_set=row_set)
        count = 0
        for row in rows:
            cells = row.cells["RunId"]
            run_id_column = "run_id".encode("utf-8")
            if run_id_column not in cells:
                continue
            cell = cells[run_id_column][0]
            run_id = cell.value.decode("utf-8")
            yield uuid.UUID(run_id)
            count += 1
            if count >= max_results:
                return

    def search_runs_by_requestor(
        self, requestor: str, max_results: int
    ) -> Generator[uuid.UUID, None, None]:
        """Generates all runs that match the given requestor in time-reserved order, the newest first."""
        prefix = f"requestor#{requestor}#"
        end_key = f"requestor#{requestor}$"

        row_set = RowSet()
        row_set.add_row_range_from_keys(prefix.encode("utf-8"), end_key.encode("utf-8"))

        rows = self.main_table.read_rows(row_set=row_set)
        count = 0
        for row in rows:
            cells = row.cells["RunId"]
            run_id_column = "run_id".encode("utf-8")
            if run_id_column not in cells:
                continue
            cell = cells[run_id_column][0]
            run_id = cell.value.decode("utf-8")
            yield uuid.UUID(run_id)
            count += 1
            if count >= max_results:
                return


def transform_target_name_to_path(target_name: str) -> str:
    """Transforms the target name to a path."""
    if not target_name.startswith("//"):
        logging.error("Invalid target name: %s", target_name)
        raise ValueError("Invalid target name")
    suffix = target_name[2:]
    if suffix.startswith(":"):
        suffix = suffix[1:]
    return suffix.replace(":", "/")


class TextLog:
    def __init__(self, blob_stream):
        self.blob_stream = blob_stream

    def read(self, size) -> str:
        try:
            return self.blob_stream.read(size)
        except google.api_core.exceptions.NotFound as exc:
            raise bazel_runner_server_lib.BazelRunnerException(
                grpc.StatusCode.NOT_FOUND, "Object not found"
            ) from exc


class BinaryLog:
    def __init__(self, blob_stream):
        self.blob_stream = blob_stream

    def read(self, size) -> bytes:
        try:
            return self.blob_stream.read(size)
        except google.api_core.exceptions.NotFound as exc:
            raise bazel_runner_server_lib.BazelRunnerException(
                grpc.StatusCode.NOT_FOUND, "Object not found"
            ) from exc


class GcpObjectAccess(bazel_runner_server_lib.ObjectAccess):
    """Class to manage the access to the objects stored in Cloud Storage."""

    def __init__(self, config, client, bucket):
        self.config = config
        assert config.gcp
        self.gcp_config = config.gcp
        self.client = client
        self.bucket = bucket
        self.storage_output_path = pathlib.Path(config.gcp.storage_output_path)
        self.bucket_name = config.gcp.bucket

    @classmethod
    def create(cls, config):
        assert config.gcp
        client = storage.Client()
        bucket = client.bucket(config.gcp.bucket)
        return cls(config, client, bucket)

    def get_test_suites_xml_content(
        self, run_id: uuid.UUID, job_id: uuid.UUID, target_name: str
    ) -> Optional[bytes]:
        """Returns the content of the test.xml file for given target."""
        try:
            if not target_name.startswith("//"):
                return None
            blob_id = transform_target_name_to_path(target_name) + "/test.xml"
            testlogs_path = (
                self.storage_output_path
                / str(run_id)
                / str(job_id)
                / "testlogs"
                / blob_id
            )
            logging.info("test suites archive: %s", testlogs_path)
            blob = self.bucket.blob(str(testlogs_path))
            return blob.download_as_bytes()
        except google.api_core.exceptions.NotFound:
            return None

    def open_command_log(
        self, run_id: uuid.UUID, job_id: uuid.UUID
    ) -> bazel_runner_server_lib.TextProtocol | None:
        """Returns a stream to a command log of a given job."""
        command_log_path = (
            self.storage_output_path / str(run_id) / str(job_id) / "command.log"
        )
        try:
            blob = self.bucket.blob(str(command_log_path))
            s = blob.open(mode="r")
            return TextLog(s)
        except google.api_core.exceptions.NotFound:
            return None

    def get_return_code(self, run_id: uuid.UUID, job_id: uuid.UUID) -> int:
        """Returns the return code of a given job."""
        try:
            command_log_path = (
                self.storage_output_path / str(run_id) / str(job_id) / "returncode.txt"
            )
            blob = self.bucket.blob(str(command_log_path))
            contents = blob.download_as_string()
            return int(contents)
        except google.api_core.exceptions.NotFound as exc:
            raise bazel_runner_server_lib.BazelRunnerException(
                grpc.StatusCode.NOT_FOUND, "Object not found"
            ) from exc

    def open_archive(
        self, run_id: uuid.UUID, job_id: uuid.UUID, blob_id: str
    ) -> Optional[bazel_runner_server_lib.BinaryProtocol]:
        """Returns an archive in a job as a binary stream."""
        testlogs_path = (
            self.storage_output_path / str(run_id) / str(job_id) / "testlogs" / blob_id
        )
        try:
            blob = self.bucket.blob(str(testlogs_path))
            s = blob.open(mode="rb")  # type: ignore
            return BinaryLog(s)
        except google.api_core.exceptions.NotFound:
            return None

    def open_bep(
        self, run_id: uuid.UUID, job_id: uuid.UUID
    ) -> Optional[bazel_runner_server_lib.BinaryProtocol]:
        """Returns the Build Event Protocol file of a job as a binary stream."""
        bep_path = self.storage_output_path / str(run_id) / str(job_id) / "bep.pb"

        try:
            blob = self.bucket.blob(str(bep_path))
            s = blob.open("rb")
            return BinaryLog(s)
        except google.api_core.exceptions.NotFound:
            return None

    def check_abort(self, run_id: uuid.UUID) -> str | None:
        """Checks if the previous step aborted the run."""
        output_directory = self.storage_output_path / str(run_id)
        aborted_file = output_directory / "ABORTED"
        logging.info("Check for abort %s", aborted_file)

        try:
            blob = self.bucket.blob(str(aborted_file))
            contents = blob.download_as_string()
            return contents
        except google.api_core.exceptions.NotFound:
            return None

    def list_archive(self, run_id: uuid.UUID, job_id: uuid.UUID) -> list[str]:
        """Lists all names of the archived files for a given job."""
        testlogs_path = (
            self.storage_output_path / str(run_id) / str(job_id) / "testlogs"
        )
        blobs = self.client.list_blobs(self.bucket_name, prefix=f"{testlogs_path}/")
        result = []
        for blob in blobs:
            archive_path = blob.name
            relative_path = str(archive_path)[len(str(testlogs_path)) + 1 :]
            result.append(relative_path)
        return result

    def list_target_archive(
        self, run_id: uuid.UUID, job_id: uuid.UUID, target_name: str
    ) -> list[bazel_runner_server_lib.Archive]:
        """Lists all names of the archived files for a given job."""
        try:
            blob_id = transform_target_name_to_path(target_name)
            testlogs_path = (
                self.storage_output_path
                / str(run_id)
                / str(job_id)
                / "testlogs"
                / blob_id
            )
            logging.info("test suites archive: %s", testlogs_path)
            blobs = self.client.list_blobs(self.bucket_name, prefix=f"{testlogs_path}/")
            result = []
            for blob in blobs:
                archive_path = blob.name
                relative_path = str(archive_path)[len(str(testlogs_path)) + 1 :]
                result.append(
                    bazel_runner_server_lib.Archive(
                        target_name_path=blob_id, relative_path=relative_path
                    )
                )
            return result
        except google.api_core.exceptions.NotFound:
            return []

    def get_output_prefix(self, run_id: uuid.UUID) -> str:
        output = self.storage_output_path / str(run_id)
        return f"{self.bucket_name}/{output}"
