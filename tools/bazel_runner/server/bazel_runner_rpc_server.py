"""GRPC server for the bazel runner service."""

# pyright: reportOptionalMemberAccess=false
import os
import argparse
import logging
import pathlib
import uuid
import re
import typing
from concurrent.futures import ThreadPoolExecutor
from contextlib import contextmanager

import grpc
import structlog
import ulid
from grpc_health.v1 import health, health_pb2_grpc
from grpc_reflection.v1alpha import reflection
from prometheus_client import Counter, start_http_server

from base.logging.struct_logging import setup_struct_logging
from base.python.cloud import gcp
from tools.bazel_runner.bep_parser.test_case_parser import parse_test_suites_xml_content
from tools.bazel_runner.server import (
    bazel_runner_server_gcp_lib,
    bazel_runner_server_lib,
    test_runner_pb2,
    test_runner_pb2_grpc,
)
from tools.bazel_runner.server.config import Config

log = structlog.get_logger()

_test_run_created = Counter(
    "bazel_runner_run_created",
    "Gauge for the number of created test runs",
)


@contextmanager
def _handle_exceptions(context: grpc.ServicerContext):
    try:
        yield
    except bazel_runner_server_lib.BazelRunnerException as ex:
        logging.error("Error while handling rpc: %s", ex)
        logging.exception(ex)
        context.abort(ex.status_code, ex.msg)
    except Exception as ex:  # pylint: disable=broad-exception-caught
        logging.error("Error while handling rpc: %s", ex)
        logging.exception(ex)
        raise


def archive_path_to_response(
    archive_path: str,
) -> test_runner_pb2.GetTestLogsResponse | None:
    response = test_runner_pb2.GetTestLogsResponse()
    m = re.match(r"^shard_([0-9]+)_of_([0-9]+)/(.*)$", archive_path)
    if m:
        response.shard_index = int(m.group(1))
        response.shard_count = int(m.group(2))
        archive_path = m.group(3)
    m = re.match(r"^test_attempts/attempt_([0-9]+).log$", archive_path)
    if m:
        response.attempt_index = int(m.group(1))
        return response
    m = re.match(r"^test.log$", archive_path)
    if m:
        return response
    return None


class TestRunnerServicerImpl(test_runner_pb2_grpc.TestRunnerServicer):
    """Implementation of the server interface of the test runner servicer."""

    def __init__(
        self,
        config: Config,
        persistence: bazel_runner_server_lib.Persistence,
        build_event_persistence: bazel_runner_server_lib.BuildEventPersistence,
        object_access: bazel_runner_server_lib.ObjectAccess,
        queue: bazel_runner_server_lib.Queue,
    ):
        self.persistence = persistence
        self.build_event_persistence = build_event_persistence
        self.config = config
        self.object_access = object_access
        self.queue = queue

    def _validate_request(self, request: test_runner_pb2.ScheduleTestRequest, context):
        """Validate the request.

        Args:
            request: the request
            context: the context
        """
        if not request.HasField("test_execution"):
            context.abort(
                code=grpc.StatusCode.INVALID_ARGUMENT, details="Missing test_execution"
            )
        assert request.test_execution

        # runs needs to be set if test_selection is not set
        if len(request.test_execution.runs) == 0 and not request.HasField(
            "test_selection"
        ):
            context.abort(
                code=grpc.StatusCode.INVALID_ARGUMENT, details="Missing test_execution"
            )

        # validate the test execution runs if they are set
        for run in request.test_execution.runs:
            if run.command not in ("build", "test", "run"):
                context.abort(
                    code=grpc.StatusCode.INVALID_ARGUMENT, details="Invalid command"
                )
            if len(run.targets) == 0:
                context.abort(
                    code=grpc.StatusCode.INVALID_ARGUMENT, details="Invalid targets"
                )

        # checkout needs to be set
        if not request.test_execution.HasField("checkout"):
            context.abort(
                code=grpc.StatusCode.INVALID_ARGUMENT, details="Missing checkout"
            )
        # pull_request_checkout or commit_checkout needs to be set
        if not request.test_execution.checkout.HasField(
            "pull_request_checkout"
        ) and not request.test_execution.checkout.HasField("commit_checkout"):
            context.abort(
                grpc.StatusCode.INVALID_ARGUMENT, "Invalid test_execution.checkout"
            )

        # owner and repo_name needs to be set
        if not request.test_execution.checkout.owner:
            context.abort(
                code=grpc.StatusCode.INVALID_ARGUMENT, details="Invalid owner"
            )
        if not request.test_execution.checkout.repo_name:
            context.abort(
                code=grpc.StatusCode.INVALID_ARGUMENT, details="Invalid repo_name"
            )

        # supersedes only valid for pull request-based tests
        if (
            request.supersedes
            and not request.test_execution.checkout.pull_request_checkout.pull_request_number
        ):
            context.abort(
                code=grpc.StatusCode.INVALID_ARGUMENT,
                details="Superseding only valid for pull request-based tests.",
            )

        if request.HasField("test_selection"):
            test_selection = request.test_selection
            checkout = request.test_execution.checkout

            if len(request.test_execution.runs) > 0:
                context.abort(
                    code=grpc.StatusCode.INVALID_ARGUMENT,
                    details="Test selection and test execution runs cannot be set at the same time.",
                )

            if not test_selection.HasField("start_checkout") and not checkout.HasField(
                "pull_request_checkout"
            ):
                context.abort(
                    grpc.StatusCode.INVALID_ARGUMENT, "Missing start_checkout"
                )
            if test_selection.HasField("start_checkout"):
                if not test_selection.start_checkout.HasField(
                    "pull_request_checkout"
                ) and not test_selection.start_checkout.HasField("commit_checkout"):
                    context.abort(
                        grpc.StatusCode.INVALID_ARGUMENT, "Invalid start_checkout"
                    )

    def ScheduleTest(self, request: test_runner_pb2.ScheduleTestRequest, context):
        """Schedule a new test run."""
        with _handle_exceptions(context):
            logging.info("ScheduleTest: %s", request)

            self._validate_request(request, context)

            if request.supersedes:
                logging.info("Check pre-creation superseding")

                run_ids = self.persistence.get_pending_runs()
                for other_run_id in run_ids:
                    other_run_info = self.persistence.get_run_info(other_run_id)
                    # do not return this run if same PR, same repo, same owner, same requestor
                    if (
                        other_run_info
                        and other_run_info.test_spec
                        and other_run_info.test_spec.test_spec.checkout.pull_request_checkout.pull_request_number
                        == request.test_execution.checkout.pull_request_checkout.pull_request_number
                        and request.test_execution.checkout.pull_request_checkout.ref
                        and other_run_info.test_spec.test_spec.checkout.pull_request_checkout.ref
                        == request.test_execution.checkout.pull_request_checkout.ref
                        and other_run_info.test_spec.test_spec.checkout.repo_name
                        == request.test_execution.checkout.repo_name
                        and other_run_info.test_spec.test_spec.checkout.owner
                        == request.test_execution.checkout.owner
                        and other_run_info.requestor == request.requestor
                        and not other_run_info.state.is_final()
                    ):
                        logging.info(
                            "Canceling creation based on superseded run: %s",
                            other_run_id,
                        )
                        context.abort(
                            grpc.StatusCode.ALREADY_EXISTS,
                            "Superseded by run {}".format(other_run_id),
                        )

            run_id = ulid.ULID().to_uuid()
            logging.info("Creating run %s", run_id)
            self.persistence.create_run(
                run_id,
                request.test_execution,
                requestor=request.requestor,
                tags=request.tags,
                supersedes=request.supersedes,
                test_selection=(
                    request.test_selection
                    if request.HasField("test_selection")
                    else None
                ),
                notification=(
                    request.notification if request.HasField("notification") else None
                ),
            )
            self.queue.notify(run_id)

            if request.supersedes:
                logging.info("Run %s supersedes another runs", run_id)

                run_ids = self.persistence.get_pending_runs()
                for other_run_id in run_ids:
                    if other_run_id == run_id:
                        # we don't cancel ourself
                        continue
                    other_run_info = self.persistence.get_run_info(other_run_id)
                    # cancel if same PR, same repo, same owner, same requestor
                    if (
                        other_run_info
                        and other_run_info.test_spec
                        and other_run_info.test_spec.test_spec.checkout.pull_request_checkout.pull_request_number
                        == request.test_execution.checkout.pull_request_checkout.pull_request_number
                        and other_run_info.test_spec.test_spec.checkout.repo_name
                        == request.test_execution.checkout.repo_name
                        and other_run_info.test_spec.test_spec.checkout.owner
                        == request.test_execution.checkout.owner
                        and other_run_info.requestor == request.requestor
                    ):
                        logging.info("Canceling superseded run: %s", other_run_id)
                        self.persistence.cancel_run(
                            other_run_id, cancelled_by=""
                        )  # automatic cancellation
                        self.queue.notify(other_run_id)

            response = test_runner_pb2.ScheduleTestResponse()
            response.run_id = str(run_id)
            _test_run_created.inc()
            return response

    def _supersede(
        self, run_id: uuid.UUID, request: test_runner_pb2.ScheduleTestRequest
    ):
        run_ids = self.persistence.get_pending_runs()
        for other_run_id in run_ids:
            if other_run_id == run_id:
                # we don't cancel ourself
                continue
            other_run_info = self.persistence.get_run_info(other_run_id)
            # cancel if same PR, same repo, same owner, same requestor
            if (
                other_run_info
                and other_run_info.test_spec
                and other_run_info.test_spec.test_spec.checkout.pull_request_checkout.pull_request_number
                == request.test_execution.checkout.pull_request_checkout.pull_request_number
                and other_run_info.test_spec.test_spec.checkout.repo_name
                == request.test_execution.checkout.repo_name
                and other_run_info.test_spec.test_spec.checkout.owner
                == request.test_execution.checkout.owner
                and other_run_info.requestor == request.requestor
                and not (other_run_info.cancel and other_run_info.cancel.cancelled)
            ):
                logging.info("Canceling superseded run: %s", other_run_id)
                self.persistence.cancel_run(
                    other_run_id, cancelled_by=""
                )  # automatic cancellation
                self.queue.notify(other_run_id)

    def CancelTest(
        self, request: test_runner_pb2.CancelTestRequest, context: grpc.ServicerContext
    ) -> test_runner_pb2.CancelTestResponse:
        """Cancel a pending test run."""
        with _handle_exceptions(context):
            logging.info("CancelTest: %s", request)
            run_id = uuid.UUID(request.run_id)
            run_info = self.persistence.get_run_info(run_id)
            if not run_info:
                context.abort(
                    code=grpc.StatusCode.NOT_FOUND, details=f"No run {run_id}"
                )
            if run_info.state.is_final():
                context.abort(
                    code=grpc.StatusCode.FAILED_PRECONDITION,
                    details="Run already in final state",
                )
            if run_info.cancel and run_info.cancel.cancelled:
                # Cancel is idempotent
                logging.info("Run is already cancelled")
                response = test_runner_pb2.CancelTestResponse()
                return response
            self.persistence.cancel_run(
                run_id,
                cancelled_by=request.cancelled_by if request.cancelled_by else None,
            )
            self.queue.notify(run_id)
            response = test_runner_pb2.CancelTestResponse()
            return response

    def GetTestInfo(
        self, request: test_runner_pb2.GetTestInfoRequest, context: grpc.ServicerContext
    ) -> test_runner_pb2.GetTestInfoResponse:
        """Get information about the tests."""
        with _handle_exceptions(context):
            logging.info("get test info request %s", request)
            run_id = uuid.UUID(request.run_id)
            run_info = self.persistence.get_run_info(run_id)
            logging.info("get test info run info %s", run_info)
            if not run_info:
                context.abort(
                    code=grpc.StatusCode.NOT_FOUND, details=f"No run {run_id}"
                )
                return test_runner_pb2.GetTestInfoResponse()
            response = test_runner_pb2.GetTestInfoResponse()
            bazel_runner_server_lib.run_info_into_proto(run_info, response)
            logging.info("get test info response %s", response)
            return response

    def GetArchiveData(
        self,
        request: test_runner_pb2.GetArchiveDataRequest,
        context: grpc.ServicerContext,
    ):
        """Get archived data."""
        with _handle_exceptions(context):
            logging.info(request)
            run_id = uuid.UUID(request.run_id)
            job_id = uuid.UUID(request.job_id)
            run_info = self.persistence.get_run_info(run_id)
            if not run_info:
                context.abort(
                    code=grpc.StatusCode.NOT_FOUND, details=f"No run {run_id}"
                )
            if not run_info.run or run_info.state.current_state != "DONE":
                context.abort(
                    code=grpc.StatusCode.FAILED_PRECONDITION, details="Invalid state"
                )
            assert run_info.run
            if not self._check_job_id(run_info, job_id):
                context.abort(code=grpc.StatusCode.NOT_FOUND, details="Not found")
            stream = self.object_access.open_archive(
                run_id, job_id, request.archive_file
            )
            if not stream:
                context.abort(code=grpc.StatusCode.NOT_FOUND, details="Not found")
            assert stream
            data = stream.read(4096)
            while data:
                response = test_runner_pb2.GetArchiveDataResponse()
                response.content = data
                yield response
                data = stream.read(4096)

    def _stream_test_log(
        self,
        run_id: uuid.UUID,
        job_id: uuid.UUID,
        archive_file: str,
        base_response: test_runner_pb2.GetTestLogsResponse,
    ) -> typing.Generator[test_runner_pb2.GetTestLogsResponse, None, None]:
        stream = self.object_access.open_archive(run_id, job_id, archive_file)
        assert stream
        data = stream.read(4096)
        while data:
            response = test_runner_pb2.GetTestLogsResponse()
            response.CopyFrom(base_response)
            response.content = data
            logging.info("response %s", response)
            yield response
            data = stream.read(4096)

    def _check_job_id(
        self,
        run_info: bazel_runner_server_lib.RunInfo | None,
        job_id: uuid.UUID,
    ) -> bool:
        """Checks if the job id is valid for the run info.

        Args:
            run_info: run info
            job_id: job id

        Returns:
            True if the job id is valid.
        """
        if not run_info:
            return False
        if job_id not in [uuid.UUID(job.job_id) for job in run_info.run.run_jobs]:
            return False
        return True

    def GetTestLogs(
        self,
        request: test_runner_pb2.GetTestLogsRequest,
        context: grpc.ServicerContext,
    ) -> typing.Generator[test_runner_pb2.GetTestLogsResponse, None, None]:
        """Get archived data."""
        with _handle_exceptions(context):
            logging.info(request)
            run_id = uuid.UUID(request.run_id)
            job_id = uuid.UUID(request.job_id)
            run_info = self.persistence.get_run_info(run_id)
            if not run_info:
                context.abort(
                    code=grpc.StatusCode.NOT_FOUND, details=f"No run {run_id}"
                )
            if not run_info.run or run_info.state.current_state != "DONE":
                context.abort(
                    code=grpc.StatusCode.FAILED_PRECONDITION, details="Invalid state"
                )
            assert run_info.run
            if not self._check_job_id(run_info, job_id):
                context.abort(code=grpc.StatusCode.NOT_FOUND, details="Not found")

            for archive_file in self.object_access.list_target_archive(
                run_id, job_id, request.target_name
            ):
                logging.info("archive file: %s", archive_file)
                base_response = archive_path_to_response(archive_file.relative_path)
                if not base_response:
                    continue
                yield from self._stream_test_log(
                    run_id,
                    job_id,
                    os.path.join(
                        archive_file.target_name_path, archive_file.relative_path
                    ),
                    base_response,
                )

    def GetCommandLog(
        self,
        request: test_runner_pb2.GetCommandLogRequest,
        context: grpc.ServicerContext,
    ):
        """Get the command line logs for a given run/job."""
        with _handle_exceptions(context):
            logging.info(request)
            run_id = uuid.UUID(request.run_id)
            job_id = uuid.UUID(request.job_id)
            run_info = self.persistence.get_run_info(run_id)
            if not run_info:
                context.abort(
                    code=grpc.StatusCode.NOT_FOUND, details=f"No run {run_id}"
                )
            if not run_info.run:
                logging.error("Run not in matching state: %s", run_info)
                context.abort(
                    code=grpc.StatusCode.FAILED_PRECONDITION,
                    details="Run not in matching state",
                )
            if not self._check_job_id(run_info, job_id):
                context.abort(
                    code=grpc.StatusCode.NOT_FOUND, details=f"No job {job_id}"
                )
            stream = self.object_access.open_command_log(run_id, job_id)
            if not stream:
                context.abort(code=grpc.StatusCode.NOT_FOUND, details="Not found")
            assert stream
            data = stream.read(4096)
            while data:
                response = test_runner_pb2.GetCommandLogResponse()
                response.content = data
                yield response
                data = stream.read(4096)

    def GetBuildEvents(
        self,
        request: test_runner_pb2.GetBuildEventsRequest,
        context: grpc.ServicerContext,
    ):
        """Get information about the tests."""
        with _handle_exceptions(context):
            logging.info("get builds events: %s", request)
            run_id = uuid.UUID(request.run_id)
            job_id = uuid.UUID(request.job_id)
            run_info = self.persistence.get_run_info(run_id)
            if not run_info:
                context.abort(
                    code=grpc.StatusCode.NOT_FOUND, details=f"No run {run_id}"
                )
            if not run_info.run:
                context.abort(
                    code=grpc.StatusCode.FAILED_PRECONDITION,
                    details="Job information not available",
                )
            assert run_info.run
            if not self._check_job_id(run_info, job_id):
                context.abort(
                    code=grpc.StatusCode.NOT_FOUND, details=f"No job {job_id}"
                )

            for (
                sequence_number,
                build_event,
                test_summary,
            ) in self.build_event_persistence.get_build_events(
                job_id,
                min_sequence_number=request.min_sequence_number,
                limit=request.limit,
            ):
                response = test_runner_pb2.GetBuildEventsResponse()
                response.sequence_number = sequence_number
                if build_event:
                    response.build_event.MergeFrom(build_event)
                if test_summary:
                    response.test_summary.MergeFrom(test_summary)
                yield response
            logging.info("get builds events done")

    def GetTestCases(
        self,
        request: test_runner_pb2.GetTestCasesRequest,
        context: grpc.ServicerContext,
    ):
        """Get information about test cases for a given run/job/target."""
        with _handle_exceptions(context):
            logging.info(request)
            run_id = uuid.UUID(request.run_id)
            job_id = uuid.UUID(request.job_id)
            run_info = self.persistence.get_run_info(run_id)
            if not run_info:
                context.abort(
                    code=grpc.StatusCode.NOT_FOUND, details=f"No run {run_id}"
                )
            if not run_info.run:
                context.abort(
                    code=grpc.StatusCode.FAILED_PRECONDITION,
                    details="Run not in matching state",
                )
            assert run_info.run
            if not self._check_job_id(run_info, job_id):
                context.abort(
                    code=grpc.StatusCode.NOT_FOUND, details=f"No job {job_id}"
                )
            content = self.object_access.get_test_suites_xml_content(
                run_id, job_id, request.target_name
            )
            response = test_runner_pb2.GetTestCasesResponse()
            if content is not None:
                response.test_cases.extend(parse_test_suites_xml_content(content))
            return response

    def GetPendingRuns(
        self,
        request: test_runner_pb2.GetPendingRunsRequest,
        context: grpc.ServicerContext,  # pylint: disable=unused-argument
    ):
        """Get the run ids of all pending runs."""
        with _handle_exceptions(context):
            logging.info(request)
            run_ids = self.persistence.get_pending_runs()
            response = test_runner_pb2.GetPendingRunsResponse()
            for run_id in run_ids:
                response.run_ids.append(str(run_id))
            return response

    def SearchRuns(
        self,
        request: test_runner_pb2.SearchRunsRequest,
        context: grpc.ServicerContext,  # pylint: disable=unused-argument
    ):
        """Get the run ids that match the search."""
        with _handle_exceptions(context):
            logging.info(request)
            if not request.tag and not request.requestor:
                context.abort(grpc.StatusCode.INVALID_ARGUMENT, "")
            if request.tag and request.requestor:
                context.abort(grpc.StatusCode.INVALID_ARGUMENT, "")
            if request.tag:
                search = self.persistence.search_runs_by_tag(
                    request.tag, max_results=request.max_results
                )
            else:
                search = self.persistence.search_runs_by_requestor(
                    request.requestor, max_results=request.max_results
                )

            for run_id in search:
                response = test_runner_pb2.SearchRunsResponse()
                response.run_id = str(run_id)
                yield response

    def GetRuns(self, request: test_runner_pb2.GetRunsRequest, context):
        try:
            logging.info("get runs: %s", request)
            oldest_run_id = None
            if request.oldest_run_id:
                try:
                    oldest_run_id = uuid.UUID(request.oldest_run_id)
                except ValueError:
                    context.abort(
                        grpc.StatusCode.INVALID_ARGUMENT, "Invalid oldest_run_id"
                    )
                    return
            newest_run_id = None
            if request.newest_run_id:
                try:
                    newest_run_id = uuid.UUID(request.newest_run_id)
                except ValueError:
                    context.abort(
                        grpc.StatusCode.INVALID_ARGUMENT, "Invalid newest_run_id"
                    )
                    return
            max_count = request.max_count if request.max_count else 20
            if max_count > 100:
                context.abort(
                    grpc.StatusCode.INVALID_ARGUMENT, "max_count must be <= 100"
                )
                return
            if max_count < 1:
                context.abort(
                    grpc.StatusCode.INVALID_ARGUMENT, "max_count must be >= 1"
                )
                return
            current_count = 0
            for run in self.persistence.get_runs(
                oldest_run_id=oldest_run_id,
                newest_run_id=newest_run_id,
                max_count=max_count,
                oldest_inclusive=request.oldest_inclusive,
                newest_inclusive=request.newest_inclusive,
            ):
                response = test_runner_pb2.GetRunsResponse()
                run_response = test_runner_pb2.TestRunInfo()
                bazel_runner_server_lib.run_info_into_proto(run, run_response)
                response.runs.append(run_response)
                current_count += 1
                yield response
            logging.info("Found %s runs", current_count)
        except Exception as ex:  # pylint: disable=broad-exception-caught
            logging.error("get runs failed: %s", ex)
            logging.exception(ex)
            raise


def _serve(config: Config):
    if config.gcp:
        persistence = bazel_runner_server_gcp_lib.GcpPersistence.create(config=config)
        build_event_persistence = (
            bazel_runner_server_gcp_lib.GcpBuildEventPersistence.create(config=config)
        )
        object_access = bazel_runner_server_gcp_lib.GcpObjectAccess.create(config)
        queue = bazel_runner_server_gcp_lib.GcpQueue.create(config)
    else:
        raise ValueError("Invalid config")

    server = grpc.server(ThreadPoolExecutor(max_workers=32))
    health_pb2_grpc.add_HealthServicer_to_server(health.HealthServicer(), server)
    test_runner_pb2_grpc.add_TestRunnerServicer_to_server(
        TestRunnerServicerImpl(
            config, persistence, build_event_persistence, object_access, queue
        ),
        server,
    )
    service_names = (
        test_runner_pb2.DESCRIPTOR.services_by_name["TestRunner"].full_name,
        reflection.SERVICE_NAME,
    )
    reflection.enable_server_reflection(service_names, server)
    server.add_insecure_port("[::]:50051")
    server.start()
    logging.info("Listening on 50051")
    server.wait_for_termination()


def main():
    """Main entry function."""
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--config-file", default=pathlib.Path("/config/config.json"), type=pathlib.Path
    )
    args = parser.parse_args()

    setup_struct_logging()

    config = Config.load_config(args.config_file)
    logging.info("Config %s", config)
    if config.gcp:
        logging.info("Service Account %s", gcp.get_active_gcp_service_account())

    # begin listening for Prometheus requests
    start_http_server(9090)

    _serve(config)


if __name__ == "__main__":
    main()
