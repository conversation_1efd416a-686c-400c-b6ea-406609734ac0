"""Module containing code shared between rpc server and processor."""

import logging
import datetime
import typing
import uuid
from dataclasses import dataclass
from typing import BinaryIO, Protocol, TextIO

import grpc

from third_party.proto.bazel_build import build_event_stream_pb2
from tools.bazel_runner.bep_parser import test_summary_pb2
from tools.bazel_runner.control import bazel_runner_pb2
from tools.bazel_runner.server import test_runner_pb2

from tools.bazel_runner.server import bazel_runner_store_pb2


class BazelRunnerException(Exception):
    """Exception thrown by the bazel runner lib."""

    def __init__(self, status_code: grpc.StatusCode, msg: str):
        self.status_code = status_code
        self.msg = msg

    def __str__(self) -> str:
        return f"BazelRunnerException({self.status_code}, {self.msg})"


@dataclass
class StateItem:
    """Class storing the current state."""

    current_state: str

    def is_final(self) -> bool:
        """Returns true if and only if the state is a final state."""
        return self.current_state in {"ERROR", "ABORT", "DONE", "CANCEL"}


@dataclass
class TestSpecItem:
    """Class storing the test execution spec."""

    test_execution: bazel_runner_pb2.BazelRunnerExecutionGroupSpec


@dataclass
class RunInfo:  # pylint: disable=too-many-instance-attributes
    """Information about a run."""

    run_id: uuid.UUID

    # current state of the run
    state: StateItem

    # create time
    create_time: datetime.datetime

    last_state_change_time: datetime.datetime

    requestor: str

    supersedes: bool

    test_selection: test_runner_pb2.TestSelectionSpec | None

    notification: test_runner_pb2.NotificationSpec | None

    tags: list[str]

    # test spec of the run
    test_spec: bazel_runner_store_pb2.TestSpecItem

    # information about the init state
    init: bazel_runner_store_pb2.InitItem | None

    # information about the run
    run: bazel_runner_store_pb2.RunItem | None

    # information about the error.
    # only set in ERROR state
    error: bazel_runner_store_pb2.ErrorItem | None

    # information about the abort.
    # only set in ABORT state
    abort: bazel_runner_store_pb2.AbortItem | None

    # information about processing errors
    processing_error: bazel_runner_store_pb2.ProcessingErrorItem | None

    # information about the done state.
    # only set in DONE state
    done: bazel_runner_store_pb2.DoneItem | None

    # information about cancellations
    cancel: bazel_runner_store_pb2.CancelItem | None


_STATE_STR_TO_STATE = {
    "INIT": test_runner_pb2.RUN_STATE_INIT,
    "CHECKOUT": test_runner_pb2.RUN_STATE_CHECKOUT,
    "RUN": test_runner_pb2.RUN_STATE_RUN,
    "POSTPROCESSING": test_runner_pb2.RUN_STATE_POSTPROCESSING,
    "ERROR": test_runner_pb2.RUN_STATE_ERROR,
    "ABORT": test_runner_pb2.RUN_STATE_ABORT,
    "CANCELLED": test_runner_pb2.RUN_STATE_CANCEL,
    "DONE": None,
}


def _is_passed(run_info: RunInfo) -> bool:
    if not run_info.done:
        return False
    if run_info.done.job_infos:
        return all(
            all(
                (
                    test_info.status == build_event_stream_pb2.PASSED
                    or test_info.status == build_event_stream_pb2.FLAKY
                )
                for test_info in job_info.tests
            )
            and job_info.return_code == 0
            for job_info in run_info.done.job_infos
        )
    if not run_info.run:
        # we are done, but run is not set, this is a run where
        # test selection didn't find any tests to run.
        return True
    return all(
        all(
            (
                test_info.status == build_event_stream_pb2.PASSED
                or test_info.status == build_event_stream_pb2.FLAKY
            )
            for test_info in job_info.job_info.tests
        )
        and job_info.state == bazel_runner_store_pb2.RunJobItem.State.SUCCEEDED
        and job_info.job_info.return_code == 0
        for job_info in run_info.run.run_jobs
    )


def get_run_state(run_info: RunInfo) -> test_runner_pb2.RunState.ValueType:
    """Returns the current state of the run."""
    state = _STATE_STR_TO_STATE[run_info.state.current_state]
    if state is None:
        assert run_info.done
        # the state is DONE
        if _is_passed(run_info):
            state = test_runner_pb2.RUN_STATE_PASSED
        else:
            state = test_runner_pb2.RUN_STATE_FAILURE
    return state


def run_info_into_proto(run_info: RunInfo, response):
    """Transfers the information from the run info to a protobuf response."""
    response.test_execution.MergeFrom(run_info.test_spec.test_spec)
    if "run_id" in response.DESCRIPTOR.fields_by_name:
        response.run_id = str(run_info.run_id)
    if run_info.run:
        for run_job in run_info.run.run_jobs:
            if run_job.job_info.job_id:
                response.jobs.append(run_job.job_info)

    # legacy support for old runs
    if run_info.done:
        for job_info in run_info.done.job_infos:
            if job_info.job_id in [job.job_id for job in response.jobs]:
                continue
            response.jobs.append(job_info)
    # in legacy mode if the run is not done, we don't have job information

    # this adds empty job information for the run jobs
    if len(response.jobs) == 0 and run_info.run:
        for run_job in run_info.run.run_jobs:
            job_info = test_runner_pb2.JobInfo()
            job_info.job_id = run_job.job_id
            response.jobs.append(job_info)

    state = get_run_state(run_info)
    if state == test_runner_pb2.RUN_STATE_ABORT:
        assert run_info.abort
        response.message = run_info.abort.message
    elif state == test_runner_pb2.RUN_STATE_RUN:
        assert run_info.run
        has_running_jobs = False
        for run_job in run_info.run.run_jobs:
            logging.info("run job: %s: %s", run_job.job_id, run_job.state)
            if run_job.state == bazel_runner_store_pb2.RunJobItem.State.RUNNING:
                has_running_jobs = True
                break
        if not has_running_jobs:
            state = test_runner_pb2.RUN_STATE_RUN_WAITING

    if run_info.cancel and run_info.cancel.cancelled:
        response.cancellation_requested = True
        response.cancelled_by = run_info.cancel.cancelled_by

    response.state = state
    response.create_time.FromDatetime(run_info.create_time)
    response.last_state_change_time.FromDatetime(run_info.last_state_change_time)  # type: ignore
    response.requestor = run_info.requestor
    for tag in run_info.tags:
        response.tags.append(tag)
    response.supersedes = run_info.supersedes

    if run_info.test_selection:
        response.test_selection.MergeFrom(run_info.test_selection)
    if run_info.notification:
        response.notification.MergeFrom(run_info.notification)


class ResultPublisher(Protocol):
    """Wrapper around low-level queue handling."""

    def notify(self, run_info: test_runner_pb2.TestRunInfo) -> None:
        """Notify the queue about a change in the given run."""
        raise NotImplementedError()


class Queue(Protocol):
    """Wrapper around low-level queue handling."""

    def notify(self, run_id: uuid.UUID):
        """Notify the queue about a change in the given run."""
        raise NotImplementedError()

    def receive(self) -> typing.Iterable[uuid.UUID]:
        """Listen to run notification events."""
        raise NotImplementedError()


class BuildEventPersistence(Protocol):
    """Handles the persistence of build event information in a database.

    This is used to store the build events of a job.
    """

    def store(
        self,
        job_id: uuid.UUID,
        invocation_id: uuid.UUID,
        sequence_number: int,
        build_event: build_event_stream_pb2.BuildEvent,
        test_summary: test_summary_pb2.TestSummary | None,
        event_time: datetime.datetime,
    ) -> None:
        """Stores the given build event."""
        raise NotImplementedError()

    def get_build_events(
        self, job_id: uuid.UUID, min_sequence_number: int, limit: int
    ) -> typing.Iterator[
        tuple[
            int,
            build_event_stream_pb2.BuildEvent | None,
            test_summary_pb2.TestSummary | None,
        ],
    ]:
        """Generates all the build events stored for the given job.

        Args:
            job_id: the job id
            min_sequence_number: the minimum sequence number
            limit: the maximum number of events to return

        Only events with a sequence_number greater than the number provided are returned.
        """
        raise NotImplementedError()


class Persistence(Protocol):
    """Handles the persistence of state information in a database."""

    def mark_processing_error(self, run_id: uuid.UUID, run_info: RunInfo) -> None:
        """Marks the processing of the run as failed.

        This tracks of often the processing failed, so either abort at some point or
        do back-off.
        """
        raise NotImplementedError()

    def create_run(
        self,
        run_id: uuid.UUID,
        test_execution: bazel_runner_pb2.BazelRunnerExecutionGroupSpec,
        requestor: str,
        tags: typing.Sequence[str],
        supersedes: bool,
        test_selection: test_runner_pb2.TestSelectionSpec | None,
        notification: test_runner_pb2.NotificationSpec | None,
    ) -> None:
        """Creates a new run.

        Args:
            run_id: the run id
            test_execution: the test execution group spec
            requestor: the requestor
            tags: the tags
            supersedes: if the run supersedes another runs
            test_selection: the test selection
            notification: the notification

        """
        raise NotImplementedError()

    def update_test_spec(
        self,
        run_id: uuid.UUID,
        test_spec: bazel_runner_pb2.BazelRunnerExecutionGroupSpec,
    ) -> None:
        """Updates the test spec of a run."""
        raise NotImplementedError()

    def update_init_state(self, run_id: uuid.UUID, operation_id: str) -> None:
        """Updates the init state of a run."""
        raise NotImplementedError()

    def get_pending_runs(self) -> list[uuid.UUID]:
        """Returns the run ids of all pending runs."""
        raise NotImplementedError()

    def search_runs_by_tag(
        self, tag: str, max_results: int
    ) -> typing.Iterator[uuid.UUID]:
        """Generates all runs that match the given tag in time-reserved order, the newest first."""
        raise NotImplementedError()

    def search_runs_by_requestor(
        self, requestor: str, max_results: int
    ) -> typing.Iterator[uuid.UUID]:
        """Generates all runs that match the given requestor.

        The runs are returned in time-reserved order, the newest first.
        """
        raise NotImplementedError()

    def get_run_info(self, run_id: uuid.UUID) -> RunInfo | None:
        """Returns information about a given run id."""
        raise NotImplementedError()

    def get_runs(
        self,
        oldest_run_id: uuid.UUID | None,
        newest_run_id: uuid.UUID | None,
        oldest_inclusive: bool,
        newest_inclusive: bool,
        max_count: int,
    ) -> typing.Iterator[RunInfo]:
        """Returns the previous runs.

        Args:
            start_run_id: the earliest run id to return
            end_run_id: the latest runs id to return
            max_count: the maximum number of runs to return

        Returns:
            The runs. The runs are returned in reverse order,
            i.e., the latest runs is returned first.
        """
        raise NotImplementedError()

    def move_to_abort_state(self, run_id: uuid.UUID, message: str) -> None:
        """Move to the aborted state."""
        raise NotImplementedError()

    def cancel_run(self, run_id: uuid.UUID, cancelled_by: str | None = None) -> None:
        """Cancel a run.

        This moves the run to the cancel state.

        Args:
            run_id: the run id
            cancelled_by: the user name of the user that cancelled the run. not set for automated cancellations.
        """
        raise NotImplementedError()

    def move_to_error_state(self, run_id: uuid.UUID, message: str) -> None:
        """Move to the error state."""
        raise NotImplementedError()

    def move_to_run_state(
        self,
        run_id: uuid.UUID,
        jobs: typing.Sequence[bazel_runner_store_pb2.RunJobItem],
    ) -> None:
        """Move to the run state.

        Args:
            run_id: run id to update
            job_ids: the job ids of the run
            has_running_jobs: if there is currently a running run job.
                        Running here indicates an actually started pod,
                        not only that a job has been scheduled.

        In this state the run jobs have been created and are either
        executing or waiting to be start (e.g. waiting for a node or test volume).
        In this state, we wait for the run jobs to finish (succeeding or failing)
        and we check for the result of the run jobs.
        """
        raise NotImplementedError()

    def move_to_done_state(self, run_id: uuid.UUID) -> datetime.datetime:
        """Move to the done state."""
        raise NotImplementedError()

    def move_to_cancelled_state(self, run_id: uuid.UUID) -> datetime.datetime:
        """Move to the cancelled state."""
        raise NotImplementedError()


@dataclass
class Archive:
    """Information about an archived file."""

    target_name_path: str
    """The path corresponding to the target name.
    e.g. "services/foo/bar/baz" for target name "//services/foo/bar:baz".
    """

    relative_path: str
    """The relative path of the file without the target name path.
    e.g. test.log or shard_1_of_3/test.log
    """


class TextProtocol(Protocol):
    """Protocol for text streams."""

    def read(self, size: int) -> str:
        """Reads a given number of characters from the stream."""
        raise NotImplementedError()


class BinaryProtocol(Protocol):
    """Protocol for binary streams."""

    def read(self, size: int) -> bytes:
        """Reads a given number of characters from the stream."""
        raise NotImplementedError()


class ObjectAccess(Protocol):
    """Class to manage the access to the objects stored in cheap persistent storage."""

    def list_archive(self, run_id: uuid.UUID, job_id: uuid.UUID) -> list[str]:
        """Lists all names of the archived files for a given job."""
        raise NotImplementedError()

    def get_return_code(self, run_id: uuid.UUID, job_id: uuid.UUID) -> int:
        """Returns the return code of a given job."""
        raise NotImplementedError()

    def get_test_suites_xml_content(
        self, run_id: uuid.UUID, job_id: uuid.UUID, target_name: str
    ) -> bytes | None:
        """Returns the content of the test.xml file for given target."""
        raise NotImplementedError()

    def list_target_archive(
        self, run_id: uuid.UUID, job_id: uuid.UUID, target_name: str
    ) -> list[Archive]:
        """Lists all names of the archived files for a given job."""
        raise NotImplementedError()

    def open_command_log(
        self, run_id: uuid.UUID, job_id: uuid.UUID
    ) -> TextProtocol | None:
        """Returns a stream to a command log of a given job."""
        raise NotImplementedError()

    def open_archive(
        self, run_id: uuid.UUID, job_id: uuid.UUID, blob_id: str
    ) -> BinaryProtocol | None:
        """Returns an archive in a job as a binary stream."""
        raise NotImplementedError()

    def open_bep(self, run_id: uuid.UUID, job_id: uuid.UUID) -> BinaryProtocol | None:
        """Returns the Build Event Protocol file of a job as a binary stream."""
        raise NotImplementedError()

    def check_abort(self, run_id: uuid.UUID) -> str | None:
        """Checks if the previous step aborted the run."""
        raise NotImplementedError()

    def get_output_prefix(self, run_id: uuid.UUID) -> str:
        """Returns the output prefix of a run."""
        raise NotImplementedError()
