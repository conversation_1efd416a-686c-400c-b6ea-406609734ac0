"""Unit tests for the bazel_runner_server_lib module."""

import datetime
import uuid

import pytest

import third_party.proto.bazel_build.build_event_stream_pb2 as build_event_stream_pb2
from tools.bazel_runner.control import bazel_runner_pb2
from tools.bazel_runner.server import bazel_runner_store_pb2
from tools.bazel_runner.server import test_runner_pb2
from tools.bazel_runner.server.bazel_runner_server_lib import (
    RunInfo,
    StateItem,
    TestSpecItem,
    _is_passed,
)


@pytest.fixture
def run_id():
    """Return a fixed run ID for testing."""
    return uuid.UUID("00000000-0000-0000-0000-000000000001")


@pytest.fixture
def create_time():
    """Return a fixed create time for testing."""
    return datetime.datetime(2023, 1, 1, 12, 0, 0)


@pytest.fixture
def last_state_change_time():
    """Return a fixed last state change time for testing."""
    return datetime.datetime(2023, 1, 1, 12, 30, 0)


@pytest.fixture
def base_run_info(run_id, create_time, last_state_change_time):
    """Return a base RunInfo object for testing."""
    test_execution = bazel_runner_pb2.BazelRunnerExecutionGroupSpec()
    test_spec_item = TestSpecItem(test_execution=test_execution)

    return RunInfo(
        run_id=run_id,
        state=StateItem(current_state="DONE"),
        create_time=create_time,
        last_state_change_time=last_state_change_time,
        requestor="test_user",
        supersedes=False,
        test_selection=None,
        notification=None,
        tags=[],
        test_spec=test_spec_item,
        init=None,
        run=None,
        error=None,
        abort=None,
        processing_error=None,
        done=None,
        cancel=None,
    )


def test_is_passed_no_done(base_run_info):
    """Test _is_passed when run_info.done is not set."""
    # When done is not set, _is_passed should return False
    assert not _is_passed(base_run_info)


def test_is_passed_done_job_infos_all_passed(base_run_info):
    """Test _is_passed when all tests in job_infos are passed."""
    # Create a DoneItem with job_infos where all tests passed
    done_item = bazel_runner_store_pb2.DoneItem()

    # Add a job with passed tests
    job_info1 = test_runner_pb2.JobInfo(job_id="job1", return_code=0)
    test_info1 = test_runner_pb2.TestInfo(
        target_name="//path/to:test1", status=build_event_stream_pb2.PASSED
    )
    job_info1.tests.append(test_info1)

    # Add another job with flaky tests (which should also count as passed)
    job_info2 = test_runner_pb2.JobInfo(job_id="job2", return_code=0)
    test_info2 = test_runner_pb2.TestInfo(
        target_name="//path/to:test2", status=build_event_stream_pb2.FLAKY
    )
    job_info2.tests.append(test_info2)

    done_item.job_infos.extend([job_info1, job_info2])

    # Set the done field in run_info
    base_run_info.done = done_item

    # _is_passed should return True
    assert _is_passed(base_run_info)


def test_is_passed_done_job_infos_some_failed(base_run_info):
    """Test _is_passed when some tests in job_infos failed."""
    # Create a DoneItem with job_infos where some tests failed
    done_item = bazel_runner_store_pb2.DoneItem()

    # Add a job with passed tests
    job_info1 = test_runner_pb2.JobInfo(job_id="job1", return_code=0)
    test_info1 = test_runner_pb2.TestInfo(
        target_name="//path/to:test1", status=build_event_stream_pb2.PASSED
    )
    job_info1.tests.append(test_info1)

    # Add another job with failed tests
    job_info2 = test_runner_pb2.JobInfo(job_id="job2", return_code=0)
    test_info2 = test_runner_pb2.TestInfo(
        target_name="//path/to:test2", status=build_event_stream_pb2.FAILED
    )
    job_info2.tests.append(test_info2)

    done_item.job_infos.extend([job_info1, job_info2])

    # Set the done field in run_info
    base_run_info.done = done_item

    # _is_passed should return False
    assert not _is_passed(base_run_info)


def test_is_passed_done_job_infos_nonzero_return_code(base_run_info):
    """Test _is_passed when all tests passed but return code is not 0."""
    # Create a DoneItem with job_infos where all tests passed but return code is not 0
    done_item = bazel_runner_store_pb2.DoneItem()

    # Add a job with passed tests but non-zero return code
    job_info = test_runner_pb2.JobInfo(job_id="job1", return_code=1)
    test_info = test_runner_pb2.TestInfo(
        target_name="//path/to:test1", status=build_event_stream_pb2.PASSED
    )
    job_info.tests.append(test_info)

    done_item.job_infos.append(job_info)

    # Set the done field in run_info
    base_run_info.done = done_item

    # _is_passed should return False
    assert not _is_passed(base_run_info)


def test_is_passed_done_no_job_infos_no_run(base_run_info):
    """Test _is_passed when done is set but job_infos is empty and run is not set."""
    # Create a DoneItem with empty job_infos
    done_item = bazel_runner_store_pb2.DoneItem()

    # Set the done field in run_info
    base_run_info.done = done_item

    # run is already None in base_run_info

    # _is_passed should return True (test selection didn't find any tests to run)
    assert _is_passed(base_run_info)


def test_is_passed_run_jobs_all_passed(base_run_info):
    """Test _is_passed when all tests in run_jobs are passed and all jobs succeeded."""
    # Create a DoneItem
    done_item = bazel_runner_store_pb2.DoneItem()
    base_run_info.done = done_item

    # Create a RunItem with run_jobs where all tests passed and all jobs succeeded
    run_item = bazel_runner_store_pb2.RunItem()

    # Add a job with passed tests
    job_info1 = test_runner_pb2.JobInfo(job_id="job1")
    test_info1 = test_runner_pb2.TestInfo(
        target_name="//path/to:test1", status=build_event_stream_pb2.PASSED
    )
    job_info1.tests.append(test_info1)

    run_job1 = bazel_runner_store_pb2.RunJobItem(
        job_id="job1", state=bazel_runner_store_pb2.RunJobItem.State.SUCCEEDED
    )
    run_job1.job_info.CopyFrom(job_info1)

    # Add another job with flaky tests (which should also count as passed)
    job_info2 = test_runner_pb2.JobInfo(job_id="job2")
    test_info2 = test_runner_pb2.TestInfo(
        target_name="//path/to:test2", status=build_event_stream_pb2.FLAKY
    )
    job_info2.tests.append(test_info2)

    run_job2 = bazel_runner_store_pb2.RunJobItem(
        job_id="job2", state=bazel_runner_store_pb2.RunJobItem.State.SUCCEEDED
    )
    run_job2.job_info.CopyFrom(job_info2)

    run_item.run_jobs.extend([run_job1, run_job2])

    # Set the run field in run_info
    base_run_info.run = run_item

    # _is_passed should return True
    assert _is_passed(base_run_info)


def test_is_passed_run_jobs_all_passed_but_nonzero_return_code(base_run_info):
    """Test _is_passed when all tests in run_jobs are passed and all jobs succeeded."""
    # Create a DoneItem
    done_item = bazel_runner_store_pb2.DoneItem()
    base_run_info.done = done_item

    # Create a RunItem with run_jobs where all tests passed and all jobs succeeded
    run_item = bazel_runner_store_pb2.RunItem()

    # Add a job with passed tests
    job_info1 = test_runner_pb2.JobInfo(job_id="job1")
    test_info1 = test_runner_pb2.TestInfo(
        target_name="//path/to:test1", status=build_event_stream_pb2.PASSED
    )
    # this can happen esp around compile errors. All tests that were executed
    # pass, but not all tests actually ran and the exit code is 1
    job_info1.return_code = 1
    job_info1.tests.append(test_info1)

    run_job1 = bazel_runner_store_pb2.RunJobItem(
        job_id="job1", state=bazel_runner_store_pb2.RunJobItem.State.SUCCEEDED
    )
    run_job1.job_info.CopyFrom(job_info1)

    run_item.run_jobs.extend([run_job1])
    base_run_info.run = run_item

    assert not _is_passed(base_run_info)


def test_is_passed_run_jobs_some_failed(base_run_info):
    """Test _is_passed when some tests in run_jobs failed."""
    # Create a DoneItem
    done_item = bazel_runner_store_pb2.DoneItem()
    base_run_info.done = done_item

    # Create a RunItem with run_jobs where some tests failed
    run_item = bazel_runner_store_pb2.RunItem()

    # Add a job with passed tests
    job_info1 = test_runner_pb2.JobInfo(job_id="job1")
    test_info1 = test_runner_pb2.TestInfo(
        target_name="//path/to:test1", status=build_event_stream_pb2.PASSED
    )
    job_info1.tests.append(test_info1)

    run_job1 = bazel_runner_store_pb2.RunJobItem(
        job_id="job1", state=bazel_runner_store_pb2.RunJobItem.State.SUCCEEDED
    )
    run_job1.job_info.CopyFrom(job_info1)

    # Add another job with failed tests
    job_info2 = test_runner_pb2.JobInfo(job_id="job2")
    test_info2 = test_runner_pb2.TestInfo(
        target_name="//path/to:test2", status=build_event_stream_pb2.FAILED
    )
    job_info2.tests.append(test_info2)

    run_job2 = bazel_runner_store_pb2.RunJobItem(
        job_id="job2", state=bazel_runner_store_pb2.RunJobItem.State.SUCCEEDED
    )
    run_job2.job_info.CopyFrom(job_info2)

    run_item.run_jobs.extend([run_job1, run_job2])

    # Set the run field in run_info
    base_run_info.run = run_item

    # _is_passed should return False
    assert not _is_passed(base_run_info)


def test_is_passed_run_jobs_not_succeeded(base_run_info):
    """Test _is_passed when all tests passed but some jobs not succeeded."""
    # Create a DoneItem
    done_item = bazel_runner_store_pb2.DoneItem()
    base_run_info.done = done_item

    # Create a RunItem with run_jobs where all tests passed but some jobs not succeeded
    run_item = bazel_runner_store_pb2.RunItem()

    # Add a job with passed tests and succeeded state
    job_info1 = test_runner_pb2.JobInfo(job_id="job1")
    test_info1 = test_runner_pb2.TestInfo(
        target_name="//path/to:test1", status=build_event_stream_pb2.PASSED
    )
    job_info1.tests.append(test_info1)

    run_job1 = bazel_runner_store_pb2.RunJobItem(
        job_id="job1", state=bazel_runner_store_pb2.RunJobItem.State.SUCCEEDED
    )
    run_job1.job_info.CopyFrom(job_info1)

    # Add another job with passed tests but failed state
    job_info2 = test_runner_pb2.JobInfo(job_id="job2")
    test_info2 = test_runner_pb2.TestInfo(
        target_name="//path/to:test2", status=build_event_stream_pb2.PASSED
    )
    job_info2.tests.append(test_info2)

    run_job2 = bazel_runner_store_pb2.RunJobItem(
        job_id="job2", state=bazel_runner_store_pb2.RunJobItem.State.FAILED
    )
    run_job2.job_info.CopyFrom(job_info2)

    run_item.run_jobs.extend([run_job1, run_job2])

    # Set the run field in run_info
    base_run_info.run = run_item

    # _is_passed should return False
    assert not _is_passed(base_run_info)


def test_is_passed_mixed_test_statuses(base_run_info):
    """Test _is_passed with a mix of passed, flaky, and failed tests."""
    # Create a DoneItem
    done_item = bazel_runner_store_pb2.DoneItem()
    base_run_info.done = done_item

    # Create a RunItem with run_jobs with mixed test statuses
    run_item = bazel_runner_store_pb2.RunItem()

    # Add a job with mixed test statuses (passed and flaky)
    job_info = test_runner_pb2.JobInfo(job_id="job1")

    test_info1 = test_runner_pb2.TestInfo(
        target_name="//path/to:test1", status=build_event_stream_pb2.PASSED
    )
    job_info.tests.append(test_info1)

    test_info2 = test_runner_pb2.TestInfo(
        target_name="//path/to:test2", status=build_event_stream_pb2.FLAKY
    )
    job_info.tests.append(test_info2)

    run_job = bazel_runner_store_pb2.RunJobItem(
        job_id="job1", state=bazel_runner_store_pb2.RunJobItem.State.SUCCEEDED
    )
    run_job.job_info.CopyFrom(job_info)

    run_item.run_jobs.append(run_job)

    # Set the run field in run_info
    base_run_info.run = run_item

    # _is_passed should return True (both PASSED and FLAKY are considered successful)
    assert _is_passed(base_run_info)


def test_is_passed_no_runs(base_run_info):
    """Test _is_passed when there are no run jobs."""
    # Create a DoneItem
    done_item = bazel_runner_store_pb2.DoneItem()
    base_run_info.done = done_item

    # _is_passed should return True (no run jobs means no tests were executed)
    assert _is_passed(base_run_info)
