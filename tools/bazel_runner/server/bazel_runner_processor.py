"""Part of the bazel runner service that handles the interaction with S3 and DynamoDB."""

import argparse
import copy
import datetime
import logging
import os
import pathlib
import random
import threading
import time
import uuid
from collections import defaultdict
from concurrent.futures import ThreadPoolExecutor
from typing import Optional
import google.api_core.exceptions
from google.protobuf import timestamp_pb2

import grpc
import opentelemetry.instrumentation.grpc
import kubernetes
import structlog
from grpc_health.v1 import health, health_pb2_grpc
from grpc_reflection.v1alpha import reflection
from kubernetes.client.exceptions import ApiException
from prometheus_client import Counter, Gauge, Histogram, start_http_server

import base.tracing
from base.logging.struct_logging import setup_struct_logging
from base.python.cloud import gcp
from base.python.signal_handler.signal_handler import GracefulSignalHandler

# from services.lib.grpc.metrics.metrics import MetricsServerInterceptor
from tools.bazel_runner.bep_parser import bep_parser, test_summary_pb2
from tools.bazel_runner.control import bazel_runner_pb2
from tools.bazel_runner.control import client as control_client
from tools.bazel_runner.server import (
    bazel_runner_server_gcp_lib,
    bazel_runner_server_lib,
    bazel_runner_store_pb2,
    test_runner_pb2,
    test_runner_pb2_grpc,
)
from tools.bazel_runner.server.config import Config
from tools.bazel_runner.test_selection_server import client as test_selection_client

log = structlog.get_logger()

tracer = base.tracing.setup_opentelemetry()

_test_run_pending_gauge = Gauge(
    "bazel_runner_pending_run_counter",
    "Gauge for the number of pending test runs in a given state",
    ["state"],
)

_test_run_finished = Counter(
    "bazel_runner_run_finished",
    "Counter for the number of finished test runs",
    labelnames=["state"],
)

INF = float("inf")

_test_run_latency_finished = Histogram(
    "bazel_runner_run_latency_seconds",
    "Histogram for the test latency (only DONE tests)",
    ["tags", "run_state"],
    buckets=[i * 60 for i in [1, 2, 4, 8, 12, 16, 24, 32, 48, 64, 96, 128, INF]],
)

_test_state_latency_finished = Histogram(
    "bazel_runner_run_state_latency_seconds",
    "Histogram for time a test run is in a given processing state",
    ["tags", "state"],
    buckets=[i * 60 for i in [1, 2, 4, 8, 12, 16, 24, 32, 48, 64, 96, 128, INF]],
)


_round_counter = Counter(
    "bazel_runner_rounds",
    "Gauge for the number of check rounds",
)


def _test_summary_to_test_info(
    test_summary: test_summary_pb2.TestSummary,
) -> Optional[test_runner_pb2.TestInfo]:
    if test_summary.state != test_summary_pb2.TestState.SUMMARY:
        return None
    test_info = test_runner_pb2.TestInfo()
    test_info.target_name = test_summary.target_name
    test_info.status = test_summary.overall_status
    return test_info


def _is_wildcard_target(target: str) -> bool:
    """Returns True if the target is a wildcard target.

    Wildcard targets are possible from the web ui.
    TODO: move wildcard targets to the test selection service to resolve them.
    """
    return target.endswith("...") or target.endswith(":all")


class Processor:
    """The main code for the bazel run server-side processing."""

    def __init__(
        self,
        persistence: bazel_runner_server_lib.Persistence,
        k8s_controller: control_client.BazelRunnerControlClient,
        object_access: bazel_runner_server_lib.ObjectAccess,
        result_publisher: bazel_runner_server_lib.ResultPublisher,
        test_selection_rpc_client: test_selection_client.TestSelectionClient,
        config: Config,
    ):
        self.persistence = persistence
        self.k8s_controller = k8s_controller
        self.object_access = object_access
        self.result_publisher = result_publisher
        self.test_selection_client = test_selection_rpc_client
        self.config = config

    def _get_test_shard_id(
        self, run_spec: bazel_runner_pb2.BazelRunnerExecutionSpec, shard_usage: set[int]
    ) -> int | None:
        usage = self.k8s_controller.get_shard_usage()
        logging.info("Volume usage: %s", usage)

        if run_spec.env == bazel_runner_pb2.SystemEnv.SYSTEM_TEST_GPU:
            test_shards = self.config.system_test_gpu_shards[:]
            limited_mode = True
        elif run_spec.env == bazel_runner_pb2.SystemEnv.SYSTEM_TEST:
            test_shards = self.config.system_test_shards[:]
            limited_mode = True
        else:
            test_shards = self.config.test_shards[:]
            limited_mode = False

        available_shards = set(test_shards).difference(shard_usage)
        if not available_shards:
            logging.info("No available shards")
            return None

        if limited_mode:
            # get lowest free
            test_shard_id = min(available_shards)
        else:
            test_shard_id = max(available_shards)

        logging.info("Selected test shard %s out of %s", test_shard_id, usage)
        return test_shard_id

    def _create_run_job(
        self,
        run_id: uuid.UUID,
        volume_id: int,
        run_info: bazel_runner_server_lib.RunInfo,
        run_spec: bazel_runner_pb2.BazelRunnerExecutionSpec,
    ) -> uuid.UUID:
        """Creates a run job."""
        output = self.object_access.get_output_prefix(run_id)
        job_id = self.k8s_controller.schedule_run(
            run_id=run_id,
            checkout_config=run_info.test_spec.test_spec.checkout,
            run_spec=run_spec,
            volume_id=volume_id,
            output=output,
        )
        logging.info("Run job id %a for run id %a", job_id, run_id)
        return job_id

    def _process_orphaned_jobs(
        self, run_id: uuid.UUID, run_info: bazel_runner_server_lib.RunInfo
    ):
        """Find and remove orphaned jobs for a given run."""
        job_ids = dict(self.k8s_controller.list_jobs_for_run(run_id))
        if run_info.run:
            for run_job in run_info.run.run_jobs:
                run_job_id = uuid.UUID(run_job.job_id)
                if run_job_id in job_ids:
                    del job_ids[run_job_id]
        for job_id, job_name in job_ids.items():
            logging.info("Detected orphaned job: %s", job_id)
            logging.info("Deleting job %s", job_name)
            self.k8s_controller.try_delete_job(job_name)

    def _cleanup_jobs(
        self, run_id: uuid.UUID, run_info: bazel_runner_server_lib.RunInfo
    ):
        """Cleans up all jobs of the run."""
        if run_info.run:
            for job in run_info.run.run_jobs:
                job_id = uuid.UUID(job.job_id)
                self.k8s_controller.delete_running_job(run_id, job_id)

    def _check_run_job(
        self, run_id: uuid.UUID, job: bazel_runner_store_pb2.RunJobItem
    ) -> control_client.JobState:
        return self.k8s_controller.check_job(run_id, uuid.UUID(job.job_id))

    def _get_job_info(
        self,
        run_id: uuid.UUID,
        run_info: bazel_runner_server_lib.RunInfo,
        job_id: uuid.UUID,
    ) -> test_runner_pb2.JobInfo | None:
        try:
            archive_files = self.object_access.list_archive(run_id, job_id)
            return_code = self.object_access.get_return_code(run_id, job_id)
            test_infos = self._get_test_infos(run_id, job_id)
            job_info = test_runner_pb2.JobInfo(
                job_id=str(job_id),
                return_code=return_code,
                archive_files=archive_files,
            )
            job_info.tests.extend(test_infos)
            return job_info
        except bazel_runner_server_lib.BazelRunnerException as ex:
            if (
                ex.status_code == grpc.StatusCode.NOT_FOUND
                and run_info.cancel
                and run_info.cancel.cancelled
            ):
                return None
            else:
                raise

    def _schedule_more_run_jobs(
        self,
        run_id: uuid.UUID,
        run_info: bazel_runner_server_lib.RunInfo,
        shard_usage: set[int],
    ) -> bool:
        """Schedules more run jobs if possible.

        Args:
            run_id: run id to update
            run_info: run info to update
            shard_usage: set of shards that are currently in use

        Returns:
            True if any run jobs were scheduled.
        """
        if not run_info.run:
            run_info.run = bazel_runner_store_pb2.RunItem()
        changed = False
        while len(run_info.run.run_jobs) != len(run_info.test_spec.test_spec.runs):
            run_spec = run_info.test_spec.test_spec.runs[len(run_info.run.run_jobs)]
            test_shard = self._get_test_shard_id(run_spec, shard_usage)
            if test_shard is None:
                break
            shard_usage.add(test_shard)
            spec = run_info.test_spec.test_spec.runs[len(run_info.run.run_jobs)]
            job_id = self._create_run_job(
                run_id,
                test_shard,
                run_info,
                spec,
            )
            logging.info("Created run job %s for run id %s", job_id, run_id)
            item = bazel_runner_store_pb2.RunJobItem(
                job_id=str(job_id),
                volume_id=test_shard,
                state=bazel_runner_store_pb2.RunJobItem.State.SCHEDULED,
                execution_spec=spec,
            )
            item.time.FromDatetime(datetime.datetime.now(datetime.timezone.utc))
            item.job_info.job_id = str(job_id)
            for target in spec.targets:
                if not _is_wildcard_target(target):
                    item.job_info.tests.append(
                        test_runner_pb2.TestInfo(target_name=target)
                    )
            run_info.run.run_jobs.append(item)
            changed = True
        return changed

    def _abort_or_error(
        self, run_id: uuid.UUID, run_info: bazel_runner_server_lib.RunInfo
    ) -> bool:
        aborted = self.object_access.check_abort(run_id)
        if aborted:
            logging.info("Run aborted: %s", aborted)
            self._cleanup_jobs(run_id, run_info)
            _test_run_finished.labels("ABORT").inc()
            self.persistence.move_to_abort_state(run_id, message=aborted)
            return True
        else:
            logging.info("Run failed")
            self._cleanup_jobs(run_id, run_info)
            _test_run_finished.labels("ERROR").inc()
            self.persistence.move_to_error_state(run_id, message="Run failed")
            return True

    def _process_run_state(
        self,
        run_id: uuid.UUID,
        run_info: bazel_runner_server_lib.RunInfo,
        shard_usage: set[int],
    ) -> bool:
        """Processes the run state.

        Args:
            run_id: run id to update
            run_info: run info to update
            shard_usage: set of shards that are currently in use

        Returns:
            True if the run state changed.
        """
        assert run_info.run
        run_state_change = False
        for run_job in run_info.run.run_jobs:
            logging.info(
                "Checking run job: job_id=%s, state=%s",
                run_job.job_id,
                bazel_runner_store_pb2.RunJobItem.State.Name(run_job.state),
            )
            if run_job.state == bazel_runner_store_pb2.RunJobItem.State.SUCCEEDED:
                check_result = control_client.JobState.SUCCEEDED
            elif run_job.state == bazel_runner_store_pb2.RunJobItem.State.FAILED:
                check_result = control_client.JobState.FAILED
            else:
                check_result = self._check_run_job(run_id, run_job)
            logging.info("Run job %s: %s", run_job.job_id, check_result)
            if check_result == control_client.JobState.SCHEDULED:
                if run_job.state == bazel_runner_store_pb2.RunJobItem.State.RUNNING:
                    # this can happen if a pod in the job failed
                    logging.info("Run job %s is scheduled", run_job.job_id)
                    run_job.state = bazel_runner_store_pb2.RunJobItem.State.SCHEDULED
                    run_state_change = True
            elif check_result == control_client.JobState.RUNNING:
                if run_job.state == bazel_runner_store_pb2.RunJobItem.State.SCHEDULED:
                    logging.info("Run job %s is running", run_job.job_id)
                    run_job.state = bazel_runner_store_pb2.RunJobItem.State.RUNNING
                    run_state_change = True
            elif check_result == control_client.JobState.SUCCEEDED:
                if run_job.state != bazel_runner_store_pb2.RunJobItem.State.SUCCEEDED:
                    logging.info("Run job %s succeeded", run_job.job_id)
                    try:
                        run_job.state = (
                            bazel_runner_store_pb2.RunJobItem.State.SUCCEEDED
                        )
                        job_info = self._get_job_info(
                            run_id, run_info, uuid.UUID(run_job.job_id)
                        )
                        if job_info:
                            run_job.job_info.CopyFrom(job_info)
                        run_state_change = True
                    except bazel_runner_server_lib.BazelRunnerException as ex:
                        if ex.status_code == grpc.StatusCode.NOT_FOUND:
                            return self._abort_or_error(run_id, run_info)
            else:  # MISSING or FAILED
                return self._abort_or_error(run_id, run_info)

        all_succeeded = False
        if len(run_info.run.run_jobs) == len(run_info.test_spec.test_spec.runs):
            all_succeeded = True
            for job in run_info.run.run_jobs:
                if job.state != bazel_runner_store_pb2.RunJobItem.State.SUCCEEDED:
                    all_succeeded = False
                    break

        if all_succeeded:
            logging.info("Run succeeded")
            _test_run_finished.labels("SUCCESS").inc()
            self.persistence.move_to_run_state(run_id, run_info.run.run_jobs)
            final_time = self.persistence.move_to_done_state(run_id)
            latency = final_time - run_info.create_time
            tags = ",".join(run_info.tags)
            state = bazel_runner_server_lib.get_run_state(run_info)
            _test_run_latency_finished.labels(
                tags, test_runner_pb2.RunState.Name(state)
            ).observe(float(latency.total_seconds()))
            return True

        if run_state_change:
            self.persistence.move_to_run_state(run_id, run_info.run.run_jobs)

        if run_info.cancel and run_info.cancel.cancelled:
            logging.info("Run has cancellation requested")
            self._cleanup_jobs(run_id, run_info)

            _test_run_finished.labels("CANCEL").inc()
            final_time = self.persistence.move_to_cancelled_state(run_id)
            latency = final_time - run_info.create_time
            tags = ",".join(run_info.tags)
            state = bazel_runner_server_lib.get_run_state(run_info)
            _test_run_latency_finished.labels(
                tags, test_runner_pb2.RunState.Name(state)
            ).observe(float(latency.total_seconds()))
            return True

        schedule_change = self._schedule_more_run_jobs(run_id, run_info, shard_usage)
        if schedule_change:
            self.persistence.move_to_run_state(run_id, run_info.run.run_jobs)
        return schedule_change or run_state_change

    def _get_test_infos(
        self, run_id: uuid.UUID, job_id: uuid.UUID
    ) -> list[test_runner_pb2.TestInfo]:
        """Returns the test infos for a given job.

        Args:
            run_id: run id
            job_id: job id

        Returns:
            List of test infos.
        """
        try:
            bep_stream = self.object_access.open_bep(run_id, job_id)
            if not bep_stream:
                return []
            test_targets = list(
                bep_parser.get_test_summaries(bep_parser.read_stream(bep_stream))  # type: ignore
            )
            result = []
            for test_summary in test_targets:
                test_info = _test_summary_to_test_info(test_summary)
                if test_info:
                    result.append(test_info)
            return result
        except google.api_core.exceptions.NotFound:
            return []
        except bazel_runner_server_lib.BazelRunnerException as ex:
            if ex.status_code == grpc.StatusCode.NOT_FOUND:
                return []
            else:
                raise

    def _request_test_selection(self, run_info: bazel_runner_server_lib.RunInfo) -> str:
        """Requests the test selection.

        Args:
            run_info: run info

        Returns:
            The operation id.
        """
        assert run_info.test_selection
        start_commit = None
        if run_info.test_selection.HasField("start_checkout"):
            start_commit = run_info.test_selection.start_checkout
        end_commit = run_info.test_spec.test_spec.checkout
        operation_id = self.test_selection_client.get_test_targets(
            start_commit,
            end_commit,
            run_info.test_selection.policy,
            run_info.test_selection.extra_query,
        )
        return operation_id

    def _check_test_selection(
        self,
        run_info: bazel_runner_server_lib.RunInfo,
        operation_id: str,
    ) -> bazel_runner_pb2.BazelRunnerExecutionGroupSpec | None:
        """Checks the test selection.

        Args:
            run_info: run info
            operation_id: operation id

        Returns:
            The test execution config if the operation is done.
        """
        assert run_info.test_selection
        msg = self.test_selection_client.get_results(operation_id)
        if not msg:
            return None

        test_execution_config = bazel_runner_pb2.BazelRunnerExecutionGroupSpec()
        test_execution_config.checkout.MergeFrom(run_info.test_spec.test_spec.checkout)

        def _complete_run(
            current_run: bazel_runner_pb2.BazelRunnerExecutionSpec,
        ):
            current_run.extra_args.append("--build_tests_only")
            current_run.extra_args.append("--keep_going")

        def _add_runs(
            test_execution_config: bazel_runner_pb2.BazelRunnerExecutionGroupSpec,
        ):
            cpu_targets = []
            c3_targets = []
            gpu_targets = []
            multi_gpu_targets = []
            large_gpu_targets = []
            system_test_targets = []
            system_test_gpu_targets = []
            for target in msg.targets:
                if "large-gpu" in target.tags:
                    large_gpu_targets.append(target.name)
                elif "multi-gpu" in target.tags:
                    multi_gpu_targets.append(target.name)
                elif "gpu" in target.tags:
                    gpu_targets.append(target.name)
                elif "premium-cpu" in target.tags:
                    c3_targets.append(target.name)
                elif "system-test-gpu" in target.tags:
                    system_test_gpu_targets.append(target.name)
                elif "system-test" in target.tags:
                    system_test_targets.append(target.name)
                else:
                    cpu_targets.append(target.name)
            if cpu_targets:
                new_run = test_execution_config.runs.add()
                new_run.command = "test"
                new_run.env = bazel_runner_pb2.SystemEnv.CPU
                for target in cpu_targets:
                    new_run.targets.append(target)
                _complete_run(new_run)
            if c3_targets:
                new_run = test_execution_config.runs.add()
                new_run.command = "test"
                new_run.env = bazel_runner_pb2.SystemEnv.PREMIUM_CPU
                for target in c3_targets:
                    new_run.targets.append(target)
                _complete_run(new_run)
            if gpu_targets:
                new_run = test_execution_config.runs.add()
                new_run.command = "test"
                new_run.env = bazel_runner_pb2.SystemEnv.SINGLE_GPU
                for target in gpu_targets:
                    new_run.targets.append(target)
                _complete_run(new_run)
            if multi_gpu_targets:
                new_run = test_execution_config.runs.add()
                new_run.command = "test"
                new_run.env = bazel_runner_pb2.SystemEnv.MULTI_GPU
                for target in multi_gpu_targets:
                    new_run.targets.append(target)
                _complete_run(new_run)
            if large_gpu_targets:
                new_run = test_execution_config.runs.add()
                new_run.command = "test"
                new_run.env = bazel_runner_pb2.SystemEnv.LARGE_GPU
                for target in large_gpu_targets:
                    new_run.targets.append(target)
                _complete_run(new_run)
            for target in system_test_targets:
                new_run = test_execution_config.runs.add()
                new_run.command = "test"
                new_run.env = bazel_runner_pb2.SystemEnv.SYSTEM_TEST
                new_run.targets.append(target)
                _complete_run(new_run)
            for target in system_test_gpu_targets:
                new_run = test_execution_config.runs.add()
                new_run.command = "test"
                new_run.env = bazel_runner_pb2.SystemEnv.SYSTEM_TEST_GPU
                new_run.targets.append(target)
                _complete_run(new_run)

        _add_runs(test_execution_config)
        return test_execution_config

    def _process_init_state(
        self,
        run_id: uuid.UUID,
        run_info: bazel_runner_server_lib.RunInfo,
        shard_usage: set[int],
    ) -> bool:
        """Processes the init state.

        Args:
            run_id: run id to update
            run_info: run info to update
            shard_usage: set of shards that are currently in use

        Returns:
            True if the run state changed.
        """
        if run_info.cancel and run_info.cancel.cancelled:
            logging.info("Cancellation requested")
            _test_run_finished.labels("CANCEL").inc()
            final_time = self.persistence.move_to_cancelled_state(run_id=run_id)
            latency = final_time - run_info.create_time
            tags = ",".join(run_info.tags)
            state = bazel_runner_server_lib.get_run_state(run_info)
            _test_run_latency_finished.labels(
                tags, test_runner_pb2.RunState.Name(state)
            ).observe(float(latency.total_seconds()))
            return True
        if run_info.test_selection and (
            not run_info.init or not run_info.init.operation_id
        ):
            try:
                logging.info("Test selection requested")
                operation_id = self._request_test_selection(run_info)
                logging.info("Created test selection operation %s", operation_id)
                self.persistence.update_init_state(run_id, operation_id)
                return True
            except grpc.RpcError as ex:
                logging.exception(ex)
                logging.error("Failed to request test selection")
                self.persistence.move_to_error_state(run_id, message=ex.details())  # type: ignore # pylint: disable=no-member
                return True
        elif (
            run_info.test_selection
            and run_info.init
            and run_info.init.operation_id
            and not run_info.test_spec.test_spec.runs
        ):
            logging.info(
                "Checking test selection operation %s", run_info.init.operation_id
            )

            operation_id = run_info.init.operation_id
            try:
                test_spec = self._check_test_selection(run_info, operation_id)
                if not test_spec:
                    return False
                logging.info(
                    "Test selection operation %s succeeded: %s", operation_id, test_spec
                )
                self.persistence.update_test_spec(run_id, test_spec)
                ri = self.persistence.get_run_info(run_id)
                assert ri
                run_info = ri
            except test_selection_client.TestSelectionException as ex:
                logging.info(
                    "Test selection operation %s failed: code %s",
                    operation_id,
                    ex.code(),
                )
                if ex.code() == grpc.StatusCode.INVALID_ARGUMENT:  # type: ignore # pylint: disable=no-member
                    logging.info("Test selection operation %s failed", operation_id)
                    self._cleanup_jobs(run_id, run_info)
                    _test_run_finished.labels("ABORT").inc()
                    self.persistence.move_to_abort_state(run_id, message=ex.details())  # type: ignore # pylint: disable=no-member
                    return False
                else:
                    logging.exception(ex)
                    logging.error("Failed to get selection: operation=%s", operation_id)
                    self.persistence.move_to_abort_state(run_id, message=ex.details())  # type: ignore # pylint: disable=no-member
                    return True
            except grpc.RpcError as ex:
                logging.exception(ex)
                logging.error(
                    "Failed to check test selection operation %s", operation_id
                )
                self.persistence.move_to_error_state(run_id, message=ex.details())  # type: ignore # pylint: disable=no-member
                return True

        if len(run_info.test_spec.test_spec.runs) == 0:
            logging.info("Nothing to do")
            self.persistence.move_to_done_state(run_id)
            return True

        self._schedule_more_run_jobs(run_id, run_info, shard_usage)
        self.persistence.move_to_run_state(
            run_id,
            run_info.run.run_jobs if run_info.run else [],
        )
        return True

    def _process_checkout_state(
        self,
        run_id: uuid.UUID,
        run_info: bazel_runner_server_lib.RunInfo,
        shard_usage: set[int],
    ) -> bool:
        """Processes the checkout state.

        Args:
            run_id: run id to update
            run_info: run info to update

        Returns:
            True if the run state changed.
        """
        self._schedule_more_run_jobs(run_id, run_info, shard_usage)
        self.persistence.move_to_run_state(
            run_id,
            run_info.run.run_jobs if run_info.run else [],
        )
        return True

    def _process_postprocessing_state(
        self,
        run_id: uuid.UUID,
        run_info: bazel_runner_server_lib.RunInfo,
        shard_usage: set[int],
    ) -> bool:
        self._schedule_more_run_jobs(run_id, run_info, shard_usage)
        self.persistence.move_to_run_state(
            run_id,
            run_info.run.run_jobs if run_info.run else [],
        )
        return True

    def _filter_run_info(
        self, run_info: bazel_runner_server_lib.RunInfo
    ) -> bazel_runner_server_lib.RunInfo:
        # do deep copy
        run_info = copy.deepcopy(run_info)
        if run_info.run:
            for job in run_info.run.run_jobs:
                job.execution_spec.ClearField("targets")
                job.job_info.ClearField("tests")
        return run_info

    def _process_cancelled_state(
        self, run_id: uuid.UUID, run_info: bazel_runner_server_lib.RunInfo
    ) -> bool:
        final_time = self.persistence.move_to_cancelled_state(run_id=run_id)
        latency = final_time - run_info.create_time
        tags = ",".join(run_info.tags)
        state = bazel_runner_server_lib.get_run_state(run_info)
        _test_run_latency_finished.labels(
            tags, test_runner_pb2.RunState.Name(state)
        ).observe(float(latency.total_seconds()))
        return True

    def _process_run(
        self,
        run_id: uuid.UUID,
        run_info: bazel_runner_server_lib.RunInfo,
        shard_usage: set[int],
    ):
        change = False
        try:
            logging.info("Process run %s: %s", run_id, self._filter_run_info(run_info))
            self._process_orphaned_jobs(run_id, run_info)
            if run_info.state.current_state == "INIT":
                change = self._process_init_state(run_id, run_info, shard_usage)
            elif run_info.state.current_state == "CHECKOUT":
                change = self._process_checkout_state(run_id, run_info, shard_usage)
            elif run_info.state.current_state == "RUN":
                change = self._process_run_state(run_id, run_info, shard_usage)
            elif run_info.state.current_state == "POSTPROCESSING":
                change = self._process_postprocessing_state(
                    run_id, run_info, shard_usage
                )
            elif run_info.state.current_state == "CANCELLED":
                change = self._process_cancelled_state(run_id, run_info)
            else:
                logging.error("Invalid state %s", run_info.state.current_state)
                raise ValueError(f"Invalid state {run_info.state.current_state}")
        except Exception as ex:
            self.persistence.mark_processing_error(run_id, run_info)
            raise ex
        return change

    def process_change(
        self, run_id: uuid.UUID, old_run_info: bazel_runner_server_lib.RunInfo
    ):
        """Process a single change.

        Args:
            run_id: The run id of the run that changed.
            old_run_info: the old run info before the change
        """
        run_info = self.persistence.get_run_info(run_id)
        if not run_info:
            logging.error("Failed to find run info for run id %s", run_id)
        else:
            duration_in_state = (
                run_info.last_state_change_time - old_run_info.last_state_change_time
            )
            tags = ",".join(run_info.tags)
            _test_state_latency_finished.labels(
                tags, old_run_info.state.current_state
            ).observe(float(duration_in_state.total_seconds()))

            response = test_runner_pb2.TestRunInfo()
            bazel_runner_server_lib.run_info_into_proto(run_info, response)
            response.run_id = str(run_id)
            self.result_publisher.notify(response)

    def run(self):
        """Main run loop."""
        logging.info("Start loop")
        _round_counter.inc()
        pending_stats = defaultdict(int)
        run_infos: list[bazel_runner_server_lib.RunInfo] = []
        shard_usage: set[int] = set()

        for run_id in self.persistence.get_pending_runs():
            structlog.contextvars.bind_contextvars(run_id=str(run_id))
            run_info = self.persistence.get_run_info(run_id)
            if not run_info:
                logging.error("Failed to find run info for run id %s", run_id)
            else:
                pending_stats[run_info.state.current_state] += 1
                if run_info.run:
                    for run_job in run_info.run.run_jobs:
                        if (
                            run_job.state
                            == bazel_runner_store_pb2.RunJobItem.State.SCHEDULED
                            or run_job.state
                            == bazel_runner_store_pb2.RunJobItem.State.RUNNING
                        ):
                            shard_usage.add(run_job.volume_id)
                run_infos.append(run_info)
            structlog.contextvars.clear_contextvars()

        for run_info in run_infos:
            run_id = run_info.run_id
            structlog.contextvars.bind_contextvars(run_id=str(run_id))
            change = self._process_run(run_id, run_info, shard_usage)
            if change:
                self.process_change(run_id, run_info)
            structlog.contextvars.clear_contextvars()

        for state, count in pending_stats.items():
            _test_run_pending_gauge.labels(state).set(count)
        logging.info("Finished loop")


class NamespaceManager(test_runner_pb2_grpc.NamespaceManagerServicer):
    def __init__(self, config: Config, shutdown_event: threading.Event):
        self.config = config
        self.shutdown_event = shutdown_event

    def AllocateNamespace(
        self,
        request: test_runner_pb2.AllocateNamespaceRequest,
        context: grpc.ServicerContext,
    ) -> test_runner_pb2.AllocateNamespaceResponse:
        context.abort(grpc.StatusCode.UNIMPLEMENTED, "Not implemented")
        return test_runner_pb2.AllocateNamespaceResponse()

    def FreeNamespace(
        self,
        request: test_runner_pb2.FreeNamespaceRequest,
        context: grpc.ServicerContext,
    ) -> test_runner_pb2.FreeNamespaceResponse:
        context.abort(grpc.StatusCode.UNIMPLEMENTED, "Not implemented")
        return test_runner_pb2.FreeNamespaceResponse()


def run(config: Config, event: threading.Event, handler: GracefulSignalHandler):
    """Entry function to run the processing loop."""

    shutdown_event = handler.get_shutdown_event()

    if config.gcp:
        persistence = bazel_runner_server_gcp_lib.GcpPersistence.create(config=config)
        object_access = bazel_runner_server_gcp_lib.GcpObjectAccess.create(config)

        result_publisher = bazel_runner_server_gcp_lib.GcpResultPublisher.create(config)
    else:
        raise ValueError("Invalid config")

    image_tag = (
        pathlib.Path(config.image_tag_file).absolute().read_text(encoding="utf-8")
    )

    k8s_controller = control_client.BazelRunnerControlClient(
        namespace=config.test_namespace,
        kube_config_path=None,
        runner_test_service_account_name=config.runner_test_service_account_name,
        runner_image=image_tag,
        bazel_cache_endpoint=config.bazel_cache_endpoint,
        bes_endpoint=config.bes_endpoint,
        cloud="GCP_US_CENTRAL1_DEV",
    )
    processor = Processor(
        persistence=persistence,
        k8s_controller=k8s_controller,
        object_access=object_access,
        result_publisher=result_publisher,
        config=config,
        test_selection_rpc_client=test_selection_client.TestSelectionClient(
            config.test_selection_endpoint,
            insecure=True,
        ),
    )

    server = grpc.server(
        ThreadPoolExecutor(max_workers=10),
        interceptors=[
            opentelemetry.instrumentation.grpc.server_interceptor(),
            # MetricsServerInterceptor(),
        ],
    )

    health_pb2_grpc.add_HealthServicer_to_server(health.HealthServicer(), server)
    test_runner_pb2_grpc.add_NamespaceManagerServicer_to_server(
        NamespaceManager(config, shutdown_event), server
    )
    reflection.enable_server_reflection(
        service_names=(
            test_runner_pb2.DESCRIPTOR.services_by_name["NamespaceManager"].full_name,
            reflection.SERVICE_NAME,
        ),
        server=server,
    )

    actual_port = server.add_insecure_port(f"[::]:{config.port}")
    server.start()
    logging.info("Listening on %s", actual_port)

    while not shutdown_event.is_set():
        setup_kubernetes(config)
        start_time = time.monotonic()
        processor.run()
        while not event.is_set() and time.monotonic() - start_time < 60.0:
            if shutdown_event.is_set():
                break
            event.wait(timeout=1.0)
        event.clear()

    logging.info("Shutting down server")
    server.stop(grace=config.shutdown_grace_period_s).wait()
    logging.info("Server shutdown complete")


def listen_k8s_job_events(config, event: threading.Event):
    """Listens on job events in the test namespace, so that the processor can ack on them."""
    try:
        setup_kubernetes(config)
        while True:
            try:
                core_api = kubernetes.client.BatchV1Api()  # type: ignore
                watcher = kubernetes.watch.Watch()  # type: ignore
                stream = watcher.stream(
                    core_api.list_namespaced_job, namespace=config.test_namespace
                )
                for raw_event in stream:
                    logging.info(
                        "Kubernetes Job Event: %s %s",
                        raw_event["type"],  # type: ignore
                        raw_event["object"].metadata.name,  # type: ignore
                    )
                    if raw_event["type"] == "ADDED":  # type: ignore
                        # we created the event, so there is no point in rechecking our state
                        continue
                    if raw_event["type"] == "DELETED":  # type: ignore
                        continue
                    event.set()
            except ApiException as ex:
                logging.error(
                    "Api error while listening on job k8s events: status code %s, ex %s",
                    ex.status,
                    ex,
                )
                if ex.status == 401 or ex.status == 410:
                    # re-authenticate
                    setup_kubernetes(config)
                    continue
                else:
                    raise
    except Exception as ex:  # pylint: disable=broad-exception-caught
        logging.error("Error while listen on k8s job events: %s", ex)
        os._exit(1)


def setup_kubernetes(config):
    if not config.kube_config:
        kubernetes.config.load_incluster_config()  # type: ignore
    else:
        kubernetes.config.kube_config.load_kube_config(config_file=config.kube_config)  # type: ignore


def listen_k8s_pod_events(config):
    """Listens on pod events in the test namespace, so that we can log them."""
    try:
        setup_kubernetes(config)
        while True:
            try:
                core_api = kubernetes.client.CoreV1Api()  # type: ignore
                watcher = kubernetes.watch.Watch()  # type: ignore
                stream = watcher.stream(
                    core_api.list_namespaced_pod, namespace=config.test_namespace
                )
                for raw_event in stream:
                    if not raw_event["object"].metadata.labels:  # type: ignore
                        continue
                    run_id = raw_event["object"].metadata.labels.get(  # type: ignore
                        "bazel-runner-run-id"
                    )
                    if run_id:
                        logging.info(
                            "Kubernetes Pod Event: %s name=%s",
                            raw_event["type"],  # type: ignore
                            raw_event["object"].metadata.name,  # type: ignore
                        )
            except ApiException as ex:
                logging.error(
                    "Api error while listening on pod k8s events: status code %s, ex %s",
                    ex.status,
                    ex,
                )
                if ex.status == 401 or ex.status == 410:
                    # re-authenticate
                    setup_kubernetes(config)
                    continue
                else:
                    raise
    except Exception as ex:  # pylint: disable=broad-exception-caught
        logging.error("Error while listen on k8s pod events: %s", ex)
        os._exit(1)


def listen_queue_events(queue: bazel_runner_server_lib.Queue, event: threading.Event):
    """Listens on the tests runner notify queue to be informed about new rpc events."""
    try:
        for message in queue.receive():
            logging.info("Queue event: %s", message)

            event.set()
    except Exception as ex:  # pylint: disable=broad-exception-caught
        logging.exception(ex)
        logging.error("Error while listen on queue events: %s", ex)
        os._exit(1)


def main():
    """Main function."""

    handler = GracefulSignalHandler()

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--config-file", default=pathlib.Path("/config/config.json"), type=pathlib.Path
    )
    args = parser.parse_args()

    setup_struct_logging()

    config = Config.load_config(args.config_file)
    logging.info("Config %s", config)
    if config.gcp:
        logging.info("Service Account %s", gcp.get_active_gcp_service_account())

    # This goes out and overrides the grpc package so the opentelemetry
    # interceptor is automatically added during client creation.
    grpc_client_instrumentor = (
        opentelemetry.instrumentation.grpc.GrpcInstrumentorClient()
    )
    grpc_client_instrumentor.instrument()

    # begin listening for Prometheus requests
    start_http_server(9090)

    event = threading.Event()

    k8s_job_listen_thread = threading.Thread(
        target=listen_k8s_job_events, args=[config, event], daemon=True
    )
    k8s_job_listen_thread.start()

    k8s_pod_listen_thread = threading.Thread(
        target=listen_k8s_pod_events, args=[config], daemon=True
    )
    k8s_pod_listen_thread.start()

    if config.gcp:
        queue = bazel_runner_server_gcp_lib.GcpQueue.create(config)
    else:
        raise ValueError("Invalid config")

    queue_listen_thread = threading.Thread(
        target=listen_queue_events, args=[queue, event], daemon=True
    )
    queue_listen_thread.start()

    run(config, event, handler)


if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        logging.fatal("fatal exception %s", e)
        raise
