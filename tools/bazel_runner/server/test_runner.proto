syntax = "proto3";

import "google/protobuf/timestamp.proto";
import "third_party/proto/bazel_build/build_event_stream.proto";
import "tools/bazel_runner/bep_parser/test_summary.proto";
import "tools/bazel_runner/control/bazel_runner.proto";
import "tools/bazel_runner/git/checkout.proto";
import "tools/bazel_runner/test_selection_server/test_selection.proto";

/// service interface for the test runner
service TestRunner {
  // schedules a new test execution run.
  rpc ScheduleTest(ScheduleTestRequest) returns (ScheduleTestResponse) {}

  // cancels a pending test execution run
  rpc CancelTest(CancelTestRequest) returns (CancelTestResponse) {}

  // get information for a pending or finished test execution run
  rpc GetTestInfo(GetTestInfoRequest) returns (GetTestInfoResponse) {}

  // returns the command logs from a finished test execution run
  rpc GetCommandLog(GetCommandLogRequest) returns (stream GetCommandLogResponse) {}

  // returns information about test cases from a finished test execution run
  rpc GetTestCases(GetTestCasesRequest) returns (GetTestCasesResponse) {}

  // returns archived files from a finished test execution run
  rpc GetArchiveData(GetArchiveDataRequest) returns (stream GetArchiveDataResponse) {}

  // returns the test logs from a finished test execution run
  rpc GetTestLogs(GetTestLogsRequest) returns (stream GetTestLogsResponse) {}

  // returns the build events of a given (pending or finished) run.
  rpc GetBuildEvents(GetBuildEventsRequest) returns (stream GetBuildEventsResponse) {}

  // return all pending runs
  rpc GetPendingRuns(GetPendingRunsRequest) returns (GetPendingRunsResponse) {}

  // return search for pending or completed runs
  rpc SearchRuns(SearchRunsRequest) returns (stream SearchRunsResponse) {}

  // return runs between two run ids
  //
  // oldest_run_id and newest_run_id are inclusive or exclusive depending on the flags
  //
  // the returns are returned in reverse order of creation with the newest first
  rpc GetRuns(GetRunsRequest) returns (stream GetRunsResponse) {}
}

// specification to find the test targets to run dynamically
message TestSelectionSpec {
  // the start commit for the impact comparision
  //
  // if not specified, the current state of the base branch of the checkout pull request is used.
  // if not specified, and the checkout is not a pull request, the operation is fail
  CheckoutSpec start_checkout = 1;

  // the test selection policy to use to filter the tests
  TestSelectionPolicy policy = 3;

  // extra query to filter the tests with.
  //
  // this is in bazel query syntax. with $1 being replaced by the set of all impacted targets.
  // e.g. `kind(".*_test", set($1)) union kind("test_suite", set($1))` for only returning tests.
  // if not set, all tests are returned.
  string extra_query = 4;
}

// information required to send a github status notification.
//
// see https://docs.github.com/en/rest/commits/statuses?apiVersion=2022-11-28
message GithubStatusNotification {
  // the owner of the repo to notify
  //
  // if not specified, the owner of the checkout's owner is used.
  string owner = 1;

  // the name of the repo to notify
  //
  // if not specified, the name of the checkout's repo name is used.
  string repo_name = 2;

  // the sha of the commit to notify
  string sha = 3;

  // the context to use for the status notification.
  //
  // a context displayed by github alongside the status notification.
  // This is a string we get to choose.
  string context = 4;
}

// sends a slack notification when the test run is done.
message SlackNotification {
  // user name, i.e. augmentcode.com suffix
  string user_name = 2;
}

message NotificationSpec {
  oneof notification {
    GithubStatusNotification github_status = 1;
    SlackNotification slack = 2;
  }
}

message ScheduleTestRequest {
  // the test execution specification to execute
  //
  // this is required
  //
  // test_execution.runs should not be set if test_selection is set.
  BazelRunnerExecutionGroupSpec test_execution = 1;

  // optional user name. it is the augmentcode.com user name for tests
  // scheduled via the test runner web UI and a opaque string for automated
  // scheduled tests.
  string requestor = 2;

  // tags associated with the run
  repeated string tags = 3;

  // if set to true, all non-finished runs with the
  // same pull requestor and requestor will be cancelled
  //
  // this is only valid for pull request runs
  bool supersedes = 4;

  // optional test selection
  //
  // test_execution.runs should not be set in this situation.
  TestSelectionSpec test_selection = 5;

  // optional notification
  NotificationSpec notification = 6;
}

message ScheduleTestResponse {
  // generated run id used to identify the run
  string run_id = 1;
}

message GetTestInfoRequest {
  // run id of the test run go get the information for
  string run_id = 1;
}

// the current state of the test run.
//
// One interesting aspect are the different non-success types:
// - ERROR: Test infrastructure problem
// - ABORT: The tests were not (fully) ran due to a problem that is likely caused
//          by an invalid request, e.g. invalid pull request, invalid target names
// - FAILURE: Tests were executed, but at least one test target did report a non-passing
//            status. The test system provided the feedback request, the feedback was
//            just negative.
// - TIMEOUT: The overall execution timed out. A  testtarget over the time limit would
//            be reported as FAILURE. This is likely an infrastructure problem.
enum RunState {
  RUN_STATE_NOT_SET = 0;

  // initial state of the test run
  RUN_STATE_INIT = 1;

  // the test run is currently checking out the source code.
  RUN_STATE_CHECKOUT = 2;

  // the tests are currently running.
  //
  // RUN_STATE_RUN_WAITING is similar. RUN_STATE_RUN indicates that the test are actively running
  // while RUN_STATE_RUN_WAITING indicates waiting to be run. When a test run consists of multiple
  // sub jobs or when there are errors during the execution, a run might switch between
  // RUN_STATE_RUN and RUN_STATE_RUN_WAITING multiple times.
  RUN_STATE_RUN = 3;

  // the tests finished and the results are being processed.
  RUN_STATE_POSTPROCESSING = 4;

  // the test run finished with an error in the test infrastructure.
  RUN_STATE_ERROR = 5;

  // the test run didn't execute because it was aborted.
  // This usually mean a procondition was not meet, e.g. the checkout wasn't
  // available for a the pull request couldn't be merged.
  RUN_STATE_ABORT = 6;

  // the test run finished successfully and all test targets passed
  RUN_STATE_PASSED = 7;

  // the test run finished, but one or more tests reported a non-passing status
  RUN_STATE_FAILURE = 8;

  // the test run was manualy cancelled
  //
  // This is the terminal state a run reached if a run for which
  // cancellations has been requested will reach.
  // However, if a test is aborted after cancellation is requested, it
  // is still marked aborted.
  RUN_STATE_CANCEL = 9;

  // the test run timed out
  RUN_STATE_TIMEOUT = 10;

  // the tests are scheduled to run, but are not currently running
  RUN_STATE_RUN_WAITING = 11;
}

// high-level information about a test target
message TestInfo {
  string target_name = 1;
  build_event_stream.TestStatus status = 2;
}

// information about a job as part of a run
message JobInfo {
  string job_id = 1;

  // return code as reported by bazel
  //
  // only set when the run is done
  int32 return_code = 2;

  // list of all archived files.
  // only set when the run is done
  repeated string archive_files = 3;

  // list of all tests attempted
  // only set when the run is done.
  repeated TestInfo tests = 4;
}

// response containing information a test run
message GetTestInfoResponse {
  // the test execution spec used
  BazelRunnerExecutionGroupSpec test_execution = 1;

  // the current state
  RunState state = 2;

  // only filled in RUN state or later
  repeated JobInfo jobs = 3;

  // the time the test run was created
  google.protobuf.Timestamp create_time = 4;

  // optional user name, i.e. augmentcode.com suffix
  string requestor = 5;

  // message set if the state is ABORT
  string message = 6;

  // all tags attached to the test run
  repeated string tags = 7;

  // the timestamp when the run when its current state
  google.protobuf.Timestamp last_state_change_time = 8;

  // true if cancellation was requested
  bool cancellation_requested = 9;

  // optional user name, i.e. augmentcode.com suffix
  string cancelled_by = 13;

  // if set to true, all non-finished runs with the
  // same pull requestor and requestor will be cancelled
  //
  // this is only valid for pull request runs
  bool supersedes = 10;

  // optional test selection
  TestSelectionSpec test_selection = 11;

  // optional notification
  NotificationSpec notification = 12;
}

message TestRunInfo {
  // run id
  string run_id = 13;

  // the test execution spec used
  BazelRunnerExecutionGroupSpec test_execution = 1;

  // the current state
  RunState state = 2;

  // only filled in RUN state or later
  repeated JobInfo jobs = 3;

  // the time the test run was created
  google.protobuf.Timestamp create_time = 4;

  // optional user name, i.e. augmentcode.com suffix
  string requestor = 5;

  // message set if the state is ABORT
  string message = 6;

  // all tags attached to the test run
  repeated string tags = 7;

  // the timestamp when the run when its current state
  google.protobuf.Timestamp last_state_change_time = 8;

  // true if cancellation was requested
  bool cancellation_requested = 9;

  // optional user name, i.e. augmentcode.com suffix
  string cancelled_by = 14;

  // if set to true, all non-finished runs with the
  // same pull requestor and requestor will be cancelled
  //
  // this is only valid for pull request runs
  bool supersedes = 10;

  // optional test selection
  TestSelectionSpec test_selection = 11;

  // optional notification
  NotificationSpec notification = 12;
}

message CancelTestRequest {
  // run id of the pending test run to cancel
  string run_id = 1;

  // optional user name, i.e. augmentcode.com suffix
  string cancelled_by = 2;
}

message CancelTestResponse {}

message GetArchiveDataRequest {
  string run_id = 1;
  string job_id = 2;
  string archive_file = 3;
}

message GetArchiveDataResponse {
  bytes content = 1;
}

// request for test logs
message GetTestLogsRequest {
  // run id of the test run to get the logs for
  string run_id = 1;

  // job id of the test run to get the logs for
  string job_id = 2;

  // target name of the test target to get the logs for
  string target_name = 3;
}

message GetTestLogsResponse {
  // shard index of the run.
  // 0 if not sharded
  int32 shard_index = 1;

  // total number of shards
  // 0 if not sharded
  int32 shard_count = 2;

  // attempt index test execution.
  // usually 0, but can be higher if the test is flaky or failing.
  int32 attempt_index = 3;

  // part of the test log
  //
  // the logs might be split into multiple parts
  bytes content = 4;
}

message GetCommandLogRequest {
  string run_id = 1;
  string job_id = 2;
}

message GetCommandLogResponse {
  string content = 1;
}

message GetBuildEventsRequest {
  string run_id = 1;
  string job_id = 2;
  // the minimal sequence number any reported response
  // should have.
  // this is used to not sent duplicate information again
  uint32 min_sequence_number = 3;

  // the maximum number of events to return
  // if not set all events will be returned
  uint32 limit = 4;
}

message GetBuildEventsResponse {
  // the raw build event
  build_event_stream.BuildEvent build_event = 1;

  // the test summary calculated by the bep parser.
  TestSummary test_summary = 2;

  // a sequence number that is is striclty increasing for a given job.
  uint32 sequence_number = 3;
}

message GetTestCasesRequest {
  string run_id = 1;
  string job_id = 2;
  string target_name = 3;
}

message GetTestCasesResponse {
  repeated TestCase test_cases = 1;
}

message GetPendingRunsRequest {}

message GetPendingRunsResponse {
  repeated string run_ids = 1;
}

/// message to search for runs
/// tag xor requestor needs to be set
message SearchRunsRequest {
  string tag = 1;
  string requestor = 2;
  uint32 max_results = 3;
}

message SearchRunsResponse {
  string run_id = 1;
}

message GetRunsRequest {
  // the oldest run id to return
  string oldest_run_id = 1;

  // the newest run id to return
  string newest_run_id = 2;

  // the maximum number of runs to return
  // if not set a default value will be used
  uint32 max_count = 3;

  bool oldest_inclusive = 4;
  bool newest_inclusive = 5;
}

message GetRunsResponse {
  // the runs will be sorted in reverse order of creation with the newest first
  repeated TestRunInfo runs = 1;
}

// Service for allocating namespaces
service NamespaceManager {
  rpc AllocateNamespace(AllocateNamespaceRequest) returns (AllocateNamespaceResponse) {}
  rpc FreeNamespace(FreeNamespaceRequest) returns (FreeNamespaceResponse) {}
}

message AllocateNamespaceRequest {
  string job_id = 1;
  string target_name = 2;

  uint32 num_gpus = 3;
}

message AllocateNamespaceResponse {
  string namespace = 1;
  string allocation_id = 2;
}

message FreeNamespaceRequest {
  string job_id = 1;
  string target_name = 2;
  string namespace = 3;
  string allocation_id = 4;
}

message FreeNamespaceResponse {}
