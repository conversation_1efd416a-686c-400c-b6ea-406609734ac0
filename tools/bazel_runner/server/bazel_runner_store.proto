// Protobuf definition controlling the on-disk state for bazel runner

// The messages here are an implementation detail of the bazel runner server
// and not exposed to other services or the external API
syntax = "proto3";

import "google/protobuf/timestamp.proto";
import "third_party/proto/bazel_build/build_event_stream.proto";
import "tools/bazel_runner/bep_parser/test_summary.proto";
import "tools/bazel_runner/control/bazel_runner.proto";
import "tools/bazel_runner/server/test_runner.proto";

// Build Event Service Event
//
// Key: "bes#{job_id}#{seq_number}
// Column Family: Event
// Column Identifier: ""
message BesEvent {
  // job id of the run job that this event belongs to
  string job_id = 1;

  // bazel generated invocation id
  // usually, a job as one invocation, but might not
  // be the case if the job failed and was restarted
  string invocation_id = 2;

  // sequence number of the event
  uint64 sequence_number = 3;

  // time of the event
  google.protobuf.Timestamp time = 4;

  // build event
  build_event_stream.BuildEvent build_event = 5;

  // a summary of the build event for easier processing later
  TestSummary test_summary = 6;
}

// Stores the current state of the run
//
// Key: "run#{run_id}
// Column Family: Main
// Column Identifier: STATE
message StateItem {
  // current state
  string state = 1;

  // time of the state change
  google.protobuf.Timestamp time = 2;
}

// Stores the test specification
//
// Key: "run#{run_id}
// Column Family: Main
// Column Identifier: TEST_SPEC
message TestSpecItem {
  // test execution specification
  BazelRunnerExecutionGroupSpec test_spec = 1;
}

// Stores about the checkout state
//
// Key: "run#{run_id}
// Column Family: Main
// Column Identifier: INIT
message InitItem {
  // operation id of the test selection
  string operation_id = 1;

  google.protobuf.Timestamp time = 2;
}

message RunJobItem {
  enum State {
    SCHEDULED = 0;
    RUNNING = 1;
    SUCCEEDED = 2;
    FAILED = 3;
  }

  // job id of the run job
  string job_id = 1;

  // time of the state change
  google.protobuf.Timestamp time = 2;

  // volume id used for the job
  int32 volume_id = 3;

  // current state of the job
  State state = 4;

  // execution specification used for this job
  BazelRunnerExecutionSpec execution_spec = 5;

  // results of the job if state is SUCCEEDED
  JobInfo job_info = 6;
}

// Stores about the runs
//
// Key: "run#{run_id}
// Column Family: Main
// Column Identifier: RUN
message RunItem {
  reserved 1, 2;

  // time of the state change
  google.protobuf.Timestamp time = 3;

  repeated RunJobItem run_jobs = 4;
}

// Stores about the error state
//
// Key: "run#{run_id}
// Column Family: Main
// Column Identifier: ERROR
message ErrorItem {
  // error message
  string message = 1;

  // time of the error state change
  google.protobuf.Timestamp time = 2;
}

// Stores about the abort state
//
// Key: "run#{run_id}
// Column Family: Main
// Column Identifier: ABORT
message AbortItem {
  // abort message
  string message = 1;

  // time of the abort state change
  google.protobuf.Timestamp time = 2;
}

// Stores about processing errors.
//
// Key: "run#{run_id}
// Column Family: Main
// Column Identifier: PROCESSING_ERROR
message ProcessingErrorItem {
  // number of times the processing (in the processing pod) failed
  // can be used e.g. for exponential backoff
  uint32 count = 1;
}

// Stores about the done state
//
// Key: "run#{run_id}
// Column Family: Main
// Column Identifier: DONE
message DoneItem {
  // summary of all run jobs
  repeated JobInfo job_infos = 1 [deprecated = true];

  // time of the state change
  google.protobuf.Timestamp time = 2;
}

// Stores information about cancellations
message CancelItem {
  bool cancelled = 1;

  // optional user name, i.e. augmentcode.com suffix
  string cancelled_by = 3;

  google.protobuf.Timestamp time = 2;
}

// Main information about the run
//
// Key: "run#{run_id}
// Column Family: Main
// Column Identifier: main
message RunMainItem {
  // creation time
  google.protobuf.Timestamp create_time = 2;

  // requestor (i.e. the engineer or github)
  string requestor = 3;

  // tags of the test job
  repeated string tags = 4;

  // if the run supersedes other runs
  bool supersedes = 5;

  // the test selection
  // only set if the run was created with a test selection
  TestSelectionSpec test_selection = 6;

  // the notification
  NotificationSpec notification = 7;
}
