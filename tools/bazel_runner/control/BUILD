load("@python_pip//:requirements.bzl", "requirement")
load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:go.bzl", "go_proto_library")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:python.bzl", "py_binary", "py_library", "py_oci_image", "py_proto_library")

py_library(
    name = "bazel_runner_lib",
    srcs = [
        "bazel_runner_lib.py",
    ],
)

py_library(
    name = "cloud_client",
    srcs = [
        "cloud_client.py",
    ],
    deps = [
        ":bazel_runner_lib",
        requirement("google-cloud-storage"),
    ],
)

py_binary(
    name = "bazel_runner_control",
    srcs = [
        "bazel_runner_control.py",
        "bazel_runner_run.py",
    ],
    main = "bazel_runner_control.py",
    deps = [
        ":bazel_runner_lib",
        ":bazel_runner_py_proto",
        ":cloud_client",
        "//base/cloud/k8s:docker",
        "//base/logging:struct_logging",
        "//base/python/cloud",
        "//base/python/cloud:env_info",
        "//base/python/cloud:gcp",
        "//tools/bazel_lib",
        "//tools/bazel_runner/git:app_token",
        "//tools/bazel_runner/git:checkout",
        requirement("protobuf"),
        requirement("kubernetes"),
    ],
)

py_oci_image(
    name = "image",
    package_name = package_name(),
    base = "//tools/docker:cbazel_test_image",
    binary = ":bazel_runner_control",
    trivy_allow_list = [
        "CVE-2024-34156",  # AU-4115
    ],
    visibility = ["//tools/bazel_runner:__subpackages__"],
)

kubecfg(
    name = "kubecfg_shell",
    src = "bazel_runner_shell.jsonnet",
    cluster_wide = True,
    deps = [
        "//deploy/common:node-lib",
    ],
)

py_library(
    name = "client",
    srcs = ["client.py"],
    data = [
        "bazel_runner_run.jsonnet",
        "//deploy/common:node-lib-files",
        "@google_jsonnet_go//cmd/jsonnet",
        "@k8s_binary//file:kubectl",
    ],
    visibility = ["//tools:__subpackages__"],
    deps = [
        ":bazel_runner_py_proto",
        requirement("python-ulid"),
        requirement("kubernetes"),
        requirement("protobuf"),
    ],
)

proto_library(
    name = "bazel_runner_proto",
    srcs = ["bazel_runner.proto"],
    visibility = ["//tools:__subpackages__"],
    deps = ["//tools/bazel_runner/git:checkout_proto"],
)

py_proto_library(
    name = "bazel_runner_py_proto",
    protos = [":bazel_runner_proto"],
    visibility = ["//tools:__subpackages__"],
    deps = ["//tools/bazel_runner/git:checkout_py_proto"],
)

go_proto_library(
    name = "bazel_runner_go_proto",
    proto = ":bazel_runner_proto",
    visibility = ["//tools:__subpackages__"],
    deps = ["//tools/bazel_runner/git:checkout_go_proto"],
)
