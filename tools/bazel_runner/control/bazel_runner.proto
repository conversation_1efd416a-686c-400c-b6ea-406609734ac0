syntax = "proto3";

import "tools/bazel_runner/git/checkout.proto";

// A group for Bazel executions specs for a given checkout
message BazelRunnerExecutionGroupSpec {
  repeated BazelRunnerExecutionSpec runs = 1;
  CheckoutSpec checkout = 2;
}

// spec for a single execution
message BazelRunnerExecutionSpec {
  // bazel command to execute (build|test|run)
  string command = 1;

  // list of bazel targets
  // the targets can containg wild cards that are expanded, e.g. `//tools/...`
  repeated string targets = 2;

  // list of any extra arguments to bazel to the bazel command
  repeated string extra_args = 3;

  // the environment the execution should use
  // a test execution spec will execute all (expanded) targets that can run in a given
  // environment, i.e. AWS_SINGLE_GPU will also execute targets that require no GPU.
  SystemEnv env = 4;
}

enum SystemEnv {
  // A single GPU, currently a single L4 GPU
  // all targets with tag "multi-gpu" are skipped
  SINGLE_GPU = 0;

  // 4 GPU, currently 4 L4 GPUs
  MULTI_GPU = 1;

  // no GPU
  // all targets with tags "gpu" or tag "multi-gpu" are skipped
  CPU = 2;

  // A single large GPU, currently a H100 GPU
  // triggered with label "large-gpu"
  LARGE_GPU = 3;

  // A single high performance CPU, e.g. Saphire Rapids (C3)
  PREMIUM_CPU = 4;

  // A system test, i.e. a test that requires a kubernetes environment
  SYSTEM_TEST = 5;

  // A system test that requires one or more GPUs
  SYSTEM_TEST_GPU = 6;
}
