"""Program to run the control code for checkout/run/post-process jobs of the <PERSON>zel runner."""

import argparse
import fcntl
import logging
import os
import pathlib
import subprocess
import sys
from contextlib import contextmanager

import structlog

import tools.bazel_runner.control.bazel_runner_run as bazel_runner_run
from base.cloud.k8s.docker import prepare_docker_credential_helper
from base.logging.struct_logging import setup_struct_logging
from base.python.cloud import env_info as env_info
from tools.bazel_runner.control import cloud_client
from tools.bazel_runner.control.bazel_runner_lib import AbortException


def _parse_args():
    parser = argparse.ArgumentParser()
    subparsers = parser.add_subparsers()
    bazel_runner_run.add_subparser(subparsers)

    args = parser.parse_args()
    return args


@contextmanager
def _lock(base_directory: pathlib.Path):
    """Lock the cache volume against other pods.

    The cache volume is sometimes incorrectly accessible by two pods at the same time.
    """
    file_path = base_directory / "lock"
    logging.info("Using lock file %s", file_path)
    file_lock = file_path.open("a")

    try:
        fcntl.flock(file_lock.fileno(), fcntl.LOCK_EX)
        logging.info("File locked")
        yield
    finally:
        fcntl.flock(file_lock.fileno(), fcntl.LOCK_UN)
        file_lock.close()


def _prepare():
    """Prepare the environment."""
    if pathlib.Path("/usr/local/nvidia").exists():
        # The CUDA driver is not available for non-root in GCP.  Make it available.
        # This cannot be done in the docker image as GCP will overwrite any driver settings
        home = pathlib.Path.home()
        local = home.joinpath(".local")
        local.mkdir(exist_ok=True)
        logging.info(
            "Making CUDA driver available from /usr/local/nvidia to %s/nvidia",
            local,
        )
        subprocess.run(
            ["sudo", "cp", "-r", "/usr/local/nvidia", str(local)],
            check=True,
        )
        subprocess.run(
            ["sudo", "chmod", "-R", "777", str(local)],
            check=True,
        )
        os.environ["PATH"] = f"{os.environ['PATH']}:{local}/nvidia/bin"
        os.environ["LD_LIBRARY_PATH"] = f"{local}/nvidia/lib:{local}/nvidia/lib64"

    # ensure that the right docker push authentication helper is called
    prepare_docker_credential_helper()


def main():
    # tini will reap zombies for us. Bazel gets confused by zombie bazel processes.
    if os.getpid() == 1:
        os.execv(  # nosec (B606)
            "/usr/bin/tini-static",
            ["/usr/bin/tini-static", "-g", "--", sys.executable] + sys.argv[:],
        )

    setup_struct_logging()

    logging.info("Args %s", sys.argv)
    args = _parse_args()
    logging.info("%s", args)

    cc: cloud_client.CloudClient = cloud_client.create(args)
    structlog.contextvars.bind_contextvars(run_id=args.run_id)

    with _lock(pathlib.Path(args.base_directory)):
        _prepare()
        env_info.print_env_info()
        try:
            if args.action == bazel_runner_run.ACTION_NAME:
                bazel_runner_run.run(args)
            else:
                sys.exit(1)
        except AbortException as ex:
            logging.warning("abort %s", ex)
            cc.handle_abort(ex)
        finally:
            env_info.print_env_info()


if __name__ == "__main__":
    main()
