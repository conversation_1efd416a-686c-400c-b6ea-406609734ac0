"""Tooling to run an bazel command within Kubernetes."""

import datetime
import logging
import pathlib
import subprocess
import tempfile
import time
import uuid
from enum import Enum
from typing import Optional, Tuple

import google.protobuf.text_format as text_format
import kubernetes
import ulid
from kubernetes.client import BatchV1<PERSON><PERSON>, CoreV1Api
from kubernetes.config.kube_config import load_kube_config

from tools.bazel_runner.control import bazel_runner_pb2
from tools.bazel_runner.git import checkout_pb2

JSONNET_BIN = "../jsonnet_go~/cmd/jsonnet/jsonnet_/jsonnet"


class JobState(Enum):
    """Represents the state of a job."""

    # the job has been created, but the latest pod is not running.
    SCHEDULED = 1

    # the job has currently a running pod
    RUNNING = 2

    # the job has been executed (and retried) and is marked failed.
    # Note a single failed pod is not failing the job, only when the pod
    # failed multiple times.
    FAILED = 3

    # the job has succeeded
    SUCCEEDED = 4

    # The job doesn't exist (likely externally removed)
    # usually an deleted job is similar to a failed job
    MISSING = 5


class BazelRunnerControlClient:
    """Client to execute remote bazel runner tests and wait for the results."""

    def __init__(
        self,
        namespace: str,
        kube_config_path: Optional[pathlib.Path],
        runner_test_service_account_name: str,
        bazel_cache_endpoint: str,
        bes_endpoint: str,
        runner_image: str,
        cloud: str,
    ):
        self.namespace = namespace
        self.kube_config_path = kube_config_path
        self.runner_test_service_account_name = runner_test_service_account_name
        self.bazel_cache_endpoint = bazel_cache_endpoint
        self.bes_endpoint = bes_endpoint
        self.runner_image = runner_image
        self.cloud = cloud

        logging.info(
            "Setup kubernetes controller: namespace %s, kube config path %s, runner image %s",
            self.namespace,
            self.kube_config_path,
            self.runner_image,
        )

    def _create_job(self, yaml_config_file: pathlib.Path):
        """Create a job based on the yaml config provided."""
        logging.info("Create job %s", yaml_config_file.read_text(encoding="utf-8"))
        cmd = [
            "../k8s_binary/file/kubectl",
        ]
        if self.kube_config_path:
            cmd.extend(
                [
                    "--kubeconfig",
                    str(self.kube_config_path),
                ]
            )
        cmd.extend(
            [
                "apply",
                "-f",
                str(yaml_config_file),
            ]
        )
        result = subprocess.run(
            cmd,
            check=False,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
        )
        if result.returncode != 0:
            logging.info("%s", result.stdout.decode())
            logging.info("%s", result.stderr.decode())
            raise ValueError("Failed to create job")

    def _generate_run_config(
        self,
        run_id: uuid.UUID,
        job_id: uuid.UUID,
        repo_name: str,
        run_spec: bazel_runner_pb2.BazelRunnerExecutionSpec,
        volume_id: int,
        output: str,
        checkout_config: checkout_pb2.CheckoutSpec,
    ) -> pathlib.Path:
        """Generate the job configuration."""
        with tempfile.NamedTemporaryFile(delete=False) as config_file:
            env_str = "CPU"
            if run_spec.env == bazel_runner_pb2.SystemEnv.SINGLE_GPU:
                env_str = "SINGLE_GPU"
            if run_spec.env == bazel_runner_pb2.SystemEnv.MULTI_GPU:
                env_str = "MULTI_GPU"
            if run_spec.env == bazel_runner_pb2.SystemEnv.LARGE_GPU:
                env_str = "LARGE_GPU"
            if run_spec.env == bazel_runner_pb2.SystemEnv.PREMIUM_CPU:
                env_str = "PREMIUM_CPU"
            if run_spec.env == bazel_runner_pb2.SystemEnv.SYSTEM_TEST:
                env_str = "SYSTEM_TEST"
            if run_spec.env == bazel_runner_pb2.SystemEnv.SYSTEM_TEST_GPU:
                env_str = "SYSTEM_TEST_GPU"
            subprocess.check_call(
                [
                    JSONNET_BIN,
                    "tools/bazel_runner/control/bazel_runner_run.jsonnet",
                    "-y",
                    "-J",
                    ".",
                    "--tla-str",
                    f"namespace={self.namespace}",
                    "--tla-str",
                    f"runId={run_id}",
                    "--tla-str",
                    f"jobName={self.get_run_job_name(run_id, job_id)}",
                    "--tla-str",
                    f"jobId={job_id}",
                    "--tla-str",
                    f"repo={repo_name}",
                    "--tla-str",
                    f"imageName={self.runner_image}",
                    "--tla-str",
                    f"executionSpec={text_format.MessageToString(run_spec)}",
                    "--tla-str",
                    f"cloud={self.cloud}",
                    "--tla-str",
                    f"env={env_str}",
                    "--tla-str",
                    f"pvId={volume_id}",
                    "--tla-str",
                    f"serviceAccountName={self.runner_test_service_account_name}",
                    "--tla-str",
                    f"bazelCacheEndpoint={self.bazel_cache_endpoint}",
                    "--tla-str",
                    f"besEndpoint={self.bes_endpoint}",
                    "--tla-str",
                    f"storageOutput={output}",
                    "--tla-str",
                    f"checkoutConfig={text_format.MessageToString(checkout_config)}",
                ],
                stdout=config_file,
            )
            return pathlib.Path(config_file.name)

    def try_delete_job(self, job_name: str):
        """Tries to delete a given job.

        However, if that fails for any reason that is not reported further.
        """
        try:
            v1 = BatchV1Api()
            v1.delete_namespaced_job(name=job_name, namespace=self.namespace)
        except Exception as ex:  # pylint: disable=broad-exception-caught
            # ignore any error
            logging.debug("Failed to delete job %s", ex)

    def _try_delete_pod(self, pod_name: str):
        """Tries to delete a given pod.

        However, if that fails for any reason that is not reported further.
        """
        try:
            v1 = CoreV1Api()
            v1.delete_namespaced_pod(name=pod_name, namespace=self.namespace)
        except Exception as ex:  # pylint: disable=broad-exception-caught
            # ignore any error
            logging.debug("Failed to delete pod %s", ex)

    def list_jobs_for_run(self, run_id: uuid.UUID) -> list[Tuple[uuid.UUID, str]]:
        """Returns all jobs for a given run."""
        v1 = BatchV1Api()
        ret = v1.list_namespaced_job(self.namespace)
        jobs = []
        for job in ret.items:
            if not job.metadata.labels:
                continue
            if job.metadata.labels.get("bazel-runner-run-id") == str(run_id):
                job_id = job.metadata.labels.get("bazel-runner-job-id")
                jobs.append((uuid.UUID(job_id), job.metadata.name))
        return jobs

    def _wait_for_job_listing(self, run_id: uuid.UUID, job_id: uuid.UUID):
        """Wait (for a limited time) for the job to actually show up.

        This is to avoid a race condition between job creation and the job
        showing up in a listing.

        If the job doesn't show up, we only display a warning. If the job is till
        missing in the next round, the job will detected as missing and error
        handling will kick in.
        """
        for _ in range(30):
            state = self.check_job(run_id, job_id)
            if state != JobState.MISSING:
                break
            time.sleep(1)
        else:
            logging.warning(
                "Job %s didn't appear after creation for run %s", job_id, run_id
            )

    def delete_running_job(self, run_id: uuid.UUID, job_id: uuid.UUID):
        """Delete running job.

        Returns false if we didn't find a running job. The job might
        not exist or not in the running state
        """
        job_name: str | None = None
        v1 = BatchV1Api()
        ret = v1.list_namespaced_job(
            self.namespace, label_selector=f"bazel-runner-run-id={str(run_id)}"
        )
        for job in ret.items:
            if not job.metadata.labels:
                continue
            if job.metadata.labels.get("bazel-runner-job-id") == str(job_id):
                if job.status.succeeded:
                    # already succeeded, we will not try to delete it
                    return False
                if job.status.failed and job.status.failed >= 6:
                    # already failed, we will not try to delete it
                    return False
                job_name = job.metadata.name
                break
        if job_name:
            logging.info(
                "Deleting job %s: run_id=%s, job_id=%s", job_name, run_id, job_id
            )
            self.try_delete_job(job_name)

            v1 = CoreV1Api()
            ret = v1.list_namespaced_pod(
                self.namespace, label_selector=f"bazel-runner-run-id={str(run_id)}"
            )
            pods = []
            for pod in ret.items:
                if not pod.metadata.labels:
                    continue
                if pod.metadata.labels.get("bazel-runner-job-id") == str(job_id):
                    pods.append(pod.metadata.name)

            for pod_name in pods:
                self._try_delete_pod(pod_name)

            return True
        return False

    def get_shard_usage(self) -> dict[int, int]:
        """Returns the shard usage of the cluster."""
        count = {}
        v1 = BatchV1Api()
        ret = v1.list_namespaced_job(
            self.namespace, label_selector="bazel-runner-shard-id"
        )
        for job in ret.items:
            if not job.metadata.labels:
                continue
            if job.status.succeeded:
                continue
            if job.status.failed and job.status.failed >= 6:
                continue
            shard_id: str = job.metadata.labels.get("bazel-runner-shard-id")
            if int(shard_id) not in count:
                count[int(shard_id)] = 0
            count[int(shard_id)] += 1
        return count

    def check_job(self, run_id: uuid.UUID, job_id: uuid.UUID) -> JobState:
        """Checks the current state of the job.

        Returns the current job state. See JobState enum for details.
        """
        v1 = BatchV1Api()
        ret = v1.list_namespaced_job(
            self.namespace, label_selector=f"bazel-runner-run-id={str(run_id)}"
        )
        for job in ret.items:
            if not job.metadata.labels:
                continue
            if job.metadata.labels.get("bazel-runner-job-id") == str(job_id):
                logging.info("Job %s: status %s", job_id, job.status)
                if job.status.succeeded:
                    return JobState.SUCCEEDED
                if job.status.failed and job.status.failed >= 6:
                    logging.info("Job %s failed: job %s", job_id, job)
                    return JobState.FAILED
                break
        else:
            logging.info("jobs: %s", ret)
            logging.error("Failed to find job for run id %s, job id %s", run_id, job_id)
            return JobState.MISSING
        # the job exists, but did we schedule a pod for it?
        v1 = CoreV1Api()
        ret = v1.list_namespaced_pod(
            self.namespace, label_selector=f"bazel-runner-run-id={str(run_id)}"
        )
        pods = []
        for pod in ret.items:
            if not pod.metadata.labels:
                continue
            if pod.metadata.labels.get("bazel-runner-job-id") == str(job_id):
                pods.append(pod)
        if not pods:
            return JobState.SCHEDULED
        pods.sort(
            key=lambda p: (p.status.start_time or datetime.datetime.max).replace(
                tzinfo=datetime.timezone.utc
            )
        )
        logging.info("Found pods: %s", [p.metadata.name for p in pods])
        last_pod = pods[-1]
        if (
            last_pod.status.container_statuses
            and last_pod.status.container_statuses[0].started
        ):
            return JobState.RUNNING
        return JobState.SCHEDULED

    def _get_pod_for_job(self, job_name: str) -> Optional[str]:
        """Finds the pod for the given pod."""
        v1 = CoreV1Api()
        ret = v1.list_namespaced_pod(
            self.namespace, label_selector=f"job-name={job_name}"
        )
        pod_name = None
        for pod in ret.items:
            pod_name = pod.metadata.name
        return pod_name

    def _get_pod_logs(self, pod_name: str) -> str:
        """Returns the logs of the given pod."""
        v1 = CoreV1Api()
        ret = v1.read_namespaced_pod_log(name=pod_name, namespace=self.namespace)
        return ret

    def _kubernetes_login(self):
        if self.kube_config_path:
            load_kube_config(config_file=str(self.kube_config_path))
        else:
            kubernetes.config.load_kube_config()  # type: ignore

    def get_run_job_name(self, run_id: uuid.UUID, job_id: uuid.UUID):
        """Return the name of the run job.

        Use the last 8 chars of the job id to have unique names. The prefix
        of the job id is timestamp-based, which can lead to collisions.
        """
        return f"bazel-{run_id}-run-{str(job_id)[-8:]}"

    def get_checkout_job_name(self, run_id: uuid.UUID, job_id: uuid.UUID):
        """Return the name of the checkout job.

        Use the last 8 chars of the job id to have unique names. The prefix
        of the job id is timestamp-based, which can lead to collisions.
        """
        return f"bazel-{run_id}-checkout-{str(job_id)[-8:]}"

    def get_postprocess_job_name(self, run_id: uuid.UUID, job_id: uuid.UUID):
        """Return the name of the post process job.

        Use the last 8 chars of the job id to have unique names. The prefix
        of the job id is timestamp-based, which can lead to collisions.
        """
        return f"bazel-{run_id}-post-{str(job_id)[-8:]}"

    def schedule_run(
        self,
        run_id: uuid.UUID,
        checkout_config: checkout_pb2.CheckoutSpec,
        run_spec: bazel_runner_pb2.BazelRunnerExecutionSpec,
        volume_id: int,
        output: str,
    ) -> uuid.UUID:
        """Schedules a new run job.

        A run job is the step in the processing that is actually executing
        the build and test commands.
        """
        job_id = ulid.ULID().to_uuid()
        yaml_config_file = self._generate_run_config(
            run_id=run_id,
            job_id=job_id,
            run_spec=run_spec,
            repo_name=checkout_config.repo_name,
            volume_id=volume_id,
            output=output,
            checkout_config=checkout_config,
        )
        self._create_job(yaml_config_file)
        self._wait_for_job_listing(run_id, job_id)
        return job_id
