"""Module to provide the functionality for the "run" step of the bazel runner controller."""

import logging
import os
import shutil
import subprocess
import sys
import grpc
from pathlib import Path
import kubernetes
from kubernetes.config import load_incluster_config
import git
import google.protobuf.text_format as text_format
import structlog
from tools.bazel_runner.control.bazel_runner_lib import AbortException
from tools.bazel_runner.git import app_token, checkout, checkout_pb2

from tools.bazel_runner.control import bazel_runner_pb2, cloud_client
from tools.bazel_runner.control.bazel_runner_lib import copy_file, copy_tree
from tools.bazel_lib import bazel

ACTION_NAME = "run"


def add_subparser(subparsers):
    """Adds the subparser for the run action."""
    parser = subparsers.add_parser("run")
    parser.set_defaults(action="run")
    parser.add_argument("--base-directory", default="/cache")
    parser.add_argument("--run-id", required=True)
    parser.add_argument("--job-id", required=True)
    parser.add_argument("--repo", default="augment")
    parser.add_argument("--execution-spec")
    parser.add_argument("--test-execution-spec")  # legacy
    parser.add_argument("--system-test-namespace", required=True)
    parser.add_argument("--bazel-cache-endpoint")
    parser.add_argument("--bes-endpoint")
    parser.add_argument("--bazel-cpu", default=7, type=int)
    parser.add_argument("--bazel-ram-mb", default=28, type=int)
    parser.add_argument("--cloud", required=True)
    parser.add_argument("--namespace-manager-endpoint")
    parser.add_argument("--storage-output", required=True)
    parser.add_argument("--wipe", action="store_true")
    parser.add_argument("--checkout-config", required=True)
    parser.add_argument("--minimal-free-disk-gb", type=int, default=100)
    parser.add_argument("--minimal-free-inodes", type=int, default=5_000_000)
    parser.add_argument("--app-token-path", type=Path, required=True)


def _get_bazel_env(
    base_directory: Path,
    job_id: str | None = None,
    system_test_namespace: str | None = None,
    namespace_manager_endpoint: str | None = None,
):
    env = bazel.get_bazel_env()
    env["XDG_CACHE_HOME"] = str(base_directory / ".cache")
    env["BAZEL_BUILD_USER"] = "test"
    if system_test_namespace:
        env["BUILD_USER_NAMESPACE"] = system_test_namespace
    env["BAZEL_EXTRA_PATH"] = "{}:/usr/local/google-cloud-sdk/bin".format(
        Path.home() / ".local" / "nvidia" / "bin"
    )
    if namespace_manager_endpoint:
        env["NAMESPACE_MANAGER_ENDPOINT"] = namespace_manager_endpoint
    env["BAZEL_EXTRA_LD_LIBRARY_PATH"] = ":".join(
        [
            str(Path.home() / ".local" / "nvidia" / "lib"),
            str(Path.home() / ".local" / "nvidia" / "lib64"),
        ]
    )
    if job_id:
        env["TEST_RUNNER_JOB_ID"] = job_id

    return env


def _translate_return_code(returncode):
    if returncode in (0, 1, 3, 4):
        # these return codes indicate that bazel did the job. The tests might have failed, but that is still a valid result.
        return 0
    else:
        return returncode


def _get_free_disk_space(directory: Path) -> int:
    """Returns the free disk space in bytes."""
    statvfs = os.statvfs(directory)
    free_space = statvfs.f_frsize * statvfs.f_bavail
    return free_space


def _get_free_inodes(directory: Path) -> int:
    """Returns the free inodes."""
    statvfs = os.statvfs(directory)
    free_space = statvfs.f_favail
    return free_space


def _checkout(args):
    base_wd = Path(args.base_directory)
    if args.wipe:
        checkout.wipe(base_wd)
    free_space = _get_free_disk_space(base_wd)
    if free_space < args.minimal_free_disk_gb * 1024 * 1024 * 1024:
        logging.warning("Low disk space. Wiping")
        # we do not wipe the checkout directories as their might be required
        # branches for other runs
        checkout.wipe(base_wd / ".cache")
        checkout.wipe(base_wd / "bazel-root")
    else:
        logging.info("Free disk space: %.3fGiB", free_space / (1024 * 1024 * 1024))
    free_inodes = _get_free_inodes(base_wd)
    if free_inodes < args.minimal_free_inodes:
        logging.warning("Low number of free inodes. Wiping")
        # we do not wipe the checkout directories as their might be required
        # branches for other runs
        checkout.wipe(base_wd / ".cache")
        checkout.wipe(base_wd / "bazel-root")

    github_token = app_token.GitHubAppTokenSource.from_directory(args.app_token_path)
    run_checkout = checkout.Checkout(
        base_wd=base_wd,
        github_user="app",
        token_source=github_token,
        home_path=Path.home(),
    )
    run_checkout.setup()
    checkout_config = text_format.Parse(
        args.checkout_config, checkout_pb2.CheckoutSpec()
    )
    logging.info("config %s", checkout_config)

    try:
        run_checkout.checkout(checkout_config=checkout_config, checkout_id=args.run_id)
    except checkout.CheckoutException as checkout_ex:
        if checkout_ex.status_code == grpc.StatusCode.INVALID_ARGUMENT:
            raise AbortException(checkout_ex.message) from checkout_ex
        else:
            raise


def find_all_testlogs_dirs(output_root: Path) -> list[Path]:
    if not output_root.exists():
        return []
    result = []
    for subfile in output_root.iterdir():
        if not subfile.is_dir():
            continue
        testlogs_path = (
            subfile / "execroot" / "_main" / "bazel-out" / "k8-fastbuild" / "testlogs"
        )
        if testlogs_path.exists():
            result.append(testlogs_path)
        # it can happen if the directory doesn't exist, e.g. if a new bazel version is used, and
        # no test logs have been created yet.
    return result


def _run_bazel(
    repo_dir: Path,
    targets_path: Path,
    execution_config: bazel_runner_pb2.BazelRunnerExecutionSpec,
    base_directory: Path,
    bep_file: Path,
    system_test_namespace: str,
    job_id: str,
    bazel_cache_endpoint: str,
    bes_endpoint: str,
    bazel_ram_mb: str,
    bazel_cpu: str,
    namespace_manager_endpoint: str,
    extra_env: dict | None = None,
):
    cmd = [
        "bazel",
        f"--output_user_root={base_directory / 'bazel-root'}",
        "--max_idle_secs=5",
        "--host_jvm_args=-Xmx4g",
        execution_config.command,
        "--build_event_binary_file",
        bep_file,
        "--target_pattern_file",
        targets_path,
        "--test_tag_filters=-manual",  # run all tests independent of the default test tags, but not manual tests
        "--curses=no",
        "--noshow_loading_progress",
        "--test_summary=short",
        "--remote_download_minimal",
        "--show_timestamps",
        "--remote_cache",
        bazel_cache_endpoint,
        "--bes_backend",
        bes_endpoint,
        "--build_request_id",
        job_id,
        "--local_ram_resources",
        str(bazel_ram_mb),
        "--local_cpu_resources",
        str(bazel_cpu),
        "--flaky_test_attempts=2",
        # see https://docs.aspect.build/guides/bazelrc/#performance-options
        "--nobuild_runfile_links",
    ]
    if execution_config.extra_args:
        cmd.extend([a for a in execution_config.extra_args])
    logging.info("Command %s", cmd)
    env = _get_bazel_env(
        base_directory, job_id, system_test_namespace, namespace_manager_endpoint
    )
    if extra_env:
        env.update(extra_env)
    result = subprocess.run(cmd, cwd=repo_dir, check=False, env=env)
    logging.info("Return code %s", result.returncode)
    return result.returncode


def _copy_output(test_path: Path, output_path: Path):
    """Copy the output of the test execution to the output path."""
    print("copy", test_path, output_path)
    if test_path.is_symlink():
        test_path = test_path.resolve()
    copy_tree(test_path.resolve(), output_path)


def _copy_command_log(test_path: Path, job_result_base_path: Path):
    """Copy the command.log to the output path for the job."""
    if test_path.is_symlink():
        test_path = test_path.readlink()
    command_log_path = test_path.parent.parent.parent.parent.parent.joinpath(
        "command.log"
    )
    if command_log_path.exists():
        copy_file(command_log_path, job_result_base_path / "command.log")


def _copy_jvm_log(test_path: Path, job_result_base_path: Path):
    """Copy the command.log to the output path for the job."""
    if test_path.is_symlink():
        test_path = test_path.readlink()
    jvm_log_path = test_path.parent.parent.parent.parent.parent.joinpath(
        "server", "jvm.out"
    )
    if jvm_log_path.exists():
        copy_file(jvm_log_path, job_result_base_path / "jvm.out")


def check_io_errors(test_path: Path):
    if test_path.is_symlink():
        test_path = test_path.readlink()
    command_log_path = test_path.parent.parent.parent.parent.parent.joinpath(
        "command.log"
    )
    if command_log_path.exists():
        if " failed: Exec failed due to IOException" in command_log_path.read_text():
            logging.warning("Detected IOException during execution")
            return False
    return True


def _clean_testlogs(test_dir: Path):
    """Cleans the previous bazel test logs (if it exists).

    This is done to ensure that all that are seen as results are from this run.
    """
    if test_dir.exists():
        link = test_dir.resolve()
        checkout.wipe(link)


def _delete_run_branch(repo_dir: Path, run_id: str):
    """Deletes the run branch."""
    repo = git.Repo(repo_dir)  # type: ignore
    logging.info(repo.git.clean("-xdf"))
    repo.heads.main.checkout(force=True)
    repo.delete_head(run_id, force=True)


def _copy_output_files(cc: cloud_client.CloudClient, base_wd: Path, run_id: str):
    """Copies the run output files from the cached location to the shared volume's run directory."""
    test_path: Path = base_wd / "results" / run_id
    if test_path.exists():
        cc.copy_tree(test_path)


def _clean_kubernetes(namespace):
    try:
        load_incluster_config()
        apps_v1 = kubernetes.client.AppsV1Api()  # type: ignore
        v1 = kubernetes.client.CoreV1Api()  # type: ignore
        ret = apps_v1.list_namespaced_deployment(namespace)
        for deployment in ret.items:
            logging.info("Delete deployment %s", deployment.metadata.name)
            apps_v1.delete_namespaced_deployment(
                deployment.metadata.name, namespace=namespace
            )
        ret = v1.list_namespaced_service(namespace=namespace)
        for service in ret.items:
            logging.info("Delete service %s", service.metadata.name)
            v1.delete_namespaced_service(service.metadata.name, namespace=namespace)
        ret = v1.list_namespaced_config_map(namespace=namespace)
        for configmap in ret.items:
            logging.info("Delete configmap %s", configmap.metadata.name)
            v1.delete_namespaced_config_map(
                configmap.metadata.name, namespace=namespace
            )
    except kubernetes.client.exceptions.ApiException as e:  # type: ignore
        if e.status == 403:
            # there is no such namespace
            pass
        else:
            raise


def _postprocess(cc: cloud_client.CloudClient, args):
    base_wd = Path(args.base_directory)

    repo_dir = base_wd / args.repo
    assert repo_dir.exists()

    if args.system_test_namespace:
        # clean up the system test namespace
        _clean_kubernetes(args.system_test_namespace)

    # copy the output files
    _copy_output_files(cc=cc, base_wd=base_wd, run_id=args.run_id)

    # delete the run branch
    # this can be done in parallel as it is not overlapping with other operations
    # on the file system
    try:
        _delete_run_branch(repo_dir, args.run_id)
    except git.exc.GitCommandError as ex:  # pylint: disable=no-member # type: ignore
        logging.warning("Failed to delete run branch %s", ex)


def run(args):
    """Runs the 'run' action."""
    structlog.contextvars.bind_contextvars(job_id=args.job_id)

    base_wd = Path(args.base_directory)

    if args.test_execution_spec:
        args.execution_spec = args.test_execution_spec

    execution_config = text_format.Parse(
        args.execution_spec, bazel_runner_pb2.BazelRunnerExecutionSpec()
    )
    logging.info("spec %s", execution_config)

    _checkout(args)

    repo_dir = base_wd / args.repo
    assert repo_dir.exists()

    result_base_path: Path = base_wd / "results" / args.run_id
    result_base_path.mkdir(parents=True, exist_ok=True)

    job_result_base_path: Path = base_wd / "results" / args.run_id / args.job_id
    job_result_base_path.mkdir(parents=True, exist_ok=True)

    targets_file: Path = job_result_base_path / "targets.txt"
    targets_file.write_text("\n".join(t for t in execution_config.targets))

    # checkout before running bazel info to ensure that the "bazel" command is good.
    test_dirs = find_all_testlogs_dirs(base_wd / "bazel-root")
    for test_dir in test_dirs:
        _clean_testlogs(test_dir=test_dir)
    returncode = _run_bazel(
        repo_dir=repo_dir,
        targets_path=targets_file,
        execution_config=execution_config,
        base_directory=base_wd,
        bep_file=job_result_base_path / "bep.pb",
        system_test_namespace=args.system_test_namespace,
        job_id=args.job_id,
        bazel_cache_endpoint=args.bazel_cache_endpoint,
        bes_endpoint=args.bes_endpoint,
        bazel_cpu=args.bazel_cpu,
        bazel_ram_mb=args.bazel_ram_mb,
        namespace_manager_endpoint=args.namespace_manager_endpoint,
    )
    logging.info("return code %s", returncode)
    test_dirs = find_all_testlogs_dirs(base_wd / "bazel-root")
    logging.info("test dirs %s", test_dirs)
    if returncode != 0:
        if test_dirs and not check_io_errors(test_dirs[0]):
            logging.warning("Detected IOException during execution")
            checkout.wipe(base_wd / "bazel-root")
    if returncode == 39:
        # 39 is the return code for the bazel cache eviction failure
        # see https://bazel.build/run/scripts
        # Note (dirk) I have seen 34 being returned when there is a bazel cache eviction failure and seperate
        # test failure.
        logging.info("Eviction failure: Running bazel with NO_BAZEL_CACHE")
        returncode = _run_bazel(
            repo_dir=repo_dir,
            targets_path=targets_file,
            execution_config=execution_config,
            base_directory=base_wd,
            bep_file=job_result_base_path / "bep.pb",
            system_test_namespace=args.system_test_namespace,
            job_id=args.job_id,
            bazel_cache_endpoint="",
            bes_endpoint=args.bes_endpoint,
            bazel_cpu=args.bazel_cpu,
            bazel_ram_mb=args.bazel_ram_mb,
            namespace_manager_endpoint=args.namespace_manager_endpoint,
            extra_env={"NO_BAZEL_CACHE": "1"},
        )

    for test_dir in test_dirs:
        _copy_command_log(test_path=test_dir, job_result_base_path=job_result_base_path)
        _copy_jvm_log(test_path=test_dir, job_result_base_path=job_result_base_path)
        _copy_output(test_path=test_dir, output_path=job_result_base_path / "testlogs")

    # write the return code file
    result_file: Path = job_result_base_path / "returncode.txt"
    result_file.write_text(str(returncode))

    _postprocess(cloud_client.create(args), args)

    sys.exit(_translate_return_code(returncode))
