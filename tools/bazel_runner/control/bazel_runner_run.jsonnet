// jsonnet file to generate a run job configuration.
//
// Usually called by schedule.py
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local env_limits = {
  LARGE_GPU: {
    limits: {
      cpu: 7,
      'nvidia.com/gpu': 1,
      memory: '%sGi' % 28,
    },
  },
  MULTI_GPU: {
    limits: {
      cpu: 7 * 2,
      'nvidia.com/gpu': 2,
      memory: '%sGi' % (28 * 2),
    },
  },
  SINGLE_GPU: {
    limits: {
      cpu: 7,
      'nvidia.com/gpu': 1,
      memory: '%sGi' % 28,
    },
  },
  CPU: {
    limits: {
      cpu: 7,
      'nvidia.com/gpu': 0,
      memory: '%sGi' % 28,
    },
  },
  SYSTEM_TEST: {
    limits: {
      cpu: 7,
      'nvidia.com/gpu': 0,
      memory: '%sGi' % 28,
    },
  },
  SYSTEM_TEST_GPU: {
    limits: {
      cpu: 7,
      'nvidia.com/gpu': 0,
      memory: '%sGi' % 28,
    },
  },
  PREMIUM_CPU: {
    limits: {
      cpu: 4,
      'nvidia.com/gpu': 0,
      memory: '%sGi' % 10,
    },
  },
};
local env_devices_visible = {
  LARGE_GPU: '0',
  MULTI_GPU: '0,1,',
  SINGLE_GPU: '0',
  CPU: '',
  SYSTEM_TEST: '',
  SYSTEM_TEST_GPU: '',
  PREMIUM_CPU: '',
};
local env_gpu_count = {
  LARGE_GPU: 1,
  MULTI_GPU: 2,
  SINGLE_GPU: 1,
  CPU: 0,
  SYSTEM_TEST: 0,
  SYSTEM_TEST_GPU: 0,
  PREMIUM_CPU: 0,
};
local envResource = {
  LARGE_GPU: 'large',
  MULTI_GPU: 'small',
  SINGLE_GPU: 'small',
  SYSTEM_TEST: null,
  SYSTEM_TEST_GPU: null,
  CPU: null,
  PREMIUM_CPU: 'premiumCpu',
};
local env_extra_args = {
  LARGE_GPU: ['--bazel-cpu', '7', '--bazel-ram-mb', std.toString(24 * 1024)],
  MULTI_GPU: ['--bazel-cpu', '14', '--bazel-ram-mb', std.toString(2 * 24 * 1024)],
  SINGLE_GPU: ['--bazel-cpu', '7', '--bazel-ram-mb', std.toString(24 * 1024)],
  CPU: ['--bazel-cpu', '7', '--bazel-ram-mb', std.toString(24 * 1024)],
  SYSTEM_TEST: ['--bazel-cpu', '7', '--bazel-ram-mb', std.toString(24 * 1024)],
  SYSTEM_TEST_GPU: ['--bazel-cpu', '7', '--bazel-ram-mb', std.toString(24 * 1024)],
  // the c3 machines that we use in dev have 3 cores and 16GB of RAM total, so we use 3 cores and 12GB of RAM
  PREMIUM_CPU: ['--bazel-cpu', '3', '--bazel-ram-mb', std.toString(12 * 1024)],
};
function(runId,
         jobName,
         jobId,
         repo,
         cloud,
         namespace,
         imageName,
         runId,
         executionSpec,
         env,
         pvId,
         serviceAccountName,
         bazelCacheEndpoint,
         besEndpoint,
         checkoutConfig,
         storageOutput)
  // allow on H100 if only CPU workload
  local tolerations = if env == 'CPU' then [{
    key: 'gpu',
    operator: 'Equal',
    value: '8h100',
    effect: 'NoSchedule',
  }] else if env == 'MULTI_GPU' then [
    // allow on multi-gpu
    {
      key: 'gpu',
      operator: 'Equal',
      value: '4l4',
      effect: 'NoSchedule',
    },
  ] else if env == 'GPU' then [
    {
      key: 'gpu',
      operator: 'Equal',
      value: '1l4',
      effect: 'NoSchedule',
    },
  ] else if env == 'LARGE_GPU' then [
    // allow on large GPU
    {
      key: 'gpu',
      operator: 'Equal',
      value: '8h100',
      effect: 'NoSchedule',
    },
  ] else if env == 'PREMIUM_CPU' then [
    // allow on C3
    {
      key: 'cpu-avx512fp16',
      operator: 'Equal',
      value: 'present',
      effect: 'NoSchedule',
    },
  ] else [];
  [
    assert env == 'MULTI_GPU' || env == 'SINGLE_GPU' || env == 'CPU' || env == 'LARGE_GPU' || env == 'PREMIUM_CPU' || env == 'SYSTEM_TEST' || env == 'SYSTEM_TEST_GPU';
    local gpuCount = env_gpu_count[env];
    local tolerations = nodeLib.tolerations(
      resource=envResource[env],
      env='DEV',
      count=gpuCount,
      cloud=cloud
    );
    local affinity = nodeLib.affinity(
      resource=envResource[env],
      env='DEV',
      count=gpuCount,
      cloud=cloud,
      extraRequiredMatchExpressions=[
        {
          key: 'topology.kubernetes.io/zone',
          operator: 'In',
          values:
            [
              'us-central1-a',
            ],
        },
      ],
      appName=null
    ) + {
      podAffinity: {
        preferredDuringSchedulingIgnoredDuringExecution: [
          {
            weight: 100,
            podAffinityTerm: {
              labelSelector: {
                matchExpressions: [
                  {
                    key: 'bazel-runner-run-id',
                    operator: 'In',
                    values: [runId],
                  },
                ],
              },
              topologyKey: 'kubernetes.io/hostname',
            },
          },
        ],
      },
    };
    {
      apiVersion: 'batch/v1',
      kind: 'Job',
      metadata: {
        name: jobName,
        namespace: namespace,
        labels: {
          'app.kubernetes.io/part-of': 'bazel-runner',
          'bazel-runner-run-id': runId,
          'bazel-runner-job-id': jobId,
          'bazel-runner-shard-id': pvId,
        },
      },
      spec: {
        // the job is removed after 24.
        ttlSecondsAfterFinished: 3600,
        template: {
          metadata: {
            labels: {
              'bazel-runner-run-id': runId,
              'bazel-runner-job-id': jobId,
            },
          },
          spec: {
            tolerations: tolerations,
            affinity: affinity,
            serviceAccountName: serviceAccountName,
            securityContext: {
              // bazel cannot be run as root
              runAsUser: 1000,
              fsGroup: 1000,
              fsGroupChangePolicy: 'OnRootMismatch',
            },
            containers: [
              {
                name: 'bazel-runner',
                image: imageName,
                args: [
                  'run',
                  '--repo',
                  repo,
                  '--run-id',
                  runId,
                  '--execution-spec',
                  executionSpec,
                  '--job-id',
                  jobId,
                  '--system-test-namespace',
                  '%s-%s' % [namespace, pvId],
                  '--bazel-cache-endpoint',
                  bazelCacheEndpoint,
                  '--bes-endpoint',
                  besEndpoint,
                  '--cloud',
                  cloud,
                  '--storage-output',
                  storageOutput,
                  '--minimal-free-disk-gb',
                  '100',
                  '--checkout-config',
                  checkoutConfig,
                  '--app-token-path',
                  '/github-app',
                ] + env_extra_args[env],
                env: [
                  {
                    name: 'CUDA_VISIBLE_DEVICES',
                    value: env_devices_visible[env],
                  },
                  {
                    name: 'AU_GPU_COUNT',
                    value: std.toString(env_gpu_count[env]),
                  },
                  {
                    name: 'AU_LARGE_GPU',
                    value: if env == 'LARGE_GPU' then '1' else '0',
                  },
                  // this is the location GKE injects the matching NVIDIA CUDA driver utils
                  {
                    name: 'TRITON_CUDA_DIRS',
                    value: '/home/<USER>/.local/nvidia/lib64/',
                  },
                ],
                volumeMounts: [
                  {
                    name: 'persistent-storage',
                    mountPath: '/mnt/efs/augment',
                  },
                  {
                    mountPath: '/github-app',
                    name: 'github-app-secret',
                  },
                  {
                    mountPath: '/cache',
                    name: 'cache-volume',
                  },
                  {
                    mountPath: '/dev/shm',
                    name: 'dshm',
                  },
                ]
                ,
                resources: env_limits[env],
              },
            ],
            volumes: [
              {
                name: 'persistent-storage',
                persistentVolumeClaim: {
                  claimName: 'filestore-checkpoint-claim',
                },
              },
              {
                name: 'github-app-secret',
                secret: {
                  secretName: 'bazel-runner-github-app-secret',  // pragma: allowlist secret
                  optional: false,
                },
              },
              {
                name: 'cache-volume',
                persistentVolumeClaim: {
                  claimName: 'bazel-runner-%s-pvc' % pvId,
                },
              },
              // add extra large shared memory
              {
                name: 'dshm',
                emptyDir: {
                  medium: 'Memory',
                  sizeLimit: '4Gi',
                },
              },
            ],
            restartPolicy: 'Never',
          },
        },
      },
    },
  ]
