// see https://www.notion.so/Bazel-based-testing-and-deployment-638c55d03c9a446c884fd0b0d0b25447?pvs=4#74fd85baf83f43fd97c47bb4485d218e for details
{
  deployment: [
    {
      name: 'bazel-runner-bigquery-export',
      kubecfg: {
        target: '//tools/bazel_runner/bazel_runner_bigquery_export:kubecfg',
        task: [
          {
            cloud: 'GCP_US_CENTRAL1_DEV',
            env: 'PROD',
            namespace: 'devtools',
          },
        ],
      },
      deployment_schedule_name: 'CICD',
      health: {
        tier: 'TIER_2',
        experts: {
          users: ['dirk'],
          slack_channel: '#system-services',
        },
      },
    },
  ],
}
