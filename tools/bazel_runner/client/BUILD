load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:python.bzl", "py_binary", "py_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

py_library(
    name = "client",
    srcs = ["bazel_runner_client.py"],
    visibility = ["//tools:__subpackages__"],
    deps = [
        "//base/python/grpc:client_options",
        "//tools/bazel_runner/control:bazel_runner_py_proto",
        "//tools/bazel_runner/server:test_runner_py_proto",
        requirement("protobuf"),
        requirement("grpcio"),
        requirement("certifi"),
    ],
)

pytest_test(
    name = "client_test",
    srcs = ["bazel_runner_client_test.py"],
    deps = [
        ":client",
    ],
)

py_library(
    name = "checkout_spec_factory",
    srcs = ["checkout_spec_factory.py"],
    visibility = ["//tools:__subpackages__"],
    deps = [
        "//tools/bazel_runner/git:checkout_py_proto",
        requirement("protobuf"),
    ],
)

pytest_test(
    name = "checkout_spec_factory_test",
    srcs = ["checkout_spec_factory_test.py"],
    deps = [
        ":checkout_spec_factory",
    ],
)

py_binary(
    name = "run_remote",
    srcs = ["run_remote.py"],
    deps = [
        ":checkout_spec_factory",
        ":client",
        "//base/logging:console_logging",
        "//tools/bazel_runner/control:bazel_runner_py_proto",
        requirement("protobuf"),
    ],
)
