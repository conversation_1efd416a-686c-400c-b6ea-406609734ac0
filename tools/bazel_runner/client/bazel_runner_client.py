"""Tooling to run an bazel command within Kubernetes."""

import collections
import datetime
import json
import logging
import os
import pathlib
import stat
import time
import typing
import uuid
from dataclasses import dataclass
from typing import Generator, Optional

import certifi
import grpc

from base.python.grpc import client_options
import tools.bazel_runner.bep_parser.test_summary_pb2 as test_summary_pb2
import tools.bazel_runner.server.test_runner_pb2 as test_runner_pb2
import tools.bazel_runner.server.test_runner_pb2_grpc as test_runner_pb2_grpc
from tools.bazel_runner.control import bazel_runner_pb2


@dataclass
class ExecutionResult:
    """Result of an BazelRunnerClient execution."""

    spec: bazel_runner_pb2.BazelRunnerExecutionGroupSpec
    requestor: str
    state: test_runner_pb2.RunState.ValueType
    jobs: typing.Sequence[test_runner_pb2.JobInfo]
    create_time: datetime.datetime
    last_state_change_time: datetime.date
    message: Optional[str]

    def overall_return_code(self) -> int:
        """Return the overall return code calculates from the job return codes."""
        if self.state == test_runner_pb2.RUN_STATE_PASSED:
            return 0
        if self.state != test_runner_pb2.RUN_STATE_FAILURE:
            return 37
        return_codes = collections.defaultdict(int)
        for job_info in self.jobs:
            return_codes[job_info.return_code] += 1
        if 1 in return_codes:
            return 1
        if 3 in return_codes:
            return 3
        if 4 in return_codes:
            return 4
        return 0

    def is_done(self):
        """Returns true if and only if the test execution finished giving the requested feedback."""
        return self.state in (
            test_runner_pb2.RUN_STATE_PASSED,
            test_runner_pb2.RUN_STATE_FAILURE,
        )


def setup_client(
    endpoint: str, insecure: bool = False
) -> test_runner_pb2_grpc.TestRunnerStub:
    """Setup the client stub for the test runner RPC."""
    json_config = json.dumps(
        {
            "methodConfig": [
                {
                    "name": [{"service": "TestRunner"}],
                    "retryPolicy": {
                        "maxAttempts": 5,
                        "initialBackoff": "1s",
                        "maxBackoff": "10s",
                        "backoffMultiplier": 2,
                        "retryableStatusCodes": ["UNAVAILABLE"],
                    },
                }
            ]
        }
    )

    if not insecure:
        creds = grpc.ssl_channel_credentials(pathlib.Path(certifi.where()).read_bytes())
        channel = grpc.secure_channel(
            endpoint,
            creds,
            options=client_options.create([("grpc.service_config", json_config)]),
        )
    else:
        channel = grpc.insecure_channel(
            endpoint,
            options=client_options.create([("grpc.service_config", json_config)]),
        )
    stub = test_runner_pb2_grpc.TestRunnerStub(channel)
    return stub


def _is_final(state: test_runner_pb2.RunState) -> bool:
    return state in {
        test_runner_pb2.RUN_STATE_ERROR,
        test_runner_pb2.RUN_STATE_ABORT,
        test_runner_pb2.RUN_STATE_FAILURE,
        test_runner_pb2.RUN_STATE_PASSED,
        test_runner_pb2.RUN_STATE_CANCEL,
        test_runner_pb2.RUN_STATE_TIMEOUT,
    }


class BazelRunnerClient:
    """Client to execute remote bazel runner tests and wait for the results."""

    def __init__(self, endpoint: str, insecure: bool = False):
        self.rpc_client = setup_client(endpoint, insecure)

    def schedule_run(
        self,
        test_execution_config: bazel_runner_pb2.BazelRunnerExecutionGroupSpec,
        requestor: str,
        tags: list[str],
        supersedes: bool = False,
        test_selection: test_runner_pb2.TestSelectionSpec | None = None,
        notification: test_runner_pb2.NotificationSpec | None = None,
    ) -> uuid.UUID:
        """Schedules a job run and returns the run id.

        Args:
            test_execution_config: the test execution group spec. It specifies the checkout and the tests to run.
            requestor: the requestor
            tags: the tags
            supersedes: if the run supersedes another runs
            test_selection: the test selection. If set to None, no test selection is done.
            notification: the notification (optional)
        """
        request = test_runner_pb2.ScheduleTestRequest()
        if test_execution_config:
            request.test_execution.MergeFrom(test_execution_config)
        if test_selection:
            request.test_selection.MergeFrom(test_selection)
        request.requestor = requestor
        for tag in tags:
            request.tags.append(tag)
        request.supersedes = supersedes
        if notification:
            request.notification.MergeFrom(notification)
        schedule_response = self.rpc_client.ScheduleTest(request)
        return uuid.UUID(schedule_response.run_id)

    def cancel_run(self, run_id: uuid.UUID, cancelled_by: str | None = None):
        """Cancels a pending job."""
        request = test_runner_pb2.CancelTestRequest(
            run_id=str(run_id), cancelled_by=cancelled_by or ""
        )
        self.rpc_client.CancelTest(request)

    def get_run_state(self, run_id: uuid.UUID) -> ExecutionResult:
        """Returns the run information for a given run id."""
        request = test_runner_pb2.GetTestInfoRequest()
        request.run_id = str(run_id)
        response = self.rpc_client.GetTestInfo(request)
        logging.debug("response %s", response)
        result = ExecutionResult(
            spec=response.test_execution,
            requestor=response.requestor,
            jobs=response.jobs,
            state=response.state,
            create_time=response.create_time.ToDatetime(),
            last_state_change_time=response.last_state_change_time.ToDatetime(),
            message=response.message,
        )
        return result

    def get_builds_events(
        self, run_id: uuid.UUID, job_id: uuid.UUID, min_sequence_number: int
    ) -> Generator[test_runner_pb2.GetBuildEventsResponse, None, None]:
        """Generates the build events for the given run and job."""
        request = test_runner_pb2.GetBuildEventsRequest()
        request.run_id = str(run_id)
        request.job_id = str(job_id)
        request.min_sequence_number = min_sequence_number
        for response in self.rpc_client.GetBuildEvents(request):
            yield response

    def get_test_cases(
        self, run_id: uuid.UUID, job_id: uuid.UUID, target_name: str
    ) -> list[test_summary_pb2.TestCase]:
        """Runs the test cases in a finished test target."""
        request = test_runner_pb2.GetTestCasesRequest()
        request.run_id = str(run_id)
        request.job_id = str(job_id)
        request.target_name = target_name
        response = self.rpc_client.GetTestCases(request)
        return response.test_cases

    def get_pending_runs(
        self,
    ) -> list[uuid.UUID]:
        """Returns the pending runds."""
        request = test_runner_pb2.GetPendingRunsRequest()
        response = self.rpc_client.GetPendingRuns(request)
        return [uuid.UUID(run_id) for run_id in response.run_ids]

    def search_runs_by_tag(
        self, tag: str, max_results: int = 10
    ) -> Generator[uuid.UUID, None, None]:
        """Returns the pending runds."""
        request = test_runner_pb2.SearchRunsRequest()
        request.tag = tag
        request.max_results = max_results
        for response in self.rpc_client.SearchRuns(request):
            yield uuid.UUID(response.run_id)

    def wait_for_run(
        self,
        run_id: uuid.UUID,
        timeout: datetime.timedelta = datetime.timedelta(hours=2),
        wait_duration: datetime.timedelta = datetime.timedelta(seconds=30),
    ) -> ExecutionResult:
        """Waits for a given run to finish."""
        response = None
        start_now = datetime.datetime.now(datetime.timezone.utc)
        while start_now + timeout > datetime.datetime.now():
            try:
                request = test_runner_pb2.GetTestInfoRequest()
                request.run_id = str(run_id)
                response = self.rpc_client.GetTestInfo(request)
                logging.debug("Response %s", response)
                if _is_final(response.state):
                    break
            except grpc.RpcError as rpc_error:
                # the server is temporarily down
                # we just assume the run isn't finished yet and continue.
                if (
                    rpc_error.code()  # pylint: disable=no-member # type: ignore
                    == grpc.StatusCode.UNAVAILABLE
                ):
                    logging.warning("Unable to check test run status: %s", rpc_error)
                else:
                    raise
            time.sleep(wait_duration.total_seconds())
        else:
            # timeout
            response = test_runner_pb2.GetTestInfoResponse()
            response.state = test_runner_pb2.RUN_STATE_TIMEOUT

        result = ExecutionResult(
            spec=response.test_execution,
            requestor=response.requestor,
            jobs=response.jobs,
            state=response.state,
            create_time=response.create_time.ToDatetime(),
            last_state_change_time=response.last_state_change_time.ToDatetime(),
            message=response.message,
        )
        return result

    def _download_archive_file(
        self,
        output_file: pathlib.Path,
        run_id: uuid.UUID,
        job_id: uuid.UUID,
        archive_file: str,
    ):
        request = test_runner_pb2.GetArchiveDataRequest()
        request.run_id = str(run_id)
        request.job_id = str(job_id)
        request.archive_file = archive_file
        logging.debug("%s -> %s", archive_file, output_file)
        with output_file.open(mode="w+b") as output:
            for response in self.rpc_client.GetArchiveData(request):
                output.write(response.content)

    def print_console_log(self, run_id: uuid.UUID, job_id: uuid.UUID):
        """Prints the console log."""
        request = test_runner_pb2.GetCommandLogRequest()
        request.run_id = str(run_id)
        request.job_id = str(job_id)
        for response in self.rpc_client.GetCommandLog(request):
            print(response.content)

    def merge_run_results(
        self,
        run_id: uuid.UUID,
        execution_result: ExecutionResult,
        local_testlogs: pathlib.Path,
    ):
        """Merge the results of the different run jobs."""
        for job_info in execution_result.jobs:
            logging.debug("merge %s", job_info)
            for test_file in job_info.archive_files:
                target_file = local_testlogs.joinpath(test_file)
                target_file.parent.mkdir(parents=True, exist_ok=True)
                if target_file.exists():
                    target_file.chmod(target_file.stat().st_mode | stat.S_IWUSR)
                self._download_archive_file(
                    target_file,
                    run_id=run_id,
                    job_id=uuid.UUID(job_info.job_id),
                    archive_file=test_file,
                )

    def get_runs(
        self,
        oldest_run_id: uuid.UUID | None = None,
        newest_run_id: uuid.UUID | None = None,
        oldest_inclusive: bool = False,
        newest_inclusive: bool = False,
        max_count: int = 100,
    ) -> typing.Iterable[test_runner_pb2.TestRunInfo]:
        """Returns the previous runs.

        Oldest_run_id and newest_run_id are inclusive or exclusive depending on the flags

        Args:
            oldest_run_id: the earliest run id to return
            newest_run_id: the latest runs id to return
            max_count: the maximum number of runs to return

        Returns:
            The runs will be sorted in reverse order of creation with the newest first
        """
        request = test_runner_pb2.GetRunsRequest()
        if oldest_run_id:
            request.oldest_run_id = str(oldest_run_id)
        if newest_run_id:
            request.newest_run_id = str(newest_run_id)
        request.oldest_inclusive = oldest_inclusive
        request.newest_inclusive = newest_inclusive
        request.max_count = max_count
        response: typing.Iterable[test_runner_pb2.GetRunsResponse] = (
            self.rpc_client.GetRuns(request)
        )
        for resp in response:
            yield from resp.runs


class NamespaceManagerClient:
    def __init__(self, endpoint: str, insecure: bool = False):
        self.rpc_client = NamespaceManagerClient._setup_client(endpoint, insecure)

    @staticmethod
    def _setup_client(
        endpoint: str, insecure: bool = False
    ) -> test_runner_pb2_grpc.NamespaceManagerStub:
        """Setup the client stub for the test runner RPC."""
        json_config = json.dumps(
            {
                "methodConfig": [
                    {
                        "name": [{"service": "NamespaceManager"}],
                        "retryPolicy": {
                            "maxAttempts": 1000,
                            "initialBackoff": "1s",
                            "maxBackoff": "10s",
                            "backoffMultiplier": 1.2,
                            "retryableStatusCodes": [
                                "UNAVAILABLE",
                                "RESOURCE_EXHAUSTED",
                            ],
                        },
                    }
                ]
            }
        )
        if not insecure:
            creds = grpc.ssl_channel_credentials(
                pathlib.Path(certifi.where()).read_bytes()
            )
            channel = grpc.secure_channel(
                endpoint,
                creds,
                options=client_options.create([("grpc.service_config", json_config)]),
            )
        else:
            channel = grpc.insecure_channel(
                endpoint,
                options=client_options.create([("grpc.service_config", json_config)]),
            )
        stub = test_runner_pb2_grpc.NamespaceManagerStub(channel)
        return stub

    def allocate_namespace(
        self, job_id: str, target_name: str, num_gpus: int
    ) -> tuple[str, str]:
        request = test_runner_pb2.AllocateNamespaceRequest()
        request.job_id = str(job_id)
        request.target_name = target_name
        request.num_gpus = num_gpus
        response = self.rpc_client.AllocateNamespace(request)
        return response.namespace, response.allocation_id

    def free_namespace(
        self, job_id: str, target_name: str, namespace: str, allocation_id: str
    ):
        request = test_runner_pb2.FreeNamespaceRequest()
        request.job_id = str(job_id)
        request.target_name = target_name
        request.namespace = namespace
        request.allocation_id = allocation_id
        self.rpc_client.FreeNamespace(request)


def namespace_allocation(num_gpus: int) -> Generator[str | None, None, None]:
    """Allocates a namespace for the current pod."""

    if "NAMESPACE_MANAGER_ENDPOINT" not in os.environ:
        yield None
        return

    client = NamespaceManagerClient(
        os.environ["NAMESPACE_MANAGER_ENDPOINT"], insecure=True
    )
    job_id = os.environ["TEST_RUNNER_JOB_ID"]
    target_name = os.environ["TARGET_NAME"]
    logging.info("allocating namespace for %s %s %s", job_id, target_name, num_gpus)
    namespace, allocation_id = client.allocate_namespace(job_id, target_name, num_gpus)
    logging.info("allocated namespace %s", namespace)
    yield namespace
    logging.info("freeing namespace %s", namespace)
    client.free_namespace(job_id, target_name, namespace, allocation_id)
    logging.info("freed namespace %s", namespace)
