# see tools/python_deps/Readme.md and/or https://www.notion.so/Bazel-Snippets-0235fb12e8ae4aec8332c610516ed5e2?pvs=4#4eb66162617e4e3cb0f5d7e55952e74a for details
#
# not on setuptools:
# we cannot upgrade to setuptools 70 without CVE-2024-6345 due to dependencies with
# opentelemetry. setuptools 69 has CVE-2024-6345, but we are not vulnerable to it.
# the test //tools/python_deps:check_lock_file_test ensures that the vulnerable pattern is not present in the lock file.
absl-py
anthropic[vertex]>=0.47.0
authlib
bandit
beautifulsoup4==4.12.3
cachetools
certifi>=2024.07.04 # CVE-2024-39689
cuda-python
cryptography>=42.0.4 # CVE-2024-26130
dataclasses_json>=0.6.1
delimited-protobuf
defusedxml
debugpy
immutabledict==4.1.0
stripe>=7.0.0
intervaltree>=3.1.0
fireworks-ai==0.15.12
flash-attn==2.5.8+augment.pytorch23
flashattn-hopper==3.0.0b1+c1d146c
flask
github-webhook
GitPython>=3.1.41 # CVE-2024-22190
gitignore-parser
gojsonnet==0.20.0
google-api-python-client-stubs
google-cloud-aiplatform[tokenization]>=1.60.0
google-genai==1.12.1
google-cloud-bigtable
google-cloud-bigquery>=3.17.0  # 3.14 is broken, 3.17.0 works.
google-cloud-pubsub
google-cloud-recaptcha-enterprise
google-cloud-storage
google-cloud-secret-manager
gql[aiohttp]>=3.5.2
grpc-interceptor
grpcio
grpcio-health-checking
grpcio-reflection
grpcio-tools
grpcio-status
# GUnicorn before 22 had CVE-2024-1135
gunicorn>=22
html5lib==1.1
InquirerPy
jupyter
jinja2
kubernetes
keras~=3.9.0 # see CVE-2025-1550, indirect dependency via tenserflow via scann
launchdarkly-api>=16.0.0
launchdarkly-server-sdk>=9.4.0
lru-dict==1.2.0
nvtx==0.2.8
markdownify==0.13.1
matplotlib
mock==4.0.3
mypy-protobuf==3.6.0
nbconvert
nbformat
notebook
numpy==1.24.0
openai>=1.70.0
opentelemetry-api
opentelemetry-exporter-otlp
opentelemetry-instrumentation-grpc
opentelemetry-sdk
pandas==2.2.1
pylspclient
prometheus-client
prometheus-flask-exporter
# should match the major version of protoc via rules_proto_grpc in MODULE.bazel
protobuf==4.25.6
# keep the version in sync with pybind11 in tools/bzl/deps/python_deps.bzl
pybind11==2.12.0
pydantic==2.5.2
PyGithub
pytz
pyjwt
pympler==1.0.1
pytest>=7.4
pytest-forked>=1.6.0
pytest-grpc>=0.8.0
pytest-shard
pytest-timeout
pytest-asyncio
python-dateutil
python-magic
python-ulid
pyyaml>=6.0.0
regex
requests_oauthlib
requests_toolbelt
safetensors
scann
segment-analytics-python
slack_sdk
structlog
tenacity
transformer-engine==0.13.0+augment.cu124.torch25.9b42134  # Uses augment pypi
unidiff==0.7.5
types-protobuf
typing_inspect
triton==3.1
torch==2.5.1
transformers==4.51.3
# NOTE(2024-07-08, arun): tree-sitter 0.22 introduces a breaking change that is
# incompatible with tree-sitter-languages.
tree-sitter~=0.21.3
tree-sitter-languages~=1.10.2
websockets>=14.0.0
# we do not depend on werkzeug directly, this is just to enforce the version
werkzeug>=3.0.3 # CVE-2024-34069
xgboost==2.1.0.dev2
syrupy==4.6.1
scikit-learn
jira==3.8.0
notion-client==2.3.0
pillow>=10.4.0  # Required for image processing in token counter
deepdiff
jsonref
