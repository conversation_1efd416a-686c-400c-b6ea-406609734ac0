local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local telemetryLib = import 'deploy/common/telemetry-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local endpoints = import 'services/deploy/endpoints.jsonnet';

function(env, namespace, cloud, namespace_config)
  local appName = 'genie';
  local backendConfig = gcpLib.createBackendConfig(app=appName,
                                                   cloud=cloud,
                                                   namespace=namespace,
                                                   healthCheck={
                                                     checkIntervalSec: 15,
                                                     port: 5000,
                                                     type: 'HTTPS',
                                                     requestPath: '/health',
                                                   },
                                                   iap=true);
  local service = {
    apiVersion: 'v1',
    kind: 'Service',
    metadata: {
      name: 'genie-svc',
      namespace: namespace,
      annotations: {
        'cloud.google.com/backend-config': std.manifestJson({ default: backendConfig.metadata.name }),
        'cloud.google.com/app-protocols': std.manifestJson({ 'https-svc': 'HTTPS' }),

      },
      labels: {
        app: appName,
      },
    },
    spec: {
      type: 'NodePort',
      selector: {
        app: appName,
      },
      ports: [
        {
          port: 5000,
          name: 'https-svc',
          targetPort: 'https-svc',
        },
      ],
    },
  };
  local serviceAccount = gcpLib.createServiceAccount(app=appName, cloud=cloud, env=env, namespace=namespace, iam=true);

  local bigtable = gcpLib.createBigtableTable(
    cloud=cloud,
    env=env,
    app=appName,
    namespace=namespace,
    tableName='genie-main',
    columnFamily=[
      {
        family: 'Main',
      },
    ],
    iamServiceAccountName=serviceAccount.iamServiceAccountName
  );
  local iapAudience = '/projects/%s/global/backendServices/%s' % [cloudInfo[cloud].projectNumber, if std.objectHas(namespace_config, 'iapAudience') then namespace_config.iapAudience else ''];

  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
  local centralClientCert = certLib.createCentralClientCert(name='%s-central-client-certificate' % appName,
                                                            namespace=namespace,
                                                            appName=appName,
                                                            env=env,
                                                            volumeName='central-client-certs',
                                                            dnsNames=grpcLib.grpcServiceNames(appName, namespace));
  local ingressHostname = if namespace == 'central' then 'genie.%s' % cloudInfo[cloud].internalDomainSuffix else 'genie.%s.t.%s' % [namespace, cloudInfo[cloud].internalDomainSuffix];
  local ingressFacingCert = certLib.createPublicServerCert(name='genie-public-server-certificate',
                                                           namespace=namespace,
                                                           appName=appName,
                                                           dnsNames=[ingressHostname],
                                                           volumeName='https-certs',
                                                           env=env);
  local config = {
    apiVersion: 'v1',
    kind: 'ConfigMap',
    metadata: {
      name: 'genie-config',
      namespace: namespace,
      annotations: {
        'reloader.stakater.com/match': 'true',
      },
      labels: {
        app: appName,
      },
    },
    data: {
      'config.json': std.manifestJson({
        bind_address: '0.0.0.0',
        port: 5000,
        metrics_server_bind_address: '0.0.0.0',
        metrics_server_port: 9090,
        full_role_name: 'genie-role',
        limited_role_name: 'genie-limited-role',
        slack_bot_endpoint: if cloud == 'GCP_US_CENTRAL1_DEV' then 'http://slack-bot-svc:80' else 'http://slack-bot-svc.devtools:80',
        minutes_to_live: if env == 'DEV' then 1 else 60,
        audience_prefix: [iapAudience],
        https_server_key: '/https-certs/tls.key',
        https_server_cert: '/https-certs/tls.crt',
        gcp_project_id: bigtable.projectId,
        gcp_instance_name: bigtable.instanceName,
        gcp_table_name: bigtable.tableName,
        cluster: cloud,
        central_client_mtls_config: if mtls then centralClientCert.config else null,
        tenant_watcher_endpoint: endpoints.getTenantWatcherGrpcUrl(env, namespace, cloud),
        trusted_acl_namespace: namespace,
        namespaces: if env == 'DEV' then [namespace] else null,
      }),
    },
  };
  local container =
    {
      name: 'genie',
      target: {
        name: '//tools/genie/backend:image',
        dst: 'genie',
      },
      args: [
        '--config-file',
        '/config/config.json',
      ],
      ports: [
        {
          containerPort: 5000,
          name: 'https-svc',
        },
      ],
      volumeMounts: [
        {
          name: 'config',
          mountPath: '/config',
          readOnly: true,
        },
        ingressFacingCert.volumeMountDef,
        centralClientCert.volumeMountDef,
      ],
      env: telemetryLib.telemetryEnv('genie', telemetryLib.collectorUri(env, namespace, cloud)) +
           [
             {
               name: 'RUST_BACKTRACE',
               value: '1',
             },
           ],
      readinessProbe: {
        httpGet: {
          path: '/health',
          scheme: 'HTTPS',
          port: 5000,
        },
        initialDelaySeconds: 5,
        periodSeconds: 10,
      },
      livenessProbe: {
        httpGet: {
          path: '/health',
          scheme: 'HTTPS',
          port: 5000,
        },
        initialDelaySeconds: 15,
        periodSeconds: 20,
      },
      resources: {
        limits: {
          cpu: 0.1,
          memory: '512Mi',
        },
      },
    };
  local roleBindings = if env == 'DEV' then [
    {
      apiVersion: 'rbac.authorization.k8s.io/v1',
      kind: 'RoleBinding',
      metadata: {
        name: 'genie-%s-role-backend-binding' % namespace,
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      subjects: [
        {
          kind: 'ServiceAccount',
          namespace: namespace,
          name: serviceAccount.name,
        },
      ],
      roleRef: {
        kind: 'ClusterRole',
        name: 'genie-backend-cluster-role',
        apiGroup: 'rbac.authorization.k8s.io',
      },
    },

  ] else [
    {
      apiVersion: 'rbac.authorization.k8s.io/v1',
      kind: 'ClusterRoleBinding',
      metadata: {
        name: 'genie-%s-role-backend-binding' % namespace,
        labels: {
          app: appName,
        },
      },
      subjects: [
        {
          kind: 'ServiceAccount',
          namespace: namespace,
          name: serviceAccount.name,
        },
      ],
      roleRef: {
        kind: 'ClusterRole',
        name: 'genie-backend-cluster-role',
        apiGroup: 'rbac.authorization.k8s.io',
      },
    },
  ];
  local genieRoleBinding = if env == 'DEV' then {
    apiVersion: 'rbac.authorization.k8s.io/v1',
    kind: 'RoleBinding',
    metadata: {
      name: 'genie-%s-role-binding' % namespace,
      namespace: namespace,
      labels: {
        app: 'genie',
      },
    },
    subjects: [
      {
        kind: 'ServiceAccount',
        namespace: namespace,
        name: serviceAccount.name,
      },
    ],
    roleRef: {
      kind: 'ClusterRole',
      name: 'genie-role',
      apiGroup: 'rbac.authorization.k8s.io',
    },

  }
  else {
    apiVersion: 'rbac.authorization.k8s.io/v1',
    kind: 'ClusterRoleBinding',
    metadata: {
      name: 'genie-%s-role-binding' % namespace,
      labels: {
        app: 'genie',
      },
    },
    subjects: [
      {
        kind: 'ServiceAccount',
        namespace: namespace,
        name: serviceAccount.name,
      },
    ],
    roleRef: {
      kind: 'ClusterRole',
      name: 'genie-role',
      apiGroup: 'rbac.authorization.k8s.io',
    },
  };
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);
  local pod =
    {
      affinity: affinity,
      tolerations: tolerations,
      priorityClassName: cloudInfo.envToPriorityClass(env),
      serviceAccountName: serviceAccount.name,
      containers: [
        container,
      ],
      volumes: [
        {
          name: 'config',
          configMap: {
            name: 'genie-config',
          },
        },
        ingressFacingCert.podVolumeDef,
        centralClientCert.podVolumeDef,
      ],
    };
  local frontendConfig = gcpLib.createFrontendConfig(app='genie', cloud=cloud, namespace=namespace);
  local ingressObjects = [
    {
      apiVersion: 'networking.k8s.io/v1',
      kind: 'Ingress',
      metadata: {
        annotations: {
          'kubernetes.io/ingress.class': 'gce',
          'cert-manager.io/cluster-issuer': certLib.getIngressIssuer(env),
          'kubernetes.io/ingress.allow-http': 'false',
          'networking.gke.io/v1beta1.FrontendConfig': frontendConfig.metadata.name,
        },
        labels: {
          app: appName,
        },
        name: 'genie-ingress',
        namespace: namespace,
      },
      spec: {
        ingressClassName: 'gce',
        tls: [
          {
            secretName: 'genie-ssl-cert',  // pragma: allowlist secret
            hosts: [ingressHostname],
          },
        ],
        rules: [
          {
            host: ingressHostname,
            http: {
              paths: [
                {
                  path: '/',
                  pathType: 'Prefix',
                  backend: {
                    service: {
                      name: 'genie-svc',
                      port: {
                        number: 5000,
                      },
                    },
                  },
                },
              ],
            },
          },
        ],
      },
    },
    frontendConfig,
    backendConfig,
  ];
  local deployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: 'genie',
      namespace: namespace,
      labels: {
        app: 'genie',
      },
      annotations: {
        'reloader.stakater.com/search': 'true',
      },
    },
    spec: {
      minReadySeconds: if env == 'DEV' then 0 else 60,
      replicas: if env == 'DEV' then 1 else 2,
      strategy: {
        type: 'RollingUpdate',
        rollingUpdate: {
          maxSurge: 1,
          maxUnavailable: 0,
        },
      },
      selector: {
        matchLabels: {
          app: 'genie',
        },
      },
      template: {
        metadata: {
          labels: {
            app: 'genie',
          },
        },
        spec: pod,
      },
    },
  };
  lib.flatten([
    config,
    ingressFacingCert.objects,
    service,
    serviceAccount.objects,
    deployment,
    bigtable.objects,
    ingressObjects,
    roleBindings,
    genieRoleBinding,
    centralClientCert.objects,
  ])
