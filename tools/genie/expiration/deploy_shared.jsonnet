function(cloud)
  [
    {
      apiVersion: 'rbac.authorization.k8s.io/v1',
      kind: 'ClusterRole',
      metadata: {
        name: 'expiration-watcher-cluster-role',
      },
      rules: [
        {
          apiGroups: [
            'rbac.authorization.k8s.io',
          ],
          resources: [
            'clusterrolebindings',
            'rolebindings',
          ],
          verbs: [
            'list',
            'delete',
          ],
        },
        {
          apiGroups: [
            'eng.augmentcode.com',
          ],
          resources: [
            'supportuiaccesses',
          ],
          verbs: [
            'list',
            'delete',
          ],
        },
      ],
    },
  ]
