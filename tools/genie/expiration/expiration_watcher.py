"""Watches for and deletes expired resources."""

from datetime import datetime, timezone
from time import sleep

import kubernetes
import structlog
from dateutil import parser
from prometheus_client import Counter, Gauge, start_http_server

from base.logging.struct_logging import setup_struct_logging
from base.python.signal_handler.signal_handler import StandardSignalHandler

log = structlog.get_logger()

# Gauge for resources that will expire but have not yet, labeled by resource (e.g.,
# "ClusterRoleBinding" or "RoleBinding").
_expiring_resources_gauge = Gauge(
    "au_expiring_resources_gauge",
    "Number of resources that will expire",
    ["resource"],
)

# Counter for the number of checks performed by the expiration watcher, labeled by status ("OK"
# or "ERROR").
_check_counter = Counter(
    "au_expiration_watcher_check_count",
    "Number of expiration watcher checks",
    ["resource", "status"],
)


def _is_expired(expires_at_str: str, now: datetime) -> bool:
    """Returns whether the given expires_at annotation is expiring."""
    expires_at = parser.parse(expires_at_str)
    return now >= expires_at


def filter_expiring_support_ui_accesses(
    support_ui_accesses: list[dict], now: datetime
) -> list[dict]:
    """Filter for SupportUIAccess resources that have expired."""
    result = []
    for r in support_ui_accesses:
        name = r["metadata"]["name"]
        namespace = r["metadata"]["namespace"]
        if r.get("spec", {}) and "expiresAt" in r["spec"]:
            expires_at = r["spec"]["expiresAt"]
            if not _is_expired(expires_at, now):
                log.info(
                    "%s/%s is not yet expired. (Expires at %s)",
                    namespace,
                    name,
                    expires_at,
                )
                continue
            log.info(
                "%s/%s is expiring. (Expires at %s)",
                namespace,
                name,
                expires_at,
            )
            result.append(r)
    return result


class ExpirationWatcher:
    """Watches for and deletes expired resources.

    This class runs a busy-loop that checks for expired resources at a given interval.

    A resource is considered expired if it has an annotation with key "expires_at" and the value is
    in the past. Timestamps are in ISO-8601 format (e.g., "2024-01-01T00:00:00Z")

    The currently supported resources are:
    - ClusterRoleBinding
    - RoleBinding
    - SupportUIAccess (Custom Resource)
    """

    def __init__(
        self,
        watch_interval_seconds: int,
        rbac_client: kubernetes.client.RbacAuthorizationV1Api,  # type: ignore
        custom_objects_client: kubernetes.client.CustomObjectsApi,  # type: ignore
    ):
        self._watch_interval_seconds = watch_interval_seconds
        self._rbac_client = rbac_client
        self._custom_objects_client = custom_objects_client

    def run(self):
        """Watch for expired resources. Does not return."""
        while True:
            now = _now()
            try:
                self._check_cluster_role_bindings(now)
                _check_counter.labels("ClusterRoleBinding", "OK").inc()
            except Exception as ex:  # pylint: disable=broad-exception-caught
                _check_counter.labels("ClusterRoleBinding", "ERROR").inc()
                log.error("Error while processing expiring resources: %s", ex)
                log.exception(ex)
            try:
                self._check_role_bindings(now)
                _check_counter.labels("RoleBinding", "OK").inc()
            except Exception as ex:  # pylint: disable=broad-exception-caught
                _check_counter.labels("RoleBinding", "ERROR").inc()
                log.error("Error while processing expiring resources: %s", ex)
                log.exception(ex)
            try:
                self._check_support_ui_accesses(now)
                _check_counter.labels("SupportUIAccess", "OK").inc()
            except Exception as ex:  # pylint: disable=broad-exception-caught
                _check_counter.labels("SupportUIAccess", "ERROR").inc()
                log.error("Error while processing expiring resources: %s", ex)
                log.exception(ex)
            sleep(self._watch_interval_seconds)

    def _check_cluster_role_bindings(self, now: datetime):
        """Delete expired ClusterRoleBindings."""
        cluster_role_bindings = [
            r for r in self._rbac_client.list_cluster_role_binding(watch=False).items
        ]
        expiring_cluster_role_bindings = [
            r
            for r in cluster_role_bindings
            if r.metadata.annotations and "expires_at" in r.metadata.annotations
        ]
        _expiring_resources_gauge.labels("ClusterRoleBinding").set(
            len(expiring_cluster_role_bindings)
        )

        if not expiring_cluster_role_bindings:
            log.info("No expiring ClusterRoleBindings")
            return

        log.info(
            "Processing %d expiring ClusterRoleBindings (out of %d total)",
            len(expiring_cluster_role_bindings),
            len(cluster_role_bindings),
        )

        for cluster_role_binding in expiring_cluster_role_bindings:
            expires_at = cluster_role_binding.metadata.annotations["expires_at"]
            if _is_expired(expires_at, now):
                self._rbac_client.delete_cluster_role_binding(
                    name=cluster_role_binding.metadata.name
                )
                log.info(
                    "Deleted ClusterRoleBinding %s", cluster_role_binding.metadata.name
                )
            else:
                log.info(
                    "%s is not yet expired. (Expires at %s)",
                    cluster_role_binding.metadata.name,
                    expires_at,
                )

    def _check_role_bindings(self, now: datetime):
        """Delete expired RoleBindings."""
        role_bindings = [
            r
            for r in self._rbac_client.list_role_binding_for_all_namespaces(
                watch=False
            ).items
        ]
        expiring_role_bindings = [
            r
            for r in role_bindings
            if r.metadata.annotations and "expires_at" in r.metadata.annotations
        ]
        _expiring_resources_gauge.labels("RoleBinding").set(len(expiring_role_bindings))

        if not expiring_role_bindings:
            log.info("No expiring RoleBindings")
            return

        log.info(
            "Processing %d expiring RoleBindings (out of %d total)",
            len(expiring_role_bindings),
            len(role_bindings),
        )

        for role_binding in expiring_role_bindings:
            expires_at = role_binding.metadata.annotations["expires_at"]
            if _is_expired(expires_at, now):
                self._rbac_client.delete_namespaced_role_binding(
                    name=role_binding.metadata.name,
                    namespace=role_binding.metadata.namespace,
                )
                log.info("Deleted RoleBinding %s", role_binding.metadata.name)
            else:
                log.info(
                    "%s/%s is not yet expired. (Expires at %s)",
                    role_binding.metadata.namespace,
                    role_binding.metadata.name,
                    expires_at,
                )

    def _check_support_ui_accesses(self, now):
        """Delete expired SupportUIAccess custom resources."""
        # Define the CRD details
        group = "eng.augmentcode.com"
        version = "v1"
        plural = "supportuiaccesses"

        # List all SupportUIAccess resources across all namespaces
        support_ui_accesses = self._custom_objects_client.list_cluster_custom_object(
            group=group,
            version=version,
            plural=plural,
        )

        # Filter for resources with expires_at in spec
        items = support_ui_accesses.get("items", [])
        expiring_support_ui_accesses = filter_expiring_support_ui_accesses(items, now)
        if not expiring_support_ui_accesses:
            log.info("No expiring SupportUIAccess resources")
            return

        log.info(
            "Processing %d expiring SupportUIAccess resources (out of %d total)",
            len(expiring_support_ui_accesses),
            len(items),
        )

        for support_ui_access in expiring_support_ui_accesses:
            name = support_ui_access["metadata"]["name"]
            namespace = support_ui_access["metadata"]["namespace"]

            self._custom_objects_client.delete_namespaced_custom_object(
                group=group,
                version=version,
                plural=plural,
                namespace=namespace,
                name=name,
            )


def _now() -> datetime:
    """Current datime in UTC.

    This only lives in its own method for ease of testing."""
    return datetime.now(timezone.utc)


def main():
    _ = StandardSignalHandler()
    setup_struct_logging()

    start_http_server(9090)

    kubernetes.config.load_incluster_config()  # type: ignore
    rbac_client = kubernetes.client.RbacAuthorizationV1Api()  # type: ignore
    custom_objects_client = kubernetes.client.CustomObjectsApi()  # type: ignore
    expiration_watcher = ExpirationWatcher(
        watch_interval_seconds=60,
        rbac_client=rbac_client,
        custom_objects_client=custom_objects_client,
    )
    expiration_watcher.run()


if __name__ == "__main__":
    main()
