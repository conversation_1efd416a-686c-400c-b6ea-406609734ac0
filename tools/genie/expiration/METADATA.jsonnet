{
  deployment: [
    {
      name: 'expiration-watcher',
      kubecfg: {
        target: '//tools/genie/expiration:kubecfg',
        task: [
          {
            namespace: 'central',
            cloud: 'GCP_US_CENTRAL1_PROD',
            env: 'PROD',
          },
          {
            namespace: 'central',
            cloud: 'GCP_US_CENTRAL1_GSC_PROD',
            env: 'PROD',
          },
          {
            namespace: 'central',
            cloud: 'GCP_EU_WEST4_PROD',
            env: 'PROD',
          },
        ],
      },
      health: {
        tier: 'TIER_1_B',
        experts: {
          users: ['dirk', 'jacqueline'],
          slack_channel: '#system-services',
        },
      },
    },
    {
      name: 'expiration-watcher-shared',
      kubecfg: {
        target: '//tools/genie/expiration:kubecfg_shared',
        task: [
          {
            cloud: 'ALL_GCP',
          },
        ],
      },
    },
  ],
}
