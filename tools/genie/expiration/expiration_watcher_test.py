"""Unit tests for expiration watcher."""

from datetime import datetime, timezone
from unittest.mock import MagicMock

from tools.genie.expiration.expiration_watcher import (
    ExpirationWatcher,
    _is_expired,
    filter_expiring_support_ui_accesses,
)


def test_is_expired():
    now = datetime(2024, 1, 1, 0, 0, 0, tzinfo=timezone.utc)
    assert _is_expired("2023-12-31T23:59:59Z", now)
    assert _is_expired("2024-01-01T00:00:00Z", now)
    assert not _is_expired("2024-01-01T00:00:01Z", now)


def test_role_binding_expiration():
    now = datetime(2024, 1, 1, 0, 0, 0, tzinfo=timezone.utc)
    mock_rbac_client = MagicMock()
    mock_custom_objects_client = MagicMock()
    expiration_watcher = ExpirationWatcher(
        watch_interval_seconds=60,
        rbac_client=mock_rbac_client,
        custom_objects_client=mock_custom_objects_client,
    )

    # Test that a RoleBidning is not deleted if it is not expired.
    unexpired_role_binding = MagicMock()
    unexpired_role_binding.metadata.annotations = {"expires_at": "2024-01-01T00:00:01Z"}
    unexpired_role_binding.metadata.name = "test-role-binding"
    unexpired_role_binding.metadata.namespace = "test-namespace"

    mock_rbac_client.list_role_binding_for_all_namespaces.return_value.items = [
        unexpired_role_binding
    ]

    expiration_watcher._check_role_bindings(now)
    mock_rbac_client.delete_namespaced_role_binding.assert_not_called()

    # Test that a RoleBinding is deleted if it is expired.
    expired_role_binding = MagicMock()
    expired_role_binding.metadata.annotations = {"expires_at": "2023-12-31T23:59:59Z"}
    expired_role_binding.metadata.name = "test-role-binding"
    expired_role_binding.metadata.namespace = "test-namespace"

    mock_rbac_client.list_role_binding_for_all_namespaces.return_value.items = [
        expired_role_binding
    ]

    expiration_watcher._check_role_bindings(now)
    mock_rbac_client.delete_namespaced_role_binding.assert_called_once_with(
        name=expired_role_binding.metadata.name,
        namespace=expired_role_binding.metadata.namespace,
    )


def test_cluster_role_binding_expiration():
    now = datetime(2024, 1, 1, 0, 0, 0, tzinfo=timezone.utc)
    mock_rbac_client = MagicMock()
    mock_custom_objects_client = MagicMock()
    expiration_watcher = ExpirationWatcher(
        watch_interval_seconds=60,
        rbac_client=mock_rbac_client,
        custom_objects_client=mock_custom_objects_client,
    )

    # Test that a ClusterRoleBinding is not deleted if it is not expired.
    unexpired_cluster_role_binding = MagicMock()
    unexpired_cluster_role_binding.metadata.annotations = {
        "expires_at": "2024-01-01T00:00:01Z"
    }
    unexpired_cluster_role_binding.metadata.name = "test-cluster-role-binding"

    mock_rbac_client.list_cluster_role_binding.return_value.items = [
        unexpired_cluster_role_binding
    ]

    expiration_watcher._check_cluster_role_bindings(now)
    mock_rbac_client.delete_cluster_role_binding.assert_not_called()

    # Test that a ClusterRoleBinding is deleted if it is expired.
    expired_cluster_role_binding = MagicMock()
    expired_cluster_role_binding.metadata.annotations = {
        "expires_at": "2023-12-31T23:59:59Z"
    }
    expired_cluster_role_binding.metadata.name = "test-cluster-role-binding"

    mock_rbac_client.list_cluster_role_binding.return_value.items = [
        expired_cluster_role_binding
    ]

    expiration_watcher._check_cluster_role_bindings(now)
    mock_rbac_client.delete_cluster_role_binding.assert_called_once_with(
        name=expired_cluster_role_binding.metadata.name
    )


def test_support_ui_access_expiration():
    # Test that a SupportUIAccess is not deleted if it is not expired.
    unexpired_support_ui_access = {
        "metadata": {"name": "test-support-ui-access", "namespace": "test-namespace"},
        "spec": {
            "userName": "test-user",
            "expiresAt": "2024-01-01T01:00:01Z",
            "scope": "full",
        },
    }
    now = datetime(2024, 1, 1, 0, 0, 0, tzinfo=timezone.utc)
    filtered = filter_expiring_support_ui_accesses([unexpired_support_ui_access], now)
    assert not filtered

    # Test that a SupportUIAccess is deleted if it is expired.
    expired_support_ui_access = {
        "metadata": {"name": "test-support-ui-access", "namespace": "test-namespace"},
        "spec": {
            "userName": "test-user",
            "expiresAt": "2023-12-31T23:59:59Z",
            "scope": "full",
        },
    }
    filtered = filter_expiring_support_ui_accesses([expired_support_ui_access], now)
    assert filtered == [expired_support_ui_access]
