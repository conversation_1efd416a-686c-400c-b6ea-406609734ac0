"""conftest.py is a special file that pytest will automatically load.

pytest will automatically load and execute before any other test files. This is a
good place to put fixtures that are used by multiple test files.
"""

import kubernetes
import time
import os
from pathlib import Path
from typing import Generator

import pytest

import base.python.k8s_test_helper.k8s_test_helper as k8s_test_helper
from base.python.cloud import cloud as cloud_lib

# Container details


def pytest_addoption(parser):
    """Add command line options to pytest."""
    parser.addoption(
        "--skip-deployment",
        action="store_true",
        help="skip deploy and delete of the models",
        default=False,
    )
    parser.addoption(
        "--cloud",
        help="Cloud to use",
        default=cloud_lib.get_default_cloud(),
        choices=cloud_lib.get_cloud_list(gcp_only=True),
    )


@pytest.fixture(scope="session")
def is_running_in_test_infra():
    return k8s_test_helper.is_running_in_test_infra()


@pytest.fixture(scope="session")
def test_deploy(
    request,
) -> Generator[k8s_test_helper.DeployInfo, None, None]:
    """Deploys a genie as pytest fixture."""
    k8s_test_helper.print_link_to_logs()
    skip_deploy = request.config.getoption("--skip-deployment")
    cloud = request.config.getoption("--cloud")
    assert cloud

    with k8s_test_helper.deploy(
        skip_deploy=skip_deploy,
        kubecfg_binaries=[
            Path("tools/genie/test/test_kubecfg.sh"),
        ],
        cloud=cloud,
    ) as deploy_info:
        yield deploy_info


def _wait_ingress(api_client: kubernetes.client.ApiClient | None, namespace, app_name):
    v1 = kubernetes.client.NetworkingV1Api(api_client)
    for _ in range(24):
        ret = v1.list_namespaced_ingress(namespace)
        for ingress in ret.items:
            if not ingress.metadata or not ingress.metadata.labels:
                continue
            ingress_app_name = ingress.metadata.labels.get("app")
            if (
                ingress_app_name == app_name
                and ingress.status.load_balancer
                and ingress.status.load_balancer.ingress
                and len(ingress.status.load_balancer.ingress) > 0
            ):
                print("Ingress is ready", flush=True)
                return ingress.spec.rules[0].host
        time.sleep(10)


@pytest.fixture(scope="session")
def genie_url(test_deploy: k8s_test_helper.DeployInfo, is_running_in_test_infra):
    if not is_running_in_test_infra:
        endpoint = _wait_ingress(
            test_deploy.kubectl.api_client,
            test_deploy.namespace,
            "genie",
        )
        # but we can't actually use the ingress as it has the IAP protection.
        # but we can hit the health endpoint

        assert endpoint, "Ingress didn't come up in time"
        with test_deploy.kubectl.port_forward("deployment/genie", 5000, 5000) as port:
            url = f"https://localhost:{port}"
            yield url
    else:
        yield f"https://genie-svc.{test_deploy.namespace}:5000"
