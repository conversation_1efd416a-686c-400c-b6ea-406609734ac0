load("@crates//:defs.bzl", "aliases", "all_crate_deps")
load("@rules_rust//cargo:defs.bzl", "cargo_build_script")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:rust.bzl", "rust_binary", "rust_oci_image", "rust_test")

proto_library(
    name = "genie_store_proto",
    srcs = ["genie_store.proto"],
    visibility = ["//tools:__subpackages__"],
    deps = [
        "//base/proto/services:access_proto",
        "@protobuf//:timestamp_proto",
    ],
)

rust_binary(
    name = "server",
    srcs = glob(["src/**/*.rs"]),
    aliases = aliases(),
    data = ["//tools/genie/frontend"],
    edition = "2021",
    proc_macro_deps = all_crate_deps(
        proc_macro = True,
    ),
    deps = all_crate_deps(
        normal = True,
    ) + [
        ":proto_gen",
        "//base/cloud/iap:iap_rs",
        "//base/logging:struct_logging_rs",
        "//base/logging/audit:audit_rs",
        "//base/metrics_server/rust:metrics_server",
        "//base/rust/tracing-tonic",
        "//services/lib/grpc/tls_config:grpc_tls_config_rs",
        "//services/tenant_watcher/client:client_rs",
        "//services/token_exchange/client:client_rs",
        "//third_party/bigtable_rs",
        "//third_party/tracing-actix-web",
    ],
)

rust_oci_image(
    name = "image",
    package_name = package_name(),
    binary = "server",
    tars = ["//tools/docker:grpc_health_probe_tar"],
    visibility = ["//tools/genie:__subpackages__"],
)

rust_test(
    name = "server_test",
    aliases = aliases(
        normal_dev = True,
        proc_macro_dev = True,
    ),
    crate = ":server",
    proc_macro_deps = all_crate_deps(
        proc_macro_dev = True,
    ),
    deps = all_crate_deps(
        normal_dev = True,
    ),
)

cargo_build_script(
    name = "proto_gen",
    srcs = [
        "build.rs",
    ],
    aliases = aliases(build = True),
    build_script_env = {
        "PROTOC": "$(execpath @protobuf//:protoc)",
    },
    data = [
        "//base/proto/services:access_proto",
        "//tools/bot:bot_proto",
        "//tools/genie/backend:genie_store_proto",
        "@protobuf//:protoc",
        "@protobuf//:timestamp_proto",
    ],
    proc_macro_deps = all_crate_deps(
        build_proc_macro = True,
    ),
    deps = all_crate_deps(
        build = True,
    ),
)

kubecfg(
    name = "kubecfg_shared",
    src = "deploy_shared.jsonnet",
    cluster_wide = True,
)
