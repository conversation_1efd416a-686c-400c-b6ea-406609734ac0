use clap::Parser;
use grpc_tls_config::TlsConfig;
use serde::{Deserialize, Serialize};
use std::{fs::File, path::Path};

/// structure representing the configuration information in the configuration file.
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Config {
    pub bind_address: String,
    pub port: u16,

    pub metrics_server_bind_address: String,
    pub metrics_server_port: u16,

    // the name of the full access role
    pub full_role_name: String,

    // the name of the limited access role
    pub limited_role_name: String,

    pub minutes_to_live: i64,

    pub slack_bot_endpoint: String,

    // populated if central client MTLS should be used.
    pub central_client_mtls_config: Option<TlsConfig>,

    pub tenant_watcher_endpoint: String,

    pub audience_prefix: Vec<String>,

    pub https_server_key: String,
    pub https_server_cert: String,

    pub gcp_project_id: String,
    pub gcp_instance_name: String,
    pub gcp_table_name: String,

    pub cluster: String,

    pub trusted_acl_namespace: String,

    // the namespaces to return instead of reading from kubernetes
    pub namespaces: Option<Vec<String>>,
}

impl Config {
    /// read the configuration from a file
    pub fn read(path: &Path) -> Result<Config, tonic::Status> {
        let file = File::open(path).map_err(|e| tonic::Status::internal(e.to_string()))?;

        let config: Config =
            serde_json::from_reader(file).map_err(|e| tonic::Status::internal(e.to_string()))?;
        Ok(config)
    }
}

#[derive(Parser, Debug)]
pub struct CliArguments {
    /// path to the configuration file
    #[arg(long)]
    pub config_file: std::path::PathBuf,
}
