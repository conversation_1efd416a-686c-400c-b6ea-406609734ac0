use std::sync::Arc;

use k8s_openapi::api::rbac::v1::RoleBinding;
use kube::CustomResource;
use kube::{
    api::{Api, PostParams},
    Client,
};
use schemars::JsonSchema;
use serde::{Deserialize, Serialize};
use tenant_watcher_client::TenantWatcherClient;

use crate::proto;

/// Represents the level of access for Kubernetes role bindings
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum AccessLevel {
    /// Limited access using the limited role name
    Limited,
    /// Full access using the full role name
    Full,
}

#[derive(
    CustomResource, Debug, Serialize, Deserialize, Default, Clone, PartialEq, Eq, JsonSchema,
)]
#[kube(
    group = "eng.augmentcode.com",
    version = "v1",
    singular = "supportuiaccess",
    plural = "supportuiaccesses",
    kind = "SupportUiAccess",
    namespaced
)]
#[allow(non_snake_case)]
pub struct SupportUiAccessSpec {
    userName: String,
    expiresAt: String,
    scope: String,
    namespaceScope: Option<String>,
    tenantId: Option<String>,
    tokenScopes: Option<Vec<String>>,
}

#[derive(Clone)]
pub struct Granter {
    // kube client
    kube_client: Client,
    // name of the role binding
    full_role_name: String,
    // limited role name
    limited_role_name: String,
    // time to live
    ttl: chrono::Duration,

    tenant_watcher_client: Arc<dyn TenantWatcherClient + Send + Sync>,
}

impl Granter {
    pub async fn new(
        full_role_name: String,
        limited_role_name: String,
        ttl: chrono::Duration,
        tenant_watcher_client: Arc<dyn TenantWatcherClient + Send + Sync>,
    ) -> Self {
        let kube_client = Client::try_default()
            .await
            .expect("Failed to create client");

        Granter {
            kube_client,
            full_role_name,
            limited_role_name,
            ttl,
            tenant_watcher_client,
        }
    }

    pub async fn get_tenant_id_and_namespace(
        &self,
        tenant_name: &str,
    ) -> Result<(String, String), Box<dyn std::error::Error>> {
        let tenants = self.tenant_watcher_client.get_tenants("").await?;
        let tenant = tenants.into_iter().find(|t| t.name == tenant_name);
        match tenant {
            Some(tenant) => Ok((tenant.id, tenant.shard_namespace)),
            None => Err(Box::new(std::io::Error::new(
                std::io::ErrorKind::NotFound,
                format!("Tenant {} does not exist", tenant_name),
            ))),
        }
    }

    pub async fn list_namespaces(&self) -> Result<Vec<String>, Box<dyn std::error::Error>> {
        let namespaces =
            kube::Api::<k8s_openapi::api::core::v1::Namespace>::all(self.kube_client.clone());
        let namespaces = namespaces.list(&Default::default()).await?;
        let namespaces = namespaces
            .iter()
            .flat_map(|ns| ns.metadata.name.clone())
            .collect();
        Ok(namespaces)
    }

    async fn check_namespace_exists(&self, namespace: &str) -> bool {
        let namespaces =
            kube::Api::<k8s_openapi::api::core::v1::Namespace>::all(self.kube_client.clone());
        namespaces.get(namespace).await.is_ok()
    }
    fn sanitize_name(&self, user_name: &str) -> String {
        user_name.replace([' ', '_'], "-")
    }

    pub fn get_expires_at(&self) -> chrono::DateTime<chrono::Utc> {
        chrono::Utc::now() + self.ttl
    }

    pub async fn create_role_binding(
        &self,
        access: &proto::access::KubernetesNamespaceAccess,
        user_name: &str,
        expires_at: chrono::DateTime<chrono::Utc>,
        access_level: AccessLevel,
    ) -> Result<(), Box<dyn std::error::Error>> {
        // format as ISO-8601
        let expires_at_str = expires_at.to_rfc3339();

        if !self.check_namespace_exists(&access.namespace).await {
            return Err(Box::new(std::io::Error::new(
                std::io::ErrorKind::NotFound,
                format!("Namespace {} does not exist", access.namespace),
            )));
        }

        let role_binding_name = format!("genie-{}", self.sanitize_name(user_name));
        let role_binding = RoleBinding {
            metadata: k8s_openapi::apimachinery::pkg::apis::meta::v1::ObjectMeta {
                name: Some(role_binding_name.to_string()),
                namespace: Some(access.namespace.to_string()),
                labels: Some({
                    let mut labels = std::collections::BTreeMap::new();
                    labels.insert("genie".to_string(), "true".to_string());
                    labels
                }),
                annotations: Some({
                    let mut annotations = std::collections::BTreeMap::new();
                    annotations.insert("expires_at".to_string(), expires_at_str);
                    annotations
                }),
                ..Default::default()
            },
            subjects: Some(vec![k8s_openapi::api::rbac::v1::Subject {
                kind: "User".to_string(),
                api_group: None,
                name: format!("{user_name}@augmentcode.com"),
                namespace: None,
            }]),
            role_ref: k8s_openapi::api::rbac::v1::RoleRef {
                api_group: "rbac.authorization.k8s.io".to_string(),
                kind: "ClusterRole".to_string(),
                name: match access_level {
                    AccessLevel::Full => self.full_role_name.clone(),
                    AccessLevel::Limited => self.limited_role_name.clone(),
                },
            },
        };

        let params = PostParams {
            ..Default::default()
        };

        let role_binding_api: Api<RoleBinding> =
            Api::namespaced(self.kube_client.clone(), &access.namespace);

        // Create the RoleBinding
        match role_binding_api.create(&params, &role_binding).await {
            Ok(created_role_binding) => {
                tracing::info!("RoleBinding created: {:?}", created_role_binding);
                Ok(())
            }
            Err(kube::Error::Api(e)) => {
                if e.code == 409 {
                    tracing::info!("RoleBinding already exists");
                    let patched_role_binding = role_binding_api
                        .replace(&role_binding_name, &params, &role_binding)
                        .await?;
                    tracing::info!("RoleBinding patched: {:?}", patched_role_binding);
                    Ok(())
                } else {
                    tracing::error!("Failed to create RoleBinding: {:?}", e);
                    Err(Box::new(e))
                }
            }
            Err(e) => {
                tracing::error!("Failed to create RoleBinding: {:?}", e);
                Err(Box::new(e))
            }
        }
    }

    pub async fn create_support_ui_access(
        &self,
        access: &proto::access::SupportUiAccess,
        user_name: &str,
        expires_at: chrono::DateTime<chrono::Utc>,
        trusted_acl_namespace: &str,
    ) -> Result<(), Box<dyn std::error::Error>> {
        let scope = access
            .scope
            .map(|scope| proto::access::SupportUiAccessScope::try_from(scope).unwrap());

        if access.tenant_name.is_empty() && scope != Some(proto::access::SupportUiAccessScope::Full)
        {
            return Err(Box::new(std::io::Error::new(
                std::io::ErrorKind::NotFound,
                "Tenant name is empty",
            )));
        }

        let (tenant_id, namespace, namespace_scope, suffix) =
            if scope == Some(proto::access::SupportUiAccessScope::Full) {
                // don't create support ui access for full access
                return Err(Box::new(std::io::Error::new(
                    std::io::ErrorKind::NotFound,
                    "Full access is not supported",
                )));
            } else if scope == Some(proto::access::SupportUiAccessScope::KubernetesLimited) {
                // don't create support ui access for kubernetes limited access
                return Err(Box::new(std::io::Error::new(
                    std::io::ErrorKind::NotFound,
                    "Kubernetes limited access is not supported",
                )));
            } else if access.tenant_name == "*" {
                (
                    Some("*".to_string()),
                    trusted_acl_namespace.to_string(),
                    "*".to_string(),
                    "-all-tenants".to_string(),
                )
            } else {
                let (tenant_id, namespace) = self
                    .get_tenant_id_and_namespace(&access.tenant_name)
                    .await?;
                (
                    Some(tenant_id),
                    trusted_acl_namespace.to_string(),
                    namespace,
                    format!("-{}", access.tenant_name),
                )
            };
        if !self.check_namespace_exists(&namespace).await {
            return Err(Box::new(std::io::Error::new(
                std::io::ErrorKind::NotFound,
                format!("Namespace {} does not exist", namespace),
            )));
        }
        let support_ui_access_name = format!("genie-{}{}", self.sanitize_name(user_name), suffix);

        // Extract token_scopes if they exist in the access object
        let token_scopes = if !access.token_scopes.is_empty() {
            Some(access.token_scopes.clone())
        } else {
            None
        };

        let mut support_ui_access = SupportUiAccess {
            metadata: k8s_openapi::apimachinery::pkg::apis::meta::v1::ObjectMeta {
                name: Some(support_ui_access_name.to_string()),
                namespace: Some(namespace.to_string()),
                labels: Some({
                    let mut labels = std::collections::BTreeMap::new();
                    labels.insert("genie".to_string(), "true".to_string());
                    labels
                }),
                ..Default::default()
            },
            spec: SupportUiAccessSpec {
                userName: user_name.to_string(),
                expiresAt: expires_at.to_rfc3339(),
                scope: match scope {
                    Some(proto::access::SupportUiAccessScope::Users) => "users".to_string(),
                    Some(proto::access::SupportUiAccessScope::Requests) => "requests".to_string(),
                    Some(proto::access::SupportUiAccessScope::Full) => "full".to_string(),
                    Some(proto::access::SupportUiAccessScope::KubernetesLimited) => {
                        "kubernetes_limited".to_string()
                    }
                    None => "".to_string(),
                },
                tenantId: tenant_id,
                namespaceScope: Some(namespace_scope),
                tokenScopes: token_scopes,
            },
        };

        let support_ui_access_api: Api<SupportUiAccess> =
            Api::namespaced(self.kube_client.clone(), &namespace);

        // Create the SupportUIAccess
        match support_ui_access_api
            .create(&PostParams::default(), &support_ui_access)
            .await
        {
            Ok(created_support_ui_access) => {
                tracing::info!("SupportUiAccess created: {:?}", created_support_ui_access);
                Ok(())
            }
            Err(kube::Error::Api(e)) => {
                if e.code == 409 {
                    let existing_support_ui_access =
                        support_ui_access_api.get(&support_ui_access_name).await?;
                    tracing::info!(
                        "SupportUiAccess already exists: {:?}",
                        existing_support_ui_access
                    );

                    support_ui_access.metadata.resource_version =
                        existing_support_ui_access.metadata.resource_version;
                    support_ui_access_api
                        .replace(
                            &support_ui_access_name,
                            &PostParams::default(),
                            &support_ui_access,
                        )
                        .await?;
                    tracing::info!("SupportUiAccess patched: {:?}", support_ui_access);
                    Ok(())
                } else {
                    tracing::error!("Failed to create SupportUiAccess: {:?}", e);
                    Err(Box::new(e))
                }
            }
            Err(e) => {
                tracing::error!("Failed to create SupportUiAccess: {:?}", e);
                Err(Box::new(e))
            }
        }
    }
}
