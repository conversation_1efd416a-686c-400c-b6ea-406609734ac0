extern crate actix_files;
extern crate actix_web;

use crate::proto::devtools_bot;
use crate::proto::devtools_bot::devtools_bot_client::DevtoolsBotClient;
use actix_csrf::extractor::Csrf;
use actix_csrf::extractor::CsrfGuarded;
use actix_csrf::extractor::CsrfToken;
use actix_csrf::CsrfMiddleware;
use actix_files as fs;
use actix_web::http::Method;
use actix_web::web::Data;
use actix_web::{web, App, HttpRequest, HttpResponse, HttpServer, Result};
use clap::Parser;
use grpc_tls_config::get_client_tls_creds;
use iap::{GcpIapVerifier, GcpIapVerifierImpl};
use prost_wkt_types::Timestamp;
use rand::rngs::StdRng;
use serde::{Deserialize, Serialize};
use std::fs::File;
use std::io;
use std::io::BufReader;
use std::path::PathBuf;
use std::sync::Arc;
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use struct_logging::setup_struct_logging;
use tenant_watcher_client::{TenantWatcherClient, TenantWatcherClientImpl};
use tokio::select;
use tonic::transport::Channel;
use tower::ServiceBuilder;
use tracing_actix_web::DefaultRootSpanBuilder;
use tracing_tonic::client::TracingService;
use uuid::Uuid;

use rustls::ServerConfig;

use crate::config::{CliArguments, Config};

mod access;
mod config;
mod metrics;
mod store;

use access::AccessLevel;

pub mod proto {

    pub mod devtools_bot {
        tonic::include_proto!("devtools_bot");
    }

    pub mod genie_store {
        tonic::include_proto!("genie_store");
    }

    pub mod access {
        tonic::include_proto!("access");
    }
}

async fn single_page_app(req: HttpRequest, state: web::Data<GenieState>) -> Result<fs::NamedFile> {
    verify_jwt(&req, state.iap_verifier.as_ref())?;
    let path: PathBuf = PathBuf::from("tools/genie/frontend/dist/index.html");
    Ok(fs::NamedFile::open(path)?)
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AccessResponse {
    /// true if the proposal was auto-approved by the system,
    /// false if the proposal needs two-key approval via slack
    pub auto_approved: bool,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ProposeAccessRequest {
    pub access: proto::access::AccessType,
    pub reason: String,
    pub csrf: CsrfToken,
}

impl CsrfGuarded for ProposeAccessRequest {
    fn csrf_token(&self) -> &CsrfToken {
        &self.csrf
    }
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ApproveAccessRequest {
    pub csrf: CsrfToken,
}

impl CsrfGuarded for ApproveAccessRequest {
    fn csrf_token(&self) -> &CsrfToken {
        &self.csrf
    }
}

fn extract_jwt_from_header(req: &HttpRequest) -> Option<String> {
    let jwt = req.headers().get("x-goog-iap-jwt-assertion");
    if let Some(jwt) = jwt {
        if let Ok(jwt) = jwt.to_str() {
            return Some(jwt.to_string());
        } else {
            tracing::warn!("JWT is not a valid string");
        }
    } else {
        tracing::warn!("JWT not found in headers");
    }
    None
}

fn verify_jwt<T: GcpIapVerifier>(req: &HttpRequest, iap_verifier: &T) -> Result<()> {
    let jwt = extract_jwt_from_header(req);
    if let Some(jwt) = jwt {
        if let Ok(email) = iap_verifier.verify(&jwt) {
            tracing::info!("Access by {}", email);
            Ok(())
        } else {
            tracing::warn!("JWT is not valid");
            Err(actix_web::error::ErrorUnauthorized("Unauthorized"))
        }
    } else {
        Err(actix_web::error::ErrorUnauthorized("Unauthorized"))
    }
}

fn extract_user_from_header(req: &HttpRequest) -> Option<String> {
    let user_email = req.headers().get("X-Goog-Authenticated-User-Email");
    if let Some(user_email) = user_email {
        if let Ok(user_email) = user_email.to_str() {
            if let Some(user_email) = user_email.strip_prefix("accounts.google.com:") {
                if let Some(user_name) = user_email.strip_suffix("@augmentcode.com") {
                    return Some(user_name.to_string());
                } else {
                    tracing::warn!("User email does not end with @augmentcode.com");
                }
            } else {
                tracing::warn!("User email does not start with accounts.google.com:");
            }
        } else {
            tracing::warn!("User email is not a valid string");
        }
    } else {
        tracing::warn!("User email not found in headers");
    }
    None
}

async fn create_slack_bot_client(
    endpoint: &str,
) -> Result<DevtoolsBotClient<TracingService>, tonic::transport::Error> {
    let endpoint = endpoint.to_string();
    tracing::info!("Creating bot client: endpoint={endpoint}",);
    let channel = Channel::from_shared(endpoint)
        .expect("Failed to parse endpoint")
        .timeout(Duration::from_secs(10))
        .connect()
        .await?;

    let channel = ServiceBuilder::new()
        .layer_fn(TracingService::new)
        .service(channel);

    let client = DevtoolsBotClient::new(channel);
    Ok(client)
}

async fn create_tenant_watcher_client(
    endpoint: &str,
    tls_config: Option<tonic::transport::ClientTlsConfig>,
) -> Result<Arc<dyn TenantWatcherClient + Send + Sync>, tonic::transport::Error> {
    tracing::info!("Creating tenant watcher client: endpoint={endpoint}",);
    let client = TenantWatcherClientImpl::new(endpoint, tls_config, Duration::from_secs(5));
    Ok(Arc::new(client))
}

async fn get_namespaces(_req: HttpRequest, state: web::Data<GenieState>) -> HttpResponse {
    if let Some(namespaces) = state.namespaces.clone() {
        return HttpResponse::Ok().json(namespaces);
    }
    let namespaces = state.granter.list_namespaces().await;
    match namespaces {
        Ok(namespaces) => HttpResponse::Ok().json(namespaces),
        Err(e) => {
            tracing::error!("Failed to list namespaces: {}", e);
            HttpResponse::InternalServerError().json("")
        }
    }
}

#[derive(Serialize, Deserialize)]
struct TenantItem {
    name: String,
    id: String,
    shard_namespace: String,
}

async fn get_tenants(_req: HttpRequest, state: web::Data<GenieState>) -> HttpResponse {
    // get the tenants for all namespaces
    let tenants = state.tenant_watcher_client.get_tenants("").await;
    match tenants {
        Ok(tenants) => HttpResponse::Ok().json(
            tenants
                .into_iter()
                .map(|t| TenantItem {
                    name: t.name,
                    id: t.id,
                    shard_namespace: t.shard_namespace,
                })
                .collect::<Vec<_>>(),
        ),
        Err(e) => {
            tracing::error!("Failed to list tenants: {}", e);
            HttpResponse::InternalServerError().json("")
        }
    }
}

#[derive(Serialize, Deserialize)]
struct TokenScopes {
    scopes: Vec<String>,
}

async fn get_token_scopes(_req: HttpRequest) -> HttpResponse {
    // Get the standard token scopes from the token exchange client
    let scopes = token_exchange_client::get_token_scopes()
        .into_iter()
        .map(|s| s.as_str_name().to_string())
        .collect::<Vec<_>>();

    HttpResponse::Ok().json(TokenScopes { scopes })
}

async fn propose_access(
    req: HttpRequest,
    item: Csrf<web::Json<ProposeAccessRequest>>,
    state: web::Data<GenieState>,
    audit_logger: web::Data<audit::AuditLogger>,
) -> HttpResponse {
    if let Err(res) = verify_jwt(&req, state.iap_verifier.as_ref()) {
        return res.error_response();
    }

    let Some(user_name) = extract_user_from_header(&req) else {
        return HttpResponse::BadRequest().body("User name must be set");
    };

    if item.access.r#type.is_none() {
        return HttpResponse::BadRequest().body("Access type missing or failed deserialization");
    }

    // Handle support UI access
    if let Some(proto::access::access_type::Type::SupportUiAccess(a)) = item.access.r#type.as_ref()
    {
        // Check if the access scope is Users, which is always allowed
        if a.scope != Some(proto::access::SupportUiAccessScope::Users as i32) {
            // Get tenants from all shards to find the right one
            if let Ok(tenants) = state.tenant_watcher_client.get_tenants("").await {
                // Find the specific tenant by name in the returned list
                if let Some(tenant) = tenants.iter().find(|t| t.name == a.tenant_name) {
                    // Check if Genie access is blocked via tenant flag
                    if let Some(config) = &tenant.config {
                        if let Some(block_genie_access) =
                            config.configs.get("block_genie_request_access")
                        {
                            if block_genie_access == "true" {
                                tracing::warn!(
                                    "Blocked access attempt to tenant {} by user {}: Genie access is blocked by tenant flag",
                                    a.tenant_name,
                                    user_name
                                );
                                return HttpResponse::Forbidden().body(
                                    "Access to this tenant is blocked by tenant configuration",
                                );
                            }
                        }
                    }
                } else {
                    tracing::error!("Tenant {} not found", a.tenant_name);
                    return HttpResponse::NotFound()
                        .body(format!("Tenant {} not found", a.tenant_name));
                }
            }
        }

        let expires_at = state.granter.get_expires_at();

        // Check if this is a token scope request (which requires approval)
        let has_token_scopes = !a.token_scopes.is_empty();

        // Auto-approval path for standard access types (Users, Requests, and KubernetesLimited)
        if a.scope != Some(proto::access::SupportUiAccessScope::Full as i32) && !has_token_scopes {
            let (create_support_ui, create_role_binding) = if a.scope
                == Some(proto::access::SupportUiAccessScope::Users as i32)
            {
                audit_logger.write_audit_log(
                    &user_name,
                    audit::INTERNAL_IAP,
                    Some(&a.tenant_name),
                    Some(audit::Resource::GenieRequest {
                        access_type: "user".to_string(),
                        business_reason: item.reason.clone(),
                        ttl: expires_at.to_rfc3339(),
                        auto_approve: true,
                    }),
                    Some(format!(
                        "User access requested (and auto-approved) for tenant {0} by user {user_name}",
                        a.tenant_name
                    )),
                );
                (true, false)
            } else if a.scope == Some(proto::access::SupportUiAccessScope::Requests as i32) {
                audit_logger.write_audit_log(
                    &user_name,
                    audit::INTERNAL_IAP,
                    Some(&a.tenant_name),
                    Some(audit::Resource::GenieRequest {
                        access_type: "request".to_string(),
                        business_reason: item.reason.clone(),
                        ttl: expires_at.to_rfc3339(),
                        auto_approve: true,
                    }),
                    Some(format!(
                        "Support UI access requested (and auto-approved) for tenant {0} by user {user_name}",
                        a.tenant_name
                    )),
                );
                (true, false)
            } else if a.scope == Some(proto::access::SupportUiAccessScope::KubernetesLimited as i32)
            {
                audit_logger.write_audit_log(
                    &user_name,
                    audit::INTERNAL_IAP,
                    Some(&a.tenant_name),
                    Some(audit::Resource::GenieRequest {
                        access_type: "kubernetes_limited".to_string(),
                        business_reason: item.reason.clone(),
                        ttl: expires_at.to_rfc3339(),
                        auto_approve: true,
                    }),
                    Some(format!(
                        "Limited Kubernetes access requested (and auto-approved) for namespace {0} by user {user_name}",
                        a.tenant_name
                    )),
                );
                (false, true)
            } else {
                return HttpResponse::BadRequest().body("Unsupported access type");
            };

            // Notify slack
            let bot_request = devtools_bot::NotifyAccessProposedRequest {
                proposer: user_name.clone(),
                reason: item.reason.clone(),
                approval_url: None,
                access: Some(item.access.clone()),
                cluster: state.cluster.clone(),
            };
            let mut slack_client = state.slack_client.as_ref().clone();
            if let Err(e) = slack_client.notify_access_proposed(bot_request).await {
                tracing::error!("Failed to notify slack: {}", e);
                return HttpResponse::InternalServerError().body("Failed to notify slack");
            }

            // Grant access
            // this is done after the slack notification so we never get in a situation where the DB
            // considers the request approved but the user never hears about it
            if create_support_ui {
                if let Err(e) = state
                    .granter
                    .create_support_ui_access(
                        a,
                        &user_name,
                        expires_at,
                        &state.trusted_acl_namespace,
                    )
                    .await
                {
                    tracing::error!("Failed to give access: {}", e);
                    return HttpResponse::InternalServerError().body("Failed to give access");
                }
            }
            if create_role_binding {
                let a = proto::access::KubernetesNamespaceAccess {
                    namespace: a.tenant_name.clone(),
                };
                if let Err(e) = state
                    .granter
                    .create_role_binding(&a, &user_name, expires_at, AccessLevel::Limited)
                    .await
                {
                    tracing::error!("Failed to give access: {}", e);
                    return HttpResponse::InternalServerError().body("Failed to give access");
                }
            }

            // we are done now for user and support access.
            // we wrote the audit log and notified slack and granted access
            HttpResponse::Ok().json(AccessResponse {
                auto_approved: true,
            })
        } else if has_token_scopes && a.scope.is_none() {
            // Token scope access requires approval
            tracing::info!("Token scope access proposed for {}: {:?}", user_name, item);
            let uuid = Uuid::new_v4().to_string();
            let Ok(approval_url) = req.url_for("approval page", [uuid.clone()]) else {
                return HttpResponse::InternalServerError()
                    .body("Failed to generate approval link");
            };

            // Log the token scope access request
            let token_scopes_str = a.token_scopes.to_vec().join(", ");
            audit_logger.write_audit_log(
                &user_name,
                audit::INTERNAL_IAP,
                Some(&a.tenant_name),
                Some(audit::Resource::GenieRequest {
                    access_type: "token_scope".to_string(),
                    business_reason: item.reason.clone(),
                    ttl: expires_at.to_rfc3339(),
                    auto_approve: false,
                }),
                Some(format!(
                    "Token scope access requested for tenant {0} with scopes [{1}] by user {user_name}",
                    a.tenant_name, token_scopes_str
                )),
            );

            // Notify slack with approval URL
            let bot_request = devtools_bot::NotifyAccessProposedRequest {
                proposer: user_name.clone(),
                reason: item.reason.clone(),
                approval_url: Some(approval_url.to_string()),
                access: Some(item.access.clone()),
                cluster: state.cluster.clone(),
            };
            let mut slack_client = state.slack_client.as_ref().clone();
            if let Err(e) = slack_client.notify_access_proposed(bot_request).await {
                tracing::error!("Failed to notify slack: {}", e);
                return HttpResponse::InternalServerError().body("Failed to notify slack");
            }

            // Store the proposal in the database
            let bigtable_entry = proto::genie_store::AccessProposal {
                access: Some(item.access.clone()),
                proposer: user_name.clone(),
                reason: item.reason.clone(),
                uuid: uuid.clone(),
                proposed_at: Some(Timestamp {
                    seconds: SystemTime::now()
                        .duration_since(UNIX_EPOCH)
                        .unwrap()
                        .as_secs() as i64,
                    nanos: 0,
                }),
                approver: "".to_string(), // unset
                approved_at: None,        // unset
            };
            if let Err(e) = state
                .genie_store
                .insert_proposal(bigtable_entry.clone())
                .await
            {
                tracing::error!("Failed to write to bigtable: {}", e);
                return HttpResponse::InternalServerError().body("Failed to write to bigtable");
            }
            tracing::info!("Token scope access proposal sent with uuid={}", uuid);
            HttpResponse::Ok().json(AccessResponse {
                auto_approved: false,
            })
        } else {
            HttpResponse::BadRequest().body("Unsupported access type")
        }
    } else if let Some(proto::access::access_type::Type::KubernetesNamespaceAccess(a)) =
        item.access.r#type.as_ref()
    {
        tracing::info!("Access proposed for {}: {:?}", user_name, item);
        let uuid = Uuid::new_v4().to_string();
        let Ok(approval_url) = req.url_for("approval page", [uuid.clone()]) else {
            return HttpResponse::InternalServerError().body("Failed to generate approval link");
        };

        audit_logger.write_audit_log(
            &user_name,
            audit::INTERNAL_IAP,
            None,
            Some(audit::Resource::GenieRequest {
                access_type: "kubernetes".to_string(),
                business_reason: item.reason.clone(),
                ttl: state.granter.get_expires_at().to_rfc3339(),
                auto_approve: false,
            }),
            Some(format!(
                "Kubernetes access requested for namespace {0} by user {user_name}",
                a.namespace
            )),
        );

        // Notify slack.
        //. This is done before the DB write so we never get in a situation where the DB
        // considers the request approved but the user never hears about it
        let bot_request = devtools_bot::NotifyAccessProposedRequest {
            proposer: user_name.clone(),
            reason: item.reason.clone(),
            approval_url: Some(approval_url.to_string()),
            access: Some(item.access.clone()),
            cluster: state.cluster.clone(),
        };
        let mut slack_client = state.slack_client.as_ref().clone();
        if let Err(e) = slack_client.notify_access_proposed(bot_request).await {
            tracing::error!("Failed to notify slack: {}", e);
            return HttpResponse::InternalServerError().body("Failed to notify slack");
        }

        let bigtable_entry = proto::genie_store::AccessProposal {
            access: Some(item.access.clone()),
            proposer: user_name.clone(),
            reason: item.reason.clone(),
            uuid: uuid.clone(),
            proposed_at: Some(Timestamp {
                seconds: SystemTime::now()
                    .duration_since(UNIX_EPOCH)
                    .unwrap()
                    .as_secs() as i64,
                nanos: 0,
            }),
            approver: "".to_string(), // unset
            approved_at: None,        // unset
        };
        if let Err(e) = state
            .genie_store
            .insert_proposal(bigtable_entry.clone())
            .await
        {
            tracing::error!("Failed to write to bigtable: {}", e);
            return HttpResponse::InternalServerError().body("Failed to write to bigtable");
        }
        tracing::info!("Access proposal sent with uuid={}", uuid);
        HttpResponse::Ok().json(AccessResponse {
            auto_approved: false,
        })
    } else {
        HttpResponse::BadRequest().body("Unsupported access type")
    }
}

async fn get_access_proposal(req: HttpRequest, state: web::Data<GenieState>) -> HttpResponse {
    if let Err(res) = verify_jwt(&req, state.iap_verifier.as_ref()) {
        return res.error_response();
    }
    let uuid = req.match_info().query("uuid");
    let Some(proposal) = state.genie_store.get_proposal(uuid).await else {
        return HttpResponse::NotFound().json(proto::genie_store::AccessProposal::default());
    };

    HttpResponse::Ok().json(proposal)
}

async fn approve_access_proposal(
    req: HttpRequest,
    _item: Csrf<web::Json<ApproveAccessRequest>>, // just to validate csrf
    audit_logger: web::Data<audit::AuditLogger>,
    state: web::Data<GenieState>,
) -> HttpResponse {
    if let Err(res) = verify_jwt(&req, state.iap_verifier.as_ref()) {
        return res.error_response();
    }

    let Some(user_name) = extract_user_from_header(&req) else {
        return HttpResponse::BadRequest().body("no user name");
    };
    let uuid = req.match_info().query("uuid");
    tracing::info!("Approve access {:?} by {:?}", uuid, user_name);
    let Some(proposal) = state.genie_store.get_proposal(uuid).await else {
        tracing::error!("UUID not found: {}", uuid);
        return HttpResponse::NotFound().body("uuid not found");
    };

    if !proposal.approver.is_empty() {
        tracing::error!("Access already approved");
        return HttpResponse::Forbidden().body("Access already approved");
    }

    let proposed_at_seconds = proposal
        .proposed_at
        .as_ref()
        .map(|t| t.seconds)
        .unwrap_or(0);
    let now_seconds = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_secs() as i64;
    if now_seconds - proposed_at_seconds > 60 * 60 {
        tracing::error!("Access proposal is more than an hour old");
        return HttpResponse::Forbidden().body("Access proposal is more than an hour old");
    }

    if user_name == proposal.proposer {
        tracing::error!("Proposer and approver are the same");
        return HttpResponse::Forbidden().body("Proposer cannot approve access");
    }

    let expires_at = state.granter.get_expires_at();

    match proposal.access.as_ref().and_then(|a| a.r#type.as_ref()) {
        Some(proto::access::access_type::Type::SupportUiAccess(a)) => {
            // Determine if this is a token scope request
            let has_token_scopes = !a.token_scopes.is_empty();
            let access_type = if has_token_scopes {
                "token_scope"
            } else {
                "support"
            };

            // Create audit log entry
            let token_scopes_str = if has_token_scopes {
                format!(
                    " with token scopes [{}]",
                    a.token_scopes.to_vec().join(", ")
                )
            } else {
                "".to_string()
            };

            audit_logger.write_audit_log(
                &proposal.proposer,
                audit::INTERNAL_IAP,
                Some(&a.tenant_name),
                Some(audit::Resource::GenieApproval {
                    access_type: access_type.to_string(),
                    business_reason: proposal.reason.clone(),
                    approved_by: user_name.clone(),
                    ttl: expires_at.to_rfc3339(),
                }),
                Some(format!(
                    "{} access to tenant {}{} for {} approved by {}",
                    if has_token_scopes {
                        "Token scope"
                    } else {
                        "Support"
                    },
                    a.tenant_name,
                    token_scopes_str,
                    proposal.proposer,
                    user_name
                )),
            );

            let request = devtools_bot::NotifyAccessApprovedRequest {
                proposer: proposal.proposer.clone(),
                approver: user_name.clone(),
                reason: proposal.reason.clone(),
                access: proposal.access.clone(),
                cluster: state.cluster.clone(),
            };
            let mut slack_client = state.slack_client.as_ref().clone();
            if let Err(e) = slack_client.notify_access_approved(request).await {
                tracing::error!("Failed to notify slack: {}", e);
                return HttpResponse::InternalServerError().body("Failed to notify slack");
            }

            if let Err(e) = state
                .granter
                .create_support_ui_access(
                    a,
                    &proposal.proposer,
                    expires_at,
                    &state.trusted_acl_namespace,
                )
                .await
            {
                tracing::error!("Failed to give access: {}", e);
                return HttpResponse::InternalServerError().body("Failed to give access");
            }
        }
        Some(proto::access::access_type::Type::KubernetesNamespaceAccess(a)) => {
            audit_logger.write_audit_log(
                &user_name,
                audit::INTERNAL_IAP,
                None,
                None,
                Some(format!(
                    "Kubernetes access to namespace {1} for {0} approved by {user_name}",
                    proposal.proposer, a.namespace
                )),
            );

            let request = devtools_bot::NotifyAccessApprovedRequest {
                proposer: proposal.proposer.clone(),
                approver: user_name.clone(),
                reason: proposal.reason.clone(),
                access: proposal.access.clone(),
                cluster: state.cluster.clone(),
            };
            let mut slack_client = state.slack_client.as_ref().clone();
            if let Err(e) = slack_client.notify_access_approved(request).await {
                tracing::error!("Failed to notify slack: {}", e);
                return HttpResponse::InternalServerError().body("Failed to notify slack");
            }

            if let Err(e) = state
                .granter
                .create_role_binding(a, &proposal.proposer, expires_at, AccessLevel::Full)
                .await
            {
                tracing::error!("Failed to give access: {}", e);
                return HttpResponse::InternalServerError().body("Failed to give access");
            }
        }
        None => {
            tracing::error!("Unsupported access type");
            return HttpResponse::Forbidden().body("Unsupported access type");
        }
    };
    // for all access types

    tracing::info!("Access approved for {}: {:?}", proposal.proposer, proposal);

    // Update the persisted entry. Slightly better to do this after the slack notification than
    // before so we never get in a situation where the DB considers the request approved but the
    // user never hears about it. It's not the end of the world if this fails either since the
    // only thing we lose is the duplicate-grant protection and a little bit of DB auditing.
    let mut bigtable_entry = proposal.clone();
    bigtable_entry.approver = user_name;
    bigtable_entry.approved_at = Some(Timestamp {
        seconds: now_seconds,
        nanos: 0,
    });
    match state
        .genie_store
        .insert_proposal(bigtable_entry.clone())
        .await
    {
        Ok(_) => HttpResponse::Ok().body("Access approved"),
        Err(e) => {
            tracing::error!("Failed to write to bigtable: {}", e);
            HttpResponse::InternalServerError().body("Failed to write to bigtable")
        }
    }
}

async fn health() -> HttpResponse {
    HttpResponse::Ok().body("Ok")
}
fn load_certs(filename: &str) -> Vec<rustls_pki_types::CertificateDer<'static>> {
    let certfile = File::open(filename).expect("cannot open certificate file");
    let mut reader = BufReader::new(certfile);
    let r: Result<Vec<rustls_pki_types::CertificateDer<'static>>, io::Error> =
        rustls_pemfile::certs(&mut reader).collect();
    r.unwrap()
}

fn load_private_key(filename: &str) -> rustls_pki_types::PrivateKeyDer<'static> {
    let keyfile = File::open(filename).expect("cannot open private key file");
    let mut reader = BufReader::new(keyfile);

    loop {
        match rustls_pemfile::read_one(&mut reader).expect("cannot parse private key .pem file") {
            Some(rustls_pemfile::Item::Pkcs1Key(key)) => {
                return rustls_pki_types::PrivateKeyDer::Pkcs1(key)
            }
            Some(rustls_pemfile::Item::Pkcs8Key(key)) => {
                return rustls_pki_types::PrivateKeyDer::Pkcs8(key)
            }
            Some(rustls_pemfile::Item::Sec1Key(key)) => {
                return rustls_pki_types::PrivateKeyDer::Sec1(key)
            }
            None => break,
            _ => {}
        }
    }

    panic!(
        "no keys found in {:?} (encrypted keys not supported)",
        filename
    );
}

fn get_ssl_builder(config: &Config) -> ServerConfig {
    // Load key files
    let certs = load_certs(&config.https_server_cert);
    let privkey = load_private_key(&config.https_server_key);

    rustls::ServerConfig::builder()
        .with_no_client_auth()
        .with_single_cert(certs, privkey)
        .expect("bad certificates/private key")
}

#[derive(Clone)]
pub struct GenieState {
    pub granter: Arc<access::Granter>,
    pub slack_client: Arc<DevtoolsBotClient<TracingService>>,
    pub iap_verifier: Arc<GcpIapVerifierImpl>,
    pub genie_store: Arc<store::GenieStore>,
    pub tenant_watcher_client: Arc<dyn TenantWatcherClient + Send + Sync>,
    pub cluster: String,
    pub trusted_acl_namespace: String,
    pub namespaces: Option<Vec<String>>,
}

async fn run(args: CliArguments) -> Result<(), Box<dyn std::error::Error>> {
    let config = Config::read(&args.config_file).expect("Failed to read config file");
    tracing::info!("{:?}", config);

    let ttl = chrono::Duration::minutes(config.minutes_to_live);

    let slack_client = create_slack_bot_client(&config.slack_bot_endpoint).await?;

    let central_client_tls_config = get_client_tls_creds(&config.central_client_mtls_config)
        .expect("Failed to create TLS config");

    let tenant_watcher_client =
        create_tenant_watcher_client(&config.tenant_watcher_endpoint, central_client_tls_config)
            .await?;

    let granter = access::Granter::new(
        config.full_role_name.clone(),
        config.limited_role_name.clone(),
        ttl,
        tenant_watcher_client.clone(),
    )
    .await;

    let iap_verifier = GcpIapVerifierImpl::new(config.audience_prefix.clone()).unwrap();

    let ssl_builder = get_ssl_builder(&config);
    let target = (config.bind_address.to_string(), config.port);
    let genie_store = store::GenieStore::new(config.clone()).await;
    let state = GenieState {
        granter: Arc::new(granter),
        slack_client: Arc::new(slack_client),
        iap_verifier: Arc::new(iap_verifier),
        genie_store: Arc::new(genie_store),
        cluster: config.cluster.clone(),
        tenant_watcher_client: tenant_watcher_client.clone(),
        trusted_acl_namespace: config.trusted_acl_namespace.clone(),
        namespaces: config.namespaces,
    };

    let audit_logger = audit::stdout_audit_logger();

    let public_server = HttpServer::new(move || {
        let csrf =
        // Use the default CSRF token settings. Among other protections,
        // this means that the CSRF token is inaccessible to Javascript.
        CsrfMiddleware::<StdRng>::new()
        // We need to disable HttpOnly, or else we can't access the cookie
        // from Javascript.
        .http_only(false)
        .set_cookie(Method::GET, "/")
        .set_cookie(Method::GET, "/approval/{uuid}");

        App::new()
            .app_data(Data::new(state.clone()))
            .app_data(Data::new(audit_logger.clone()))
            .wrap(csrf)
            .wrap(tracing_actix_web::TracingLogger::<DefaultRootSpanBuilder>::new())
            .route("/health", web::get().to(health))
            .route("/api/namespaces", web::get().to(get_namespaces))
            .route("/api/tenants", web::get().to(get_tenants))
            .route("/api/token_scopes", web::get().to(get_token_scopes))
            .route("/api/access/proposals", web::post().to(propose_access))
            .service(
                web::resource("/approval/{uuid}")
                    .name("approval page") // so `url_for` can find it
                    .guard(actix_web::guard::Get())
                    .to(single_page_app),
            )
            .route(
                "/api/access/proposals/{uuid}",
                web::get().to(get_access_proposal),
            )
            .route(
                "/api/access/proposals/{uuid}",
                web::post().to(approve_access_proposal),
            )
            .route("/", web::get().to(single_page_app))
            .service(fs::Files::new("/", "tools/genie/frontend/dist").index_file("index.html"))
    })
    .bind_rustls_0_23(target, ssl_builder) // Separate prefix, port, target, println! not to show "Not registered service error"
    .unwrap();

    public_server.addrs().iter().for_each(|addr| {
        println!("Listening on {}", addr);
    });

    metrics_server::setup_default_metrics();

    let metrics_server = metrics_server::setup_metrics_http_server(
        &config.metrics_server_bind_address,
        config.metrics_server_port,
    )?;

    select! {
        public_server = public_server.run() => {
            panic!("public_server failed: {public_server:?}");
        },
        metrics_server = metrics_server => {
            panic!("metrics_server failed: {metrics_server:?}");
        },
    };
}

#[actix_web::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    setup_struct_logging().expect("Failed to setup logging");

    let args = CliArguments::parse();
    tracing::info!("{:?}", args);

    run(args).await
}
