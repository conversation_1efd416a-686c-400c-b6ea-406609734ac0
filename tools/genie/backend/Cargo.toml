[package]
name = "genie-backend"
version = "0.1.0"
edition = "2021"

[dependencies]
actix-web = { workspace = true }
actix-files =  { workspace = true }
actix-csrf = { workspace = true }
bigtable_rs = { workspace = true }
grpc_tls_config = { path = "../../../services/lib/grpc/tls_config" }
tonic-build = { workspace = true }
tonic = { workspace = true }
tokio = { workspace = true }
tower = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
async-trait = { workspace = true }
prost-wkt = { workspace = true }
prost-wkt-types = { workspace = true }
prost = { workspace = true }
prost-types = { workspace = true }
rand = { workspace = true }
futures ={ workspace = true }
clap = { workspace = true }
actix-web-lab = { workspace = true }
prometheus = { workspace = true }
lazy_static = { workspace = true }
tracing = { workspace = true }
k8s-openapi = { workspace = true }
kube = { workspace = true }
schemars = { workspace = true }
tracing-tonic = { path = "../../../base/rust/tracing-tonic" }
tracing-actix-web = { path = "../../../third_party/tracing-actix-web" }
tenant_watcher_client ={ path = "../../../services/tenant_watcher/client" }
token_exchange_client = { path = "../../../services/token_exchange/client" }
iap = { path = "../../../base/cloud/iap" }
chrono = { workspace = true }
http = { workspace = true }
struct_logging = { path = "../../../base/logging" }
metrics-server = { path = "../../../base/metrics_server/rust" }
uuid =  { workspace = true }
rustls = {workspace = true}
rustls-pemfile = {workspace = true}
rustls-pki-types = {workspace = true}
audit = { path = "../../../base/logging/audit" }

[dev-dependencies]
actix-rt = { workspace = true }
tonic-build = { workspace = true }

[build-dependencies]
tonic-build = { workspace = true }
