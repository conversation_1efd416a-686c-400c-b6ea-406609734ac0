// access approval react component
import { useEffect, useState } from "react";
import "./App.css";
import { LayoutComponent } from "./lib/layout";
import { Button, Form, Divider, Typography, Checkbox, message } from "antd";
import { AccessProposal, approveAccess, getAccessProposal } from "./lib/access";
import { useParams } from "react-router-dom";
const { Text, Link } = Typography;

function ApproveAccessComponent() {
  // TODO error handling
  const uuid = useParams().uuid!;
  const [requestAccessForm] = Form.useForm();
  const [proposal, setProposalData] = useState<AccessProposal | undefined>(
    undefined,
  );
  const [disableSubmit, setDisableSubmit] = useState(false);

  const [messageApi, contextHolder] = message.useMessage();

  useEffect(() => {
    const fetchAccessProposal = async () => {
      if (proposal !== undefined) {
        return;
      }
      try {
        const proposalData = await getAccessProposal(uuid);
        setProposalData(proposalData);
      } catch (e) {
        messageApi.open({
          type: "error",
          content: JSON.stringify(e),
          duration: 10,
        });
      }
    };
    fetchAccessProposal();
  }, [uuid, proposal, messageApi]);

  const onFormFinish = () => {
    approveAccess(uuid)
      .then(() => {
        setDisableSubmit(true);
        messageApi.open({
          type: "success",
          content: "Access Granted",
          duration: 0, // Should be done with the form-- don't expire
        });
      })
      .catch((error) => {
        const message = error.response?.data ?? error.message;
        messageApi.open({
          type: "error",
          content: `Error while processing the request: ${message}`,
          duration: 10,
        });
      });
  };

  const accessDescription = proposal?.access ? (
    "support_ui_access" in proposal.access ? (
      <p>
        <b>{proposal?.proposer}@augmentcode.com</b> has requested access to
        Support UI for tenant{" "}
        <b>{proposal.access.support_ui_access.tenant_name}</b>
      </p>
    ) : (
      <p>
        <b>{proposal?.proposer}@augmentcode.com</b> has requested access to
        Kubernetes resources for namespace{" "}
        <b>{proposal.access.kubernetes_namespace_access.namespace}</b>
      </p>
    )
  ) : null;

  return (
    <div>
      {contextHolder}
      <Text>
        <h3>Approve Access to Production Namespace</h3>
        {accessDescription}
        <p>
          Reason: <b>{proposal?.reason}</b>
        </p>
        <p>
          Click the button below to approve this access. Access requests are
          logged and audited.
        </p>
        <p>
          See{" "}
          <Link
            href="https://www.notion.so/DRAFT-Accessing-Customer-Code-673fa7a1fd314a43ba9c0e4af1a9d13f?pvs=4"
            target="_blank"
          >
            here
          </Link>{" "}
          for more details.
        </p>
      </Text>
      <Divider />
      <Form
        name="request"
        form={requestAccessForm}
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 16 }}
        style={{ maxWidth: 600 }}
        onFinish={onFormFinish}
        disabled={disableSubmit}
        autoComplete="on"
      >
        <Form.Item
          name="confirm"
          label="Confirmation"
          valuePropName="checked"
          rules={[
            () => ({
              validator(_, value) {
                if (!value) {
                  return Promise.reject(
                    new Error("Please confirm that you have read the rules."),
                  );
                }
                return Promise.resolve();
              },
            }),
          ]}
        >
          <Checkbox>
            I have read and agree to the{" "}
            <Link href="https://www.notion.so/DRAFT-Accessing-Customer-Code-673fa7a1fd314a43ba9c0e4af1a9d13f?pvs=4">
              rules for accessing tenants
            </Link>
            .
          </Checkbox>
        </Form.Item>

        <Form.Item wrapperCol={{ offset: 8, span: 16 }}>
          <Button type="primary" htmlType="submit">
            Approve
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
}

function ApproveAccess() {
  const children = <ApproveAccessComponent />;

  return <LayoutComponent children={children} breadcrumbs={[]} />;
}

export default ApproveAccess;
