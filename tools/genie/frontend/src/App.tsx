// homepage react component
import {
  InfoCircleOutlined,
  UserOutlined,
  FileSearchOutlined,
  KeyOutlined,
  KubernetesOutlined,
  CloudServerOutlined,
  LockOutlined,
} from "@ant-design/icons";
import {
  AutoComplete,
  Button,
  Checkbox,
  Divider,
  Form,
  Input,
  Tabs,
  TabsProps,
  Typography,
  message,
} from "antd";
import { useEffect, useState } from "react";
import React from "react";
import { useSearchParams } from "react-router-dom";
import "./App.css";
import {
  RequestAccessData,
  Tenant,
  getNamespaces,
  getTokenScopes,
  getTenants,
  requestAccess,
  wildcardTenant,
} from "./lib/access";
import { LayoutComponent } from "./lib/layout";

const { Text, Link } = Typography;

function getRedirectUrlFromQuery(): string | null {
  const urlParams = new URLSearchParams(window.location.search);
  return urlParams.get("redirect_url");
}

function UserAccessComponent({
  onFormFinish,
  disableSubmit,
}: {
  onFormFinish: (values: RequestAccessData, resetForm: () => void) => void;
  disableSubmit: boolean;
}) {
  const [requestAccessForm] = Form.useForm();

  const onInternalFormFinish = (values: RequestAccessData) => {
    values.access_type = "add_users";
    onFormFinish(values, () => requestAccessForm.resetFields());
  };

  return (
    <>
      <Form
        name="request"
        form={requestAccessForm}
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 16 }}
        style={{ maxWidth: 600 }}
        onFinish={onInternalFormFinish}
        disabled={disableSubmit}
        autoComplete="on"
      >
        <Text>
          <p>Request permission to manage users in all tenants.</p>
        </Text>
        <Divider />

        <Form.Item name="tenant" initialValue="*" hidden={true}>
          <Input type="hidden" />
        </Form.Item>

        <Form.Item
          name="reason"
          label="Business Reason"
          tooltip={{
            title: "Please describe the reason for access.",
            icon: <InfoCircleOutlined />,
          }}
          rules={[
            {
              required: true,
              message: "Please describe the reason for access.",
            },
          ]}
        >
          <Input.TextArea />
        </Form.Item>

        <Form.Item
          name="confirm"
          label="Confirmation"
          valuePropName="checked"
          rules={[
            () => ({
              validator(_, value) {
                if (!value) {
                  return Promise.reject(
                    new Error("Please confirm that you have read the rules."),
                  );
                }
                return Promise.resolve();
              },
            }),
          ]}
        >
          <Checkbox>
            I have read and agree to the{" "}
            <Link href="https://www.notion.so/DRAFT-Accessing-Customer-Code-673fa7a1fd314a43ba9c0e4af1a9d13f?pvs=4">
              rules for accessing tenants
            </Link>
            .
          </Checkbox>
        </Form.Item>

        <Form.Item wrapperCol={{ offset: 8, span: 16 }}>
          <Button type="primary" htmlType="submit">
            Submit
          </Button>
        </Form.Item>
      </Form>
    </>
  );
}

function RequestsAccessComponent({
  tenants,
  namespaces,
  onFormFinish,
  disableSubmit,
  prefillTenant,
  tenantId,
}: {
  tenants: Tenant[];
  namespaces: string[];
  onFormFinish: (values: RequestAccessData, resetForm: () => void) => void;
  disableSubmit: boolean;
  prefillTenant: string;
  tenantId: string;
}) {
  const [requestAccessForm] = Form.useForm();

  // Look up tenantId in tenants and fill out Tenant field
  useEffect(() => {
    var matchingTenant: string = prefillTenant;

    if (tenantId && tenants && Array.isArray(tenants) && !matchingTenant) {
      matchingTenant =
        tenants.find((tenant) => tenant.id === tenantId)?.name || "";
    }

    if (matchingTenant) {
      requestAccessForm.setFieldsValue({
        tenant: matchingTenant,
      });
    }
  }, [tenantId, tenants, requestAccessForm, prefillTenant]);

  let tenantAutoCompleteOptions: { value: string }[] = [];
  if (tenants && Array.isArray(tenants)) {
    tenantAutoCompleteOptions = tenants.map((m) => {
      return { value: m.name };
    });
  }

  const onInternalFormFinish = (values: RequestAccessData) => {
    values.access_type = "request_insight";
    onFormFinish(values, () => requestAccessForm.resetFields());
  };

  return (
    <>
      <Text>
        <p>
          Request permission to view requests and content. This is highly
          restricted access that should used with explicit customer permission.
        </p>
      </Text>
      <Divider />

      <Form
        name="request"
        form={requestAccessForm}
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 16 }}
        style={{ maxWidth: 600 }}
        onFinish={onInternalFormFinish}
        disabled={disableSubmit}
        autoComplete="on"
      >
        <Form.Item
          label="Tenant"
          name="tenant"
          tooltip={{ title: "Name of tenant", icon: <InfoCircleOutlined /> }}
          rules={[
            {
              required: true,
              message: "Please input the name of the tenant",
            },
          ]}
        >
          <AutoComplete
            options={tenantAutoCompleteOptions}
            filterOption={(inputValue, option) =>
              option!.value.toUpperCase().indexOf(inputValue.toUpperCase()) !==
              -1
            }
          />
        </Form.Item>

        <Form.Item
          name="reason"
          label="Business Reason"
          tooltip={{
            title: "Please describe the reason for access.",
            icon: <InfoCircleOutlined />,
          }}
          rules={[
            {
              required: true,
              message: "Please describe the reason for access.",
            },
          ]}
        >
          <Input.TextArea />
        </Form.Item>

        <Form.Item
          name="confirm"
          label="Confirmation"
          valuePropName="checked"
          rules={[
            () => ({
              validator(_, value) {
                if (!value) {
                  return Promise.reject(
                    new Error("Please confirm that you have read the rules."),
                  );
                }
                return Promise.resolve();
              },
            }),
          ]}
        >
          <Checkbox>
            I have read and agree to the{" "}
            <Link href="https://www.notion.so/DRAFT-Accessing-Customer-Code-673fa7a1fd314a43ba9c0e4af1a9d13f?pvs=4">
              rules for accessing tenants
            </Link>
            .
          </Checkbox>
        </Form.Item>

        <Form.Item wrapperCol={{ offset: 8, span: 16 }}>
          <Button type="primary" htmlType="submit">
            Submit
          </Button>
        </Form.Item>
      </Form>
    </>
  );
}

function TokenScopeAccessComponent({
  tenants,
  namespaces,
  onFormFinish,
  disableSubmit,
  prefillTenant,
  tenantId,
}: {
  tenants: Tenant[];
  namespaces: string[];
  onFormFinish: (values: RequestAccessData, resetForm: () => void) => void;
  disableSubmit: boolean;
  prefillTenant: string;
  tenantId: string;
}) {
  const [requestAccessForm] = Form.useForm();
  const [tokenScopes, setTokenScopes] = useState<string[]>([]);
  const [messageApi, contextHolder] = message.useMessage();

  // Fetch token scopes
  useEffect(() => {
    const fetchTokenScopes = async () => {
      try {
        const scopes = await getTokenScopes();
        setTokenScopes(scopes);
      } catch (e) {
        messageApi.open({
          type: "error",
          content: `Failed to fetch token scopes: ${JSON.stringify(e)}`,
          duration: 10,
        });
      }
    };
    fetchTokenScopes();
  }, [messageApi]);

  // Look up tenantId in tenants and fill out Tenant field
  useEffect(() => {
    var matchingTenant: string = prefillTenant;

    if (tenantId && tenants && Array.isArray(tenants) && !matchingTenant) {
      matchingTenant =
        tenants.find((tenant) => tenant.id === tenantId)?.name || "";
    }

    if (matchingTenant) {
      requestAccessForm.setFieldsValue({
        tenant: matchingTenant,
      });
    }
  }, [tenantId, tenants, requestAccessForm, prefillTenant]);

  let tenantAutoCompleteOptions: { value: string }[] = [];
  if (tenants && Array.isArray(tenants)) {
    tenantAutoCompleteOptions = tenants.map((m) => {
      return { value: m.name };
    });
  }

  const onInternalFormFinish = (values: RequestAccessData) => {
    values.access_type = "token_scope";
    onFormFinish(values, () => requestAccessForm.resetFields());
  };

  return (
    <>
      {contextHolder}
      <Text>
        <p>
          Request custom access to token scopes. These can be highly restricted
          access that should only be used with explicit customer permission.
        </p>
      </Text>
      <Divider />

      <Form
        name="token_scope"
        form={requestAccessForm}
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 16 }}
        style={{ maxWidth: 600 }}
        onFinish={onInternalFormFinish}
        disabled={disableSubmit}
        autoComplete="on"
      >
        <Form.Item
          label="Tenant"
          name="tenant"
          tooltip={{ title: "Name of tenant", icon: <InfoCircleOutlined /> }}
          rules={[
            {
              required: true,
              message: "Please input the name of the tenant",
            },
          ]}
        >
          <AutoComplete
            options={tenantAutoCompleteOptions}
            filterOption={(inputValue, option) =>
              option!.value.toUpperCase().indexOf(inputValue.toUpperCase()) !==
              -1
            }
          />
        </Form.Item>

        <Form.Item
          label="Token Scopes"
          name="token_scopes"
          tooltip={{
            title: "Select one or more token scopes to request access to",
            icon: <InfoCircleOutlined />,
          }}
          rules={[
            {
              required: true,
              message: "Please select at least one token scope",
            },
          ]}
        >
          <Checkbox.Group>
            {tokenScopes.map((scope) => (
              <div key={scope}>
                <Checkbox value={scope}>{scope}</Checkbox>
              </div>
            ))}
          </Checkbox.Group>
        </Form.Item>

        <Form.Item
          name="reason"
          label="Business Reason"
          tooltip={{
            title: "Please describe the reason for access.",
            icon: <InfoCircleOutlined />,
          }}
          rules={[
            {
              required: true,
              message: "Please describe the reason for access.",
            },
          ]}
        >
          <Input.TextArea />
        </Form.Item>

        <Form.Item
          name="confirm"
          label="Confirmation"
          valuePropName="checked"
          rules={[
            () => ({
              validator(_, value) {
                if (!value) {
                  return Promise.reject(
                    new Error("Please confirm that you have read the rules."),
                  );
                }
                return Promise.resolve();
              },
            }),
          ]}
        >
          <Checkbox>
            I have read and agree to the{" "}
            <Link href="https://www.notion.so/DRAFT-Accessing-Customer-Code-673fa7a1fd314a43ba9c0e4af1a9d13f?pvs=4">
              rules for accessing tenants
            </Link>
            .
          </Checkbox>
        </Form.Item>

        <Form.Item wrapperCol={{ offset: 8, span: 16 }}>
          <Button type="primary" htmlType="submit">
            Submit
          </Button>
        </Form.Item>
      </Form>
    </>
  );
}

function KubernetesAccessComponent({
  namespaces,
  onFormFinish,
  disableSubmit,
}: {
  namespaces: string[];
  onFormFinish: (values: RequestAccessData, resetForm: () => void) => void;
  disableSubmit: boolean;
}) {
  const [requestAccessForm] = Form.useForm();

  let namespaceAutoCompleteOptions: { value: string }[] = [];
  if (namespaces && Array.isArray(namespaces)) {
    namespaceAutoCompleteOptions = namespaces.map((m) => {
      return { value: m };
    });
  }

  const onInternalFormFinish = (values: RequestAccessData) => {
    values.access_type = "kubernetes";
    onFormFinish(values, () => requestAccessForm.resetFields());
  };

  return (
    <>
      <Text>
        <p>
          Request permission for access to restricted Kubernetes resources. This
          is highly restricted access that should only be used for as part of
          incident resolution if other methods have failed. Prefer using the
          limited Kubernetes access mode if possible.
        </p>
      </Text>
      <Divider />

      <Form
        name="request"
        form={requestAccessForm}
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 16 }}
        style={{ maxWidth: 600 }}
        onFinish={onInternalFormFinish}
        disabled={disableSubmit}
        autoComplete="on"
      >
        <Form.Item
          label="Namespace"
          name="namespace"
          tooltip={{ title: "Name of namespace", icon: <InfoCircleOutlined /> }}
          rules={[
            {
              required: true,
              message: "Please input the name of the namespace",
            },
          ]}
        >
          <AutoComplete
            options={namespaceAutoCompleteOptions}
            filterOption={(inputValue, option) =>
              option!.value.toUpperCase().indexOf(inputValue.toUpperCase()) !==
              -1
            }
          />
        </Form.Item>

        <Form.Item
          name="reason"
          label="Business Reason"
          tooltip={{
            title: "Please describe the reason for access.",
            icon: <InfoCircleOutlined />,
          }}
          rules={[
            {
              required: true,
              message: "Please describe the reason for access.",
            },
          ]}
        >
          <Input.TextArea />
        </Form.Item>

        <Form.Item
          name="confirm"
          label="Confirmation"
          valuePropName="checked"
          rules={[
            () => ({
              validator(_, value) {
                if (!value) {
                  return Promise.reject(
                    new Error("Please confirm that you have read the rules."),
                  );
                }
                return Promise.resolve();
              },
            }),
          ]}
        >
          <Checkbox>
            I have read and agree to the{" "}
            <Link href="https://www.notion.so/DRAFT-Accessing-Customer-Code-673fa7a1fd314a43ba9c0e4af1a9d13f?pvs=4">
              rules for accessing tenants
            </Link>
            .
          </Checkbox>
        </Form.Item>

        <Form.Item wrapperCol={{ offset: 8, span: 16 }}>
          <Button type="primary" htmlType="submit">
            Submit
          </Button>
        </Form.Item>
      </Form>
    </>
  );
}

function KubernetesLimitedAccessComponent({
  namespaces,
  onFormFinish,
  disableSubmit,
}: {
  namespaces: string[];
  onFormFinish: (values: RequestAccessData, resetForm: () => void) => void;
  disableSubmit: boolean;
}) {
  const [requestAccessForm] = Form.useForm();

  let namespaceAutoCompleteOptions: { value: string }[] = [];
  if (namespaces && Array.isArray(namespaces)) {
    namespaceAutoCompleteOptions = namespaces.map((m) => {
      return { value: m };
    });
  }

  const onInternalFormFinish = (values: RequestAccessData) => {
    values.access_type = "kubernetes_limited";
    onFormFinish(values, () => requestAccessForm.resetFields());
  };

  return (
    <>
      <Text>
        <p>
          Request limited permission for access to Kubernetes resources. This
          access mode does not require approval and provides limited access to
          Kubernetes resources within a specific namespace. It gives the
          permissions to delete resources (e.g. pods) and to scale deployments.
        </p>
      </Text>
      <Divider />

      <Form
        name="request"
        form={requestAccessForm}
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 16 }}
        style={{ maxWidth: 600 }}
        onFinish={onInternalFormFinish}
        disabled={disableSubmit}
        autoComplete="on"
      >
        <Form.Item
          label="Namespace"
          name="namespace"
          tooltip={{ title: "Name of namespace", icon: <InfoCircleOutlined /> }}
          rules={[
            {
              required: true,
              message: "Please input the name of the namespace",
            },
          ]}
        >
          <AutoComplete
            options={namespaceAutoCompleteOptions}
            filterOption={(inputValue, option) =>
              option!.value.toUpperCase().indexOf(inputValue.toUpperCase()) !==
              -1
            }
          />
        </Form.Item>

        <Form.Item
          name="reason"
          label="Business Reason"
          tooltip={{
            title: "Please describe the reason for access.",
            icon: <InfoCircleOutlined />,
          }}
          rules={[
            {
              required: true,
              message: "Please describe the reason for access.",
            },
          ]}
        >
          <Input.TextArea />
        </Form.Item>

        <Form.Item
          name="confirm"
          label="Confirmation"
          valuePropName="checked"
          rules={[
            () => ({
              validator(_, value) {
                if (!value) {
                  return Promise.reject(
                    new Error("Please confirm that you have read the rules."),
                  );
                }
                return Promise.resolve();
              },
            }),
          ]}
        >
          <Checkbox>
            I have read and agree to the{" "}
            <Link href="https://www.notion.so/DRAFT-Accessing-Customer-Code-673fa7a1fd314a43ba9c0e4af1a9d13f?pvs=4">
              rules for accessing tenants
            </Link>
            .
          </Checkbox>
        </Form.Item>

        <Form.Item wrapperCol={{ offset: 8, span: 16 }}>
          <Button type="primary" htmlType="submit">
            Submit
          </Button>
        </Form.Item>
      </Form>
    </>
  );
}

type ActiveTab = {
  key: string;
};

function RequestAccessComponent() {
  const [searchParams] = useSearchParams();
  const [namespaces, setNamespaceData] = useState<string[] | undefined>(
    undefined,
  );
  const [tenants, setTenants] = useState<Tenant[] | undefined>(undefined);
  const [disableSubmit, setDisableSubmit] = useState(false);
  const [activeTabData, setActiveTabData] = useState<ActiveTab>({
    key: getInitialTabKey(searchParams),
  });

  const [messageApi, contextHolder] = message.useMessage();
  const redirectUrl = getRedirectUrlFromQuery();

  // Helper function to determine initial tab key based on query params
  function getInitialTabKey(params: URLSearchParams): string {
    if (params.get("kind") === "token") {
      return "3";
    }

    // If kind=requests is in the URL, select the Requests Access tab (key "2")
    if (params.get("kind") === "requests") {
      return "2";
    }

    // Default to first tab
    return "1";
  }

  useEffect(() => {
    const fetchNamespaces = async () => {
      if (namespaces !== undefined) {
        return;
      }
      try {
        const namespaces = await getNamespaces();
        setNamespaceData(namespaces);
      } catch (e) {
        messageApi.open({
          type: "error",
          content: JSON.stringify(e),
          duration: 10,
        });
      }
    };
    fetchNamespaces();
  });

  useEffect(() => {
    const fetchTenants = async () => {
      if (tenants !== undefined) {
        return;
      }
      try {
        const tenants = await getTenants();
        setTenants(tenants);
      } catch (e) {
        messageApi.open({
          type: "error",
          content: JSON.stringify(e),
          duration: 10,
        });
      }
    };
    fetchTenants();
  });

  const onFormFinish = (values: RequestAccessData, resetForm: () => void) => {
    setDisableSubmit(true);
    requestAccess(values)
      .then((approved) => {
        let message = "";
        if (approved && redirectUrl) {
          message = "Access approved. Redirecting to " + redirectUrl;
        } else if (approved) {
          message = "Access approved";
        } else {
          message =
            "Access approval pending (check #system-services-oncall slack)";
        }
        messageApi.open({
          type: "success",
          content: message,
          duration: 0, // Should be done with the form-- don't expire
        });

        resetForm(); // Reset the form on success
        setDisableSubmit(false);

        if (approved && redirectUrl) {
          setTimeout(() => {
            window.location.href = redirectUrl;
          }, 2000); // 2 seconds delay
        }
      })
      .catch((error) => {
        setDisableSubmit(false);
        resetForm(); // Reset the form on error too
        const message = error.response?.data ?? error.message;
        messageApi.open({
          type: "error",
          content: `Error while processing the request: ${message}`,
          duration: 10,
        });
      });
  };

  const items: TabsProps["items"] = [
    {
      key: "1",
      label: (
        <span>
          <UserOutlined /> {/* Added space after icon */}
          <span style={{ marginLeft: "4px" }}>User Management</span>
        </span>
      ),
      children: (
        <UserAccessComponent
          onFormFinish={onFormFinish}
          disableSubmit={disableSubmit}
        />
      ),
    },
    {
      key: "2",
      label: (
        <span>
          <FileSearchOutlined />
          <span style={{ marginLeft: "4px" }}>Requests Access</span>
        </span>
      ),
      children: (
        <RequestsAccessComponent
          tenants={tenants!}
          namespaces={namespaces!}
          onFormFinish={onFormFinish}
          disableSubmit={disableSubmit}
          tenantId={searchParams.get("tenant_id") || ""}
          prefillTenant={searchParams.get("tenant") || ""}
        />
      ),
    },
    {
      key: "3",
      label: (
        <span>
          <KeyOutlined />
          <span style={{ marginLeft: "4px" }}>Custom Token Scopes</span>
        </span>
      ),
      children: (
        <TokenScopeAccessComponent
          tenants={tenants!}
          namespaces={namespaces!}
          onFormFinish={onFormFinish}
          disableSubmit={disableSubmit}
          tenantId={searchParams.get("tenant_id") || ""}
          prefillTenant={searchParams.get("tenant") || ""}
        />
      ),
    },
    {
      key: "4",
      label: (
        <span>
          <KubernetesOutlined />
          <span style={{ marginLeft: "4px" }}>
            Kubernetes (Limited Permissions)
          </span>
        </span>
      ),
      children: (
        <KubernetesLimitedAccessComponent
          namespaces={namespaces!}
          onFormFinish={onFormFinish}
          disableSubmit={disableSubmit}
        />
      ),
    },
    {
      key: "5",
      label: (
        <span>
          <LockOutlined />
          <span style={{ marginLeft: "4px", color: "#f5222d" }}>
            Kubernetes (Full Permissions)
          </span>
        </span>
      ),
      children: (
        <KubernetesAccessComponent
          namespaces={namespaces!}
          onFormFinish={onFormFinish}
          disableSubmit={disableSubmit}
        />
      ),
    },
  ];

  const handleTabChange = (key: string) => {
    setActiveTabData({
      key: key,
    });
  };
  const tabChildren = (
    <Tabs
      defaultActiveKey={activeTabData.key}
      items={items}
      onChange={handleTabChange}
    />
  );

  return (
    <div>
      {contextHolder}
      <Text>
        <h3>Request Access to Production System</h3>
        <p>
          Genie grants access to criticial and <b>restricted</b> parts of the
          production system.
        </p>
        <p>
          Access should only be requested for valid business reasons according
          to with the policies. See{" "}
          <Link
            href="https://www.notion.so/DRAFT-Accessing-Customer-Code-673fa7a1fd314a43ba9c0e4af1a9d13f?pvs=4"
            target="_blank"
          >
            here
          </Link>{" "}
          for more details.
        </p>
        <p>
          Please select the tab for the type of access you are requesting (User
          Management, Requests Access, Custom Token Scopes, Kubernetes with
          Limited Permissions, or Kubernetes with Full Permissions) and fill out
          the form below to request access to a production system. Access
          requests are logged and audited. Some forms of access require
          second-person approval.
        </p>
      </Text>
      <Divider />
      {tabChildren}
    </div>
  );
}

function App() {
  const children = <RequestAccessComponent />;

  return <LayoutComponent children={children} breadcrumbs={[]} />;
}

export default App;
