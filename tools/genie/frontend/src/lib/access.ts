import axios from "axios";

export type RequestAccessData = {
  access_type:
    | "add_users"
    | "request_insight"
    | "kubernetes"
    | "token_scope"
    | "kubernetes_limited";
  namespace?: string;
  tenant?: string;
  reason: string;
  confirm: boolean;
  token_scopes?: string[];
};

export async function getNamespaces(): Promise<string[]> {
  const { data: response }: { data: string[] } =
    await axios.get(`/api/namespaces`);
  return response;
}

export type AccessType =
  | {
      support_ui_access: {
        tenant_name: string;
        scope?: number; // enum mapping defined in access.proto
        token_scopes?: string[];
      };
    }
  | {
      kubernetes_namespace_access: {
        namespace: string;
      };
    };

export type ProposeAccessData = {
  access: AccessType;
  reason: string;
};

type AccessResponse = {
  auto_approved: boolean;
};

function accessType(data: RequestAccessData): AccessType {
  switch (data.access_type) {
    case "add_users":
      if (!data.tenant) {
        throw new Error("Tenant name must be set");
      }
      return {
        support_ui_access: {
          tenant_name: data.tenant,
          scope: 1, // USERS
          token_scopes: [],
        },
      };
    case "request_insight":
      if (!data.tenant) {
        throw new Error("Tenant must be set");
      }
      return {
        support_ui_access: {
          tenant_name: data.tenant,
          scope: 0, // REQUESTS
          token_scopes: [],
        },
      };
    case "token_scope":
      if (!data.tenant) {
        throw new Error("Tenant must be set");
      }
      if (!data.token_scopes || data.token_scopes.length === 0) {
        throw new Error("Token scopes must be set");
      }
      return {
        support_ui_access: {
          tenant_name: data.tenant,
          token_scopes: data.token_scopes,
        },
      };
    case "kubernetes":
      if (!data.namespace) {
        throw new Error("Namespace must be set");
      }
      return {
        kubernetes_namespace_access: {
          namespace: data.namespace,
        },
      };
    case "kubernetes_limited":
      if (!data.namespace) {
        throw new Error("Namespace must be set");
      }
      // For kubernetes_limited, we use SupportUiAccess with KUBERNETES_LIMITED scope
      // The backend will create limited role bindings without support UI access
      return {
        support_ui_access: {
          tenant_name: data.namespace,
          scope: 3, // KUBERNETES_LIMITED
          token_scopes: [],
        },
      };
    default:
      throw new Error("Unknown access type");
  }
}

/**
 * Get a cookie from the browser.
 *
 * @param name The name of the cookie to get.
 * @returns The value of the cookie.
 */
function getCookie(name: string): string | undefined {
  // document.cookie returns the cookies set as semicolon separated key=value pairs
  // see https://developer.mozilla.org/en-US/docs/Web/API/Document/cookie
  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  if (parts.length === 2) {
    return parts.pop()?.split(";").shift();
  }
  return undefined;
}

export async function requestAccess(data: RequestAccessData): Promise<boolean> {
  // Get the CSRF token from the browser
  // if we cannot find the csrf token, we will use an empty string, which will be rejected by the backend
  const csrf = getCookie("__Host-Csrf-Token") || "";
  let { data: response }: { data: AccessResponse } = await axios.post(
    `/api/access/proposals`,
    {
      access: accessType(data),
      reason: data.reason,
      csrf: csrf,
    },
  );
  return response.auto_approved;
}

export async function approveAccess(uuid: string): Promise<void> {
  const csrf = getCookie("__Host-Csrf-Token");
  await axios.post(`/api/access/proposals/${uuid}`, {
    csrf: csrf,
  });
}

export type AccessProposal = {
  access: AccessType;
  reason: string;
  proposer: string;
  uuid: string;
};

export async function getAccessProposal(uuid: string): Promise<AccessProposal> {
  const { data: response }: { data: AccessProposal } = await axios.get(
    `/api/access/proposals/${uuid}`,
  );
  return response;
}

export type Tenant = {
  name: string;
  id: string;
  shard_namespace: string;
};

export let wildcardTenant: Tenant = {
  name: "*",
  id: "",
  shard_namespace: "*",
};

export async function getTenants(): Promise<Tenant[]> {
  const { data: response }: { data: any } = await axios.get(`/api/tenants`);

  response.sort((a: Tenant, b: Tenant) => a.name.localeCompare(b.name));

  return response;
}

export async function getTokenScopes(): Promise<string[]> {
  const { data: response }: { data: { scopes: string[] } } =
    await axios.get(`/api/token_scopes`);
  return response.scopes;
}
