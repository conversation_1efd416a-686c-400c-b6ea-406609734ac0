# kubeconfig file that can be used within container images
#
# needs to be updated if there are new clusters
# the certificate-authority-data is a base64 of the PEM encoded CA certificate and is not considerd a secret.
apiVersion: v1
clusters:
  - cluster:
      certificate-authority-data: 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
      server: https://35.238.222.213
    name: gke_system-services-dev_us-central1_us-central1-dev
  - cluster:
      certificate-authority-data: 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
      server: https://34.91.12.147
    name: gke_system-services-prod_europe-west4_eu-west4-prod
  - cluster:
      certificate-authority-data: 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
      server: https://*************
    name: gke_system-services-prod_us-central1_us-central1-prod
  - cluster:
      certificate-authority-data: 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
      server: https://************
    name: gke_system-services-prod-gsc_us-central1_prod-gsc
  - cluster:
      certificate-authority-data: 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
      server: https://***********
    name: gke_agent-sandbox-prod_us-central1_gcp-prod-agent0
  - cluster:
      certificate-authority-data: 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
      server: https://***********
    name: gke_agent-sandbox-prod_europe-west4_gcp-eu-w4-prod-agent0

contexts:
  - context:
      cluster: gke_system-services-dev_us-central1_us-central1-dev
      user: gke_system-services-dev_us-central1_us-central1-dev
    name: gke_system-services-dev_us-central1_us-central1-dev
  - context:
      cluster: gke_system-services-prod_europe-west4_eu-west4-prod
      user: gke_system-services-prod_europe-west4_eu-west4-prod
    name: gke_system-services-prod_europe-west4_eu-west4-prod
  - context:
      cluster: gke_system-services-prod_us-central1_us-central1-prod
      user: gke_system-services-prod_us-central1_us-central1-prod
    name: gke_system-services-prod_us-central1_us-central1-prod
  - context:
      cluster: gke_system-services-prod-gsc_us-central1_prod-gsc
      user: gke_system-services-prod-gsc_us-central1_prod-gsc
    name: gke_system-services-prod-gsc_us-central1_prod-gsc
  - context:
      cluster: gke_agent-sandbox-prod_us-central1_gcp-prod-agent0
      user: gke_agent-sandbox-prod_us-central1_gcp-prod-agent0
    name: gke_agent-sandbox-prod_us-central1_gcp-prod-agent0
  - context:
      cluster: gke_agent-sandbox-prod_europe-west4_gcp-eu-w4-prod-agent0
      user: gke_agent-sandbox-prod_europe-west4_gcp-eu-w4-prod-agent0
    name: gke_agent-sandbox-prod_europe-west4_gcp-eu-w4-prod-agent0
current-context: gke_system-services-dev_us-central1_us-central1-dev
kind: Config
preferences: {}
users:
  # see https://cloud.google.com/kubernetes-engine/docs/how-to/api-server-authentication
  - name: gke_system-services-dev_us-central1_us-central1-dev
    user:
      exec:
        apiVersion: client.authentication.k8s.io/v1beta1
        args:
          - --use_application_default_credentials
        env: null
        command: ../../../gke-gcloud-auth-plugin/gke-gcloud-auth-plugin
        interactiveMode: IfAvailable
        provideClusterInfo: true
  - name: gke_system-services-prod_europe-west4_eu-west4-prod
    user:
      exec:
        apiVersion: client.authentication.k8s.io/v1beta1
        args:
          - --use_application_default_credentials
        command: ../../../gke-gcloud-auth-plugin/gke-gcloud-auth-plugin
        interactiveMode: IfAvailable
        provideClusterInfo: true
  - name: gke_system-services-prod_us-central1_us-central1-prod
    user:
      exec:
        apiVersion: client.authentication.k8s.io/v1beta1
        args:
          - --use_application_default_credentials
        command: ../../../gke-gcloud-auth-plugin/gke-gcloud-auth-plugin
        interactiveMode: IfAvailable
        provideClusterInfo: true
  - name: gke_system-services-prod-gsc_us-central1_prod-gsc
    user:
      exec:
        apiVersion: client.authentication.k8s.io/v1beta1
        args:
          - --use_application_default_credentials
        command: ../../../gke-gcloud-auth-plugin/gke-gcloud-auth-plugin
        interactiveMode: IfAvailable
        provideClusterInfo: true
  - name: gke_agent-sandbox-prod_us-central1_gcp-prod-agent0
    user:
      exec:
        apiVersion: client.authentication.k8s.io/v1beta1
        args:
          - --use_application_default_credentials
        command: ../../../gke-gcloud-auth-plugin/gke-gcloud-auth-plugin
        interactiveMode: IfAvailable
        provideClusterInfo: true
  - name: gke_agent-sandbox-prod_europe-west4_gcp-eu-w4-prod-agent0
    user:
      exec:
        apiVersion: client.authentication.k8s.io/v1beta1
        args:
          - --use_application_default_credentials
        command: ../../../gke-gcloud-auth-plugin/gke-gcloud-auth-plugin
        interactiveMode: IfAvailable
        provideClusterInfo: true
