load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:metadata.bzl", "metadata_test")
load("//tools/bzl:python.bzl", "py_binary", "py_library", "py_oci_image")

py_binary(
    name = "cm_deletion_util",
    srcs = [
        "cm_deletion_util.py",
    ],
    deps = [
        "//base/logging:console_logging",
        "//services/content_manager/client",
        "//services/lib/grpc:grpc_args_parser",
        "//services/lib/grpc:token_parser",
    ],
)

py_binary(
    name = "ri_deletion_util",
    srcs = [
        "ri_deletion_util.py",
    ],
    deps = [
        "//base/logging:console_logging",
        "//services/bigtable_proxy/client:client_py",
        "//services/lib/grpc:grpc_args_parser",
        "//services/lib/grpc:token_parser",
    ],
)

py_library(
    name = "cm_deletion_lib",
    srcs = ["cm_deletion_lib.py"],
    deps = [
        requirement("google-cloud-bigtable"),
        "//base/logging:struct_logging",
        "//services/bigtable_proxy/client:client_py",
        "//services/lib/request_context:request_context_py",
    ],
)

py_binary(
    name = "user_events_util",
    srcs = ["user_events_util.py"],
    deps = [
        "//base/logging:console_logging",
        requirement("google-cloud-bigquery"),
    ],
)

py_binary(
    name = "gcs_deletion_util",
    srcs = ["gcs_deletion_util.py"],
    deps = [
        "//base/logging:console_logging",
        requirement("google-cloud-storage"),
    ],
)

py_binary(
    name = "discovery_cm_reset",
    srcs = ["discovery_cm_reset.py"],
    deps = [
        ":cm_deletion_lib",
        "//base/logging:struct_logging",
        "//services/bigtable_proxy/client:client_py",
        "//services/lib/grpc/tls_config:grpc_tls_config_py",
        "//services/lib/request_context:request_context_py",
        "//services/token_exchange/client:client_py",
    ],
)

py_oci_image(
    name = "discovery_cm_reset_image",
    package_name = package_name(),
    binary = ":discovery_cm_reset",
)

kubecfg(
    name = "discovery_reset_kubecfg",
    src = "discovery_cm_reset.jsonnet",
    data = [
        ":discovery_cm_reset_image",
    ],
    visibility = [
        "//services/deploy:__subpackages__",
    ],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:config-map-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/gcp:gcp-lib",
        "//services/deploy:endpoints",
    ],
)

py_binary(
    name = "i1_inc_gcs_deletion",
    srcs = ["i1_inc_gcs_deletion.py"],
    deps = [
        "//base/logging:console_logging",
        "//base/logging:struct_logging",
        requirement("google-cloud-bigquery"),
        requirement("google-cloud-storage"),
        ":gcs_deletion_util",
    ],
)

py_oci_image(
    name = "i1_inc_gcs_deletion_image",
    package_name = package_name(),
    binary = ":i1_inc_gcs_deletion",
)

kubecfg(
    name = "i1_inc_gcs_deletion_kubecfg",
    src = "i1_inc_gcs_deletion.jsonnet",
    data = [
        ":i1_inc_gcs_deletion_image",
    ],
    visibility = [
        "//services/deploy:__subpackages__",
    ],
    deps = [
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/gcp:gcp-lib",
        "//services/request_insight/analytics_dataset:dataset_lib",
        "//services/request_insight/lib:bigquery_lib",
        "//services/request_insight/support_database/search_dataset:dataset_lib",
    ],
)

py_binary(
    name = "i1_inc_blob_deletion",
    srcs = ["i1_inc_blob_deletion.py"],
    deps = [
        "//base/logging:console_logging",
        "//base/logging:struct_logging",
        requirement("google-cloud-bigquery"),
        requirement("google-cloud-storage"),
        ":cm_deletion_util",
        ":gcs_deletion_util",
        "//services/content_manager/client",
        "//services/lib/grpc/tls_config:grpc_tls_config_py",
        "//services/lib/request_context:request_context_py",
        "//services/token_exchange/client:client_py",
    ],
)

py_oci_image(
    name = "i1_inc_blob_deletion_image",
    package_name = package_name(),
    binary = ":i1_inc_blob_deletion",
    trivy_allow_list = [
    ],
)

kubecfg(
    name = "i1_inc_blob_deletion_kubecfg",
    src = "i1_inc_blob_deletion.jsonnet",
    data = [
        ":i1_inc_blob_deletion_image",
    ],
    visibility = [
        "//services/deploy:__subpackages__",
    ],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:config-map-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/gcp:gcp-lib",
        "//services/deploy:endpoints",
        "//services/request_insight/analytics_dataset:dataset_lib",
        "//services/request_insight/lib:bigquery_lib",
    ],
)

metadata_test(
    name = "metadata_test",
    src = "METADATA.jsonnet",
    deps = [
        ":i1_inc_blob_deletion_kubecfg",
    ],
)
