"""Script for deleting objects from the RI GCS export blob & event buckets."""

import argparse
import logging
import os
import time
import threading

from google.cloud import storage
from google.api_core import exceptions

from base.logging.console_logging import setup_console_logging


DEFAULT_PROJECT = "system-services-prod"
DEFAULT_BUCKET_EVENTS = "us-prod-request-insight-events-nonenterprise"

BATCH_SIZE = 100
BACKOFF = [1, 2, 4, 8, 16, 32, 60]
# Successfully used a value of 20 for the vanguard blob deletion job https://github.com/augmentcode/augment/pull/12895/files
N_THREADS = 10


# From vanguard blob deletion job https://github.com/augmentcode/augment/pull/12895/files
def delete_batched_backoff(
    client: storage.Client, bucket: storage.Bucket, blobs: list[storage.Blob]
):
    if not blobs:
        return

    # Create batches
    batches = []
    batch = []
    for blob in blobs:
        batch.append(blob)
        if len(batch) >= BATCH_SIZE:
            batches.append(batch)
            batch = []
    if batch:
        batches.append(batch)

    # Delete by batch
    for i, batch in enumerate(batches):
        logging.debug(f"Deleting batch {i+1}/{len(batches)} of size {len(batch)}")
        for delay_idx, delay in enumerate(BACKOFF):
            try:
                if batch:
                    with client.batch():
                        for blob in batch:
                            bucket.delete_blob(blob.name)
                break

            except exceptions.TooManyRequests as e:  # 429
                logging.warn(
                    "Got client error %s on batch delete, retrying in %d seconds",
                    e,
                    delay,
                )
            except exceptions.InternalServerError as e:  # 500
                logging.warn(
                    "Got client error %s on batch delete, retrying in %d seconds",
                    e,
                    delay,
                )
            except exceptions.BadGateway as e:  # 502
                logging.warn(
                    "Got client error %s on batch delete, retrying in %d seconds",
                    e,
                    delay,
                )
            except exceptions.ServiceUnavailable as e:  # 503
                logging.warn(
                    "Got client error %s on batch delete, retrying in %d seconds",
                    e,
                    delay,
                )
            except exceptions.GatewayTimeout as e:  # 504
                logging.warn(
                    "Got client error %s on batch delete, retrying in %d seconds",
                    e,
                    delay,
                )
            except exceptions.NotFound as e:  # 404
                # If this is the first attempt, propagate the NotFound error
                if delay_idx == 0:
                    raise e

                # If a blob in the batch was not found on a retry, verify each blob's existence
                # This handles cases where the API returns a 500 but actually deleted the blobs
                batch_new = []
                for blob in batch:
                    if bucket.blob(blob.name).exists():
                        batch_new.append(blob)
                batch = batch_new
            except Exception as e:
                logging.error(
                    "Got unexpected error %s on batch delete, retrying in %d seconds",
                    e,
                    delay,
                )

            time.sleep(delay)

        # after final backoff try one final time
        else:
            if batch:
                with client.batch():
                    for blob in batch:
                        bucket.delete_blob(blob.name)


# Reads a file containing a list of entries, one per line, and returns a list
# of the entries
def read_file_entries(file_path: str) -> list[str]:
    with open(os.path.expanduser(file_path), "r") as f:
        lines = f.read().splitlines()
        entries = [
            line.split()[0] for line in lines
        ]  # ignore any additional information
        return entries


def _request_events_thread(
    thread: int,
    tenant_id: str,
    client: storage.Client,
    bucket: storage.Bucket,
    request_ids: list[str],
    thread_return: dict,
    dry_run: bool = True,
):
    logging.debug(f"Thread {thread}: Starting request events deletion thread")

    not_present = 0
    to_delete = []
    for i, req_id in enumerate(request_ids):
        logging.debug(f"Thread {thread}: Processing request {i+1}/{len(request_ids)}")
        prefix = f"{tenant_id}/request/{req_id}/"
        blobs = list(bucket.list_blobs(prefix=prefix))

        if len(blobs) == 0:
            logging.warning(f"Found no request events for request ID {req_id}")
            not_present += 1
            continue
        else:
            logging.info(f"Found {len(blobs)} request events for request ID {req_id}")
            if not dry_run:
                to_delete.extend(blobs)

    if not dry_run and to_delete:
        logging.debug(f"Thread {thread}: Deleting {len(to_delete)} request events")
        delete_batched_backoff(client, bucket, to_delete)

    # Store count of found, not found. Python's GIL should prevent race conditions
    thread_return[thread] = (len(request_ids) - not_present, not_present)


def delete_request_events(
    tenant_id: str,
    client: storage.Client,
    bucket: storage.Bucket,
    project: str,
    request_ids: list[str],
    dry_run: bool = True,
    n_threads: int = N_THREADS,
):
    if dry_run:
        logging.info("Running in dry-run mode. No deletions will be made")
    else:
        logging.info("Running in non-dry-run mode. Deletions will be made")
    logging.info(
        f"Running with tenant={tenant_id}, bucket={bucket.name}, project={project}"
    )

    # Create threads for deleting request events in parallel
    thread_return = {}
    threads = []
    for i in range(n_threads):
        t = threading.Thread(
            target=_request_events_thread,
            args=(
                i,
                tenant_id,
                client,
                bucket,
                request_ids[i::n_threads],
                thread_return,
                dry_run,
            ),
        )
        t.start()
        threads.append(t)
    for t in threads:
        t.join()

    # Check the number of request ids found and not found, and the number of thread results to ensure all threads succeeded
    found = sum(count for count, _ in thread_return.values())
    not_found = sum(not_present for _, not_present in thread_return.values())
    if found + not_found != len(request_ids):
        raise ValueError(
            f"Found {found} request events and {not_found} not found, but expected a total of {len(request_ids)}. A thread must have failed."
        )
    if len(thread_return) != n_threads:
        raise ValueError(
            f"Expected {n_threads} thread results but got {len(thread_return)}. A thread must have failed."
        )

    if dry_run:
        logging.info(f"Found events for {found} request ids")
    else:
        logging.info(f"Deleted events for {found} request ids")

    if not_found > 0:
        logging.warning(f"Did not find events for {not_found} request ids")


def _delete_batched_backoff_thread(
    thread: int,
    client: storage.Client,
    bucket: storage.Bucket,
    blobs: list[storage.Blob],
    thread_return: dict,
):
    logging.debug(f"Thread {thread}: Starting delete batched backoff thread")
    delete_batched_backoff(client, bucket, blobs)
    thread_return[thread] = True


def delete_session_events(
    tenant_id: str,
    client: storage.Client,
    bucket: storage.Bucket,
    project: str,
    session_ids: list[str],
    dry_run: bool = True,
    n_threads: int = N_THREADS,
):
    if dry_run:
        logging.info("Running in dry-run mode. No deletions will be made")
    else:
        logging.info("Running in non-dry-run mode. Deletions will be made")
    logging.info(
        f"Running with tenant={tenant_id}, bucket={bucket.name}, project={project}"
    )

    not_present = 0
    for session_id in session_ids:
        prefix = f"{tenant_id}/session/{session_id}/"
        blobs = list(bucket.list_blobs(prefix=prefix))

        if len(blobs) == 0:
            logging.warning(f"Found no session events for session ID {session_id}")
            not_present += 1
            continue
        elif dry_run:
            logging.info(
                f"Found {len(blobs)} session events for session ID {session_id}"
            )
        else:
            logging.info(
                f"Deleting {len(blobs)} session events for session ID {session_id}"
            )

            # We expect few session ids so apply multithread to deletions but not finding the blobs
            thread_return = {}
            threads = []
            for i in range(n_threads):
                t = threading.Thread(
                    target=_delete_batched_backoff_thread,
                    args=(
                        i,
                        client,
                        bucket,
                        blobs[i::n_threads],
                        thread_return,
                    ),
                )
                t.start()
                threads.append(t)
            for t in threads:
                t.join()

            # Check the number of thread results to ensure all threads succeeded
            if len(thread_return) != n_threads:
                raise ValueError(
                    f"Expected {n_threads} thread results but got {len(thread_return)}. A thread must have failed."
                )

    if dry_run:
        logging.info(f"Found events for {len(session_ids) - not_present} session ids")
    else:
        logging.info(f"Deleted events for {len(session_ids) - not_present} session ids")

    if not_present > 0:
        logging.warning(f"Did not find events for {not_present} session ids")


def _delete_blobs_thread(
    thread: int,
    tenant_id: str,
    client: storage.Client,
    bucket: storage.Bucket,
    blob_names: list[str],
    thread_return: dict,
    dry_run: bool = True,
):
    logging.debug(f"Thread {thread}: Starting blob deletion thread")

    not_present = 0
    to_delete = []
    for blob_name in blob_names:
        # We had per-namespace blob buckets (e.g. augment-blob-exporter-i0-staging), which are deprecated
        # in favor of a single bucket (e.g. us-staging-blobs-nonenterprise) for all non-enterprise tenants.
        # In the deprecated per-namespace blob buckets, blobs are stored under blobs/{blob_name}, and in
        # new single bucket, blobs are stored under {tenant_id}/blobs/{blob_name}.
        if bucket.name and bucket.name.endswith("-blobs-nonenterprise"):
            blob = bucket.blob(f"{tenant_id}/blobs/{blob_name}")
        else:
            blob = bucket.blob(f"blobs/{blob_name}")

        if not blob.exists():
            logging.warning(f"Found no blob for blob name {blob_name}")
            not_present += 1
            continue
        else:
            logging.info(f"Found blob for blob name {blob_name}")
            if not dry_run:
                to_delete.append(blob)

    if not dry_run and to_delete:
        logging.debug(f"Thread {thread}: Deleting {len(to_delete)} blobs")
        delete_batched_backoff(client, bucket, to_delete)

    # Store count of found, not found. Python's GIL should prevent race conditions
    thread_return[thread] = (len(blob_names) - not_present, not_present)


def delete_blobs(
    tenant_id: str,
    client: storage.Client,
    bucket: storage.Bucket,
    project: str,
    blob_names: list[str],
    dry_run: bool = True,
    n_threads: int = N_THREADS,
):
    if dry_run:
        logging.info("Running in dry-run mode. No deletions will be made")
    else:
        logging.info("Running in non-dry-run mode. Deletions will be made")
    logging.info(f"Running with bucket={bucket.name}, project={project}")

    # Create threads for deleting blobs in parallel
    thread_return = {}
    threads = []
    for i in range(n_threads):
        t = threading.Thread(
            target=_delete_blobs_thread,
            args=(
                i,
                tenant_id,
                client,
                bucket,
                blob_names[i::n_threads],
                thread_return,
                dry_run,
            ),
        )
        t.start()
        threads.append(t)
    for t in threads:
        t.join()

    # Check the number of blob names found and not found, and the number of thread results to ensure all threads succeeded
    found = sum(count for count, _ in thread_return.values())
    not_found = sum(not_present for _, not_present in thread_return.values())
    if found + not_found != len(blob_names):
        raise ValueError(
            f"Found {found} blobs and {not_found} not found, but expected a total of {len(blob_names)}. A thread must have failed."
        )
    if len(thread_return) != n_threads:
        raise ValueError(
            f"Expected {n_threads} thread results but got {len(thread_return)}. A thread must have failed."
        )

    if dry_run:
        logging.info(f"Found blobs for {len(blob_names) - not_found} blob names")
    else:
        logging.info(f"Deleted blobs for {len(blob_names) - not_found} blob names")

    if not_found > 0:
        logging.warning(f"Did not find blobs for {not_found} blob names")


if __name__ == "__main__":
    setup_console_logging(add_timestamp=False)

    # Parse the common command line arguments
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--project",
        type=str,
        default=DEFAULT_PROJECT,
        help="The name of the project to delete objects from (system-services-prod or system-services-dev).",
    )
    parser.add_argument(
        "--dry-run",
        default=True,
        action=argparse.BooleanOptionalAction,
        help="If true (default), will run in dry-run mode and no deletions will be made. If false (--no-dry-run), will actually delete objects.",
    )
    parser.add_argument(
        "--n-threads",
        type=int,
        default=N_THREADS,
        help="The number of threads to use for deleting objects.",
    )

    # Parse the specific command line arguments for each deletion type
    subparsers = parser.add_subparsers()

    request_events_parser = subparsers.add_parser("requests")
    request_events_parser.set_defaults(action="requests")
    request_events_parser.add_argument(
        "--tenant-id",
        type=str,
        required=True,
        help="The tenant ID of the tenant to delete objects from.",
    )
    request_events_parser.add_argument(
        "--bucket",
        type=str,
        default=DEFAULT_BUCKET_EVENTS,
        help="The name of the bucket to delete objects from.",
    )
    request_events_parser.add_argument(
        "--request-ids-file",
        type=str,
        default="~/request_ids.txt",
        help="The path to a file containing a list of request IDs, one per line, to delete.",
    )

    session_events_parser = subparsers.add_parser("sessions")
    session_events_parser.set_defaults(action="sessions")
    session_events_parser.add_argument(
        "--tenant-id",
        type=str,
        required=True,
        help="The tenant ID of the tenant to delete objects from.",
    )
    session_events_parser.add_argument(
        "--bucket",
        type=str,
        default=DEFAULT_BUCKET_EVENTS,
        help="The name of the bucket to delete objects from.",
    )
    session_events_parser.add_argument(
        "--session-ids-file",
        type=str,
        default="~/session_ids.txt",
        help="The path to a file containing a list of session IDs, one per line, to delete.",
    )

    blobs_parser = subparsers.add_parser("blobs")
    blobs_parser.set_defaults(action="blobs")
    blobs_parser.add_argument(
        "--tenant-id",
        type=str,
        required=True,
        help="The tenant ID of the tenant to delete objects from.",
    )
    blobs_parser.add_argument(
        "--bucket",
        type=str,
        # Since the blobs bucket is tenant-specific, we don't have a default
        required=True,
        help="The name of the bucket to delete objects from.",
    )
    blobs_parser.add_argument(
        "--blob-names-file",
        type=str,
        default="~/blob_names.txt",
        help="The path to a file containing a list of blob names, one per line, to delete.",
    )

    args = parser.parse_args()

    # Initialize GCS client and bucket
    client = storage.Client(project=args.project)
    bucket = client.bucket(args.bucket)

    # Call appropriate deletion functions based on provided arguments
    if args.action == "requests":
        request_ids = read_file_entries(args.request_ids_file)
        logging.info(f"Found {len(request_ids)} request IDs in {args.request_ids_file}")
        delete_request_events(
            tenant_id=args.tenant_id,
            client=client,
            bucket=bucket,
            project=args.project,
            request_ids=request_ids,
            dry_run=args.dry_run,
            n_threads=args.n_threads,
        )
    elif args.action == "sessions":
        session_ids = read_file_entries(args.session_ids_file)
        logging.info(f"Found {len(session_ids)} session IDs in {args.session_ids_file}")
        delete_session_events(
            tenant_id=args.tenant_id,
            client=client,
            bucket=bucket,
            project=args.project,
            session_ids=session_ids,
            dry_run=args.dry_run,
            n_threads=args.n_threads,
        )
    elif args.action == "blobs":
        blob_names = read_file_entries(args.blob_names_file)
        logging.info(f"Found {len(blob_names)} blob names in {args.blob_names_file}")
        delete_blobs(
            tenant_id=args.tenant_id,
            client=client,
            bucket=bucket,
            project=args.project,
            blob_names=blob_names,
            dry_run=args.dry_run,
            n_threads=args.n_threads,
        )
    else:
        raise ValueError(f"Unknown action: {args.action}")
