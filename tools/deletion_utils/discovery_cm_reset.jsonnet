local certLib = import 'deploy/common/cert-lib.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local endpointsLib = import 'services/deploy/endpoints.jsonnet';

function(env, namespace, cloud, namespace_config)
  local appName = 'discovery-cm-reset-backfill';

  local clientCert = certLib.createClientCert('%s-client-certificate' % appName,
                                              namespace=namespace,
                                              appName=appName,
                                              dnsNames=['cm-deletion-job.%s' % namespace],
                                              volumeName='client-certs');

  // creates a client certificate so that the pod can authenticiate to grpc servers running in the
  // central namespace
  local centralClientCert = certLib.createCentralClientCert(
    name='%s-central-client-cert' % appName,
    namespace=namespace,
    env=env,
    appName=appName,
    dnsNames=['cm-deletion-job.%s' % namespace],
    volumeName='central-client-certs',
  );

  local config = {
    tenant_id: '547bc5d2ea39a8a3605ae752ab3437f7',  // discovery0
    namespace: namespace,
    // the user index hit last-known-good on 9/18 so delete from before 9/25 just to be safe
    min_time: 0,
    max_time: 1727247600,  // timestamp for 9/25
    client_mtls: clientCert.config,
    central_client_mtls: centralClientCert.config,
    token_exchange_endpoint: endpointsLib.getTokenExchangeGrpcUrl(env=env, namespace=namespace, cloud=cloud),
    bigtable_proxy_endpoint: 'bigtable-proxy-svc:50051',
    dry_run: false,
  };
  local configMap = configMapLib.createConfigMap(appName=appName, namespace=namespace, config=config);

  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=null);

  local container = {
    name: appName,
    target: {
      name: '//tools/deletion_utils:discovery_cm_reset_image',
      dst: 'discovery_cm_reset',
    },
    resources: {
      limits: {
        cpu: 1,
        memory: '8Gi',
      },
    },
    volumeMounts: [
      configMap.volumeMountDef,
      clientCert.volumeMountDef,
      centralClientCert.volumeMountDef,
    ],
  };
  local pod = {
    containers: [
      container,
    ],
    volumes: [
      configMap.podVolumeDef,
      clientCert.podVolumeDef,
      centralClientCert.podVolumeDef,
    ],
  };
  local job = {
    apiVersion: 'batch/v1',
    kind: 'Job',
    metadata: {
      name: appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'reloader.stakater.com/search': 'true',
      },
    },
    spec: {
      template: {
        metadata: {
          labels: {
            app: appName,
          },
        },
        spec: pod + {
          restartPolicy: 'Never',
          tolerations: tolerations,
          affinity: affinity,
        },
      },
    },
  };
  lib.flatten([
    configMap.objects,
    clientCert.objects,
    centralClientCert.objects,
    job,
  ])
