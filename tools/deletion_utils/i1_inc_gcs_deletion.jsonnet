function(env, namespace, cloud, namespace_config)
  local lib = import 'deploy/common/lib.jsonnet';
  local nodeLib = import 'deploy/common/node-lib.jsonnet';
  local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
  local analyticsDatasetLib = (import 'services/request_insight/analytics_dataset/dataset_lib.jsonnet')(
    cloud, env, namespace, namespace_config.flags.useSharedDevRequestInsightBigquery
  );
  local searchDatasetLib = (import 'services/request_insight/support_database/search_dataset/dataset_lib.jsonnet')(
    cloud, env, namespace, false
  );
  local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
  local bigqueryLib = import 'services/request_insight/lib/bigquery_lib.jsonnet';

  local projectId = cloudInfo[cloud].projectId;

  local appName = 'i1-inc-gcs-deletion';
  local shortAppName = 'inc-gcs-del';
  local serviceAccount = gcpLib.createServiceAccount(
    appName, env, cloud, namespace, iam=true, overridePrefix=shortAppName
  );

  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=null);

  // GCS Access
  local bucketName = 'us-prod-request-insight-events-nonenterprise';
  local bucketAccess = {
    apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
    kind: 'IAMPartialPolicy',
    metadata: {
      name: '%s-policy' % bucketName,
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    spec: {
      resourceRef: {
        kind: 'StorageBucket',
        external: bucketName,
      },
      bindings: [
        {
          role: 'roles/storage.objectAdmin',
          members: [
            { member: 'serviceAccount:%s' % serviceAccount.serviceAccountGcpEmailAddress },
          ],
        },
      ],
    },
  };

  // BigQuery Access
  local datasetAccess = lib.flatten([
    // Analytics dataset
    bigqueryLib.datasetAccess(
      namespace,
      appName,
      analyticsDatasetLib.readonlyCloudIdentityGroup,
      analyticsDatasetLib.dataNamespace,
      serviceAccount.serviceAccountGcpEmailAddress,
    ),
    // Search dataset
    bigqueryLib.datasetAccess(
      namespace,
      appName,
      searchDatasetLib.readonlyCloudIdentityGroup,
      searchDatasetLib.dataNamespace,
      serviceAccount.serviceAccountGcpEmailAddress,
    ),
    // Give permission to start BigQuery jobs (needed to run queries).
    gcpLib.grantAccess(
      name='%s-bigquery-job-policy' % appName,
      env=env,
      namespace=namespace,
      appName=appName,
      resourceRef={
        kind: 'Project',
        external: 'project/%s' % projectId,
      },
      bindings=[
        {
          role: 'roles/bigquery.jobUser',
          members: [
            {
              memberFrom: {
                serviceAccountRef: {
                  name: serviceAccount.iamServiceAccountName,
                },
              },
            },
          ],
        },
      ]
    ),
    // Give access to PII (needed for querying for user email in the analytics user table)
    gcpLib.grantAccess(
      name='%s-pii-finegrained-reader-access' % appName,
      env=env,
      namespace=namespace,
      appName=appName,
      resourceRef={
        kind: 'DataCatalogPolicyTag',
        external: bigqueryLib.dataAccessPolicyTag(cloud, env, 'pii'),
      },
      bindings=[
        {
          role: 'roles/datacatalog.categoryFineGrainedReader',
          members: [
            {
              memberFrom: {
                serviceAccountRef: {
                  name: serviceAccount.iamServiceAccountName,
                },
              },
            },
          ],
        },
      ],
    ),
  ]);

  local container = {
    name: appName,
    target: {
      name: '//tools/deletion_utils:i1_inc_gcs_deletion_image',
      dst: 'i1_inc_gcs_deletion',
    },
    resources: {
      limits: {
        cpu: 1,
        memory: '8Gi',
      },
    },
  };
  local pod = {
    serviceAccountName: serviceAccount.name,
    priorityClassName: cloudInfo.envToPriorityClass(env),
    containers: [
      container,
    ],
  };
  local job = {
    apiVersion: 'batch/v1',
    kind: 'Job',
    metadata: {
      name: appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'reloader.stakater.com/search': 'true',
      },
    },
    spec: {
      backoffLimit: 8,
      template: {
        metadata: {
          labels: {
            app: appName,
          },
        },
        spec: pod + {
          restartPolicy: 'OnFailure',
          tolerations: tolerations,
          affinity: affinity,
        },
      },
    },
  };
  lib.flatten([
    job,
    serviceAccount.objects,
    datasetAccess,
    bucketAccess,
  ])
