"""Script for deleting user blobs from the content manager BigTable."""

import argparse
import sys
import logging
from datetime import datetime
from typing import Optional
from zoneinfo import ZoneInfo

from google.protobuf.timestamp_pb2 import Timestamp

from base.logging.console_logging import setup_console_logging
from services.lib.grpc import grpc_args_parser, token_parser
from services.content_manager.client.content_manager_client import ContentManagerClient
from services.lib.request_context.request_context import RequestContext


GET_USER_BLOBS_LIMIT = 10000
BATCH_DELETE_LIMIT = 1000
TIMEZONE = ZoneInfo("America/Los_Angeles")


def _timestamp_proto_to_unix(timestamp: Timestamp) -> int:
    assert (
        timestamp.nanos == 0
    ), "cannot convert timestamp with nonzero nanos to integer unix timestamp"
    return timestamp.seconds


def user_blobs(
    rpc_client: ContentManagerClient,
    request_context: RequestContext,
    tenant_id: str,
    user_id: str,
    min_timestamp: Optional[int] = None,
    max_timestamp: Optional[int] = None,
) -> list[tuple[str, Timestamp]]:
    min_time = (
        datetime.fromtimestamp(min_timestamp, TIMEZONE).strftime("%Y-%m-%d %H:%M:%S")
        if min_timestamp
        else "<no min>"
    )
    max_time = (
        datetime.fromtimestamp(max_timestamp, TIMEZONE).strftime("%Y-%m-%d %H:%M:%S")
        if max_timestamp
        else "<no max>"
    )
    logging.info(
        "finding all blobs uploaded between min time %s and max time %s (timezone %s)",
        min_time,
        max_time,
        TIMEZONE,
    )

    blobs: list[tuple[str, Timestamp]] = []  # tuple of name, timestamp
    # NOTE(mpauly): Since Timestamp is not hashable we use the seconds field instead.
    seen: set[tuple[str, int]] = (
        set()
    )  # set of blob names, timestamp tuples we've seen so far

    # Do a sort of manual pagination using the timestamps. We start with min
    # and max timestamp with the user provided values or the whole range if not
    # provided, then lower the max_timestamp on subsequent calls.
    iteration_max = max_timestamp
    while True:
        logging.debug(
            "making GetUserBlobs request with min_timestamp=%s, max_timestamp=%s",
            min_timestamp,
            iteration_max,
        )

        response = rpc_client.get_user_blobs(
            user_id,
            tenant_id,
            request_context,
            limit=GET_USER_BLOBS_LIMIT,
            min_timestamp=Timestamp(seconds=min_timestamp) if min_timestamp else None,
            max_timestamp=Timestamp(seconds=iteration_max) if iteration_max else None,
        )
        if not response:
            break

        first_timestamp = _timestamp_proto_to_unix(
            response[0].time
        )  # first in the response ordering, latest upload time
        last_timestamp = _timestamp_proto_to_unix(
            response[-1].time
        )  # last in the response ordering, earliest upload time
        logging.debug(
            "received %d blobs: first_timestamp=%d last_timestamp=%d",
            len(response),
            first_timestamp,
            last_timestamp,
        )

        if first_timestamp == last_timestamp and len(response) == GET_USER_BLOBS_LIMIT:
            raise Exception(
                "All blobs in response have the same timestamp, unable to search for next page"
            )

        for blob in response:
            if (blob.blob_name, blob.time.seconds) not in seen:
                blobs.append((blob.blob_name, blob.time))
                seen.add((blob.blob_name, blob.time.seconds))

        # less than limit implies we're on the last page
        if len(response) < GET_USER_BLOBS_LIMIT:
            logging.debug(
                "received fewer blobs than the limit of %d, stopping iteration",
                GET_USER_BLOBS_LIMIT,
            )
            break

        iteration_max = last_timestamp

    logging.info("received %s blobs", len(blobs))

    return blobs


def delete_blobs(
    rpc_client: ContentManagerClient,
    request_context: RequestContext,
    tenant_id: str,
    user_id: Optional[str],
    blobs: list[tuple[str, Timestamp]],
    dry_run: bool = True,
    ignore_index: bool = False,
):
    for i in range(0, len(blobs), BATCH_DELETE_LIMIT):
        batch = blobs[i : i + BATCH_DELETE_LIMIT]
        logging.info(
            "(dry_run=%s) Processing batch %d, size: %d%s",
            dry_run,
            i // BATCH_DELETE_LIMIT + 1,
            len(batch),
            " (ignoring user index)" if ignore_index else "",
        )

        if dry_run:
            continue

        # If ignore_index is True and user_id is None, use an empty string
        effective_user_id = "" if ignore_index and user_id is None else user_id

        # Create the blob user tuples with the effective user ID
        blob_user_tuples = []
        for blob_name, timestamp in batch:
            blob_user_tuples.append((blob_name, effective_user_id, timestamp))
        result = rpc_client.batch_delete_blobs(
            blob_user_tuples,
            request_context,
            tenant_id,
            ignore_index=ignore_index,
        )

        if not result:
            logging.error("Error deleting batch, aborting deletion")
            exit(1)

    logging.info(
        "(dry_run=%s) Deleted %d blobs%s",
        dry_run,
        len(blobs),
        " (ignored user index)" if ignore_index else "",
    )


def main():
    setup_console_logging(add_timestamp=False)

    parser = argparse.ArgumentParser()
    grpc_args_parser.add_endpoint_args(parser)
    token_parser.add_token_args(parser)
    subparsers = parser.add_subparsers()

    user_blobs_parser = subparsers.add_parser("user_blobs")
    user_blobs_parser.set_defaults(action="user_blobs")
    user_blobs_parser.add_argument(
        "--tenant-id",
        type=str,
        required=True,
        help="Id of the tenant to find uploaded blobs from",
    )
    user_blobs_parser.add_argument(
        "--user-id",
        type=str,
        required=True,
        help="Id of the user to find uploaded blobs from",
    )
    user_blobs_parser.add_argument(
        "--min-timestamp",
        type=int,
        help="Minimum unix timestamp for blobs filter",
    )
    user_blobs_parser.add_argument(
        "--max-timestamp",
        type=int,
        help="Max unix timestamp for blobs filter",
    )
    user_blobs_parser.add_argument(
        "--output-file",
        type=str,
        help="Filename to write blob names. If not provided will write to stdout",
    )

    delete_blobs_parser = subparsers.add_parser("delete_blobs")
    delete_blobs_parser.set_defaults(action="delete_blobs")
    delete_blobs_parser.add_argument(
        "--tenant-id",
        type=str,
        required=True,
        help="Id of the tenant to find uploaded blobs from",
    )
    delete_blobs_parser.add_argument(
        "--user-id",
        type=str,
        help="Id of the user to find uploaded blobs from. Not required when --ignore-index is used.",
    )
    delete_blobs_parser.add_argument(
        "--blob-names-file",
        type=str,
        help="File containing a blob name per line to delete. Each line is split on spaces, and should contain the blob name and timestamp. The best way to produce such a file is to use the output of the 'user_blobs' command.",
    )
    delete_blobs_parser.add_argument(
        "--dry-run",
        default=True,
        action=argparse.BooleanOptionalAction,
        help="If true, will run in dry run mode (no delete). If false (--no-dry-run), then will actually delete the blobs",
    )
    delete_blobs_parser.add_argument(
        "--ignore-index",
        default=False,
        action=argparse.BooleanOptionalAction,
        help="If true, will delete the raw and transformed blob content without affecting user index entries. This is useful for cleaning up inconsistent blobs",
    )

    args = parser.parse_args()

    try:
        with grpc_args_parser.create_client(
            args,
            ContentManagerClient.create_for_endpoint,
            default_service_name="content-manager-svc",
            default_endpoint="content-manager-svc:50051",
        ) as rpc_client:
            token = token_parser.get_token(args)
            request_context = RequestContext.create(auth_token=token)

            if args.action == "user_blobs":
                blobs = user_blobs(
                    rpc_client,
                    request_context,
                    args.tenant_id,
                    args.user_id,
                    args.min_timestamp,
                    args.max_timestamp,
                )

                blob_lines = []
                for name, timestamp in blobs:
                    full_timestamp = timestamp.ToDatetime(TIMEZONE).strftime(
                        "%Y-%m-%d %H:%M:%S"
                    )
                    unix_timestamp = _timestamp_proto_to_unix(timestamp)
                    blob_lines.append(f"{name} {unix_timestamp} ({full_timestamp})")

                if args.output_file:
                    # Only overwrite the file if blobs were found
                    if blob_lines:
                        with open(args.output_file, "w") as f:
                            for line in blob_lines:
                                f.write(line + "\n")
                    logging.info("wrote blob names to file %s", args.output_file)
                else:
                    for line in blob_lines:
                        logging.info("%s", line)

            elif args.action == "delete_blobs":
                if not args.blob_names_file:
                    logging.error("Please specify --blob-names-file")
                    sys.exit(1)

                # Check if user_id is required
                if not args.ignore_index and not args.user_id:
                    logging.error(
                        "--user-id is required when --ignore-index is not specified"
                    )
                    sys.exit(1)

                blobs: list[tuple[str, Timestamp]] = []
                with open(args.blob_names_file, "r") as f:
                    for line in f.read().splitlines():
                        if not line:
                            continue

                        items = line.split(" ")
                        if len(items) < 2:
                            logging.error("Invalid line in blob names file: %s", line)
                            sys.exit(1)
                        blob_name = items[0]
                        timestamp = Timestamp(seconds=int(items[1]))
                        blobs.append((blob_name, timestamp))

                delete_blobs(
                    rpc_client,
                    request_context,
                    args.tenant_id,
                    args.user_id,
                    blobs,
                    args.dry_run,
                    args.ignore_index,
                )
            else:
                logging.error("Unknown action %s", args.action)
                sys.exit(1)

    except KeyboardInterrupt:
        sys.exit(1)

    except argparse.ArgumentError as e:
        logging.error("%s", e)
        sys.exit(1)


if __name__ == "__main__":
    main()
