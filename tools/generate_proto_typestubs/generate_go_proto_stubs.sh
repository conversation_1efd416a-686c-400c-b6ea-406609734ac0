#!/bin/bash
# This script generates Go proto stubs in the repository.
#
# Usage: `bazel run //tools/generate_proto_typestubs:generate_go_proto_stubs [--gomock-only] [target_pattern]`.
#
# Parameters:
#   --gomock-only - Optional. Only generate gomock files, skip proto stubs.
#   target_pattern - Optional. Bazel target pattern to query for go_proto_library targets (default: //...)
#

# Parse command line arguments
GOMOCK_ONLY=false
for arg in "$@"; do
	case $arg in
	--gomock-only)
		GOMOCK_ONLY=true
		shift
		;;
	esac
done

cleanup() {
	if [ "$?" -ne 0 ]; then
		echo "Received interrupt signal or error. Cleaning up and exiting..."
		exit 1
	fi
	exit 0
}

# Trap SIGINT (Ctrl+C) and SIGTERM signals
trap cleanup SIGINT SIGTERM

if [ -z "$BUILD_WORKSPACE_DIRECTORY" ]; then
	echo "BUILD_WORKSPACE_DIRECTORY not set (run with bazel)" >&2
	exit 1
fi

cd "$BUILD_WORKSPACE_DIRECTORY"

start_time=$(date +%s)

# Set the target pattern (default: //...)
TARGET_PATTERN=${1:-"//..."}

# Define the generate_target function before using it
generate_target() {
	local target=$1
	local success=0

	# Ensure we handle targets correctly by checking if they contain a colon
	if [[ "$target" == *":"* ]]; then
		package_path=$(echo "$target" | cut -d ':' -f1)
		target_name=$(echo "$target" | cut -d ':' -f2)

		# Try all possible naming conventions for the stub generation target
		possible_targets=(
			"$package_path:generate_${target_name}_stubs" # go_proto_library convention
			"$package_path:${target_name}.generate_stubs" # go_grpc_library convention
			"$package_path:${target_name}_generate_stubs" # alternative convention
		)

		for stub_target in "${possible_targets[@]}"; do
			echo "Trying to generate stubs for $target ($stub_target)"
			if bazel run "$stub_target" 2>/dev/null; then
				success=1
				break
			fi
		done

		if [ $success -eq 0 ]; then
			echo "  - No generate_stubs target found for $target"

			# As a fallback, try to build the target directly
			echo "  - Trying to build the target directly: $target"
			if bazel build "$target" 2>/dev/null; then
				echo "  - Successfully built $target"
				success=1

				# Create symbolic links for the generated files if needed
				handle_directory_structure "$target"
			fi
		fi
	else
		echo "Warning: Unexpected target format: $target (missing ':')"
	fi
}

# Function to handle directory structure issues
handle_directory_structure() {
	local target=$1

	# Extract package path and target name
	local package_path=$(echo "$target" | cut -d ':' -f1)
	local target_name=$(echo "$target" | cut -d ':' -f2)

	# Remove leading // from package path
	local clean_package_path=${package_path#//}

	# Get the importpath from the BUILD file
	local importpath=""
	if grep -q "importpath" "$clean_package_path/BUILD"; then
		importpath=$(grep "importpath" "$clean_package_path/BUILD" | head -1 | sed -E 's/.*importpath[[:space:]]*=[[:space:]]*"([^"]+)".*/\1/')
		echo "  - Found importpath: $importpath"
	fi

	# Find the proto files in bazel-bin
	local proto_files=$(find "bazel-bin/$clean_package_path" -name "*.pb.go" 2>/dev/null)

	if [ -n "$proto_files" ]; then
		# Determine the source directory
		local src_dir=$(dirname $(echo "$proto_files" | head -1) | sed "s|bazel-bin/||")

		# Create the proto directory if it doesn't exist
		mkdir -p "$clean_package_path/proto"

		# Copy the proto files to the proto directory
		for file in $proto_files; do
			local filename=$(basename "$file")
			cp -f "$file" "$clean_package_path/proto/$filename"
			echo "  - Copied $filename to $clean_package_path/proto/"
		done

		# If we found an importpath, check if it matches the directory structure
		if [ -n "$importpath" ]; then
			# Extract the last part of the importpath (after the last /)
			local import_dir=$(echo "$importpath" | rev | cut -d'/' -f1 | rev)

			# If the import directory is different from proto, create it and add symlinks
			if [ "$import_dir" != "proto" ]; then
				echo "  - Import directory ($import_dir) is different from proto"
				mkdir -p "$clean_package_path/$import_dir"

				# Create symbolic links from import_dir to proto
				for file in $proto_files; do
					local filename=$(basename "$file")
					local link_path="$clean_package_path/$import_dir/$filename"

					# Create symbolic link if it doesn't exist
					if [ ! -L "$link_path" ]; then
						ln -sf "../proto/$filename" "$link_path"
						echo "  - Created symbolic link for $filename in $import_dir"
					fi
				done
			fi
		fi
	fi
}

# Function to ensure symbolic links are created for all packages
ensure_symlinks() {
	echo "Ensuring symbolic links for all packages..."

	# Find all go_proto_library and go_grpc_library targets in BUILD files
	local build_files=$(find . -name "BUILD" -type f -exec grep -l "go_.*_library" {} \;)

	for build_file in $build_files; do
		local package_dir=$(dirname "$build_file")

		# Skip non-directory paths
		if [ ! -d "$package_dir" ]; then
			continue
		fi

		# Get the importpath from the BUILD file
		if grep -q "importpath" "$build_file"; then
			local importpath=$(grep "importpath" "$build_file" | head -1 | sed -E 's/.*importpath[[:space:]]*=[[:space:]]*"([^"]+)".*/\1/')

			if [ -n "$importpath" ]; then
				# Extract the last part of the importpath (after the last /)
				local import_dir=$(echo "$importpath" | rev | cut -d'/' -f1 | rev)

				# If proto directory exists and import_dir is different, create symlinks
				if [ -d "$package_dir/proto" ] && [ "$import_dir" != "proto" ]; then
					echo "  - Checking package: $package_dir"

					# Create the import directory if it doesn't exist
					mkdir -p "$package_dir/$import_dir"

					# Find all .pb.go files in the proto directory
					local proto_files=$(find "$package_dir/proto" -name "*.pb.go" 2>/dev/null)

					for file in $proto_files; do
						local filename=$(basename "$file")
						local link_path="$package_dir/$import_dir/$filename"

						# Create symbolic link if it doesn't exist
						if [ ! -L "$link_path" ]; then
							ln -sf "../proto/$filename" "$link_path"
							echo "  - Created symbolic link for $filename in $import_dir"
						fi
					done
				fi
			fi
		fi
	done
}

# Only generate proto stubs if --gomock-only is not specified
if [ "$GOMOCK_ONLY" = false ]; then
	echo "Generating Go proto stubs for pattern: $TARGET_PATTERN"

	# Handle wildcard patterns for specific directories
	if [[ "$TARGET_PATTERN" == *"/..."* ]]; then
		echo "Processing targets for pattern: $TARGET_PATTERN"

		# Extract the package path without the /... suffix
		package_path=${TARGET_PATTERN%/...}

		# Find all go_proto_library and go_grpc_library targets in the specified directory
		echo "Finding Go proto and gRPC targets in $package_path..."
		PACKAGE_TARGETS=$(NO_BAZEL_PRINT=1 bazel query "kind(go_proto_library, $TARGET_PATTERN) + kind(go_grpc_library, $TARGET_PATTERN)" 2>/dev/null)

		if [ -n "$PACKAGE_TARGETS" ]; then
			echo "Found targets: $PACKAGE_TARGETS"
			for target in $PACKAGE_TARGETS; do
				generate_target "$target"
			done
		fi

		# Ensure symbolic links are created
		ensure_symlinks

		# If we're processing a specific directory pattern, we're done
		if [[ "$TARGET_PATTERN" != "//..." ]]; then
			proto_end_time=$(date +%s)
			proto_execution_time=$((proto_end_time - start_time))
			proto_minutes=$((proto_execution_time / 60))
			proto_seconds=$((proto_execution_time % 60))
			echo "Done generating Go proto stubs for $TARGET_PATTERN in ${proto_minutes}m ${proto_seconds}s."
			exit 0
		fi
	# Handle specific targets directly
	elif [[ "$TARGET_PATTERN" == *":"* && "$TARGET_PATTERN" != "//..." ]]; then
		echo "Processing specific target: $TARGET_PATTERN"
		generate_target "$TARGET_PATTERN"

		# Ensure symbolic links are created
		ensure_symlinks

		proto_end_time=$(date +%s)
		proto_execution_time=$((proto_end_time - start_time))
		proto_minutes=$((proto_execution_time / 60))
		proto_seconds=$((proto_execution_time % 60))
		echo "Done generating specific Go proto stub in ${proto_minutes}m ${proto_seconds}s."
		exit 0
	fi

	echo "Finding Go proto and gRPC targets with bazel..."
	GO_TARGETS=$(NO_BAZEL_PRINT=1 bazel query "kind(go_proto_library, $TARGET_PATTERN) + kind(go_grpc_library, $TARGET_PATTERN)" 2>/dev/null)

	total_targets=$(echo "$GO_TARGETS" | wc -w)
	completed_targets=0

	echo "Processing $total_targets targets ..."

	# Process targets sequentially
	for target in $GO_TARGETS; do
		generate_target "$target"
		completed_targets=$((completed_targets + 1))
		echo "[$completed_targets/$total_targets] Processing..."
	done

	proto_end_time=$(date +%s)
	proto_execution_time=$((proto_end_time - start_time))
	proto_minutes=$((proto_execution_time / 60))
	proto_seconds=$((proto_execution_time % 60))

	echo "Done generating Go proto stubs in ${proto_minutes}m ${proto_seconds}s."
else
	echo "Skipping proto stub generation (--gomock-only specified)"
fi

# This section has been moved to the conditional block above

# Now generate gomock files
echo "Finding gomock targets with bazel..."
GOMOCK_TARGETS=$(NO_BAZEL_PRINT=1 bazel query "kind(gomock, //...)" 2>/dev/null)

generate_gomock_target() {
	local target=$1
	local success=0

	# Ensure we handle targets correctly by checking if they contain a colon
	if [[ "$target" == *":"* ]]; then
		# Remove the leading // from the package path
		package_path=$(echo "$target" | cut -d ':' -f1 | sed 's|^//||')
		target_name=$(echo "$target" | cut -d ':' -f2)

		echo "Generating mocks for $target"
		if bazel build "$target" &>/dev/null; then
			# Find the output file path
			output_file=$(bazel query --output=xml "$target" | grep -o 'out="[^"]*"' | head -1 | cut -d '"' -f2)
			if [ -n "$output_file" ]; then
				# Get the directory and filename
				output_dir=$(dirname "$output_file")
				filename=$(basename "$output_file")

				# Create the target directory if it doesn't exist
				mkdir -p "$package_path/$output_dir"

				# Copy the generated file with a .mock.go extension
				new_filename="${filename%.go}.mock.go"
				cp -f "bazel-bin/$output_file" "$package_path/$output_dir/$new_filename"
				echo "  - Copied mock file to $package_path/$output_dir/$new_filename"
				success=1
			else
				# If we can't find the output file from the query, try to guess based on conventions
				echo "  - Couldn't determine output file from query, trying to guess..."
				# Look for generated files in bazel-bin
				possible_files=$(find bazel-bin/$package_path -name "*.go" -type f | grep -i "mock")
				for file in $possible_files; do
					if [ -f "$file" ]; then
						rel_path=${file#bazel-bin/}
						dir_path=$(dirname "$rel_path")
						filename=$(basename "$rel_path")
						new_filename="${filename%.go}.mock.go"

						# Create the target directory if it doesn't exist
						# Remove the leading // from the directory path
						clean_dir_path=$(echo "$dir_path" | sed 's|^//||')
						mkdir -p "$clean_dir_path"

						# Copy the generated file with a .mock.go extension
						cp -f "$file" "$clean_dir_path/$new_filename"
						echo "  - Copied mock file to $clean_dir_path/$new_filename"
						success=1
					fi
				done
			fi
		else
			echo "  - Failed to build $target"
		fi
	else
		echo "Warning: Unexpected target format: $target (missing ':')"
	fi
}

total_gomock_targets=$(echo "$GOMOCK_TARGETS" | wc -w)
completed_gomock_targets=0

echo "Processing $total_gomock_targets gomock targets ..."

# Process gomock targets sequentially
for target in $GOMOCK_TARGETS; do
	generate_gomock_target "$target"
	completed_gomock_targets=$((completed_gomock_targets + 1))
	echo "[$completed_gomock_targets/$total_gomock_targets] Processing..."
done

echo "Done generating gomock files."

# Final check to ensure symbolic links are created for all packages
ensure_symlinks
