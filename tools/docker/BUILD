# base docker containers

load("@rules_oci//oci:defs.bzl", "oci_image")
load("@rules_pkg//pkg:tar.bzl", "pkg_tar")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:trivy.bzl", "trivy_test")

BASE_IMAGES = [
    "ubuntu2004_ci_base_image",
    "cuda_base_image",
    "ubuntu_base_image",
    "cbazel_base_image",
]

[trivy_test(
    name = "{}.trivy_test".format(base_image),
    timeout = "eternal",
    src = ":{}".format(base_image, base_image),
    allow_list = [] if base_image != "cbazel_base_image" else [
        "CVE-2024-34156",  # gcloud (AU-4115)
        "CVE-2025-47273",  # setuptools path traversal vulnerability (AU-10286)
    ],
    severity = "CRITICAL,HIGH",
    tags = ["lint"],
) for base_image in BASE_IMAGES]

pkg_tar(
    name = "grpc_health_probe_tar",
    srcs = ["@com_github_grpc_ecosystem_grpc_health_probe//:grpc-health-probe"],
    package_dir = "/usr/bin",
    visibility = ["//visibility:public"],
)

pkg_tar(
    name = "docker_credential_gcr_tar",
    srcs = ["@com_github_googlecloudplatform_docker_credential_gcr_v2//:docker-credential-gcr"],
    package_dir = "/usr/bin",
    target_compatible_with = ["@platforms//os:linux"],
    visibility = ["//visibility:public"],
)

# a base container image with git and sudo installed
oci_image(
    name = "ubuntu2004_ci_base_image",
    base = "@ubuntu_ci_base_image//:ubuntu_ci_base_image",
    target_compatible_with = ["@platforms//os:linux"],
    visibility = [
        "//tools:__subpackages__",
    ],
)

# a base docker image for cuda usage
oci_image(
    name = "cuda_base_image",
    base = "@cuda_base_image//:cuda_base_image",
    visibility = [
        "//services:__subpackages__",
        "//tools:__subpackages__",
    ],
)

oci_image(
    name = "ubuntu_base_image",
    base = "@ubuntu_base_image//:ubuntu_base_image",
    visibility = [
        "//services:__subpackages__",
        "//tools:__subpackages__",
    ],
)

oci_image(
    name = "cbazel_base_image",
    base = "@cbazel_base_image//:cbazel_base_image",
    tars = [
        ":docker_credential_gcr_tar",
    ],
    visibility = [
        "//tools:__subpackages__",
    ],
)

oci_image(
    name = "cbazel_test_image",
    base = "@cbazel_test_base_image//:cbazel_test_base_image",
    # we need the docker credential helper to be able to pull base images
    # and push images are part of build and test operations.
    tars = [
        ":docker_credential_gcr_tar",
    ],
    visibility = [
        "//tools:__subpackages__",
    ],
)

# a base container image with git installed
oci_image(
    name = "ubuntu_git_base_image",
    base = "@ubuntu_git_base_image//:ubuntu_git_base_image",
    visibility = [
        "//services/integrations/github:__subpackages__",
        "//tools:__subpackages__",
    ],
)

# a base container image with tini installed
oci_image(
    name = "ubuntu_tini_base_image",
    base = "@ubuntu_tini_base_image//:ubuntu_tini_base_image",
    visibility = [
        "//services/auth/central:__subpackages__",
        "//tools:__subpackages__",
    ],
)

kubecfg(
    name = "kubecfg_shared",
    src = "deploy_shared.jsonnet",
    cluster_wide = True,
)

sh_binary(
    name = "update_base_images",
    srcs = ["update_base_images.sh"],
    data = glob(["dockerfiles/*"]),
)

# The redis base image
oci_image(
    name = "redis_base_image",
    base = "@redis//:redis",
    visibility = [
        "//services:__subpackages__",
    ],
)
