load("@aspect_rules_lint//format:defs.bzl", "format_multirun", "format_test")
load("@bazel_skylib//rules:native_binary.bzl", "native_binary")
load("@hedron_compile_commands//:refresh_compile_commands.bzl", "refresh_compile_commands")
load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:python.bzl", "py_binary", "py_library", "pytest_test")

package(default_visibility = ["//visibility:public"])

sh_binary(
    name = "jsonnetfmt_wrapper",
    srcs = ["jsonnetfmt_wrapper.sh"],
    data = [":jsonnetfmt"],
    visibility = ["//visibility:public"],
)

format_multirun(
    name = "format",
    cc = ":clang-format",
    go = "@aspect_rules_lint//format:gofumpt",
    jsonnet = ":jsonnetfmt_wrapper",
    protocol_buffer = ":buf",
    python = ":ruff",
    rust = "@rules_rust//tools/rustfmt:upstream_rustfmt",
    shell = "@aspect_rules_lint//format:shfmt",
    starlark = "@buildifier_prebuilt//:buildifier",
    visibility = ["//:__subpackages__"],
    yaml = "@aspect_rules_lint//format:yamlfmt",
)

format_test(
    name = "format_test",
    size = "small",
    cc = ":clang-format",
    go = "@aspect_rules_lint//format:gofumpt",
    jsonnet = ":jsonnetfmt_wrapper",
    no_sandbox = True,  # Enables formatting the entire workspace, paired with 'workspace' attribute
    protocol_buffer = ":buf",
    python = ":ruff",
    rust = "@rules_rust//tools/rustfmt:upstream_rustfmt",
    shell = "@aspect_rules_lint//format:shfmt",
    starlark = "@buildifier_prebuilt//:buildifier",
    workspace = "//:.shellcheckrc",  # A file in the workspace root, where the no_sandbox mode will run the formatter
    yaml = "@aspect_rules_lint//format:yamlfmt",
)

py_library(
    name = "user_info",
    srcs = ["user_info.py"],
    visibility = ["//visibility:public"],
)

py_library(
    name = "eng_info",
    srcs = ["eng_info.py"],
    data = [
        "//deploy/common:eng_json",
    ],
    visibility = ["//visibility:public"],
    deps = [
        requirement("dataclasses-json"),
    ],
)

pytest_test(
    name = "eng_info_test",
    srcs = ["eng_info_test.py"],
    deps = [
        ":eng_info",
    ],
)

alias(
    name = "kubectl",
    actual = "@k8s_binary//file:kubectl",
)

alias(
    name = "jsonnet",
    actual = "@google_jsonnet_go//cmd/jsonnet",
    visibility = ["//visibility:public"],
)

alias(
    name = "jsonnetfmt",
    actual = "@google_jsonnet_go//cmd/jsonnetfmt:jsonnetfmt",
    visibility = ["//visibility:public"],
)

alias(
    name = "jsonnet_lint",
    actual = "@google_jsonnet_go//cmd/jsonnet-lint:jsonnet-lint",
    visibility = ["//visibility:public"],
)

# see https://www.notion.so/Runbook-How-to-create-a-sealed-secret-2bc2040cb1ca46b19f0a7b2a77610abb?pvs=4
# on how to create a sealed secret
alias(
    name = "kubeseal",
    actual = "@com_github_bitnami_labs_sealed_secrets//cmd/kubeseal:kubeseal",
)

refresh_compile_commands(
    name = "refresh_compile_commands",
    targets = {
        "//...": "",
    },
)

alias(
    name = "clang-format",
    actual = "@llvm_toolchain//:clang-format",
)

py_binary(
    name = "cbazel_dev_pod",
    srcs = ["cbazel_dev_pod.py"],
    data = [
        "cbazel_dev_pod.jsonnet",
    ],
    deps = [
        "//base/python/cloud",
        "//tools/kubecfg:kubecfg_lib",
        requirement("InquirerPy"),
        "//base/logging:console_logging",
    ],
)

alias(
    name = "grpc_cli",
    actual = "@grpc//test/cpp/util:grpc_cli",
)

sh_binary(
    name = "parse_logs_bin",
    srcs = ["parse_logs"],
)

sh_binary(
    name = "get_prod_support_url",
    srcs = ["get_prod_support_url.sh"],
)

alias(
    name = "ruff",
    actual = select({
        "@bazel_tools//src/conditions:linux_x86_64": "@ruff_x86_64-unknown-linux-gnu//:ruff",
        "@bazel_tools//src/conditions:linux_aarch64": "@ruff_aarch64-unknown-linux-gnu//:ruff",
        "@bazel_tools//src/conditions:darwin_arm64": "@ruff_aarch64-apple-darwin//:ruff",
        "@bazel_tools//src/conditions:darwin_x86_64": "@ruff_x86_64-apple-darwin//:ruff",
        "@bazel_tools//src/conditions:windows_x64": "@ruff_x86_64-pc-windows-msvc//:ruff.exe",
    }),
)

alias(
    name = "buf",
    actual = "@rules_buf_toolchains//:buf",
)

native_binary(
    name = "clang_tidy",
    src = select(
        {
            "@bazel_tools//src/conditions:linux_x86_64": "@llvm_toolchain//:clang-tidy",
            "@bazel_tools//src/conditions:linux_aarch64": "@llvm_toolchain//:clang-tidy",
            "@bazel_tools//src/conditions:darwin_x86_64": "@llvm_toolchain//:clang-tidy",
            "@bazel_tools//src/conditions:darwin_arm64": "@llvm_toolchain//:clang-tidy",
        },
    ),
    out = "clang_tidy",
)
