local cloudInfo = import 'deploy/common/cloud_info.jsonnet';

function(cloud)
  local namespace = 'monitoring';
  local appName = 'prometheus-pushgateway';

  local deployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    spec: {
      replicas: 1,
      selector: {
        matchLabels: {
          app: appName,
        },
      },
      template: {
        metadata: {
          labels: {
            app: appName,
          },
        },
        spec: {
          priorityClassName: cloudInfo.envToPriorityClass(if cloudInfo.isDevCluster(cloud) then 'DEV' else 'PROD'),
          containers: [
            {
              name: 'pushgateway',
              image: 'prom/pushgateway:v1.6.2',
              ports: [
                {
                  containerPort: 9091,
                  name: 'http',
                },
              ],
              resources: {
                limits: {
                  cpu: '200m',
                  memory: '256Mi',
                },
                requests: {
                  cpu: '100m',
                  memory: '128Mi',
                },
              },
              livenessProbe: {
                httpGet: {
                  path: '/-/healthy',
                  port: 9091,
                },
                initialDelaySeconds: 30,
                periodSeconds: 10,
              },
              readinessProbe: {
                httpGet: {
                  path: '/-/ready',
                  port: 9091,
                },
                initialDelaySeconds: 5,
                periodSeconds: 5,
              },
            },
          ],
        },
      },
    },
  };

  local service = {
    apiVersion: 'v1',
    kind: 'Service',
    metadata: {
      name: appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    spec: {
      ports: [
        {
          name: 'http',
          port: 9091,
          targetPort: 9091,
        },
      ],
      selector: {
        app: appName,
      },
    },
  };

  // Add PodMonitoring to scrape metrics from Pushgateway
  local podMonitoring = {
    apiVersion: 'monitoring.googleapis.com/v1',
    kind: 'PodMonitoring',
    metadata: {
      name: appName,
      namespace: namespace,
    },
    spec: {
      selector: {
        matchLabels: {
          app: appName,
        },
      },
      endpoints: [
        {
          port: 9091,
          interval: '30s',
          path: '/metrics',
        },
      ],
    },
  };

  [deployment, service, podMonitoring]
