local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local monitoringLib = import 'deploy/gcp/monitoring-lib.jsonnet';

local completionTeamPodPrefixes = [
  'completion-',
  'infer-elden',
  'embedder-methanol',
  'embedding-indexer-methanol',
  'embedder-starethanol-smart',
  'embedding-indexer-starethanol-smart',
];
local completionTeamPodFilter = 'pod=~"(%s).*"' % std.join('|', completionTeamPodPrefixes);
local nextEditTeamPodPrefixes = [
  'infer-raven',
  'next-edit-',
  'embedder-raven',
  'embedding-indexer-raven',
];
local nextEditTeamPodFilter = 'pod=~"(%s).*"' % std.join('|', nextEditTeamPodPrefixes);
local chatTeamPodPrefixes = [
  'chat-',
  'edit-',
  'embedder-chatanol',
  'embedding-indexer-chatanol',
  'infer-forger',
  'infer-pleasehold',
  'infer-sentry',
  'third-party-arbiter',
];
local chatTeamPodFilter = 'pod=~"(%s).*"' % std.join('|', chatTeamPodPrefixes);
local extendedContextTeamPodPrefixes = [
  'github-',
  'glean-',
  'slack-bot-',
];
local extendedContextTeamPodFilter = 'pod=~"(%s).*"' % std.join('|', extendedContextTeamPodPrefixes);
local insightsTeamPodPrefixes = [
  'request-insight-',
  'ri-',
  'gcs-proxy',
];
local insightsTeamPodFilter = 'pod=~"(%s).*"' % std.join('|', insightsTeamPodPrefixes);
// We want to ignore GKE namespaces in some alerts, since we don't directly control those
local namespaceGke = 'kube-system|gmp-public|gmp-system';
local uncategorizedPodFilter = 'namespace!~"%s", pod!~"(%s).*"' % [namespaceGke, std.join('|', completionTeamPodPrefixes + nextEditTeamPodPrefixes + chatTeamPodPrefixes + extendedContextTeamPodPrefixes + insightsTeamPodPrefixes)];

// Deployment prefixes generally match pod prefixes
local completionTeamDeploymentFilter = 'deployment=~"(%s).*"' % std.join('|', completionTeamPodPrefixes);
local nextEditTeamDeploymentFilter = 'deployment=~"(%s).*"' % std.join('|', nextEditTeamPodPrefixes);
local chatTeamDeploymentFilter = 'deployment=~"(%s).*"' % std.join('|', chatTeamPodPrefixes);
local extendedContextTeamDeploymentFilter = 'deployment=~"(%s).*"' % std.join('|', extendedContextTeamPodPrefixes);
local insightsTeamDeploymentFilter = 'deployment=~"(%s).*"' % std.join('|', insightsTeamPodPrefixes);
local uncategorizedDeploymentFilter = 'namespace!~"%s", deployment!~"(%s).*"' % [namespaceGke, std.join('|', completionTeamPodPrefixes + nextEditTeamPodPrefixes + chatTeamPodPrefixes + extendedContextTeamPodPrefixes + insightsTeamPodPrefixes)];

function(cloud)
  local lowDiskSpec = {
    displayName: 'Persistent volume free disk space low',
    conditionPrometheusQueryLanguage: {
      duration: '300s',
      evaluationInterval: '60s',
      labels: { severity: 'warning' },
      query: |||
        min without (job, instance) (kubelet_volume_stats_available_bytes{namespace!~"test.*|dev-.*"}
        / kubelet_volume_stats_capacity_bytes{namespace!~"test.*|dev-.*"}) < 0.10
      |||,
    },
  };
  local lowH100Spec = {
    displayName: 'H100 free machines low',
    conditionPrometheusQueryLanguage: {
      duration: '900s',
      evaluationInterval: '300s',
      labels: { severity: 'warning' },
      query: |||
        sum by(cluster) (kube_node_spec_taint{key="gpu", value="8h100"}) * 8
        - sum by(cluster) (
          max by(cluster, pod) (kube_pod_tolerations{key="gpu", value="8h100"}) * max by(cluster, pod) (kube_pod_container_resource_requests{resource="nvidia_com_gpu"}) > 0
          and max by(cluster, pod) (kube_pod_container_status_terminated) == 0
        ) < 2
      |||,
    },
  };
  local lowE2Spec = {
    displayName: 'E2 machines near autoscaling limit',
    conditionPrometheusQueryLanguage: {
      duration: '900s',
      evaluationInterval: '300s',
      labels: { severity: 'warning' },
      query: |||
        sum(avg_over_time(compute_googleapis_com:instance_group_size{monitored_resource="instance_group",instance_group_name=~".*-pool-cpu-.*",location=~"%(region)s-.*"}[10m])) > %(threshold)d
      ||| % {
        region: cloudInfo[cloud].region,
        // 90% threshold * autoscaling limit from node_groups.jsonnet * number of zones per region
        threshold: 0.9 * 512 * 3,
      },
    },
  };

  // au_inference_host_context_processing_latency_v2_seconds measures how long requests
  // are spending on their context processing rounds. Since context processing is exclusive
  // (only one request can be doing it at a time) and is the only part of inference that is
  // exclusive, summing those latencies and treating them as a fraction of the aggregation
  // interval gives us a rough estimate of utilization % per pod (i.e. as we approach 100%
  // we may be queueing and delaying requests). The further `max_over_time` interval hijinks
  // on the queries are to prevent the alert from missing cases where the load is spiky but
  // still frequently exceeds our threshold.
  local inferenceUtilizationSpec = {
    displayName: 'Inference GPU utilization sustained load over 70%',
    conditionPrometheusQueryLanguage: {
      duration: '1800s',
      evaluationInterval: '300s',
      labels: { severity: 'warning' },
      // This query is designed to detect high average utilization of the model over long (30 minute) timescales.
      query: |||
        max_over_time(
          avg by(model_name, cluster) (
            max by (model_name, cluster, instance) (
              rate(au_inference_host_context_processing_latency_v2_seconds_sum{namespace="central"}[30m])
            )
          )[1d:30m]
        ) > 0.7
      |||,
    },
  };

  local crashloopAlert(cloud, displayName, filter, durationSeconds, team) =
    local spec = {
      displayName: displayName,
      conditionPrometheusQueryLanguage: {
        duration: '%ds' % (durationSeconds),
        evaluationInterval: '60s',
        labels: { severity: 'warning' },
        query: |||
          sum by (cluster, namespace, deployment)(
            label_replace(
              increase(kube_pod_container_status_restarts_total{%(filter)s}[10m]),
              "deployment", "$1", "pod", "(.*)-[^-]+-[^-]+$"
            )
          )
          >=
          sum by (cluster, namespace, deployment) (
              label_replace(
                kube_pod_container_info{%(filter)s} offset 5m,
                "deployment", "$1", "pod", "(.*)-[^-]+-[^-]+$"
              )
          ) * 0.9
        ||| % {
          filter: filter,
        },
      },
    };
    local name = std.asciiLower(std.strReplace(displayName, ' ', '-'));
    local description = 'Deployment %s in namespace %s cluster %s has been crashlooping over the past %d+ minutes' % [
      monitoringLib.label('deployment'),
      monitoringLib.label('namespace'),
      monitoringLib.label('cluster'),
      durationSeconds / 60,
    ];
    monitoringLib.alertPolicy(cloud, spec, name, description, team);

  local highCpuSpec = {
    displayName: 'High container CPU usage',
    conditionPrometheusQueryLanguage: {
      duration: '1200s',
      evaluationInterval: '60s',
      labels: { severity: 'warning' },
      // We mostly care about high CPU usage for services in the client request critical path. We exclude devtools and GKE resources.
      query: |||
        avg by (container_name, namespace_name, cluster_name) (max_over_time(kubernetes_io:container_cpu_limit_utilization{monitored_resource="k8s_container",namespace_name!~"devtools|%s"}[10m])) > 0.9
      ||| % namespaceGke,
    },
  };

  local podHighMemoryAlert(cloud, displayName, podPrefixFilter, durationSeconds, team, extraFilter=null) =
    local podFilter = if podPrefixFilter != null then ', pod_name=~"(%s).*"' % std.join('|', podPrefixFilter) else '';
    local filter = if extraFilter != null then ', %s' % extraFilter else '';
    local spec = {
      displayName: displayName,
      conditionPrometheusQueryLanguage: {
        duration: '%ds' % (durationSeconds),
        evaluationInterval: '60s',
        labels: { severity: 'warning' },
        query: |||
          quantile by (container_name, cluster_name) (
            0.99,
            avg_over_time(kubernetes_io:container_memory_limit_utilization{
              monitored_resource="k8s_container",
              namespace_name!~"devtools|%s"%s%s
            }[5m])
          ) > 0.9
        ||| % [namespaceGke, podFilter, filter],
      },
    };
    local name = std.asciiLower(std.strReplace(displayName, ' ', '-'));
    local description = 'Over 1%% of pods %s in cluster %s have high memory usage for %d+ minutes.' % [
      monitoringLib.label('container_name'),
      monitoringLib.label('cluster_name'),
      durationSeconds / 60,
    ];
    monitoringLib.alertPolicy(cloud, spec, name, description, team);

  local deploymentAutoscaleMaxReplicasAlert(cloud, displayName, filter, maxReplicaCount) =
    local spec = {
      displayName: displayName,
      conditionPrometheusQueryLanguage: {
        duration: '300s',
        evaluationInterval: '60s',
        labels: { severity: 'warning' },
        query: |||
          min_over_time(sum by (cluster, namespace, deployment)(label_replace(kube_pod_container_status_running{%(filter)s}, "deployment", "$1", "pod", "(.+)-[0-9a-f]+-[0-9a-z]+$"))[1h:10m]) >= %(maxReplicaCount)d
        ||| % {
          filter: filter,
          maxReplicaCount: maxReplicaCount,
        },
      },
    };
    local name = std.asciiLower(std.strReplace(displayName, ' ', '-'));
    local description = 'Deployment %s in namespace %s cluster %s been scaled to max replicas for 1 hour or more.' % [
      monitoringLib.label('deployment'),
      monitoringLib.label('namespace'),
      monitoringLib.label('cluster'),
    ];
    monitoringLib.alertPolicy(cloud, spec, name, description);

  local unschedulablePodSpec = {
    displayName: 'Cannot schedule pod',
    conditionPrometheusQueryLanguage: {
      duration: '600s',
      evaluationInterval: '60s',
      labels: { severity: 'warning' },
      query: |||
        kube_pod_status_unschedulable{namespace!~"%s"} > 0
      ||| % namespaceGke,
    },
  };

  local deploymentWithNoReplicasAlert(cloud, displayName, filter, durationSeconds, team) =
    local spec = {
      displayName: displayName,
      conditionPrometheusQueryLanguage: {
        duration: '%ds' % (durationSeconds),
        evaluationInterval: '60s',
        labels: { severity: 'error' },
        query: |||
          (max_over_time(kube_deployment_status_replicas_ready{%(filter)s}[5m]) == 0) and (max_over_time(kube_deployment_spec_replicas{%(filter)s}[5m]) > 0)
        ||| % {
          filter: filter,
        },
      },
    };
    local name = std.asciiLower(std.strReplace(displayName, ' ', '-'));
    local description = 'Deployment %s in namespace %s cluster %s has had 0 running replicas for the last %d+ minutes' % [
      monitoringLib.label('deployment'),
      monitoringLib.label('namespace'),
      monitoringLib.label('cluster'),
      durationSeconds / 60,
    ];
    monitoringLib.alertPolicy(cloud, spec, name, description, team);


  local deploymentReplicasUnavailableAlert(cloud, displayName, filter, durationSeconds, team) =
    local spec = {
      displayName: displayName,
      conditionPrometheusQueryLanguage: {
        duration: '%ds' % (durationSeconds),
        evaluationInterval: '60s',
        labels: { severity: 'warning' },
        query: |||
          min_over_time(kube_deployment_status_replicas_unavailable{%(filter)s}[5m]) > 0
        ||| % {
          filter: filter,
        },
      },
    };
    local name = std.asciiLower(std.strReplace(displayName, ' ', '-'));
    local description = 'Deployment %s in namespace %s cluster %s has had unavailable replicas for the last %d+ minutes' % [
      monitoringLib.label('deployment'),
      monitoringLib.label('namespace'),
      monitoringLib.label('cluster'),
      durationSeconds / 60,
    ];
    monitoringLib.alertPolicy(cloud, spec, name, description, team);

  local signupsRunningLowCondition = {
    conditionPrometheusQueryLanguage: {
      duration: '300s',
      evaluationInterval: '600s',
      query: |||
        max(au_auth_central_signup_credits{namespace="central"}) < 250
      |||,
      labels: { severity: 'error' },
    },
    displayName: 'Signups running low',
  };

  [
    monitoringLib.alertPolicy(cloud, lowDiskSpec, 'pv-free-space', 'A volume has less than 10% free disk space'),
    monitoringLib.alertPolicy(cloud, highCpuSpec, 'cpu-usage-alert', 'Container %s in namespace %s cluster %s has high CPU usage for 10+ minutes.' % [
      monitoringLib.label('container_name'),
      monitoringLib.label('namespace_name'),
      monitoringLib.label('cluster_name'),
    ]),
    podHighMemoryAlert(cloud, 'Completion pod high memory usage', completionTeamPodPrefixes, 1200, team='completion'),
    podHighMemoryAlert(cloud, 'Next Edit pod high memory usage', nextEditTeamPodPrefixes, 1200, team='next-edit'),
    podHighMemoryAlert(cloud, 'Chat pod high memory usage', chatTeamPodPrefixes, 1200, team='chat'),
    podHighMemoryAlert(cloud, 'Extended Context pod high memory usage', extendedContextTeamPodPrefixes, 1200, team='extended-context'),
    podHighMemoryAlert(cloud, 'Insights pod high memory usage', insightsTeamPodPrefixes, 1200, team='insights'),

    // remove all prefixes "claimed" by teams and allow embeddings search. High memory on embeddings search and content manager is expected
    podHighMemoryAlert(cloud,
                       'Uncategorized pod high memory usage',
                       null,
                       1200,
                       team='default',
                       extraFilter='pod_name!~"(%s).*", pod_name!~"embeddings-search.*|content-manager.*|working-set.*"' % std.join('|', completionTeamPodPrefixes +
                                                                                                                                         nextEditTeamPodPrefixes + chatTeamPodPrefixes + extendedContextTeamPodPrefixes + insightsTeamPodPrefixes)),
    monitoringLib.alertPolicy(cloud, unschedulablePodSpec, 'unschedulable-pod-alert', 'Pod %s in namespace %s cluster %s has failed to schedule for the last 10+ minutes.' % [
      monitoringLib.label('pod'),
      monitoringLib.label('namespace'),
      monitoringLib.label('cluster'),
    ]),

    deploymentAutoscaleMaxReplicasAlert(cloud, 'Bigtable proxy hitting autoscaling limit', 'container="bigtable-proxy"', 16),
    deploymentAutoscaleMaxReplicasAlert(cloud, 'Checkpoint indexer hitting autoscaling limit', 'container="checkpoint-indexer"', 4),

    deploymentWithNoReplicasAlert(cloud, 'Completions deployment has no running replicas', completionTeamDeploymentFilter, 600, team='completion'),
    deploymentWithNoReplicasAlert(cloud, 'Next Edit deployment has no running replicas', nextEditTeamDeploymentFilter, 600, team='next-edit'),
    deploymentWithNoReplicasAlert(cloud, 'Chat deployment has no running replicas', chatTeamDeploymentFilter, 600, team='chat'),
    deploymentWithNoReplicasAlert(cloud, 'Extended Context deployment has no running replicas', extendedContextTeamDeploymentFilter, 600, team='extended-context'),
    deploymentWithNoReplicasAlert(cloud, 'Insights deployment has no running replicas', insightsTeamDeploymentFilter, 600, team='insights'),
    deploymentWithNoReplicasAlert(cloud, 'Deployment has no running replicas', uncategorizedDeploymentFilter, 600, team='default'),

    deploymentReplicasUnavailableAlert(cloud, 'Completions deployment has unavailable replicas', completionTeamDeploymentFilter, 1200, team='completion'),
    deploymentReplicasUnavailableAlert(cloud, 'Next Edit deployment has unavailable replicas', nextEditTeamDeploymentFilter, 1200, team='next-edit'),
    deploymentReplicasUnavailableAlert(cloud, 'Chat deployment has unavailable replicas', chatTeamDeploymentFilter, 1200, team='chat'),
    deploymentReplicasUnavailableAlert(cloud, 'Extended Context deployment has unavailable replicas', extendedContextTeamDeploymentFilter, 1200, team='extended-context'),
    deploymentReplicasUnavailableAlert(cloud, 'Insights deployment has unavailable replicas', insightsTeamDeploymentFilter, 1200, team='insights'),
    deploymentReplicasUnavailableAlert(cloud, 'Deployment has unavailable replicas', uncategorizedDeploymentFilter, 1200, team='default'),

    // We want this alert to run in the dev and prod projects, so that we know
    // if we are low in GPUs in any cluster in either of those projects
    monitoringLib.alertPolicy(cloud, lowH100Spec, 'gpu-h100-free', 'Less than 2 free H100 GPU are available in cluster %s' % monitoringLib.label('cluster'), enableInDev=true),
    // Warn if more than 90% of E2 (CPU) autoscaling limit is being used
    monitoringLib.alertPolicy(cloud, lowE2Spec, 'e2-free-machines-low', 'E2 machines near autoscaling limit in %s' % cloud),
    // Warn if we may need to add GPUs to a model
    monitoringLib.alertPolicy(cloud, inferenceUtilizationSpec, 'inference-gpu-utilization', 'Saturating %s GPUs in cluster %s; may need more replicas.' % [monitoringLib.label('model_name'), monitoringLib.label('cluster')]),

    crashloopAlert(cloud, 'Completion container crashloop', completionTeamPodFilter, 15 * 60, team='completion'),
    crashloopAlert(cloud, 'Next Edit container crashloop', nextEditTeamPodFilter, 15 * 60, team='next-edit'),
    crashloopAlert(cloud, 'Chat container crashloop', chatTeamPodFilter, 15 * 60, team='chat'),
    crashloopAlert(cloud, 'Extended Context container crashloop', extendedContextTeamPodFilter, 15 * 60, team='extended-context'),
    crashloopAlert(cloud, 'Insights container crashloop', insightsTeamPodFilter, 15 * 60, team='insights'),
    crashloopAlert(cloud, 'Container crashloop', uncategorizedPodFilter, 15 * 60, team='default'),
    // The crashloop alert has a higher threshold in GKE namespaces, since we
    // don't directly control those
    crashloopAlert(cloud, 'GKE container crashloop', 'namespace=~"%s"' % namespaceGke, 60 * 60, team='default'),

    monitoringLib.alertPolicy(cloud, signupsRunningLowCondition, 'signups-running-low', 'Signups are running low', team='self-serve'),
  ]
