load(
    "@rules_jsonnet//jsonnet:jsonnet.bzl",
    "jsonnet_library",
)
load("//tools/bzl:go.bzl", "go_oci_image")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:metadata.bzl", "metadata_test")
load("//tools/bzl/helm:helm_template.bzl", "helm_template")

go_oci_image(
    name = "pagerduty-exporter-image",
    package_name = package_name(),
    binary = "@com_github_webdevops_pagerduty_exporter//:pagerduty-exporter",
    entrypoint = "/pagerduty-exporter_/pagerduty-exporter",
    workdir = "/pagerduty-exporter_",
)

kubecfg(
    name = "kubecfg_pagerduty_exporter",
    src = "pagerduty-exporter.jsonnet",
    data = [
        ":pagerduty-exporter-image",
    ],
    deps = [
        "//deploy/common:cloud_info",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/gcp:gcp-lib",
    ],
)

kubecfg(
    name = "kubecfg_monitoring",
    src = "monitoring.jsonnet",
    cluster_wide = True,
    deps = [
        "//deploy/common:cloud_info",
        "//deploy/gcp:monitoring-lib",
    ],
)

kubecfg(
    name = "kubecfg_managed_prometheus",
    src = "managed_prometheus.jsonnet",
    cluster_wide = True,
    deps = [
        "//deploy/common:cloud_info",
        "//deploy/common:lib",
        "//deploy/gcp:gcp-lib",
    ],
)

kubecfg(
    name = "kubecfg_grafana",
    src = "grafana.jsonnet",
    cluster_wide = True,
    deps = [
        "//deploy/common:cloud_info",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/gcp:gcp-lib",
    ],
)

kubecfg(
    name = "kubecfg_logging",
    src = "logging.jsonnet",
    cluster_wide = True,
    deps = [
        "//deploy/common:cloud_info",
    ],
)

kubecfg(
    name = "kubecfg_dcgm_exporter",
    src = "dcgm-exporter.yaml",
    cluster_wide = True,
    lint = False,
    norewrite = True,
)

kubecfg(
    name = "kubecfg_kube_state_metrics",
    src = "kube-state-metrics.yaml",
    cluster_wide = True,
    lint = False,
)

kubecfg(
    name = "kubecfg_opentelemetry_collector",
    src = "opentelemetry-collector.jsonnet",
    cluster_wide = True,
    data = [
        "//tools/monitoring:opentelemetry-collector.yaml",
    ],
    deps = [
        "//deploy/common:cloud_info",
        "//deploy/common:lib",
        "//deploy/gcp:gcp-lib",
    ],
)

kubecfg(
    name = "kubecfg_pushgateway",
    src = "pushgateway.jsonnet",
    cluster_wide = True,
    deps = [
        "//deploy/common:cloud_info",
    ],
)

jsonnet_library(
    name = "pagerduty_lib",
    srcs = [
        "pagerduty_sealed.jsonnet",
    ],
)

jsonnet_library(
    name = "agents-monitoring-lib",
    srcs = ["agents-monitoring-lib.jsonnet"],
    deps = [
        "//deploy/gcp:monitoring-lib",
    ],
)

kubecfg(
    name = "kubecfg_agents_monitoring",
    src = "agents-monitoring.jsonnet",
    cloud = [
        "GCP_AGENT_US_CENTRAL1_PROD",
        "GCP_AGENT_EU_WEST4_PROD",
    ],
    cluster_wide = True,
    deps = [
        "//deploy/common:cloud_info",
        "//deploy/common:lib",
        "//tools/monitoring:agents-monitoring-lib",
    ],
)

metadata_test(
    name = "metadata_test",
    src = "METADATA.jsonnet",
    deps = [
        ":kubecfg_agents_monitoring",
        ":kubecfg_dcgm_exporter",
        ":kubecfg_grafana",
        ":kubecfg_kube_state_metrics",
        ":kubecfg_logging",
        ":kubecfg_managed_prometheus",
        ":kubecfg_monitoring",
        ":kubecfg_opentelemetry_collector",
        ":kubecfg_pagerduty_exporter",
        ":kubecfg_pushgateway",
    ],
)
