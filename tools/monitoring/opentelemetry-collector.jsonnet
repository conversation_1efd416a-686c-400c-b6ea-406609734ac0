local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';

// opentelemetry-collector.yaml was generated using
//   kubectl kustomize https://github.com/GoogleCloudPlatform/otlp-k8s-ingest.git/k8s/base
local opentelemetryCollectorRawYaml = importstr 'opentelemetry-collector.yaml';
local opentelemetryCollectorObjects = std.map(
  function(obj) obj + {
    metadata+: {
      labels+: {
        app: 'opentelemetry-collector',
      },
    },
  }, std.filter(function(obj) obj.kind != 'Namespace' && obj.kind != 'ServiceAccount', std.parseYaml(opentelemetryCollectorRawYaml))
);

function(cloud)
  local serviceAccount = gcpLib.createServiceAccount(
    app='opentelemetry-collector',
    cloud=cloud,
    env='PROD',
    namespace='opentelemetry',
    iam=true,
    serviceAccountName='opentelemetry-collector',
    iamServiceAccountName='opentelemetry-collector-iam',
  );

  lib.flatten([
    opentelemetryCollectorObjects,
    serviceAccount.serviceAccountObjects,
    if cloudInfo.isLeadCluster(cloud) then serviceAccount.workloadIdentityObjects,
    if cloudInfo.isLeadCluster(cloud) then {
      apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
      kind: 'IAMPartialPolicy',
      metadata: {
        name: 'opentelemetry-collector-project-policy',
        namespace: 'opentelemetry',
        labels: {
          app: 'opentelemetry-collector',
        },
      },
      spec: {
        resourceRef: {
          kind: 'Project',
          external: 'projects/%s' % cloudInfo[cloud].projectId,
        },
        bindings: [
          {
            role: 'roles/monitoring.metricWriter',
            members: [
              { member: 'serviceAccount:%s' % serviceAccount.serviceAccountGcpEmailAddress },
            ],
          },
          {
            role: 'roles/logging.logWriter',
            members: [
              { member: 'serviceAccount:%s' % serviceAccount.serviceAccountGcpEmailAddress },
            ],
          },
          {
            role: 'roles/cloudtrace.agent',
            members: [
              { member: 'serviceAccount:%s' % serviceAccount.serviceAccountGcpEmailAddress },
            ],
          },
        ],
      },
    },
  ])
