// see https://www.notion.so/Bazel-based-testing-and-deployment-638c55d03c9a446c884fd0b0d0b25447?pvs=4#74fd85baf83f43fd97c47bb4485d218e for details
{
  deployment: [
    {
      name: 'central-monitoring',
      kubecfg: {
        target: '//tools/monitoring:kubecfg_monitoring',
        task: [
          {
            cloud: 'ALL_LEADS',
          },
        ],
      },
    },
    {
      name: 'dcgm-exporter',
      kubecfg: {
        target: '//tools/monitoring:kubecfg_dcgm_exporter',
        task: [
          {
            cloud: 'ALL_GCP',
          },
        ],
      },
    },
    {
      name: 'grafana',
      kubecfg: {
        target: '//tools/monitoring:kubecfg_grafana',
        task: [
          {
            cloud: 'ALL_GCP',
          },
        ],
      },
    },
    {
      name: 'pagerduty-exporter',
      kubecfg: {
        target: '//tools/monitoring:kubecfg_pagerduty_exporter',
        task: [
          {
            namespace: 'devtools',
            env: 'PROD',
            cloud: 'GCP_US_CENTRAL1_PROD',
          },
        ],
      },
      health: {
        tier: 'TIER_2',
        experts: {
          users: ['dirk'],
          slack_channel: '#system-services',
        },
      },
    },
    {
      name: 'logging',
      kubecfg: {
        target: '//tools/monitoring:kubecfg_logging',
        task: [
          {
            cloud: 'GCP_US_CENTRAL1_PROD',
          },
        ],
      },
    },
    {
      name: 'managed-prometheus',
      kubecfg: {
        target: '//tools/monitoring:kubecfg_managed_prometheus',
        task: [
          {
            cloud: 'ALL_GCP',
          },
        ],
      },
    },
    {
      name: 'pushgateway',
      kubecfg: {
        target: '//tools/monitoring:kubecfg_pushgateway',
        task: [
          {
            cloud: 'ALL_GCP',
          },
        ],
      },
    },
    {
      name: 'opentelemetry-collector',
      kubecfg: {
        target: '//tools/monitoring:kubecfg_opentelemetry_collector',
        task: [
          {
            cloud: 'ALL_GCP',
          },
        ],
      },
      health: {
        tier: 'TIER_1_B',
        experts: {
          users: ['costa'],
          slack_channel: '#system-services',
        },
      },
    },
    {
      name: 'kube-state-metrics',
      kubecfg: {
        target: '//tools/monitoring:kubecfg_kube_state_metrics',
        task: [
          {
            cloud: 'ALL_GCP',
          },
        ],
      },
    },
    {
      name: 'agents-monitoring',
      kubecfg: {
        target: '//tools/monitoring:kubecfg_agents_monitoring',
        task: [
          {
            cloud: 'ALL_GCP_AGENTS',
          },
        ],
      },
      deployment_schedule_name: 'DISABLED',
      health: {
        tier: 'TIER_2',
        experts: {
          users: ['marcmac'],
          slack_channel: '#remote-agents',
        },
      },
    },
  ],
}
